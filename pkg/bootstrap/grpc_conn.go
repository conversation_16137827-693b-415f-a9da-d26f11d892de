package bootstrap

import (
	"context"
	"crypto/tls"
	"errors"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	kgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/logging"
	appmetrics "gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/metrics"
	"go.opentelemetry.io/otel"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

var clientMetrics = appmetrics.NewOtelRequestMetrics(appmetrics.Options{
	Module: appmetrics.MetricModuleGrpcClient,
})

type GrpcConfig struct {
	Endpoint string
	Timeout  time.Duration
	Secured  bool
	Logger   log.Logger
}

func InitGrpcConn(connConf *GrpcConfig, grpcOpts ...grpc.DialOption) (*grpc.ClientConn, error) {
	if connConf.Timeout == 0 {
		connConf.Timeout = 10 * time.Second
	}
	if connConf.Logger == nil {
		connConf.Logger = log.GetLogger()
	}

	ctx, cancel := context.WithTimeout(context.Background(), connConf.Timeout)
	defer cancel()

	//grpcOpts = append(grpcOpts, grpc.WithBlock())
	//grpcOpts = append(grpcOpts, grpc.WithResolvers(dns.NewBuilder(connTimeout)))
	grpcOpts = append(grpcOpts, grpc.WithDisableHealthCheck())

	if !connConf.Secured {
		grpcOpts = append(grpcOpts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	}

	var opts []kgrpc.ClientOption
	opts = append(opts, kgrpc.WithEndpoint(connConf.Endpoint))
	opts = append(opts, kgrpc.WithTimeout(connConf.Timeout))
	opts = append(opts, kgrpc.WithTLSConfig(&tls.Config{
		InsecureSkipVerify: false,
		MinVersion:         tls.VersionTLS12,
	}))
	opts = append(opts, kgrpc.WithOptions(grpcOpts...))
	opts = append(opts, kgrpc.WithMiddleware(
		tracing.Client(
			tracing.WithTracerProvider(otel.GetTracerProvider()),
			tracing.WithPropagator(otel.GetTextMapPropagator()),
		),
		metrics.Client(
			metrics.WithRequests(clientMetrics.HandledCounter),
			metrics.WithSeconds(clientMetrics.HandledHistogram),
		),
		logging.Client(connConf.Logger),
	))

	if connConf.Secured {
		return kgrpc.Dial(ctx, opts...)
	}

	return kgrpc.DialInsecure(ctx, opts...)
}

func WithMetadataAppendInterceptor(kv ...string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		if len(kv)%2 != 0 {
			return errors.New("metadata key value must be even")
		}
		ctx = metadata.AppendToOutgoingContext(ctx, kv...)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func WithCliTraceFromCtxInterceptor(getFunc func(ctx context.Context) string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		ctx = metadata.AppendToOutgoingContext(ctx,
			"trace_id", getFunc(ctx),
		)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func TraceIDFromCtx(ctx context.Context) string {
	traceIDValFromTracing := tracing.TraceID()(ctx)
	traceIDStrFromTracing, ok := traceIDValFromTracing.(string)
	if ok && traceIDStrFromTracing != "" {
		return traceIDStrFromTracing
	}

	traceIDValFromCtx := ctx.Value("trace.id")
	traceIDStrFromCtx, ok := traceIDValFromCtx.(string)
	if ok && traceIDStrFromCtx != "" {
		return traceIDStrFromCtx
	}

	return ""
}
