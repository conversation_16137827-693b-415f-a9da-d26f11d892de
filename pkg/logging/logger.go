package logging

import (
	"os"

	logzap "github.com/go-kratos/kratos/contrib/log/zap/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type LoggerInfo struct {
	Id      string
	Name    string
	Version string
}

func MustNewLogger(serviceInfo *LoggerInfo) log.Logger {
	encoderConfig := zap.NewProductionConfig()
	logLevel, _ := zap.ParseAtomicLevel("debug")

	enc := zapcore.NewJSONEncoder(encoderConfig.EncoderConfig)
	core := zapcore.NewCore(
		enc, zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout)),
		logLevel,
	)

	l := logzap.NewLogger(zap.New(core))
	logger := log.With(l,
		"service.id", serviceInfo.Id,
		"service.name", serviceInfo.Name,
		"service.version", serviceInfo.Version,
		"caller", log.DefaultCaller,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
	)
	log.SetLogger(logger)
	return logger
}
