package maintenance

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	redisUni "github.com/go-redis/redis/v8"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/platform/common/redis"
)

var ProviderSet = wire.NewSet(NewMaintenance)

const (
	defaultTimeout        = 5 * time.Second
	maintenanceKey        = "fin:installment:maintenance"
	maintenanceKeyPattern = "fin:installment:maintenance:%s"
)

type Handler interface {
	GetMaintenanceState(ctx context.Context, partner partner.PartnerCode) (State, error)
	SetMaintenanceState(ctx context.Context, partner partner.PartnerCode, mState State) error
	CheckFeaturesMaintenance(ctx context.Context, partner partner.PartnerCode, feats []Feat) (bool, error)
}

type maintenance struct {
	logger *log.Helper
	cache  redis.CacheNoCaller
}

func NewMaintenance(cache redis.CacheNoCaller, kLogger log.Logger) Handler {
	logging := log.With(kLogger, "module", "maintenance")
	logger := log.NewHelper(logging)
	return &maintenance{
		logger: logger,
		cache:  cache,
	}
}

func (m *maintenance) GetMaintenanceState(ctx context.Context, partner partner.PartnerCode) (State, error) {
	redisCli := m.cache.GetRedisClient()
	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	var mData State
	var mKey = fmt.Sprintf(maintenanceKeyPattern, partner.Lowercase())
	err := redisCli.HGetAll(ctx, mKey).Scan(&mData)
	if errors.Is(err, redisUni.Nil) {
		return State{}, nil
	}
	if err != nil {
		m.logger.WithContext(ctx).Errorf("get maintenance state failed, err=%v", err)
		return State{}, errors.Wrap(err, "get maintenance state from cache failed")
	}
	return mData, nil
}

func (m *maintenance) SetMaintenanceState(ctx context.Context, partner partner.PartnerCode, mState State) error {
	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	if mState.All {
		mState.Onboarding = true
		mState.Purchase = true
		mState.Repayment = true
	}

	mData := map[string]interface{}{
		FeatAll.String():        mState.All,
		FeatOnboarding.String(): mState.Onboarding,
		FeatPurchase.String():   mState.Purchase,
		FeatRepayment.String():  mState.Repayment,
	}
	mKey := fmt.Sprintf(maintenanceKeyPattern, partner.Lowercase())
	if err := m.cache.HSetWithMap(ctx, mKey, mData); err != nil {
		m.logger.WithContext(ctx).Errorf("set maintenance state failed, err=%v", err)
		return errors.Wrap(err, "set maintenance state into cache failed")
	}
	return nil
}

func (m *maintenance) CheckFeaturesMaintenance(ctx context.Context, partner partner.PartnerCode, feats []Feat) (bool, error) {
	mState, err := m.GetMaintenanceState(ctx, partner)
	if err != nil {
		return false, err
	}
	if mState.All {
		return true, nil
	}

	featMap := map[Feat]bool{
		FeatOnboarding: mState.Onboarding,
		FeatPurchase:   mState.Purchase,
		FeatRepayment:  mState.Repayment,
	}
	for _, feat := range feats {
		value, ok := featMap[feat]
		if value && ok {
			return true, nil
		}
	}
	return false, nil
}
