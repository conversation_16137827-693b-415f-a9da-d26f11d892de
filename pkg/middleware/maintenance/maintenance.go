package maintenance

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

const (
	ReasonInternalError            = "INTERNAL_ERROR"
	ReasonResourceUnderMaintenance = "RESOURCE_UNDER_MAINTENANCE"
)

func Server(maintMgr maintenance.Handler, maintElms []maintenance.Element) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			tr, ok := transport.FromServerContext(ctx)
			if !ok {
				return nil, errors.InternalServer(ReasonInternalError, "wrong context for middleware")
			}

			element, ok := findMaintenanceElement(maintElms, tr.Operation())
			if !ok {
				return handler(ctx, req)
			}

			// TODO: get default partner from context or request
			defaultPartner := partner.PartnerCIMB
			underMaintain, err := maintMgr.CheckFeaturesMaintenance(ctx, defaultPartner, element.Features)
			if err != nil {
				return handler(ctx, req)
			}
			if underMaintain {
				return nil, errors.ServiceUnavailable(ReasonResourceUnderMaintenance, "api/service is under maintenance")
			}
			return handler(ctx, req)
		}
	}
}

func findMaintenanceElement(maintElms []maintenance.Element, operation string) (*maintenance.Element, bool) {
	for _, e := range maintElms {
		if e.Operation == operation {
			return &e, true
		}
	}
	return nil, false
}
