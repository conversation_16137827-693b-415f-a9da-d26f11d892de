package metrics

import (
	"fmt"

	prom "github.com/prometheus/client_golang/prometheus"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/metric"
)

const NamespaceMetricHttpServer = "http_server"
const NamespaceMetricGrpcServer = "grpc_server"
const NamespaceMetricGrpcClient = "grpc_client"

const (
	MetricPaymentMeter = "fin.installment.payment"
)

const (
	MetricModuleHttpServer = "http_server"
	MetricModuleHttpClient = "http_client"
	MetricModuleGrpcServer = "grpc_server"
	MetricModuleGrpcClient = "grpc_client"
	MetricModuleRepository = "repository"

	MetricNameDurationSecond   = "duration_seconds"
	MetricNameRequestCounter   = "requests_code_total"
	MetricNameRequestHistogram = "requests"
)

type RequestMetrics struct {
	HandledCounter   *prom.CounterVec
	HandledHistogram *prom.HistogramVec
}

type Options struct {
	Name      string
	Module    string
	Namespace string
}

type OtelRequestMetrics struct {
	HandledCounter   metric.Int64Counter
	HandledHistogram metric.Float64Histogram
}

func NewOtelRequestMetrics(opts Options) *OtelRequestMetrics {
	meter := otel.GetMeterProvider().Meter(opts.Namespace)
	counterName := getMetricName(opts.Module, MetricNameRequestCounter)
	counterMetric, _ := meter.Int64Counter(
		counterName,
		metric.WithUnit("{call}"),
		metric.WithDescription("The total number of processed requests"),
	)
	histogramName := getMetricName(opts.Module, MetricNameRequestHistogram)
	histogramMetric, _ := meter.Float64Histogram(
		histogramName,
		metric.WithUnit("s"),
		metric.WithDescription("requests duration(sec)."),
		metric.WithExplicitBucketBoundaries(prom.DefBuckets...),
	)
	return &OtelRequestMetrics{
		HandledCounter:   counterMetric,
		HandledHistogram: histogramMetric,
	}
}

func NewRequestMetrics(namespace string) *RequestMetrics {
	m := &RequestMetrics{
		HandledCounter: prom.NewCounterVec(prom.CounterOpts{
			Namespace: namespace,
			Subsystem: "requests",
			Name:      "code_total",
			Help:      "The total number of processed requests",
		}, []string{"kind", "operation", "code", "reason"}),
		HandledHistogram: prom.NewHistogramVec(prom.HistogramOpts{
			Namespace: namespace,
			Subsystem: "requests",
			Name:      "duration_sec",
			Help:      "server requests duration(sec).",
			Buckets:   prom.DefBuckets,
		}, []string{"kind", "operation"}),
	}
	prom.MustRegister(m.HandledHistogram, m.HandledCounter)
	return m
}

func getMetricName(module string, name string) string {
	return fmt.Sprintf("%s_%s", module, name)
}
