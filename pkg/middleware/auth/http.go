package auth

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
	"strings"
)

type authKey struct{}

type Authenticator interface {
	Authenticate(ctx context.Context, sessionID string) (*sessionv1.Session, error)
}

const (

	// bearerWord the bearer key word for authorization
	bearerWord string = "Bearer"

	// bearerFormat authorization token format
	bearerFormat string = "Bearer %s"

	// authorization<PERSON>ey holds the key used to store the JWT Token in the request tokenHeader.
	authorizationKey string = "Authorization"

	headerCookie   string = "cookie"
	headerZLPToken        = "zlp_token"
)

var (
	ErrMissingAuthenticator = errors.Unauthorized(reason, "authenticator is missing")
	ErrSessionEmpty         = errors.Unauthorized(reason, "token is empty")
	ErrAuthenticateFail     = errors.Unauthorized(reason, "authenticate failed")
	ErrWrongContext         = errors.Unauthorized(reason, "wrong context for middleware")
)

func retrieveSessionId(header transport.Transporter) string {
	auths := strings.SplitN(header.RequestHeader().Get(authorizationKey), " ", 2)
	if len(auths) == 2 && strings.EqualFold(auths[0], bearerWord) {
		return auths[1]
	}

	header.RequestHeader().Get("xxx-api")

	cookie := header.RequestHeader().Get(headerCookie)
	if len(cookie) == 0 {
		return ""
	}

	cookieKVs := strings.Split(cookie, ";")

	for _, pair := range cookieKVs {
		kv := strings.Split(pair, "=")
		if len(kv) < 2 {
			continue
		}

		if strings.TrimSpace(kv[0]) == headerZLPToken {
			return strings.TrimSpace(kv[1])
		}
	}
	return ""
}

func Server(authenticator Authenticator) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				if authenticator == nil {
					return nil, ErrMissingAuthenticator
				}

				sessionID := retrieveSessionId(header)
				if len(sessionID) == 0 {
					return nil, ErrSessionEmpty
				}

				session, err := authenticator.Authenticate(ctx, sessionID)
				if err != nil {
					return nil, ErrAuthenticateFail
				}

				ctx = NewContext(ctx, session)
				return handler(ctx, req)
			}
			return nil, ErrWrongContext
		}
	}
}

// NewContext put auth info into context
func NewContext(ctx context.Context, info *sessionv1.Session) context.Context {
	return context.WithValue(ctx, authKey{}, info)
}

func FromContext(ctx context.Context) (session *sessionv1.Session, ok bool) {
	session, ok = ctx.Value(authKey{}).(*sessionv1.Session)
	return
}
