package auth

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"strings"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

type ApiKeyValidator interface {
	Validate(ctx context.Context, clientId, clientKey string) (bool, error)
}

const (
	apiKeyHeader string = "X-API-KEY"
	//clientIdHeader  string = "client-id"
	reason string = "authentication"
)

var (
	//ErrMissingAuthenticator = errors.Unauthorized(reason, "authenticator is missing")
	ErrApiKeyEmpty = errors.Unauthorized(reason, "api key is empty")
	//ErrWrongContext         = errors.Unauthorized(reason, "wrong context for middleware")
	ErrAuthenticationFail = errors.Unauthorized(reason, "authentication failed")
)

func ApiKeyMiddleware(registerApiKeys []string) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				if len(registerApiKeys) == 0 {
					return false, ErrMissingAuthenticator
				}

				apiKey := header.RequestHeader().Get(apiKeyHeader)
				if len(apiKey) == 0 {
					return false, ErrApiKeyEmpty
				}

				userKey := ""
				for _, key := range registerApiKeys {
					keyPair := strings.Split(key, ":")
					if len(keyPair) != 2 || keyPair[0] == "" || keyPair[1] == "" {
						continue
					}

					if apiKey == keyPair[1] {
						userKey = keyPair[0]
					}
				}

				if userKey == "" {
					return false, ErrAuthenticationFail
				}

				//ctx = context.WithValue(ctx, "client-id", userKey)
				//ctx = context.WithValue(ctx, "api-key", apiKey)

				return handler(ctx, req)
			}
			return false, ErrWrongContext
		}
	}
}
