syntax = "proto3";

package account_service.v1;

import "validate/validate.proto";

option go_package = "installment-service/api/account-service/v1;accountv1";

service Account {
  rpc GetAccountForPayment(GetAccountForPaymentRequest) returns (GetAccountForPaymentResponse) {}
}

message UserAccount {
  int64 account_id = 1 [json_name = 'account_id'];
  Status status = 2 [json_name = 'status'];
  string source = 3 [json_name = 'source'];
  string partner_code = 4 [json_name = 'partner_code'];
  string partner_account_name = 5 [json_name = 'partner_account_name'];
  string partner_account_number = 6 [json_name = 'partner_account_number'];
  int64 installment_limit = 7 [json_name = 'installment_limit'];
  int64 installment_balance = 8 [json_name = 'installment_balance'];
}

// Account status
enum Status {
  UNSPECIFIED = 0;
  ACTIVE = 1;
  INACTIVE = 2;
  BLOCKED = 3;
  CLOSED = 4;
}

message GetAccountForPaymentRequest {
  int64 zalopay_id = 1 [json_name = 'zalopay_id', (validate.rules).int64.gt = 0];
  string partner_code = 2 [json_name = 'partner_code', (validate.rules).string.min_len = 1];
}

message GetAccountForPaymentResponse {
  UserAccount account = 1;
}
