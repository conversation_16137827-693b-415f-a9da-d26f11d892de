// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/account/v1/account_service.proto

package accountv1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Account status
type Status int32

const (
	Status_UNSPECIFIED Status = 0
	Status_ACTIVE      Status = 1
	Status_INACTIVE    Status = 2
	Status_BLOCKED     Status = 3
	Status_CLOSED      Status = 4
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "BLOCKED",
		4: "CLOSED",
	}
	Status_value = map[string]int32{
		"UNSPECIFIED": 0,
		"ACTIVE":      1,
		"INACTIVE":    2,
		"BLOCKED":     3,
		"CLOSED":      4,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_account_v1_account_service_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_external_services_account_v1_account_service_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_external_services_account_v1_account_service_proto_rawDescGZIP(), []int{0}
}

type UserAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId            int64  `protobuf:"varint,1,opt,name=account_id,proto3" json:"account_id,omitempty"`
	Status               Status `protobuf:"varint,2,opt,name=status,proto3,enum=account_service.v1.Status" json:"status,omitempty"`
	Source               string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	PartnerCode          string `protobuf:"bytes,4,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	PartnerAccountName   string `protobuf:"bytes,5,opt,name=partner_account_name,proto3" json:"partner_account_name,omitempty"`
	PartnerAccountNumber string `protobuf:"bytes,6,opt,name=partner_account_number,proto3" json:"partner_account_number,omitempty"`
	InstallmentLimit     int64  `protobuf:"varint,7,opt,name=installment_limit,proto3" json:"installment_limit,omitempty"`
	InstallmentBalance   int64  `protobuf:"varint,8,opt,name=installment_balance,proto3" json:"installment_balance,omitempty"`
}

func (x *UserAccount) Reset() {
	*x = UserAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_account_v1_account_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccount) ProtoMessage() {}

func (x *UserAccount) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_account_v1_account_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccount.ProtoReflect.Descriptor instead.
func (*UserAccount) Descriptor() ([]byte, []int) {
	return file_external_services_account_v1_account_service_proto_rawDescGZIP(), []int{0}
}

func (x *UserAccount) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *UserAccount) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_UNSPECIFIED
}

func (x *UserAccount) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *UserAccount) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *UserAccount) GetPartnerAccountName() string {
	if x != nil {
		return x.PartnerAccountName
	}
	return ""
}

func (x *UserAccount) GetPartnerAccountNumber() string {
	if x != nil {
		return x.PartnerAccountNumber
	}
	return ""
}

func (x *UserAccount) GetInstallmentLimit() int64 {
	if x != nil {
		return x.InstallmentLimit
	}
	return 0
}

func (x *UserAccount) GetInstallmentBalance() int64 {
	if x != nil {
		return x.InstallmentBalance
	}
	return 0
}

type GetAccountForPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZalopayId   int64  `protobuf:"varint,1,opt,name=zalopay_id,proto3" json:"zalopay_id,omitempty"`
	PartnerCode string `protobuf:"bytes,2,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
}

func (x *GetAccountForPaymentRequest) Reset() {
	*x = GetAccountForPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_account_v1_account_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountForPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountForPaymentRequest) ProtoMessage() {}

func (x *GetAccountForPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_account_v1_account_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountForPaymentRequest.ProtoReflect.Descriptor instead.
func (*GetAccountForPaymentRequest) Descriptor() ([]byte, []int) {
	return file_external_services_account_v1_account_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAccountForPaymentRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetAccountForPaymentRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type GetAccountForPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account *UserAccount `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetAccountForPaymentResponse) Reset() {
	*x = GetAccountForPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_account_v1_account_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountForPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountForPaymentResponse) ProtoMessage() {}

func (x *GetAccountForPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_account_v1_account_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountForPaymentResponse.ProtoReflect.Descriptor instead.
func (*GetAccountForPaymentResponse) Descriptor() ([]byte, []int) {
	return file_external_services_account_v1_account_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAccountForPaymentResponse) GetAccount() *UserAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

var File_external_services_account_v1_account_service_proto protoreflect.FileDescriptor

var file_external_services_account_v1_account_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xe9, 0x02, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x32, 0x0a, 0x14, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a,
	0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x73, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0a,
	0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x7a, 0x61, 0x6c, 0x6f, 0x70,
	0x61, 0x79, 0x5f, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x59, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x39, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x4c, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x32, 0x86, 0x01, 0x0a, 0x07,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x7b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46,
	0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x36, 0x5a, 0x34, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_account_v1_account_service_proto_rawDescOnce sync.Once
	file_external_services_account_v1_account_service_proto_rawDescData = file_external_services_account_v1_account_service_proto_rawDesc
)

func file_external_services_account_v1_account_service_proto_rawDescGZIP() []byte {
	file_external_services_account_v1_account_service_proto_rawDescOnce.Do(func() {
		file_external_services_account_v1_account_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_account_v1_account_service_proto_rawDescData)
	})
	return file_external_services_account_v1_account_service_proto_rawDescData
}

var file_external_services_account_v1_account_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_external_services_account_v1_account_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_external_services_account_v1_account_service_proto_goTypes = []any{
	(Status)(0),                          // 0: account_service.v1.Status
	(*UserAccount)(nil),                  // 1: account_service.v1.UserAccount
	(*GetAccountForPaymentRequest)(nil),  // 2: account_service.v1.GetAccountForPaymentRequest
	(*GetAccountForPaymentResponse)(nil), // 3: account_service.v1.GetAccountForPaymentResponse
}
var file_external_services_account_v1_account_service_proto_depIdxs = []int32{
	0, // 0: account_service.v1.UserAccount.status:type_name -> account_service.v1.Status
	1, // 1: account_service.v1.GetAccountForPaymentResponse.account:type_name -> account_service.v1.UserAccount
	2, // 2: account_service.v1.Account.GetAccountForPayment:input_type -> account_service.v1.GetAccountForPaymentRequest
	3, // 3: account_service.v1.Account.GetAccountForPayment:output_type -> account_service.v1.GetAccountForPaymentResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_external_services_account_v1_account_service_proto_init() }
func file_external_services_account_v1_account_service_proto_init() {
	if File_external_services_account_v1_account_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_account_v1_account_service_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*UserAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_account_v1_account_service_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetAccountForPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_account_v1_account_service_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetAccountForPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_account_v1_account_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_account_v1_account_service_proto_goTypes,
		DependencyIndexes: file_external_services_account_v1_account_service_proto_depIdxs,
		EnumInfos:         file_external_services_account_v1_account_service_proto_enumTypes,
		MessageInfos:      file_external_services_account_v1_account_service_proto_msgTypes,
	}.Build()
	File_external_services_account_v1_account_service_proto = out.File
	file_external_services_account_v1_account_service_proto_rawDesc = nil
	file_external_services_account_v1_account_service_proto_goTypes = nil
	file_external_services_account_v1_account_service_proto_depIdxs = nil
}
