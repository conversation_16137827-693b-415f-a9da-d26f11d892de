// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/acquiring_core/user_payment.proto

package acquiring_core

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AssetStatus int32

const (
	AssetStatus_ASSET_STATUS_UNSPECIFIED AssetStatus = 0
	AssetStatus_ACTIVE                   AssetStatus = 1
	AssetStatus_INACTIVE                 AssetStatus = 2
	AssetStatus_MAINTENANCE              AssetStatus = 3
	AssetStatus_REACH_LIMIT              AssetStatus = 4
	AssetStatus_NEED_ACTION              AssetStatus = 5
)

// Enum value maps for AssetStatus.
var (
	AssetStatus_name = map[int32]string{
		0: "ASSET_STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "MAINTENANCE",
		4: "REACH_LIMIT",
		5: "NEED_ACTION",
	}
	AssetStatus_value = map[string]int32{
		"ASSET_STATUS_UNSPECIFIED": 0,
		"ACTIVE":                   1,
		"INACTIVE":                 2,
		"MAINTENANCE":              3,
		"REACH_LIMIT":              4,
		"NEED_ACTION":              5,
	}
)

func (x AssetStatus) Enum() *AssetStatus {
	p := new(AssetStatus)
	*p = x
	return p
}

func (x AssetStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[0].Descriptor()
}

func (AssetStatus) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[0]
}

func (x AssetStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetStatus.Descriptor instead.
func (AssetStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{0}
}

type OrderStatus int32

const (
	OrderStatus_ORDER_STATUS_UNSPECIFIED OrderStatus = 0
	OrderStatus_CREATED                  OrderStatus = 1
	OrderStatus_PROCESSING               OrderStatus = 2
	OrderStatus_PENDING                  OrderStatus = 3
	OrderStatus_SUCCESS                  OrderStatus = 4
	OrderStatus_FAILED                   OrderStatus = 5
	OrderStatus_AUTHORIZED               OrderStatus = 6
	OrderStatus_CANCELED                 OrderStatus = 7
)

// Enum value maps for OrderStatus.
var (
	OrderStatus_name = map[int32]string{
		0: "ORDER_STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "PROCESSING",
		3: "PENDING",
		4: "SUCCESS",
		5: "FAILED",
		6: "AUTHORIZED",
		7: "CANCELED",
	}
	OrderStatus_value = map[string]int32{
		"ORDER_STATUS_UNSPECIFIED": 0,
		"CREATED":                  1,
		"PROCESSING":               2,
		"PENDING":                  3,
		"SUCCESS":                  4,
		"FAILED":                   5,
		"AUTHORIZED":               6,
		"CANCELED":                 7,
	}
)

func (x OrderStatus) Enum() *OrderStatus {
	p := new(OrderStatus)
	*p = x
	return p
}

func (x OrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[1].Descriptor()
}

func (OrderStatus) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[1]
}

func (x OrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderStatus.Descriptor instead.
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{1}
}

type PaymentStatus int32

const (
	PaymentStatus_PAYMENT_STATUS_UNSPECIFIED PaymentStatus = 0
	PaymentStatus_PAYMENT_PROCESSING         PaymentStatus = 1
	PaymentStatus_PAYMENT_PENDING            PaymentStatus = 2
	PaymentStatus_PAYMENT_SUCCESS            PaymentStatus = 3
	PaymentStatus_PAYMENT_FAILED             PaymentStatus = 4
	PaymentStatus_PAYMENT_AUTHORIZED         PaymentStatus = 5
)

// Enum value maps for PaymentStatus.
var (
	PaymentStatus_name = map[int32]string{
		0: "PAYMENT_STATUS_UNSPECIFIED",
		1: "PAYMENT_PROCESSING",
		2: "PAYMENT_PENDING",
		3: "PAYMENT_SUCCESS",
		4: "PAYMENT_FAILED",
		5: "PAYMENT_AUTHORIZED",
	}
	PaymentStatus_value = map[string]int32{
		"PAYMENT_STATUS_UNSPECIFIED": 0,
		"PAYMENT_PROCESSING":         1,
		"PAYMENT_PENDING":            2,
		"PAYMENT_SUCCESS":            3,
		"PAYMENT_FAILED":             4,
		"PAYMENT_AUTHORIZED":         5,
	}
)

func (x PaymentStatus) Enum() *PaymentStatus {
	p := new(PaymentStatus)
	*p = x
	return p
}

func (x PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[2].Descriptor()
}

func (PaymentStatus) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[2]
}

func (x PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentStatus.Descriptor instead.
func (PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{2}
}

type TransStatus int32

const (
	TransStatus_TRANS_STATUS_UNSPECIFIED TransStatus = 0
	TransStatus_TRANS_INIT               TransStatus = 1
	TransStatus_TRANS_REQUESTED          TransStatus = 2
	TransStatus_TRANS_PROCESSING         TransStatus = 3
	TransStatus_TRANS_PENDING            TransStatus = 4
	TransStatus_TRANS_SUCCESS            TransStatus = 5
	TransStatus_TRANS_FAILED             TransStatus = 6
	TransStatus_TRANS_CANCELED           TransStatus = 7
)

// Enum value maps for TransStatus.
var (
	TransStatus_name = map[int32]string{
		0: "TRANS_STATUS_UNSPECIFIED",
		1: "TRANS_INIT",
		2: "TRANS_REQUESTED",
		3: "TRANS_PROCESSING",
		4: "TRANS_PENDING",
		5: "TRANS_SUCCESS",
		6: "TRANS_FAILED",
		7: "TRANS_CANCELED",
	}
	TransStatus_value = map[string]int32{
		"TRANS_STATUS_UNSPECIFIED": 0,
		"TRANS_INIT":               1,
		"TRANS_REQUESTED":          2,
		"TRANS_PROCESSING":         3,
		"TRANS_PENDING":            4,
		"TRANS_SUCCESS":            5,
		"TRANS_FAILED":             6,
		"TRANS_CANCELED":           7,
	}
)

func (x TransStatus) Enum() *TransStatus {
	p := new(TransStatus)
	*p = x
	return p
}

func (x TransStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[3].Descriptor()
}

func (TransStatus) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[3]
}

func (x TransStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransStatus.Descriptor instead.
func (TransStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{3}
}

type CaptureMethod int32

const (
	CaptureMethod_CAPTURE_METHOD_UNSPECIFIED CaptureMethod = 0
	CaptureMethod_CAPTURE_METHOD_AUTO        CaptureMethod = 1
	CaptureMethod_CAPTURE_METHOD_MANUAL      CaptureMethod = 2
)

// Enum value maps for CaptureMethod.
var (
	CaptureMethod_name = map[int32]string{
		0: "CAPTURE_METHOD_UNSPECIFIED",
		1: "CAPTURE_METHOD_AUTO",
		2: "CAPTURE_METHOD_MANUAL",
	}
	CaptureMethod_value = map[string]int32{
		"CAPTURE_METHOD_UNSPECIFIED": 0,
		"CAPTURE_METHOD_AUTO":        1,
		"CAPTURE_METHOD_MANUAL":      2,
	}
)

func (x CaptureMethod) Enum() *CaptureMethod {
	p := new(CaptureMethod)
	*p = x
	return p
}

func (x CaptureMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaptureMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[4].Descriptor()
}

func (CaptureMethod) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[4]
}

func (x CaptureMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaptureMethod.Descriptor instead.
func (CaptureMethod) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{4}
}

type PaymentMethod int32

const (
	PaymentMethod_PAYMENT_METHOD_UNSPECIFIED PaymentMethod = 0
	PaymentMethod_WBL                        PaymentMethod = 1  // Wallet balance
	PaymentMethod_BDC                        PaymentMethod = 2  // Bank domestic card
	PaymentMethod_BDA                        PaymentMethod = 3  // Bank account
	PaymentMethod_BIC                        PaymentMethod = 4  // Bank international card
	PaymentMethod_BID                        PaymentMethod = 5  // Bank international debit card
	PaymentMethod_EMVCO                      PaymentMethod = 7  // Bank transfer by VietQR
	PaymentMethod_MMF                        PaymentMethod = 8  // Micro investment
	PaymentMethod_PLT                        PaymentMethod = 9  // Buy now pay later
	PaymentMethod_UPI                        PaymentMethod = 10 // Bank transfer by UPIQR
	PaymentMethod_INST                       PaymentMethod = 11 // Installment
	PaymentMethod_FD                         PaymentMethod = 12 // User fixed deposit account
	PaymentMethod_EMVTF                      PaymentMethod = 13 // Bank transfer by VietQR for Transfer
	PaymentMethod_THIRDPS                    PaymentMethod = 14 // Third party sof
	PaymentMethod_MBP                        PaymentMethod = 15 // Multi bill
)

// Enum value maps for PaymentMethod.
var (
	PaymentMethod_name = map[int32]string{
		0:  "PAYMENT_METHOD_UNSPECIFIED",
		1:  "WBL",
		2:  "BDC",
		3:  "BDA",
		4:  "BIC",
		5:  "BID",
		7:  "EMVCO",
		8:  "MMF",
		9:  "PLT",
		10: "UPI",
		11: "INST",
		12: "FD",
		13: "EMVTF",
		14: "THIRDPS",
		15: "MBP",
	}
	PaymentMethod_value = map[string]int32{
		"PAYMENT_METHOD_UNSPECIFIED": 0,
		"WBL":                        1,
		"BDC":                        2,
		"BDA":                        3,
		"BIC":                        4,
		"BID":                        5,
		"EMVCO":                      7,
		"MMF":                        8,
		"PLT":                        9,
		"UPI":                        10,
		"INST":                       11,
		"FD":                         12,
		"EMVTF":                      13,
		"THIRDPS":                    14,
		"MBP":                        15,
	}
)

func (x PaymentMethod) Enum() *PaymentMethod {
	p := new(PaymentMethod)
	*p = x
	return p
}

func (x PaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[5].Descriptor()
}

func (PaymentMethod) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[5]
}

func (x PaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethod.Descriptor instead.
func (PaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{5}
}

type DestAssetType int32

const (
	DestAssetType_DEST_ASSET_TYPE_UNSPECIFIED DestAssetType = 0
	DestAssetType_MERCHANT                    DestAssetType = 1 // Merchant payable
	DestAssetType_USER                        DestAssetType = 2 // User wallet
	DestAssetType_FDA                         DestAssetType = 3 // User fixed deposit account
	DestAssetType_NMCA                        DestAssetType = 4 // Merchant Bank Account Napas
	DestAssetType_UBDA                        DestAssetType = 5 // User Bank Domestic Account
	DestAssetType_UBDC                        DestAssetType = 6 // User Bank Domestic Card
	DestAssetType_UBIC                        DestAssetType = 7 // User Bank International Credit Card
	DestAssetType_UBID                        DestAssetType = 8 // User Bank International Debit Card
	DestAssetType_MMBP                        DestAssetType = 9 // Multi Bill
)

// Enum value maps for DestAssetType.
var (
	DestAssetType_name = map[int32]string{
		0: "DEST_ASSET_TYPE_UNSPECIFIED",
		1: "MERCHANT",
		2: "USER",
		3: "FDA",
		4: "NMCA",
		5: "UBDA",
		6: "UBDC",
		7: "UBIC",
		8: "UBID",
		9: "MMBP",
	}
	DestAssetType_value = map[string]int32{
		"DEST_ASSET_TYPE_UNSPECIFIED": 0,
		"MERCHANT":                    1,
		"USER":                        2,
		"FDA":                         3,
		"NMCA":                        4,
		"UBDA":                        5,
		"UBDC":                        6,
		"UBIC":                        7,
		"UBID":                        8,
		"MMBP":                        9,
	}
)

func (x DestAssetType) Enum() *DestAssetType {
	p := new(DestAssetType)
	*p = x
	return p
}

func (x DestAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DestAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[6].Descriptor()
}

func (DestAssetType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[6]
}

func (x DestAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DestAssetType.Descriptor instead.
func (DestAssetType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{6}
}

type WalletType int32

const (
	WalletType_MAIN       WalletType = 0
	WalletType_TEMPORARY  WalletType = 1
	WalletType_RESTRICTED WalletType = 2
)

// Enum value maps for WalletType.
var (
	WalletType_name = map[int32]string{
		0: "MAIN",
		1: "TEMPORARY",
		2: "RESTRICTED",
	}
	WalletType_value = map[string]int32{
		"MAIN":       0,
		"TEMPORARY":  1,
		"RESTRICTED": 2,
	}
)

func (x WalletType) Enum() *WalletType {
	p := new(WalletType)
	*p = x
	return p
}

func (x WalletType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WalletType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[7].Descriptor()
}

func (WalletType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[7]
}

func (x WalletType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WalletType.Descriptor instead.
func (WalletType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{7}
}

type PaymentType int32

const (
	PaymentType_PAYMENT_TYPE_UNSPECIFIED PaymentType = 0
	PaymentType_PAYMENT                  PaymentType = 1
	PaymentType_WITHDRAW                 PaymentType = 2
	PaymentType_TOPUP                    PaymentType = 3
	PaymentType_IBFT                     PaymentType = 4
	PaymentType_TRANSFER                 PaymentType = 5
	PaymentType_LIQUIDATE                PaymentType = 6
	PaymentType_VISA_DIRECT              PaymentType = 7
)

// Enum value maps for PaymentType.
var (
	PaymentType_name = map[int32]string{
		0: "PAYMENT_TYPE_UNSPECIFIED",
		1: "PAYMENT",
		2: "WITHDRAW",
		3: "TOPUP",
		4: "IBFT",
		5: "TRANSFER",
		6: "LIQUIDATE",
		7: "VISA_DIRECT",
	}
	PaymentType_value = map[string]int32{
		"PAYMENT_TYPE_UNSPECIFIED": 0,
		"PAYMENT":                  1,
		"WITHDRAW":                 2,
		"TOPUP":                    3,
		"IBFT":                     4,
		"TRANSFER":                 5,
		"LIQUIDATE":                6,
		"VISA_DIRECT":              7,
	}
)

func (x PaymentType) Enum() *PaymentType {
	p := new(PaymentType)
	*p = x
	return p
}

func (x PaymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[8].Descriptor()
}

func (PaymentType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[8]
}

func (x PaymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentType.Descriptor instead.
func (PaymentType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{8}
}

type PaymentSystem int32

const (
	PaymentSystem_PAYMENT_SYSTEM_UNSPECIFIED PaymentSystem = 0
	PaymentSystem_WALLET                     PaymentSystem = 1
	PaymentSystem_GATEWAY                    PaymentSystem = 2
)

// Enum value maps for PaymentSystem.
var (
	PaymentSystem_name = map[int32]string{
		0: "PAYMENT_SYSTEM_UNSPECIFIED",
		1: "WALLET",
		2: "GATEWAY",
	}
	PaymentSystem_value = map[string]int32{
		"PAYMENT_SYSTEM_UNSPECIFIED": 0,
		"WALLET":                     1,
		"GATEWAY":                    2,
	}
)

func (x PaymentSystem) Enum() *PaymentSystem {
	p := new(PaymentSystem)
	*p = x
	return p
}

func (x PaymentSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[9].Descriptor()
}

func (PaymentSystem) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[9]
}

func (x PaymentSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentSystem.Descriptor instead.
func (PaymentSystem) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{9}
}

type PromotionType int32

const (
	PromotionType_PROMOTION_TYPE_UNSPECIFIED PromotionType = 0
	PromotionType_VOUCHER                    PromotionType = 1
	PromotionType_DIRECT_DISCOUNT            PromotionType = 2
)

// Enum value maps for PromotionType.
var (
	PromotionType_name = map[int32]string{
		0: "PROMOTION_TYPE_UNSPECIFIED",
		1: "VOUCHER",
		2: "DIRECT_DISCOUNT",
	}
	PromotionType_value = map[string]int32{
		"PROMOTION_TYPE_UNSPECIFIED": 0,
		"VOUCHER":                    1,
		"DIRECT_DISCOUNT":            2,
	}
)

func (x PromotionType) Enum() *PromotionType {
	p := new(PromotionType)
	*p = x
	return p
}

func (x PromotionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PromotionType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[10].Descriptor()
}

func (PromotionType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[10]
}

func (x PromotionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PromotionType.Descriptor instead.
func (PromotionType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{10}
}

type FeeType int32

const (
	FeeType_FEE_TYPE_UNSPECIFIED FeeType = 0
	FeeType_SERVICE_FEE          FeeType = 1
	FeeType_TRANS_FEE            FeeType = 2
	FeeType_PROMOTION_FEE        FeeType = 3
)

// Enum value maps for FeeType.
var (
	FeeType_name = map[int32]string{
		0: "FEE_TYPE_UNSPECIFIED",
		1: "SERVICE_FEE",
		2: "TRANS_FEE",
		3: "PROMOTION_FEE",
	}
	FeeType_value = map[string]int32{
		"FEE_TYPE_UNSPECIFIED": 0,
		"SERVICE_FEE":          1,
		"TRANS_FEE":            2,
		"PROMOTION_FEE":        3,
	}
)

func (x FeeType) Enum() *FeeType {
	p := new(FeeType)
	*p = x
	return p
}

func (x FeeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeeType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[11].Descriptor()
}

func (FeeType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[11]
}

func (x FeeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeeType.Descriptor instead.
func (FeeType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{11}
}

type OsType int32

const (
	OsType_OS_TYPE_UNSPECIFIED OsType = 0
	OsType_ANDROID             OsType = 1
	OsType_IOS                 OsType = 2
	OsType_OTHER_OS            OsType = 99
)

// Enum value maps for OsType.
var (
	OsType_name = map[int32]string{
		0:  "OS_TYPE_UNSPECIFIED",
		1:  "ANDROID",
		2:  "IOS",
		99: "OTHER_OS",
	}
	OsType_value = map[string]int32{
		"OS_TYPE_UNSPECIFIED": 0,
		"ANDROID":             1,
		"IOS":                 2,
		"OTHER_OS":            99,
	}
)

func (x OsType) Enum() *OsType {
	p := new(OsType)
	*p = x
	return p
}

func (x OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[12].Descriptor()
}

func (OsType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[12]
}

func (x OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OsType.Descriptor instead.
func (OsType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{12}
}

type PlatformType int32

const (
	PlatformType_PLATFORM_TYPE_UNSPECIFIED PlatformType = 0
	PlatformType_ZPA                       PlatformType = 1
	PlatformType_ZPI                       PlatformType = 2
	PlatformType_OTHER_PLATFORM            PlatformType = 99
)

// Enum value maps for PlatformType.
var (
	PlatformType_name = map[int32]string{
		0:  "PLATFORM_TYPE_UNSPECIFIED",
		1:  "ZPA",
		2:  "ZPI",
		99: "OTHER_PLATFORM",
	}
	PlatformType_value = map[string]int32{
		"PLATFORM_TYPE_UNSPECIFIED": 0,
		"ZPA":                       1,
		"ZPI":                       2,
		"OTHER_PLATFORM":            99,
	}
)

func (x PlatformType) Enum() *PlatformType {
	p := new(PlatformType)
	*p = x
	return p
}

func (x PlatformType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlatformType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[13].Descriptor()
}

func (PlatformType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[13]
}

func (x PlatformType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlatformType.Descriptor instead.
func (PlatformType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{13}
}

type ClientSource int32

const (
	ClientSource_CLIENT_SOURCE_UNSPECIFIED ClientSource = 0
	ClientSource_CASHIER                   ClientSource = 1
	ClientSource_OTHER_SOURCE              ClientSource = 99
)

// Enum value maps for ClientSource.
var (
	ClientSource_name = map[int32]string{
		0:  "CLIENT_SOURCE_UNSPECIFIED",
		1:  "CASHIER",
		99: "OTHER_SOURCE",
	}
	ClientSource_value = map[string]int32{
		"CLIENT_SOURCE_UNSPECIFIED": 0,
		"CASHIER":                   1,
		"OTHER_SOURCE":              99,
	}
)

func (x ClientSource) Enum() *ClientSource {
	p := new(ClientSource)
	*p = x
	return p
}

func (x ClientSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientSource) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[14].Descriptor()
}

func (ClientSource) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[14]
}

func (x ClientSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientSource.Descriptor instead.
func (ClientSource) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{14}
}

type PaymentSolution int32

const (
	PaymentSolution_PAYMENT_SOLUTION_UNSPECIFIED PaymentSolution = 0
	PaymentSolution_CARD_ON_FILE                 PaymentSolution = 1
	PaymentSolution_GOOGLE_PAY                   PaymentSolution = 2
	PaymentSolution_APPLE_PAY                    PaymentSolution = 3
)

// Enum value maps for PaymentSolution.
var (
	PaymentSolution_name = map[int32]string{
		0: "PAYMENT_SOLUTION_UNSPECIFIED",
		1: "CARD_ON_FILE",
		2: "GOOGLE_PAY",
		3: "APPLE_PAY",
	}
	PaymentSolution_value = map[string]int32{
		"PAYMENT_SOLUTION_UNSPECIFIED": 0,
		"CARD_ON_FILE":                 1,
		"GOOGLE_PAY":                   2,
		"APPLE_PAY":                    3,
	}
)

func (x PaymentSolution) Enum() *PaymentSolution {
	p := new(PaymentSolution)
	*p = x
	return p
}

func (x PaymentSolution) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentSolution) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[15].Descriptor()
}

func (PaymentSolution) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[15]
}

func (x PaymentSolution) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentSolution.Descriptor instead.
func (PaymentSolution) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{15}
}

type ExternalBankAsset_TokenType int32

const (
	ExternalBankAsset_TOKEN_TYPE_UNSPECIFIED ExternalBankAsset_TokenType = 0
	ExternalBankAsset_TOKEN_TYPE_LINKED      ExternalBankAsset_TokenType = 1
	ExternalBankAsset_TOKEN_TYPE_CPS         ExternalBankAsset_TokenType = 2
	ExternalBankAsset_TOKEN_TYPE_CARD_TOKEN  ExternalBankAsset_TokenType = 3
)

// Enum value maps for ExternalBankAsset_TokenType.
var (
	ExternalBankAsset_TokenType_name = map[int32]string{
		0: "TOKEN_TYPE_UNSPECIFIED",
		1: "TOKEN_TYPE_LINKED",
		2: "TOKEN_TYPE_CPS",
		3: "TOKEN_TYPE_CARD_TOKEN",
	}
	ExternalBankAsset_TokenType_value = map[string]int32{
		"TOKEN_TYPE_UNSPECIFIED": 0,
		"TOKEN_TYPE_LINKED":      1,
		"TOKEN_TYPE_CPS":         2,
		"TOKEN_TYPE_CARD_TOKEN":  3,
	}
)

func (x ExternalBankAsset_TokenType) Enum() *ExternalBankAsset_TokenType {
	p := new(ExternalBankAsset_TokenType)
	*p = x
	return p
}

func (x ExternalBankAsset_TokenType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExternalBankAsset_TokenType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[16].Descriptor()
}

func (ExternalBankAsset_TokenType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[16]
}

func (x ExternalBankAsset_TokenType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExternalBankAsset_TokenType.Descriptor instead.
func (ExternalBankAsset_TokenType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{43, 0}
}

type ExternalBankAsset_RoutingType int32

const (
	ExternalBankAsset_ROUTING_TYPE_UNSPECIFIED ExternalBankAsset_RoutingType = 0
	ExternalBankAsset_ROUTING_TYPE_WITHDRAW    ExternalBankAsset_RoutingType = 1
	ExternalBankAsset_ROUTING_TYPE_IBFT        ExternalBankAsset_RoutingType = 2
)

// Enum value maps for ExternalBankAsset_RoutingType.
var (
	ExternalBankAsset_RoutingType_name = map[int32]string{
		0: "ROUTING_TYPE_UNSPECIFIED",
		1: "ROUTING_TYPE_WITHDRAW",
		2: "ROUTING_TYPE_IBFT",
	}
	ExternalBankAsset_RoutingType_value = map[string]int32{
		"ROUTING_TYPE_UNSPECIFIED": 0,
		"ROUTING_TYPE_WITHDRAW":    1,
		"ROUTING_TYPE_IBFT":        2,
	}
)

func (x ExternalBankAsset_RoutingType) Enum() *ExternalBankAsset_RoutingType {
	p := new(ExternalBankAsset_RoutingType)
	*p = x
	return p
}

func (x ExternalBankAsset_RoutingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExternalBankAsset_RoutingType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_acquiring_core_user_payment_proto_enumTypes[17].Descriptor()
}

func (ExternalBankAsset_RoutingType) Type() protoreflect.EnumType {
	return &file_external_services_acquiring_core_user_payment_proto_enumTypes[17]
}

func (x ExternalBankAsset_RoutingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExternalBankAsset_RoutingType.Descriptor instead.
func (ExternalBankAsset_RoutingType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{43, 1}
}

type CreateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=1"
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"required,prefix_date,max=40"
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	// Pay to amount. The currency is always VND.
	// @gotags: validate:"min=1"
	Amount int64 `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	// @gotags: validate:"required,valid"
	PaymentType PaymentType `protobuf:"varint,9,opt,name=payment_type,json=paymentType,proto3,enum=acquiring_core.upay.v1.PaymentType" json:"payment_type,omitempty"`
	// @gotags: validate:"required"
	DestAsset *DestAsset `protobuf:"bytes,12,opt,name=dest_asset,json=destAsset,proto3" json:"dest_asset,omitempty"`
	// @gotags: validate:"required"
	Metadata *OrderMeta `protobuf:"bytes,13,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Specify when the fund will be captured from the payer.
	// If not set, CAPTURE_METHOD_AUTO is the default; i.e.,
	// the fund will be captured immediately after the order is confirmed successfully.
	CaptureMethod CaptureMethod `protobuf:"varint,14,opt,name=capture_method,json=captureMethod,proto3,enum=acquiring_core.upay.v1.CaptureMethod" json:"capture_method,omitempty"`
	// The payment amount and currency that merchant requests to receive.
	PaymentAmount  *Amount           `protobuf:"bytes,15,opt,name=payment_amount,json=paymentAmount,proto3" json:"payment_amount,omitempty"`
	QuoteCurrency  *QuoteCurrency    `protobuf:"bytes,16,opt,name=quote_currency,json=quoteCurrency,proto3" json:"quote_currency,omitempty"`
	DeprecatedData map[string]string `protobuf:"bytes,99,rep,name=deprecated_data,json=deprecatedData,proto3" json:"deprecated_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOrderRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *CreateOrderRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *CreateOrderRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreateOrderRequest) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_PAYMENT_TYPE_UNSPECIFIED
}

func (x *CreateOrderRequest) GetDestAsset() *DestAsset {
	if x != nil {
		return x.DestAsset
	}
	return nil
}

func (x *CreateOrderRequest) GetMetadata() *OrderMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateOrderRequest) GetCaptureMethod() CaptureMethod {
	if x != nil {
		return x.CaptureMethod
	}
	return CaptureMethod_CAPTURE_METHOD_UNSPECIFIED
}

func (x *CreateOrderRequest) GetPaymentAmount() *Amount {
	if x != nil {
		return x.PaymentAmount
	}
	return nil
}

func (x *CreateOrderRequest) GetQuoteCurrency() *QuoteCurrency {
	if x != nil {
		return x.QuoteCurrency
	}
	return nil
}

func (x *CreateOrderRequest) GetDeprecatedData() map[string]string {
	if x != nil {
		return x.DeprecatedData
	}
	return nil
}

type CreateLegacyOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=1"
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"required,max=100"
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	// @gotags: validate:"min=1"
	Amount int64 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// @gotags: validate:"required"
	Metadata *OrderMeta `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// @gotags: validate:"required"
	Mac string `protobuf:"bytes,5,opt,name=mac,proto3" json:"mac,omitempty"`
}

func (x *CreateLegacyOrderRequest) Reset() {
	*x = CreateLegacyOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLegacyOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLegacyOrderRequest) ProtoMessage() {}

func (x *CreateLegacyOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLegacyOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateLegacyOrderRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{1}
}

func (x *CreateLegacyOrderRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *CreateLegacyOrderRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *CreateLegacyOrderRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreateLegacyOrderRequest) GetMetadata() *OrderMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateLegacyOrderRequest) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

type CreateOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo      int64                  `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	OrderToken   string                 `protobuf:"bytes,2,opt,name=order_token,json=orderToken,proto3" json:"order_token,omitempty"`
	OrderUrl     string                 `protobuf:"bytes,3,opt,name=order_url,json=orderUrl,proto3" json:"order_url,omitempty"`
	Status       OrderStatus            `protobuf:"varint,4,opt,name=status,proto3,enum=acquiring_core.upay.v1.OrderStatus" json:"status,omitempty"`
	ReasonStatus *ReasonStatus          `protobuf:"bytes,5,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
	CreateTime   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Checksum     string                 `protobuf:"bytes,7,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *CreateOrderResponse) Reset() {
	*x = CreateOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResponse) ProtoMessage() {}

func (x *CreateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateOrderResponse) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOrderResponse) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

func (x *CreateOrderResponse) GetOrderToken() string {
	if x != nil {
		return x.OrderToken
	}
	return ""
}

func (x *CreateOrderResponse) GetOrderUrl() string {
	if x != nil {
		return x.OrderUrl
	}
	return ""
}

func (x *CreateOrderResponse) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *CreateOrderResponse) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

func (x *CreateOrderResponse) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CreateOrderResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// *
// ConfirmPayRequest contains necessary information to confirm a payment.
type ConfirmPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The order to be confirmed.
	// @gotags: validate:"min=1"
	OrderNo int64 `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	// The one who confirms pay for the order.
	// Can be either User or Merchant,
	// or nil in-case of External Payment (for example, user is charged by Napas and not a Zalopay user).
	Payer *Payer `protobuf:"bytes,3,opt,name=payer,proto3" json:"payer,omitempty"`
	// The assets that will be charged from the payer.
	// @gotags: validate:"eq=1,dive,required"
	SourceAssets []*SourceAsset `protobuf:"bytes,4,rep,name=source_assets,json=sourceAssets,proto3" json:"source_assets,omitempty"`
	// @gotags: validate:"required"
	Metadata *PaymentMeta `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// The checksum of the order,
	// used to verify that the order is not modified before confirming pay.
	// TODO: require after client update to pass this field
	Checksum string `protobuf:"bytes,10,opt,name=checksum,proto3" json:"checksum,omitempty"`
	// The fee information that is confirmed by the user,
	// this field is only required if the client wants to validate the confirmed fee.
	// Otherwise, the fee will be auto calculated by the system.
	ConfirmedFee *ConfirmedFee `protobuf:"bytes,11,opt,name=confirmed_fee,json=confirmedFee,proto3" json:"confirmed_fee,omitempty"`
	// @gotags: validate:"dive"
	PromotionItems []*PromotionItem `protobuf:"bytes,12,rep,name=promotion_items,json=promotionItems,proto3" json:"promotion_items,omitempty"`
	PaymentOption  *PaymentOption   `protobuf:"bytes,13,opt,name=payment_option,json=paymentOption,proto3" json:"payment_option,omitempty"`
	// Theses data is used for compatibility with old version.
	// Basically, theses include the same data as the current Kafka TransLog of TPE.
	// This map will be merge with the embed_data in the CreateOrderRequest, existing data will be overwritten.
	DeprecatedData map[string]string `protobuf:"bytes,99,rep,name=deprecated_data,json=deprecatedData,proto3" json:"deprecated_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ConfirmPayRequest) Reset() {
	*x = ConfirmPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmPayRequest) ProtoMessage() {}

func (x *ConfirmPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmPayRequest.ProtoReflect.Descriptor instead.
func (*ConfirmPayRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{3}
}

func (x *ConfirmPayRequest) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

func (x *ConfirmPayRequest) GetPayer() *Payer {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *ConfirmPayRequest) GetSourceAssets() []*SourceAsset {
	if x != nil {
		return x.SourceAssets
	}
	return nil
}

func (x *ConfirmPayRequest) GetMetadata() *PaymentMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ConfirmPayRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *ConfirmPayRequest) GetConfirmedFee() *ConfirmedFee {
	if x != nil {
		return x.ConfirmedFee
	}
	return nil
}

func (x *ConfirmPayRequest) GetPromotionItems() []*PromotionItem {
	if x != nil {
		return x.PromotionItems
	}
	return nil
}

func (x *ConfirmPayRequest) GetPaymentOption() *PaymentOption {
	if x != nil {
		return x.PaymentOption
	}
	return nil
}

func (x *ConfirmPayRequest) GetDeprecatedData() map[string]string {
	if x != nil {
		return x.DeprecatedData
	}
	return nil
}

type ConfirmPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo      int64                  `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	Status       OrderStatus            `protobuf:"varint,2,opt,name=status,proto3,enum=acquiring_core.upay.v1.OrderStatus" json:"status,omitempty"`
	ReasonStatus *ReasonStatus          `protobuf:"bytes,3,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
	PayTime      *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=pay_time,json=payTime,proto3" json:"pay_time,omitempty"`
	PaymentNo    int64                  `protobuf:"varint,5,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
}

func (x *ConfirmPayResponse) Reset() {
	*x = ConfirmPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmPayResponse) ProtoMessage() {}

func (x *ConfirmPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmPayResponse.ProtoReflect.Descriptor instead.
func (*ConfirmPayResponse) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{4}
}

func (x *ConfirmPayResponse) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

func (x *ConfirmPayResponse) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *ConfirmPayResponse) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

func (x *ConfirmPayResponse) GetPayTime() *timestamppb.Timestamp {
	if x != nil {
		return x.PayTime
	}
	return nil
}

func (x *ConfirmPayResponse) GetPaymentNo() int64 {
	if x != nil {
		return x.PaymentNo
	}
	return 0
}

type CaptureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A UUID to identify the request.
	// @gotags: validate:"required,uuid"
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// The order to be captured.
	// @gotags: validate:"min=1"
	OrderNo int64 `protobuf:"varint,2,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	// The amount to be captured.
	// @gotags: validate:"min=1"
	Amount int64 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *CaptureRequest) Reset() {
	*x = CaptureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptureRequest) ProtoMessage() {}

func (x *CaptureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptureRequest.ProtoReflect.Descriptor instead.
func (*CaptureRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{5}
}

func (x *CaptureRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CaptureRequest) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

func (x *CaptureRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type CaptureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CaptureResponse) Reset() {
	*x = CaptureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptureResponse) ProtoMessage() {}

func (x *CaptureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptureResponse.ProtoReflect.Descriptor instead.
func (*CaptureResponse) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{6}
}

type CancelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The order to be canceled.
	// @gotags: validate:"min=1"
	OrderNo int64 `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
}

func (x *CancelRequest) Reset() {
	*x = CancelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelRequest) ProtoMessage() {}

func (x *CancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelRequest.ProtoReflect.Descriptor instead.
func (*CancelRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{7}
}

func (x *CancelRequest) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

type CancelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelResponse) Reset() {
	*x = CancelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelResponse) ProtoMessage() {}

func (x *CancelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelResponse.ProtoReflect.Descriptor instead.
func (*CancelResponse) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{8}
}

type GetOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=1"
	OrderNo int64 `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
}

func (x *GetOrderRequest) Reset() {
	*x = GetOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderRequest) ProtoMessage() {}

func (x *GetOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderRequest.ProtoReflect.Descriptor instead.
func (*GetOrderRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{9}
}

func (x *GetOrderRequest) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

type GetOrderByTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	OrderToken string `protobuf:"bytes,1,opt,name=order_token,json=orderToken,proto3" json:"order_token,omitempty"`
}

func (x *GetOrderByTokenRequest) Reset() {
	*x = GetOrderByTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderByTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderByTokenRequest) ProtoMessage() {}

func (x *GetOrderByTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderByTokenRequest.ProtoReflect.Descriptor instead.
func (*GetOrderByTokenRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{10}
}

func (x *GetOrderByTokenRequest) GetOrderToken() string {
	if x != nil {
		return x.OrderToken
	}
	return ""
}

type GetOrderByAppTransIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=1"
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"required,prefix_date,max=100"
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
}

func (x *GetOrderByAppTransIdRequest) Reset() {
	*x = GetOrderByAppTransIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderByAppTransIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderByAppTransIdRequest) ProtoMessage() {}

func (x *GetOrderByAppTransIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderByAppTransIdRequest.ProtoReflect.Descriptor instead.
func (*GetOrderByAppTransIdRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{11}
}

func (x *GetOrderByAppTransIdRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetOrderByAppTransIdRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

type ConsultUserAssetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// @gotags: validate:"min=1"
	AppId int32 `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"min=1"
	Amount int64 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// @gotags: validate:"required,valid"
	PaymentType PaymentType `protobuf:"varint,4,opt,name=payment_type,json=paymentType,proto3,enum=acquiring_core.upay.v1.PaymentType" json:"payment_type,omitempty"`
	Device      *Device     `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *ConsultUserAssetsRequest) Reset() {
	*x = ConsultUserAssetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultUserAssetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultUserAssetsRequest) ProtoMessage() {}

func (x *ConsultUserAssetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultUserAssetsRequest.ProtoReflect.Descriptor instead.
func (*ConsultUserAssetsRequest) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{12}
}

func (x *ConsultUserAssetsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ConsultUserAssetsRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *ConsultUserAssetsRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ConsultUserAssetsRequest) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_PAYMENT_TYPE_UNSPECIFIED
}

func (x *ConsultUserAssetsRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type ConsultUserAssetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*ConsultUserAssetsResponse_UserAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
}

func (x *ConsultUserAssetsResponse) Reset() {
	*x = ConsultUserAssetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultUserAssetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultUserAssetsResponse) ProtoMessage() {}

func (x *ConsultUserAssetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultUserAssetsResponse.ProtoReflect.Descriptor instead.
func (*ConsultUserAssetsResponse) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{13}
}

func (x *ConsultUserAssetsResponse) GetAssets() []*ConsultUserAssetsResponse_UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type ConsultSourceAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Asset:
	//
	//	*ConsultSourceAsset_UserWallet
	//	*ConsultSourceAsset_UserBank
	//	*ConsultSourceAsset_UserFinance
	Asset isConsultSourceAsset_Asset `protobuf_oneof:"asset"`
}

func (x *ConsultSourceAsset) Reset() {
	*x = ConsultSourceAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultSourceAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultSourceAsset) ProtoMessage() {}

func (x *ConsultSourceAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultSourceAsset.ProtoReflect.Descriptor instead.
func (*ConsultSourceAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{14}
}

func (m *ConsultSourceAsset) GetAsset() isConsultSourceAsset_Asset {
	if m != nil {
		return m.Asset
	}
	return nil
}

func (x *ConsultSourceAsset) GetUserWallet() *ConsultSourceAsset_ConsultUserWalletAsset {
	if x, ok := x.GetAsset().(*ConsultSourceAsset_UserWallet); ok {
		return x.UserWallet
	}
	return nil
}

func (x *ConsultSourceAsset) GetUserBank() *ConsultSourceAsset_ConsultUserBankAsset {
	if x, ok := x.GetAsset().(*ConsultSourceAsset_UserBank); ok {
		return x.UserBank
	}
	return nil
}

func (x *ConsultSourceAsset) GetUserFinance() *ConsultSourceAsset_ConsultUserFinancialAsset {
	if x, ok := x.GetAsset().(*ConsultSourceAsset_UserFinance); ok {
		return x.UserFinance
	}
	return nil
}

type isConsultSourceAsset_Asset interface {
	isConsultSourceAsset_Asset()
}

type ConsultSourceAsset_UserWallet struct {
	UserWallet *ConsultSourceAsset_ConsultUserWalletAsset `protobuf:"bytes,1,opt,name=user_wallet,json=userWallet,proto3,oneof"`
}

type ConsultSourceAsset_UserBank struct {
	UserBank *ConsultSourceAsset_ConsultUserBankAsset `protobuf:"bytes,2,opt,name=user_bank,json=userBank,proto3,oneof"`
}

type ConsultSourceAsset_UserFinance struct {
	UserFinance *ConsultSourceAsset_ConsultUserFinancialAsset `protobuf:"bytes,3,opt,name=user_finance,json=userFinance,proto3,oneof"`
}

func (*ConsultSourceAsset_UserWallet) isConsultSourceAsset_Asset() {}

func (*ConsultSourceAsset_UserBank) isConsultSourceAsset_Asset() {}

func (*ConsultSourceAsset_UserFinance) isConsultSourceAsset_Asset() {}

// *
// `Order` presents the order information.
//
// An order must be created for collecting payment from a `Payer`.
// After the creation, `Payer` can confirm the payment using one or more payment solutions (e.g. Cashier, Agreement, Napas, etc.),
// which will end up calling the method `ConfirmPay`.
//
// Once the payment is confirmed, the order will change to `PROCESSING` status, and the payment will be processed asynchronously.
// When the processing is done, the order will change to `SUCCESS`, `FAILED` or `PENDING` status.
//
// Each time the OrderStatus is changed, an Order message will be sent to Kafka.
// Clients SHOULD listen to Kafka to get the latest status instead of polling using `GetOrder`.
// When producing the Kafka message, we use the library `google.golang.org/protobuf/encoding/protojson`,
// which follow this standard: https://developers.google.com/protocol-buffers/docs/proto3#json.
// Clients SHOULD make sure to enable the option DiscardUnknown when unmarshal the message for forward compatibility.
// For example, in Golang, the code should be:
// ```go
//
//	unmarshaler := protojson.UnmarshalOptions{
//	  DiscardUnknown: true
//	}
//
// ```
//
// Clients SHOULD schedule a job for getting the latest status of the order to prevent missing Kafka messages.
//
// Possible `OrderStatus`(s) are:
// * `CREATED`: The order is created, but the payment is not confirmed yet.
// * `PROCESSING`: The payment is confirmed, and the payment is being processed.
// * `SUCCESS`: The payment is completed successfully.
// * `FAILED`: The payment is completed unsuccessfully.
// * `PENDING`: The payment is processed, but the system can't sure about the result. Manual intervention is required.
//
// When an order is completed which the status `FAILED` or `PENDING`, the `reason_status` will be set to indicate the reason.
// See `ReasonStatus` for more details about the message structure.
//
// If the failure reason is defined by Acquiring Core, the value of field `domain` will be `ACQUIRING`,
// and the possible values of field `reason` are:
// * `SUBMIT_PAYMENT_ENGINE_FAILED`: Submit transaction to Payment Engine failed due to network issue.
// * `ORDER_EXPIRED`: The order is expired. The order can be confirmed pay only within the TTL.
// * `INTERNAL`: System error.
// * `SOF_BALANCE_INSUFFICIENT`: The balance of provided `SourceAssets` is not enough to pay for the order.
// * `FRAUD_REJECTED`: The order is rejected by the risk engine because of fraud.
// * `RISK_REJECTED`: The order is rejected by the risk engine because of risk.
// * `RISK_CHALLENGED`: The order is challenged by the risk engine, payer can do some actions and retry the payment.
// * `PAYER_EXCEED_LIMIT`: The payer has exceeded the limit.
// * `PAYMENT_EXCEED_LIMIT`: The payment has exceeded the limit.
// * `PERMISSION_DENIED`: The payer does not have permission to pay for the order with the provided `SourceAssets`.
// * `PAYER_AUTHENTICATION_FAILED`: Input wrong bank OTP.
// * `SBV_2345_AUTHENTICATION_FAILED`: The SBV 2345 authentication failed.
// Other values of `reason` are possible.
type Order struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo        int64                  `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	AppId          int32                  `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppTransId     string                 `protobuf:"bytes,3,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	Amount         int64                  `protobuf:"varint,8,opt,name=amount,proto3" json:"amount,omitempty"`
	PaymentType    PaymentType            `protobuf:"varint,10,opt,name=payment_type,json=paymentType,proto3,enum=acquiring_core.upay.v1.PaymentType" json:"payment_type,omitempty"`
	DestAsset      *DestAsset             `protobuf:"bytes,15,opt,name=dest_asset,json=destAsset,proto3" json:"dest_asset,omitempty"`
	Status         OrderStatus            `protobuf:"varint,16,opt,name=status,proto3,enum=acquiring_core.upay.v1.OrderStatus" json:"status,omitempty"`
	ReasonStatus   *ReasonStatus          `protobuf:"bytes,17,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Payment        *Payment               `protobuf:"bytes,21,opt,name=payment,proto3" json:"payment,omitempty"`
	Metadata       *OrderMeta             `protobuf:"bytes,22,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ExpireTime     *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Checksum       string                 `protobuf:"bytes,24,opt,name=checksum,proto3" json:"checksum,omitempty"`
	CaptureMethod  CaptureMethod          `protobuf:"varint,25,opt,name=capture_method,json=captureMethod,proto3,enum=acquiring_core.upay.v1.CaptureMethod" json:"capture_method,omitempty"`
	PaymentAmount  *Amount                `protobuf:"bytes,26,opt,name=payment_amount,json=paymentAmount,proto3" json:"payment_amount,omitempty"`
	QuoteCurrency  *QuoteCurrency         `protobuf:"bytes,27,opt,name=quote_currency,json=quoteCurrency,proto3" json:"quote_currency,omitempty"`
	OrderToken     string                 `protobuf:"bytes,28,opt,name=order_token,json=orderToken,proto3" json:"order_token,omitempty"`
	DeprecatedData map[string]string      `protobuf:"bytes,99,rep,name=deprecated_data,json=deprecatedData,proto3" json:"deprecated_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Order) Reset() {
	*x = Order{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{15}
}

func (x *Order) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

func (x *Order) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *Order) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *Order) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Order) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_PAYMENT_TYPE_UNSPECIFIED
}

func (x *Order) GetDestAsset() *DestAsset {
	if x != nil {
		return x.DestAsset
	}
	return nil
}

func (x *Order) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *Order) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

func (x *Order) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Order) GetPayment() *Payment {
	if x != nil {
		return x.Payment
	}
	return nil
}

func (x *Order) GetMetadata() *OrderMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Order) GetExpireTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireTime
	}
	return nil
}

func (x *Order) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *Order) GetCaptureMethod() CaptureMethod {
	if x != nil {
		return x.CaptureMethod
	}
	return CaptureMethod_CAPTURE_METHOD_UNSPECIFIED
}

func (x *Order) GetPaymentAmount() *Amount {
	if x != nil {
		return x.PaymentAmount
	}
	return nil
}

func (x *Order) GetQuoteCurrency() *QuoteCurrency {
	if x != nil {
		return x.QuoteCurrency
	}
	return nil
}

func (x *Order) GetOrderToken() string {
	if x != nil {
		return x.OrderToken
	}
	return ""
}

func (x *Order) GetDeprecatedData() map[string]string {
	if x != nil {
		return x.DeprecatedData
	}
	return nil
}

type Payment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PaymentNo int64  `protobuf:"varint,1,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
	Payer     *Payer `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// Deprecated: use `source_asset_list.source_assets` instead.
	//
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	SourceAssets []*SourceAsset `protobuf:"bytes,3,rep,name=source_assets,json=sourceAssets,proto3" json:"source_assets,omitempty"`
	Status       PaymentStatus  `protobuf:"varint,4,opt,name=status,proto3,enum=acquiring_core.upay.v1.PaymentStatus" json:"status,omitempty"`
	ReasonStatus *ReasonStatus  `protobuf:"bytes,5,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
	// Deprecated: use `transaction_list.transactions` instead.
	//
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	Transactions []*Transaction         `protobuf:"bytes,6,rep,name=transactions,proto3" json:"transactions,omitempty"`
	PayTime      *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time,omitempty"`
	Metadata     *PaymentMeta           `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Deprecated: use `promotion_list.promotion_items` instead.
	//
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	PromotionItems []*PromotionItem `protobuf:"bytes,9,rep,name=promotion_items,json=promotionItems,proto3" json:"promotion_items,omitempty"`
	// Deprecated: use `fee_list.fee_items` instead.
	//
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	FeeItems        []*FeeItem       `protobuf:"bytes,10,rep,name=fee_items,json=feeItems,proto3" json:"fee_items,omitempty"`
	SourceAssetList *SourceAssetList `protobuf:"bytes,11,opt,name=source_asset_list,json=sourceAssetList,proto3" json:"source_asset_list,omitempty"`
	TransactionList *TransactionList `protobuf:"bytes,12,opt,name=transaction_list,json=transactionList,proto3" json:"transaction_list,omitempty"`
	PromotionList   *PromotionList   `protobuf:"bytes,13,opt,name=promotion_list,json=promotionList,proto3" json:"promotion_list,omitempty"`
	FeeList         *FeeList         `protobuf:"bytes,14,opt,name=fee_list,json=feeList,proto3" json:"fee_list,omitempty"`
	// Only has meaning when order capture method is MANUAL.
	Authorization  *Authorization    `protobuf:"bytes,15,opt,name=authorization,proto3" json:"authorization,omitempty"`
	PaymentOption  *PaymentOption    `protobuf:"bytes,16,opt,name=payment_option,json=paymentOption,proto3" json:"payment_option,omitempty"`
	DeprecatedData map[string]string `protobuf:"bytes,99,rep,name=deprecated_data,json=deprecatedData,proto3" json:"deprecated_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Payment) Reset() {
	*x = Payment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Payment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Payment) ProtoMessage() {}

func (x *Payment) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Payment.ProtoReflect.Descriptor instead.
func (*Payment) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{16}
}

func (x *Payment) GetPaymentNo() int64 {
	if x != nil {
		return x.PaymentNo
	}
	return 0
}

func (x *Payment) GetPayer() *Payer {
	if x != nil {
		return x.Payer
	}
	return nil
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *Payment) GetSourceAssets() []*SourceAsset {
	if x != nil {
		return x.SourceAssets
	}
	return nil
}

func (x *Payment) GetStatus() PaymentStatus {
	if x != nil {
		return x.Status
	}
	return PaymentStatus_PAYMENT_STATUS_UNSPECIFIED
}

func (x *Payment) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *Payment) GetTransactions() []*Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *Payment) GetPayTime() *timestamppb.Timestamp {
	if x != nil {
		return x.PayTime
	}
	return nil
}

func (x *Payment) GetMetadata() *PaymentMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *Payment) GetPromotionItems() []*PromotionItem {
	if x != nil {
		return x.PromotionItems
	}
	return nil
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *Payment) GetFeeItems() []*FeeItem {
	if x != nil {
		return x.FeeItems
	}
	return nil
}

func (x *Payment) GetSourceAssetList() *SourceAssetList {
	if x != nil {
		return x.SourceAssetList
	}
	return nil
}

func (x *Payment) GetTransactionList() *TransactionList {
	if x != nil {
		return x.TransactionList
	}
	return nil
}

func (x *Payment) GetPromotionList() *PromotionList {
	if x != nil {
		return x.PromotionList
	}
	return nil
}

func (x *Payment) GetFeeList() *FeeList {
	if x != nil {
		return x.FeeList
	}
	return nil
}

func (x *Payment) GetAuthorization() *Authorization {
	if x != nil {
		return x.Authorization
	}
	return nil
}

func (x *Payment) GetPaymentOption() *PaymentOption {
	if x != nil {
		return x.PaymentOption
	}
	return nil
}

func (x *Payment) GetDeprecatedData() map[string]string {
	if x != nil {
		return x.DeprecatedData
	}
	return nil
}

type SourceAssetList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceAssets []*SourceAsset `protobuf:"bytes,1,rep,name=source_assets,json=sourceAssets,proto3" json:"source_assets,omitempty"`
	TotalAmount  int64          `protobuf:"varint,2,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
}

func (x *SourceAssetList) Reset() {
	*x = SourceAssetList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceAssetList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceAssetList) ProtoMessage() {}

func (x *SourceAssetList) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceAssetList.ProtoReflect.Descriptor instead.
func (*SourceAssetList) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{17}
}

func (x *SourceAssetList) GetSourceAssets() []*SourceAsset {
	if x != nil {
		return x.SourceAssets
	}
	return nil
}

func (x *SourceAssetList) GetTotalAmount() int64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

type TransactionList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transactions []*Transaction `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *TransactionList) Reset() {
	*x = TransactionList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionList) ProtoMessage() {}

func (x *TransactionList) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionList.ProtoReflect.Descriptor instead.
func (*TransactionList) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{18}
}

func (x *TransactionList) GetTransactions() []*Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type PromotionList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PromotionItems []*PromotionItem `protobuf:"bytes,1,rep,name=promotion_items,json=promotionItems,proto3" json:"promotion_items,omitempty"`
	TotalAmount    int64            `protobuf:"varint,2,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
}

func (x *PromotionList) Reset() {
	*x = PromotionList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PromotionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromotionList) ProtoMessage() {}

func (x *PromotionList) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromotionList.ProtoReflect.Descriptor instead.
func (*PromotionList) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{19}
}

func (x *PromotionList) GetPromotionItems() []*PromotionItem {
	if x != nil {
		return x.PromotionItems
	}
	return nil
}

func (x *PromotionList) GetTotalAmount() int64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

type FeeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeeItems    []*FeeItem `protobuf:"bytes,1,rep,name=fee_items,json=feeItems,proto3" json:"fee_items,omitempty"`
	TotalAmount int64      `protobuf:"varint,2,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
}

func (x *FeeList) Reset() {
	*x = FeeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeList) ProtoMessage() {}

func (x *FeeList) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeList.ProtoReflect.Descriptor instead.
func (*FeeList) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{20}
}

func (x *FeeList) GetFeeItems() []*FeeItem {
	if x != nil {
		return x.FeeItems
	}
	return nil
}

func (x *FeeList) GetTotalAmount() int64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

type Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId        int64            `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	OrderPayId     int64            `protobuf:"varint,2,opt,name=order_pay_id,json=orderPayId,proto3" json:"order_pay_id,omitempty"`
	TransStatus    TransStatus      `protobuf:"varint,3,opt,name=trans_status,json=transStatus,proto3,enum=acquiring_core.upay.v1.TransStatus" json:"trans_status,omitempty"`
	ReasonStatus   *ReasonStatus    `protobuf:"bytes,4,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
	SourceAssets   []*SourceAsset   `protobuf:"bytes,5,rep,name=source_assets,json=sourceAssets,proto3" json:"source_assets,omitempty"`
	DestAsset      *DestAsset       `protobuf:"bytes,6,opt,name=dest_asset,json=destAsset,proto3" json:"dest_asset,omitempty"`
	Amount         int64            `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	PromotionItems []*PromotionItem `protobuf:"bytes,8,rep,name=promotion_items,json=promotionItems,proto3" json:"promotion_items,omitempty"`
	FeeItems       []*FeeItem       `protobuf:"bytes,9,rep,name=fee_items,json=feeItems,proto3" json:"fee_items,omitempty"`
	Metadata       *TransactionMeta `protobuf:"bytes,10,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{21}
}

func (x *Transaction) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *Transaction) GetOrderPayId() int64 {
	if x != nil {
		return x.OrderPayId
	}
	return 0
}

func (x *Transaction) GetTransStatus() TransStatus {
	if x != nil {
		return x.TransStatus
	}
	return TransStatus_TRANS_STATUS_UNSPECIFIED
}

func (x *Transaction) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

func (x *Transaction) GetSourceAssets() []*SourceAsset {
	if x != nil {
		return x.SourceAssets
	}
	return nil
}

func (x *Transaction) GetDestAsset() *DestAsset {
	if x != nil {
		return x.DestAsset
	}
	return nil
}

func (x *Transaction) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Transaction) GetPromotionItems() []*PromotionItem {
	if x != nil {
		return x.PromotionItems
	}
	return nil
}

func (x *Transaction) GetFeeItems() []*FeeItem {
	if x != nil {
		return x.FeeItems
	}
	return nil
}

func (x *Transaction) GetMetadata() *TransactionMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type Amount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The currency code of the amount. The value of this field must be an alphabetic code that follows the ISO 4217 standard.
	// For example, "VND" for Vietnamese dong, "USD" for US dollar.
	// @gotags: validate:"required,iso4217"
	Currency string `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	// The value of the amount as a natural number. By default, the value of this field is in the smallest currency unit.
	// For example, if the currency is USD and the amount is $1.00, set the value of this field to 100 (cent); or if the
	// currency is JPY and the amount is ¥1, set the value of this field to 1 (yen).
	// @gotags: validate:"min=1"
	Value int64 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Amount) Reset() {
	*x = Amount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Amount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Amount) ProtoMessage() {}

func (x *Amount) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Amount.ProtoReflect.Descriptor instead.
func (*Amount) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{22}
}

func (x *Amount) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Amount) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type QuoteCurrency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A currency pair, of which the first listed currency (also called base currency) is quoted against the second
	// currency (also called quote currency). The value of this field is in format of {base_currency}/{quote_currency},
	// where {base_currency} and {quote_currency} are alphabetic codes that follow the ISO 4217 standard. For example,
	// if the base currency is Vietnamese dong and the quote currency is US dollar, the value of this field should be "VND/USD".
	// @gotags: validate:"required,max=7"
	Pair string `protobuf:"bytes,1,opt,name=pair,proto3" json:"pair,omitempty"`
	// The quotation of the exchange rate between the currency pair.
	// @gotags: validate:"required"
	Price float64 `protobuf:"fixed64,2,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *QuoteCurrency) Reset() {
	*x = QuoteCurrency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuoteCurrency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuoteCurrency) ProtoMessage() {}

func (x *QuoteCurrency) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuoteCurrency.ProtoReflect.Descriptor instead.
func (*QuoteCurrency) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{23}
}

func (x *QuoteCurrency) GetPair() string {
	if x != nil {
		return x.Pair
	}
	return ""
}

func (x *QuoteCurrency) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type Authorization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
}

func (x *Authorization) Reset() {
	*x = Authorization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Authorization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Authorization) ProtoMessage() {}

func (x *Authorization) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Authorization.ProtoReflect.Descriptor instead.
func (*Authorization) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{24}
}

func (x *Authorization) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

// *
// `ReasonStatus` presents the reason of a error status.
// The status can be `OrderStatus`, `TransStatus` or `AssetStatus`...
type ReasonStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The domain defines the reason of the error. For example: 'ACQUIRING', 'DOMAIN_PAYMENT_ENGINE'...
	Domain string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	// The reason of the error. This is a constant value that identifies the cause of the error.
	// Error reasons are unique within a particular domain of errors.
	// For example: 'PERMISSION_DENIED', 'INTERNAL'...
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// Additional information about the error.
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// A human-readable description of the error, it is safe to return for the end-user.
	FriendlyMessage string `protobuf:"bytes,4,opt,name=friendly_message,json=friendlyMessage,proto3" json:"friendly_message,omitempty"`
	// The details of the error from other domains.
	Details []*ReasonDetail `protobuf:"bytes,5,rep,name=details,proto3" json:"details,omitempty"`
}

func (x *ReasonStatus) Reset() {
	*x = ReasonStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReasonStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReasonStatus) ProtoMessage() {}

func (x *ReasonStatus) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReasonStatus.ProtoReflect.Descriptor instead.
func (*ReasonStatus) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{25}
}

func (x *ReasonStatus) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ReasonStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReasonStatus) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ReasonStatus) GetFriendlyMessage() string {
	if x != nil {
		return x.FriendlyMessage
	}
	return ""
}

func (x *ReasonStatus) GetDetails() []*ReasonDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

// *
// `ReasonDetail` presents the detail of a error from a domain.
type ReasonDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The domain defines the reason. For example: 'DOMAIN_PAYMENT_ENGINE'...
	Domain string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	// The reason of the error. This is a constant value that identifies the cause of the error.
	// Error reasons are unique within a particular domain of errors.
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// Additional information about the error.
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ReasonDetail) Reset() {
	*x = ReasonDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReasonDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReasonDetail) ProtoMessage() {}

func (x *ReasonDetail) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReasonDetail.ProtoReflect.Descriptor instead.
func (*ReasonDetail) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{26}
}

func (x *ReasonDetail) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ReasonDetail) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReasonDetail) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// *
// `OrderMeta` contains extra information about an Order. For example: AppConfig, Merchant information...
type OrderMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"max=5,dive,keys,label,min=1,max=50,endkeys,label,min=1,max=50"
	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @gotags: validate:"request_time_within=-15m 20m"
	AppTime int64 `protobuf:"varint,2,opt,name=app_time,json=appTime,proto3" json:"app_time,omitempty"`
	// @gotags: validate:"omitempty,max=50"
	AppUser string `protobuf:"bytes,3,opt,name=app_user,json=appUser,proto3" json:"app_user,omitempty"`
	// @gotags: validate:"omitempty,max=256"
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// @gotags: validate:"omitempty,max=20"
	ProductCode string     `protobuf:"bytes,5,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	AppConfig   *AppConfig `protobuf:"bytes,6,opt,name=app_config,json=appConfig,proto3" json:"app_config,omitempty"`
	// @gotags: validate:"omitempty,max=2048"
	Items string `protobuf:"bytes,7,opt,name=items,proto3" json:"items,omitempty"`
	// @gotags: validate:"omitempty,max=2048"
	EmbedData string `protobuf:"bytes,8,opt,name=embed_data,json=embedData,proto3" json:"embed_data,omitempty"`
	// DisplayObject is used to display the order information on some UI systems like: cashier, merchant tool...
	DisplayObject *DisplayObject     `protobuf:"bytes,9,opt,name=display_object,json=displayObject,proto3" json:"display_object,omitempty"`
	SubAppId      string             `protobuf:"bytes,10,opt,name=sub_app_id,json=subAppId,proto3" json:"sub_app_id,omitempty"`
	SubAppUser    string             `protobuf:"bytes,11,opt,name=sub_app_user,json=subAppUser,proto3" json:"sub_app_user,omitempty"`
	SubMerchant   *SubMerchant       `protobuf:"bytes,12,opt,name=sub_merchant,json=subMerchant,proto3" json:"sub_merchant,omitempty"`
	MerchantPromo *MerchantPromotion `protobuf:"bytes,13,opt,name=merchant_promo,json=merchantPromo,proto3" json:"merchant_promo,omitempty"`
	BillNumber    string             `protobuf:"bytes,14,opt,name=bill_number,json=billNumber,proto3" json:"bill_number,omitempty"`
	RefTransId    int64              `protobuf:"varint,15,opt,name=ref_trans_id,json=refTransId,proto3" json:"ref_trans_id,omitempty"`
}

func (x *OrderMeta) Reset() {
	*x = OrderMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderMeta) ProtoMessage() {}

func (x *OrderMeta) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderMeta.ProtoReflect.Descriptor instead.
func (*OrderMeta) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{27}
}

func (x *OrderMeta) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *OrderMeta) GetAppTime() int64 {
	if x != nil {
		return x.AppTime
	}
	return 0
}

func (x *OrderMeta) GetAppUser() string {
	if x != nil {
		return x.AppUser
	}
	return ""
}

func (x *OrderMeta) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OrderMeta) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *OrderMeta) GetAppConfig() *AppConfig {
	if x != nil {
		return x.AppConfig
	}
	return nil
}

func (x *OrderMeta) GetItems() string {
	if x != nil {
		return x.Items
	}
	return ""
}

func (x *OrderMeta) GetEmbedData() string {
	if x != nil {
		return x.EmbedData
	}
	return ""
}

func (x *OrderMeta) GetDisplayObject() *DisplayObject {
	if x != nil {
		return x.DisplayObject
	}
	return nil
}

func (x *OrderMeta) GetSubAppId() string {
	if x != nil {
		return x.SubAppId
	}
	return ""
}

func (x *OrderMeta) GetSubAppUser() string {
	if x != nil {
		return x.SubAppUser
	}
	return ""
}

func (x *OrderMeta) GetSubMerchant() *SubMerchant {
	if x != nil {
		return x.SubMerchant
	}
	return nil
}

func (x *OrderMeta) GetMerchantPromo() *MerchantPromotion {
	if x != nil {
		return x.MerchantPromo
	}
	return nil
}

func (x *OrderMeta) GetBillNumber() string {
	if x != nil {
		return x.BillNumber
	}
	return ""
}

func (x *OrderMeta) GetRefTransId() int64 {
	if x != nil {
		return x.RefTransId
	}
	return 0
}

// *
// `SubMerchant` contains information about the merchant that is not in the Zalopay system. Such as merchants of A+, Napas...
type SubMerchant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayName   string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Mcc           string `protobuf:"bytes,2,opt,name=mcc,proto3" json:"mcc,omitempty"`
	SubMerchantId string `protobuf:"bytes,3,opt,name=sub_merchant_id,json=subMerchantId,proto3" json:"sub_merchant_id,omitempty"`
}

func (x *SubMerchant) Reset() {
	*x = SubMerchant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubMerchant) ProtoMessage() {}

func (x *SubMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubMerchant.ProtoReflect.Descriptor instead.
func (*SubMerchant) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{28}
}

func (x *SubMerchant) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *SubMerchant) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *SubMerchant) GetSubMerchantId() string {
	if x != nil {
		return x.SubMerchantId
	}
	return ""
}

// *
// `MerchantPromotion` contains information about the promotion that is not in the Zalopay system. Such as promotions of A+, Napas...
type MerchantPromotion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=1,dive"
	PromoDetails []*MerchantPromotionDetails `protobuf:"bytes,1,rep,name=promo_details,json=promoDetails,proto3" json:"promo_details,omitempty"`
}

func (x *MerchantPromotion) Reset() {
	*x = MerchantPromotion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantPromotion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPromotion) ProtoMessage() {}

func (x *MerchantPromotion) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPromotion.ProtoReflect.Descriptor instead.
func (*MerchantPromotion) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{29}
}

func (x *MerchantPromotion) GetPromoDetails() []*MerchantPromotionDetails {
	if x != nil {
		return x.PromoDetails
	}
	return nil
}

type MerchantPromotionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The amount that is saved by be applied the promotion.
	// @gotags: validate:"required"
	SavingsAmount *Amount `protobuf:"bytes,2,opt,name=savingsAmount,proto3" json:"savingsAmount,omitempty"`
}

func (x *MerchantPromotionDetails) Reset() {
	*x = MerchantPromotionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantPromotionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPromotionDetails) ProtoMessage() {}

func (x *MerchantPromotionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPromotionDetails.ProtoReflect.Descriptor instead.
func (*MerchantPromotionDetails) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{30}
}

func (x *MerchantPromotionDetails) GetSavingsAmount() *Amount {
	if x != nil {
		return x.SavingsAmount
	}
	return nil
}

// *
// `PaymentMeta` contains extra information about a Payment. For example: PaymentSystem, device information...
type PaymentMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"max=5,dive,keys,label,min=1,max=50,endkeys,label,min=1,max=50"
	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// PaymentSystem is used to identify the source of payment
	// This field will override the value of `zpSystem` in `deprecated_data`
	// @gotags: validate:"required,valid"
	PaymentSystem PaymentSystem `protobuf:"varint,2,opt,name=payment_system,json=paymentSystem,proto3,enum=acquiring_core.upay.v1.PaymentSystem" json:"payment_system,omitempty"`
	// @gotags: validate:"omitempty,max=20"
	ProductCode string `protobuf:"bytes,3,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	// Device information of the payer who makes the payment.
	Device *Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	// RedirectURL is used to redirect the payer back to a specific URL in some cases. For example: Bank app redirects back to Zalopay app.
	RedirectUrl     string          `protobuf:"bytes,5,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
	AuthInfo        string          `protobuf:"bytes,6,opt,name=auth_info,json=authInfo,proto3" json:"auth_info,omitempty"`
	ClientSource    ClientSource    `protobuf:"varint,7,opt,name=client_source,json=clientSource,proto3,enum=acquiring_core.upay.v1.ClientSource" json:"client_source,omitempty"`
	PaymentSolution PaymentSolution `protobuf:"varint,8,opt,name=payment_solution,json=paymentSolution,proto3,enum=acquiring_core.upay.v1.PaymentSolution" json:"payment_solution,omitempty"`
}

func (x *PaymentMeta) Reset() {
	*x = PaymentMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMeta) ProtoMessage() {}

func (x *PaymentMeta) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMeta.ProtoReflect.Descriptor instead.
func (*PaymentMeta) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{31}
}

func (x *PaymentMeta) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PaymentMeta) GetPaymentSystem() PaymentSystem {
	if x != nil {
		return x.PaymentSystem
	}
	return PaymentSystem_PAYMENT_SYSTEM_UNSPECIFIED
}

func (x *PaymentMeta) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *PaymentMeta) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *PaymentMeta) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

func (x *PaymentMeta) GetAuthInfo() string {
	if x != nil {
		return x.AuthInfo
	}
	return ""
}

func (x *PaymentMeta) GetClientSource() ClientSource {
	if x != nil {
		return x.ClientSource
	}
	return ClientSource_CLIENT_SOURCE_UNSPECIFIED
}

func (x *PaymentMeta) GetPaymentSolution() PaymentSolution {
	if x != nil {
		return x.PaymentSolution
	}
	return PaymentSolution_PAYMENT_SOLUTION_UNSPECIFIED
}

// *
// `PaymentOption` contains flags that define payment behaviors.
type PaymentOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Indicates whether authentication is denied.
	// Transaction will be marked as 'FAILED' if that payment requires authentication.
	DenyAuthn bool `protobuf:"varint,1,opt,name=deny_authn,json=denyAuthn,proto3" json:"deny_authn,omitempty"`
	// Indicates whether the order has a future payment.
	// Merchant won't see the final status of the order through openAPI unless the payment is 'SUCCESS' or this flag is set to false.
	HasFuturePayment bool `protobuf:"varint,2,opt,name=has_future_payment,json=hasFuturePayment,proto3" json:"has_future_payment,omitempty"`
}

func (x *PaymentOption) Reset() {
	*x = PaymentOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentOption) ProtoMessage() {}

func (x *PaymentOption) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentOption.ProtoReflect.Descriptor instead.
func (*PaymentOption) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{32}
}

func (x *PaymentOption) GetDenyAuthn() bool {
	if x != nil {
		return x.DenyAuthn
	}
	return false
}

func (x *PaymentOption) GetHasFuturePayment() bool {
	if x != nil {
		return x.HasFuturePayment
	}
	return false
}

type AppConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallbackUrl string `protobuf:"bytes,1,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	RedirectUrl string `protobuf:"bytes,2,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
	// TTL is the time-to-live of the order. It is valid in range [5m, 30d]
	Ttl *durationpb.Duration `protobuf:"bytes,3,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *AppConfig) Reset() {
	*x = AppConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppConfig) ProtoMessage() {}

func (x *AppConfig) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppConfig.ProtoReflect.Descriptor instead.
func (*AppConfig) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{33}
}

func (x *AppConfig) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *AppConfig) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

func (x *AppConfig) GetTtl() *durationpb.Duration {
	if x != nil {
		return x.Ttl
	}
	return nil
}

type Payer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Payer:
	//
	//	*Payer_User
	//	*Payer_Merchant
	Payer isPayer_Payer `protobuf_oneof:"payer"`
}

func (x *Payer) Reset() {
	*x = Payer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Payer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Payer) ProtoMessage() {}

func (x *Payer) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Payer.ProtoReflect.Descriptor instead.
func (*Payer) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{34}
}

func (m *Payer) GetPayer() isPayer_Payer {
	if m != nil {
		return m.Payer
	}
	return nil
}

func (x *Payer) GetUser() *User {
	if x, ok := x.GetPayer().(*Payer_User); ok {
		return x.User
	}
	return nil
}

func (x *Payer) GetMerchant() *Merchant {
	if x, ok := x.GetPayer().(*Payer_Merchant); ok {
		return x.Merchant
	}
	return nil
}

type isPayer_Payer interface {
	isPayer_Payer()
}

type Payer_User struct {
	User *User `protobuf:"bytes,14,opt,name=user,proto3,oneof"`
}

type Payer_Merchant struct {
	Merchant *Merchant `protobuf:"bytes,15,opt,name=merchant,proto3,oneof"`
}

func (*Payer_User) isPayer_Payer() {}

func (*Payer_Merchant) isPayer_Payer() {}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	UserId  string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PinHash string `protobuf:"bytes,2,opt,name=pin_hash,json=pinHash,proto3" json:"pin_hash,omitempty"`
	ZaloId  string `protobuf:"bytes,3,opt,name=zalo_id,json=zaloId,proto3" json:"zalo_id,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{35}
}

func (x *User) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *User) GetPinHash() string {
	if x != nil {
		return x.PinHash
	}
	return ""
}

func (x *User) GetZaloId() string {
	if x != nil {
		return x.ZaloId
	}
	return ""
}

type Merchant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	MerchantId string `protobuf:"bytes,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
}

func (x *Merchant) Reset() {
	*x = Merchant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Merchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Merchant) ProtoMessage() {}

func (x *Merchant) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Merchant.ProtoReflect.Descriptor instead.
func (*Merchant) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{36}
}

func (x *Merchant) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

type SourceAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=0"
	Amount int64 `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// @gotags: validate:"required"
	PaymentMethod PaymentMethod `protobuf:"varint,2,opt,name=payment_method,json=paymentMethod,proto3,enum=acquiring_core.upay.v1.PaymentMethod" json:"payment_method,omitempty"`
	// @gotags: validate:"required"
	//
	// Types that are assignable to SourceAsset:
	//
	//	*SourceAsset_UserWallet
	//	*SourceAsset_UserBank
	//	*SourceAsset_UserFinance
	//	*SourceAsset_UserFixedDeposit
	//	*SourceAsset_ThirdParty
	//	*SourceAsset_InternalMerchant
	SourceAsset isSourceAsset_SourceAsset `protobuf_oneof:"source_asset"`
}

func (x *SourceAsset) Reset() {
	*x = SourceAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceAsset) ProtoMessage() {}

func (x *SourceAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceAsset.ProtoReflect.Descriptor instead.
func (*SourceAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{37}
}

func (x *SourceAsset) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *SourceAsset) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_PAYMENT_METHOD_UNSPECIFIED
}

func (m *SourceAsset) GetSourceAsset() isSourceAsset_SourceAsset {
	if m != nil {
		return m.SourceAsset
	}
	return nil
}

func (x *SourceAsset) GetUserWallet() *UserWalletAsset {
	if x, ok := x.GetSourceAsset().(*SourceAsset_UserWallet); ok {
		return x.UserWallet
	}
	return nil
}

func (x *SourceAsset) GetUserBank() *UserBankAsset {
	if x, ok := x.GetSourceAsset().(*SourceAsset_UserBank); ok {
		return x.UserBank
	}
	return nil
}

func (x *SourceAsset) GetUserFinance() *UserFinancialAsset {
	if x, ok := x.GetSourceAsset().(*SourceAsset_UserFinance); ok {
		return x.UserFinance
	}
	return nil
}

func (x *SourceAsset) GetUserFixedDeposit() *UserFixedDepositAsset {
	if x, ok := x.GetSourceAsset().(*SourceAsset_UserFixedDeposit); ok {
		return x.UserFixedDeposit
	}
	return nil
}

func (x *SourceAsset) GetThirdParty() *ThirdPartyAsset {
	if x, ok := x.GetSourceAsset().(*SourceAsset_ThirdParty); ok {
		return x.ThirdParty
	}
	return nil
}

func (x *SourceAsset) GetInternalMerchant() *InternalMerchantAsset {
	if x, ok := x.GetSourceAsset().(*SourceAsset_InternalMerchant); ok {
		return x.InternalMerchant
	}
	return nil
}

type isSourceAsset_SourceAsset interface {
	isSourceAsset_SourceAsset()
}

type SourceAsset_UserWallet struct {
	UserWallet *UserWalletAsset `protobuf:"bytes,3,opt,name=user_wallet,json=userWallet,proto3,oneof"`
}

type SourceAsset_UserBank struct {
	UserBank *UserBankAsset `protobuf:"bytes,4,opt,name=user_bank,json=userBank,proto3,oneof"`
}

type SourceAsset_UserFinance struct {
	UserFinance *UserFinancialAsset `protobuf:"bytes,5,opt,name=user_finance,json=userFinance,proto3,oneof"`
}

type SourceAsset_UserFixedDeposit struct {
	UserFixedDeposit *UserFixedDepositAsset `protobuf:"bytes,6,opt,name=user_fixed_deposit,json=userFixedDeposit,proto3,oneof"`
}

type SourceAsset_ThirdParty struct {
	ThirdParty *ThirdPartyAsset `protobuf:"bytes,7,opt,name=third_party,json=thirdParty,proto3,oneof"`
}

type SourceAsset_InternalMerchant struct {
	InternalMerchant *InternalMerchantAsset `protobuf:"bytes,8,opt,name=internal_merchant,json=internalMerchant,proto3,oneof"`
}

func (*SourceAsset_UserWallet) isSourceAsset_SourceAsset() {}

func (*SourceAsset_UserBank) isSourceAsset_SourceAsset() {}

func (*SourceAsset_UserFinance) isSourceAsset_SourceAsset() {}

func (*SourceAsset_UserFixedDeposit) isSourceAsset_SourceAsset() {}

func (*SourceAsset_ThirdParty) isSourceAsset_SourceAsset() {}

func (*SourceAsset_InternalMerchant) isSourceAsset_SourceAsset() {}

type DestAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required,valid"
	DestAssetType DestAssetType `protobuf:"varint,1,opt,name=dest_asset_type,json=destAssetType,proto3,enum=acquiring_core.upay.v1.DestAssetType" json:"dest_asset_type,omitempty"`
	// @gotags: validate:"required"
	//
	// Types that are assignable to DestAsset:
	//
	//	*DestAsset_Merchant
	//	*DestAsset_UserWallet
	//	*DestAsset_UserFixedDeposit
	//	*DestAsset_MerchantBankNapas
	//	*DestAsset_ExternalBank
	//	*DestAsset_InternalMerchant
	DestAsset isDestAsset_DestAsset `protobuf_oneof:"dest_asset"`
}

func (x *DestAsset) Reset() {
	*x = DestAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestAsset) ProtoMessage() {}

func (x *DestAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestAsset.ProtoReflect.Descriptor instead.
func (*DestAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{38}
}

func (x *DestAsset) GetDestAssetType() DestAssetType {
	if x != nil {
		return x.DestAssetType
	}
	return DestAssetType_DEST_ASSET_TYPE_UNSPECIFIED
}

func (m *DestAsset) GetDestAsset() isDestAsset_DestAsset {
	if m != nil {
		return m.DestAsset
	}
	return nil
}

func (x *DestAsset) GetMerchant() *MerchantAsset {
	if x, ok := x.GetDestAsset().(*DestAsset_Merchant); ok {
		return x.Merchant
	}
	return nil
}

func (x *DestAsset) GetUserWallet() *UserWalletAsset {
	if x, ok := x.GetDestAsset().(*DestAsset_UserWallet); ok {
		return x.UserWallet
	}
	return nil
}

func (x *DestAsset) GetUserFixedDeposit() *UserFixedDepositAsset {
	if x, ok := x.GetDestAsset().(*DestAsset_UserFixedDeposit); ok {
		return x.UserFixedDeposit
	}
	return nil
}

func (x *DestAsset) GetMerchantBankNapas() *MerchantBankNapasAsset {
	if x, ok := x.GetDestAsset().(*DestAsset_MerchantBankNapas); ok {
		return x.MerchantBankNapas
	}
	return nil
}

func (x *DestAsset) GetExternalBank() *ExternalBankAsset {
	if x, ok := x.GetDestAsset().(*DestAsset_ExternalBank); ok {
		return x.ExternalBank
	}
	return nil
}

func (x *DestAsset) GetInternalMerchant() *InternalMerchantAsset {
	if x, ok := x.GetDestAsset().(*DestAsset_InternalMerchant); ok {
		return x.InternalMerchant
	}
	return nil
}

type isDestAsset_DestAsset interface {
	isDestAsset_DestAsset()
}

type DestAsset_Merchant struct {
	Merchant *MerchantAsset `protobuf:"bytes,2,opt,name=merchant,proto3,oneof"`
}

type DestAsset_UserWallet struct {
	UserWallet *UserWalletAsset `protobuf:"bytes,3,opt,name=user_wallet,json=userWallet,proto3,oneof"`
}

type DestAsset_UserFixedDeposit struct {
	UserFixedDeposit *UserFixedDepositAsset `protobuf:"bytes,4,opt,name=user_fixed_deposit,json=userFixedDeposit,proto3,oneof"`
}

type DestAsset_MerchantBankNapas struct {
	MerchantBankNapas *MerchantBankNapasAsset `protobuf:"bytes,5,opt,name=merchant_bank_napas,json=merchantBankNapas,proto3,oneof"`
}

type DestAsset_ExternalBank struct {
	ExternalBank *ExternalBankAsset `protobuf:"bytes,6,opt,name=external_bank,json=externalBank,proto3,oneof"`
}

type DestAsset_InternalMerchant struct {
	InternalMerchant *InternalMerchantAsset `protobuf:"bytes,7,opt,name=internal_merchant,json=internalMerchant,proto3,oneof"`
}

func (*DestAsset_Merchant) isDestAsset_DestAsset() {}

func (*DestAsset_UserWallet) isDestAsset_DestAsset() {}

func (*DestAsset_UserFixedDeposit) isDestAsset_DestAsset() {}

func (*DestAsset_MerchantBankNapas) isDestAsset_DestAsset() {}

func (*DestAsset_ExternalBank) isDestAsset_DestAsset() {}

func (*DestAsset_InternalMerchant) isDestAsset_DestAsset() {}

type ThirdPartyAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	PspId string `protobuf:"bytes,1,opt,name=psp_id,json=pspId,proto3" json:"psp_id,omitempty"`
}

func (x *ThirdPartyAsset) Reset() {
	*x = ThirdPartyAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdPartyAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdPartyAsset) ProtoMessage() {}

func (x *ThirdPartyAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdPartyAsset.ProtoReflect.Descriptor instead.
func (*ThirdPartyAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{39}
}

func (x *ThirdPartyAsset) GetPspId() string {
	if x != nil {
		return x.PspId
	}
	return ""
}

type UserWalletAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required,number"
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// @gotags: validate:"valid"
	Type WalletType `protobuf:"varint,2,opt,name=type,proto3,enum=acquiring_core.upay.v1.WalletType" json:"type,omitempty"`
}

func (x *UserWalletAsset) Reset() {
	*x = UserWalletAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserWalletAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserWalletAsset) ProtoMessage() {}

func (x *UserWalletAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserWalletAsset.ProtoReflect.Descriptor instead.
func (*UserWalletAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{40}
}

func (x *UserWalletAsset) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserWalletAsset) GetType() WalletType {
	if x != nil {
		return x.Type
	}
	return WalletType_MAIN
}

type InternalMerchantAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InternalMerchantAsset) Reset() {
	*x = InternalMerchantAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalMerchantAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalMerchantAsset) ProtoMessage() {}

func (x *InternalMerchantAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalMerchantAsset.ProtoReflect.Descriptor instead.
func (*InternalMerchantAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{41}
}

type UserBankAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// BindingID represents a linked bank account/card/etc.
	BindingId       string `protobuf:"bytes,1,opt,name=binding_id,json=bindingId,proto3" json:"binding_id,omitempty"`
	BindingSystemId string `protobuf:"bytes,2,opt,name=binding_system_id,json=bindingSystemId,proto3" json:"binding_system_id,omitempty"`
	EscrowAccountNo string `protobuf:"bytes,3,opt,name=escrow_account_no,json=escrowAccountNo,proto3" json:"escrow_account_no,omitempty"`
	BcTransId       string `protobuf:"bytes,4,opt,name=bc_trans_id,json=bcTransId,proto3" json:"bc_trans_id,omitempty"`
	// Issuing bank
	BankCode          string `protobuf:"bytes,5,opt,name=bank_code,json=bankCode,proto3" json:"bank_code,omitempty"`
	BankConnectorCode string `protobuf:"bytes,6,opt,name=bank_connector_code,json=bankConnectorCode,proto3" json:"bank_connector_code,omitempty"`
	// BimID represents a bank account/card/etc. It can be either linked or unlinked.
	BimId                 string `protobuf:"bytes,7,opt,name=bim_id,json=bimId,proto3" json:"bim_id,omitempty"`
	F6No                  string `protobuf:"bytes,8,opt,name=f6no,proto3" json:"f6no,omitempty"`
	L4No                  string `protobuf:"bytes,9,opt,name=l4no,proto3" json:"l4no,omitempty"`
	CardScheme            string `protobuf:"bytes,10,opt,name=card_scheme,json=cardScheme,proto3" json:"card_scheme,omitempty"` // e.g. VISA, MASTERCARD, JCB
	FirstAccountNo        string `protobuf:"bytes,11,opt,name=first_account_no,json=firstAccountNo,proto3" json:"first_account_no,omitempty"`
	LastAccountNo         string `protobuf:"bytes,12,opt,name=last_account_no,json=lastAccountNo,proto3" json:"last_account_no,omitempty"`
	Token                 string `protobuf:"bytes,13,opt,name=token,proto3" json:"token,omitempty"`                                                                  //Payment Solution CARD_ON_FILE, GOOGLE_PAY FPAN
	ThirdPartyPaymentInfo string `protobuf:"bytes,14,opt,name=third_party_payment_info,json=thirdPartyPaymentInfo,proto3" json:"third_party_payment_info,omitempty"` //Payment Solution APPLE_PAY, GOOGLE_PAY DPAN
}

func (x *UserBankAsset) Reset() {
	*x = UserBankAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBankAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBankAsset) ProtoMessage() {}

func (x *UserBankAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBankAsset.ProtoReflect.Descriptor instead.
func (*UserBankAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{42}
}

func (x *UserBankAsset) GetBindingId() string {
	if x != nil {
		return x.BindingId
	}
	return ""
}

func (x *UserBankAsset) GetBindingSystemId() string {
	if x != nil {
		return x.BindingSystemId
	}
	return ""
}

func (x *UserBankAsset) GetEscrowAccountNo() string {
	if x != nil {
		return x.EscrowAccountNo
	}
	return ""
}

func (x *UserBankAsset) GetBcTransId() string {
	if x != nil {
		return x.BcTransId
	}
	return ""
}

func (x *UserBankAsset) GetBankCode() string {
	if x != nil {
		return x.BankCode
	}
	return ""
}

func (x *UserBankAsset) GetBankConnectorCode() string {
	if x != nil {
		return x.BankConnectorCode
	}
	return ""
}

func (x *UserBankAsset) GetBimId() string {
	if x != nil {
		return x.BimId
	}
	return ""
}

func (x *UserBankAsset) GetF6No() string {
	if x != nil {
		return x.F6No
	}
	return ""
}

func (x *UserBankAsset) GetL4No() string {
	if x != nil {
		return x.L4No
	}
	return ""
}

func (x *UserBankAsset) GetCardScheme() string {
	if x != nil {
		return x.CardScheme
	}
	return ""
}

func (x *UserBankAsset) GetFirstAccountNo() string {
	if x != nil {
		return x.FirstAccountNo
	}
	return ""
}

func (x *UserBankAsset) GetLastAccountNo() string {
	if x != nil {
		return x.LastAccountNo
	}
	return ""
}

func (x *UserBankAsset) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserBankAsset) GetThirdPartyPaymentInfo() string {
	if x != nil {
		return x.ThirdPartyPaymentInfo
	}
	return ""
}

type ExternalBankAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	BankCode          string                        `protobuf:"bytes,1,opt,name=bank_code,json=bankCode,proto3" json:"bank_code,omitempty"`
	FullName          string                        `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	AccountNo         string                        `protobuf:"bytes,3,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	CardNo            string                        `protobuf:"bytes,4,opt,name=card_no,json=cardNo,proto3" json:"card_no,omitempty"`
	InquiryTransId    string                        `protobuf:"bytes,5,opt,name=inquiry_trans_id,json=inquiryTransId,proto3" json:"inquiry_trans_id,omitempty"`
	RoutingType       ExternalBankAsset_RoutingType `protobuf:"varint,6,opt,name=routing_type,json=routingType,proto3,enum=acquiring_core.upay.v1.ExternalBankAsset_RoutingType" json:"routing_type,omitempty"`
	TokenType         ExternalBankAsset_TokenType   `protobuf:"varint,7,opt,name=token_type,json=tokenType,proto3,enum=acquiring_core.upay.v1.ExternalBankAsset_TokenType" json:"token_type,omitempty"`
	Token             string                        `protobuf:"bytes,8,opt,name=token,proto3" json:"token,omitempty"`
	SenderName        string                        `protobuf:"bytes,9,opt,name=sender_name,json=senderName,proto3" json:"sender_name,omitempty"`                         // use for IBFT
	FirstNo           string                        `protobuf:"bytes,10,opt,name=first_no,json=firstNo,proto3" json:"first_no,omitempty"`                                 // First account/card no
	LastNo            string                        `protobuf:"bytes,11,opt,name=last_no,json=lastNo,proto3" json:"last_no,omitempty"`                                    // Last account/card no
	BankConnectorCode string                        `protobuf:"bytes,12,opt,name=bank_connector_code,json=bankConnectorCode,proto3" json:"bank_connector_code,omitempty"` // use for VISA_DIRECT
}

func (x *ExternalBankAsset) Reset() {
	*x = ExternalBankAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalBankAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalBankAsset) ProtoMessage() {}

func (x *ExternalBankAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalBankAsset.ProtoReflect.Descriptor instead.
func (*ExternalBankAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{43}
}

func (x *ExternalBankAsset) GetBankCode() string {
	if x != nil {
		return x.BankCode
	}
	return ""
}

func (x *ExternalBankAsset) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *ExternalBankAsset) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *ExternalBankAsset) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *ExternalBankAsset) GetInquiryTransId() string {
	if x != nil {
		return x.InquiryTransId
	}
	return ""
}

func (x *ExternalBankAsset) GetRoutingType() ExternalBankAsset_RoutingType {
	if x != nil {
		return x.RoutingType
	}
	return ExternalBankAsset_ROUTING_TYPE_UNSPECIFIED
}

func (x *ExternalBankAsset) GetTokenType() ExternalBankAsset_TokenType {
	if x != nil {
		return x.TokenType
	}
	return ExternalBankAsset_TOKEN_TYPE_UNSPECIFIED
}

func (x *ExternalBankAsset) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ExternalBankAsset) GetSenderName() string {
	if x != nil {
		return x.SenderName
	}
	return ""
}

func (x *ExternalBankAsset) GetFirstNo() string {
	if x != nil {
		return x.FirstNo
	}
	return ""
}

func (x *ExternalBankAsset) GetLastNo() string {
	if x != nil {
		return x.LastNo
	}
	return ""
}

func (x *ExternalBankAsset) GetBankConnectorCode() string {
	if x != nil {
		return x.BankConnectorCode
	}
	return ""
}

type MerchantAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerchantId string `protobuf:"bytes,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// @gotags: validate:"min=1"
	AppId     int32  `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	PaymentId string `protobuf:"bytes,3,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
}

func (x *MerchantAsset) Reset() {
	*x = MerchantAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAsset) ProtoMessage() {}

func (x *MerchantAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAsset.ProtoReflect.Descriptor instead.
func (*MerchantAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{44}
}

func (x *MerchantAsset) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *MerchantAsset) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *MerchantAsset) GetPaymentId() string {
	if x != nil {
		return x.PaymentId
	}
	return ""
}

type UserFinancialAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PartnerCode string `protobuf:"bytes,2,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	ChargeInfo  string `protobuf:"bytes,3,opt,name=charge_info,json=chargeInfo,proto3" json:"charge_info,omitempty"`
}

func (x *UserFinancialAsset) Reset() {
	*x = UserFinancialAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFinancialAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFinancialAsset) ProtoMessage() {}

func (x *UserFinancialAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFinancialAsset.ProtoReflect.Descriptor instead.
func (*UserFinancialAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{45}
}

func (x *UserFinancialAsset) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserFinancialAsset) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *UserFinancialAsset) GetChargeInfo() string {
	if x != nil {
		return x.ChargeInfo
	}
	return ""
}

type UserFixedDepositAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required,number"
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *UserFixedDepositAsset) Reset() {
	*x = UserFixedDepositAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFixedDepositAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFixedDepositAsset) ProtoMessage() {}

func (x *UserFixedDepositAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFixedDepositAsset.ProtoReflect.Descriptor instead.
func (*UserFixedDepositAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{46}
}

func (x *UserFixedDepositAsset) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type MerchantBankNapasAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	BankCode string `protobuf:"bytes,1,opt,name=bank_code,json=bankCode,proto3" json:"bank_code,omitempty"`
	// @gotags: validate:"required"
	BankConnectorCode string `protobuf:"bytes,2,opt,name=bank_connector_code,json=bankConnectorCode,proto3" json:"bank_connector_code,omitempty"`
	// @gotags: validate:"required"
	InquiryTransId string `protobuf:"bytes,3,opt,name=inquiry_trans_id,json=inquiryTransId,proto3" json:"inquiry_trans_id,omitempty"`
	// @gotags: validate:"required"
	AccountNo string `protobuf:"bytes,4,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	// @gotags: validate:"required"
	HolderName string `protobuf:"bytes,5,opt,name=holder_name,json=holderName,proto3" json:"holder_name,omitempty"`
	// @gotags: validate:"required"
	OrgAccType string `protobuf:"bytes,6,opt,name=org_acc_type,json=orgAccType,proto3" json:"org_acc_type,omitempty"`
	// @gotags: validate:"required"
	TargetAccType string `protobuf:"bytes,7,opt,name=target_acc_type,json=targetAccType,proto3" json:"target_acc_type,omitempty"`
	// @gotags: validate:"required"
	TransReference string `protobuf:"bytes,8,opt,name=trans_reference,json=transReference,proto3" json:"trans_reference,omitempty"`
}

func (x *MerchantBankNapasAsset) Reset() {
	*x = MerchantBankNapasAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantBankNapasAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantBankNapasAsset) ProtoMessage() {}

func (x *MerchantBankNapasAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantBankNapasAsset.ProtoReflect.Descriptor instead.
func (*MerchantBankNapasAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{47}
}

func (x *MerchantBankNapasAsset) GetBankCode() string {
	if x != nil {
		return x.BankCode
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetBankConnectorCode() string {
	if x != nil {
		return x.BankConnectorCode
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetInquiryTransId() string {
	if x != nil {
		return x.InquiryTransId
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetHolderName() string {
	if x != nil {
		return x.HolderName
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetOrgAccType() string {
	if x != nil {
		return x.OrgAccType
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetTargetAccType() string {
	if x != nil {
		return x.TargetAccType
	}
	return ""
}

func (x *MerchantBankNapasAsset) GetTransReference() string {
	if x != nil {
		return x.TransReference
	}
	return ""
}

type PromotionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	Type PromotionType `protobuf:"varint,1,opt,name=type,proto3,enum=acquiring_core.upay.v1.PromotionType" json:"type,omitempty"`
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	CampaignId string `protobuf:"bytes,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	PromoSig string `protobuf:"bytes,3,opt,name=promo_sig,json=promoSig,proto3" json:"promo_sig,omitempty"`
	// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
	Amount int64 `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// Types that are assignable to Item:
	//
	//	*PromotionItem_Voucher
	//	*PromotionItem_DirectDiscount
	Item isPromotionItem_Item `protobuf_oneof:"item"`
}

func (x *PromotionItem) Reset() {
	*x = PromotionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PromotionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromotionItem) ProtoMessage() {}

func (x *PromotionItem) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromotionItem.ProtoReflect.Descriptor instead.
func (*PromotionItem) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{48}
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *PromotionItem) GetType() PromotionType {
	if x != nil {
		return x.Type
	}
	return PromotionType_PROMOTION_TYPE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *PromotionItem) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *PromotionItem) GetPromoSig() string {
	if x != nil {
		return x.PromoSig
	}
	return ""
}

// Deprecated: Marked as deprecated in external_services/acquiring_core/user_payment.proto.
func (x *PromotionItem) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (m *PromotionItem) GetItem() isPromotionItem_Item {
	if m != nil {
		return m.Item
	}
	return nil
}

func (x *PromotionItem) GetVoucher() *Voucher {
	if x, ok := x.GetItem().(*PromotionItem_Voucher); ok {
		return x.Voucher
	}
	return nil
}

func (x *PromotionItem) GetDirectDiscount() *DirectDiscount {
	if x, ok := x.GetItem().(*PromotionItem_DirectDiscount); ok {
		return x.DirectDiscount
	}
	return nil
}

type isPromotionItem_Item interface {
	isPromotionItem_Item()
}

type PromotionItem_Voucher struct {
	Voucher *Voucher `protobuf:"bytes,5,opt,name=voucher,proto3,oneof"`
}

type PromotionItem_DirectDiscount struct {
	DirectDiscount *DirectDiscount `protobuf:"bytes,6,opt,name=direct_discount,json=directDiscount,proto3,oneof"`
}

func (*PromotionItem_Voucher) isPromotionItem_Item() {}

func (*PromotionItem_DirectDiscount) isPromotionItem_Item() {}

type Voucher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	CampaignId string `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	// @gotags: validate:"required"
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// @gotags: validate:"required"
	Sig string `protobuf:"bytes,3,opt,name=sig,proto3" json:"sig,omitempty"`
	// @gotags: validate:"min=1"
	Amount  int64                  `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	UseTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=use_time,json=useTime,proto3" json:"use_time,omitempty"`
}

func (x *Voucher) Reset() {
	*x = Voucher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Voucher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Voucher) ProtoMessage() {}

func (x *Voucher) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Voucher.ProtoReflect.Descriptor instead.
func (*Voucher) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{49}
}

func (x *Voucher) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *Voucher) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Voucher) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

func (x *Voucher) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Voucher) GetUseTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UseTime
	}
	return nil
}

type DirectDiscount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required"
	CampaignId string `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	// @gotags: validate:"required"
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// @gotags: validate:"required"
	Sig string `protobuf:"bytes,3,opt,name=sig,proto3" json:"sig,omitempty"`
	// @gotags: validate:"min=1"
	Amount int64 `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *DirectDiscount) Reset() {
	*x = DirectDiscount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectDiscount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectDiscount) ProtoMessage() {}

func (x *DirectDiscount) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectDiscount.ProtoReflect.Descriptor instead.
func (*DirectDiscount) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{50}
}

func (x *DirectDiscount) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *DirectDiscount) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DirectDiscount) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

func (x *DirectDiscount) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

// ConfirmedFee is used to validate the fee that is confirmed by the user.
type ConfirmedFee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=0"
	TotalAmount int64 `protobuf:"varint,1,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
}

func (x *ConfirmedFee) Reset() {
	*x = ConfirmedFee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmedFee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmedFee) ProtoMessage() {}

func (x *ConfirmedFee) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmedFee.ProtoReflect.Descriptor instead.
func (*ConfirmedFee) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{51}
}

func (x *ConfirmedFee) GetTotalAmount() int64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

type FeeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   FeeType `protobuf:"varint,1,opt,name=type,proto3,enum=acquiring_core.upay.v1.FeeType" json:"type,omitempty"`
	Amount int64   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *FeeItem) Reset() {
	*x = FeeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeItem) ProtoMessage() {}

func (x *FeeItem) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeItem.ProtoReflect.Descriptor instead.
func (*FeeItem) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{52}
}

func (x *FeeItem) GetType() FeeType {
	if x != nil {
		return x.Type
	}
	return FeeType_FEE_TYPE_UNSPECIFIED
}

func (x *FeeItem) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId    string       `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DeviceModel string       `protobuf:"bytes,2,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"` // e.g. iPhone11,2 or Xiaomi M2003J15SC
	OsType      OsType       `protobuf:"varint,3,opt,name=os_type,json=osType,proto3,enum=acquiring_core.upay.v1.OsType" json:"os_type,omitempty"`
	OsVersion   string       `protobuf:"bytes,4,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"` // e.g. 15.6.1
	Platform    PlatformType `protobuf:"varint,5,opt,name=platform,proto3,enum=acquiring_core.upay.v1.PlatformType" json:"platform,omitempty"`
	AppVersion  string       `protobuf:"bytes,6,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"` // e.g. 7.20.0
	UserIp      string       `protobuf:"bytes,7,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{53}
}

func (x *Device) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Device) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *Device) GetOsType() OsType {
	if x != nil {
		return x.OsType
	}
	return OsType_OS_TYPE_UNSPECIFIED
}

func (x *Device) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *Device) GetPlatform() PlatformType {
	if x != nil {
		return x.Platform
	}
	return PlatformType_PLATFORM_TYPE_UNSPECIFIED
}

func (x *Device) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *Device) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

type DisplayObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"min=1,max=10,dive,required"
	Fields []*DisplayObject_Field `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *DisplayObject) Reset() {
	*x = DisplayObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayObject) ProtoMessage() {}

func (x *DisplayObject) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayObject.ProtoReflect.Descriptor instead.
func (*DisplayObject) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{54}
}

func (x *DisplayObject) GetFields() []*DisplayObject_Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

type TransactionMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       int32  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ProductCode string `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
}

func (x *TransactionMeta) Reset() {
	*x = TransactionMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionMeta) ProtoMessage() {}

func (x *TransactionMeta) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionMeta.ProtoReflect.Descriptor instead.
func (*TransactionMeta) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{55}
}

func (x *TransactionMeta) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *TransactionMeta) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

type ConsultUserAssetsResponse_UserAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl       string              `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	PaymentMethod PaymentMethod       `protobuf:"varint,3,opt,name=payment_method,json=paymentMethod,proto3,enum=acquiring_core.upay.v1.PaymentMethod" json:"payment_method,omitempty"`
	SourceAsset   *ConsultSourceAsset `protobuf:"bytes,4,opt,name=source_asset,json=sourceAsset,proto3" json:"source_asset,omitempty"`
	Status        AssetStatus         `protobuf:"varint,5,opt,name=status,proto3,enum=acquiring_core.upay.v1.AssetStatus" json:"status,omitempty"`
	ReasonStatus  *ReasonStatus       `protobuf:"bytes,6,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
}

func (x *ConsultUserAssetsResponse_UserAsset) Reset() {
	*x = ConsultUserAssetsResponse_UserAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultUserAssetsResponse_UserAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultUserAssetsResponse_UserAsset) ProtoMessage() {}

func (x *ConsultUserAssetsResponse_UserAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultUserAssetsResponse_UserAsset.ProtoReflect.Descriptor instead.
func (*ConsultUserAssetsResponse_UserAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ConsultUserAssetsResponse_UserAsset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConsultUserAssetsResponse_UserAsset) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *ConsultUserAssetsResponse_UserAsset) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_PAYMENT_METHOD_UNSPECIFIED
}

func (x *ConsultUserAssetsResponse_UserAsset) GetSourceAsset() *ConsultSourceAsset {
	if x != nil {
		return x.SourceAsset
	}
	return nil
}

func (x *ConsultUserAssetsResponse_UserAsset) GetStatus() AssetStatus {
	if x != nil {
		return x.Status
	}
	return AssetStatus_ASSET_STATUS_UNSPECIFIED
}

func (x *ConsultUserAssetsResponse_UserAsset) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

type ConsultSourceAsset_ConsultUserWalletAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wallet  *UserWalletAsset `protobuf:"bytes,1,opt,name=wallet,proto3" json:"wallet,omitempty"`
	Balance int64            `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *ConsultSourceAsset_ConsultUserWalletAsset) Reset() {
	*x = ConsultSourceAsset_ConsultUserWalletAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultSourceAsset_ConsultUserWalletAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultSourceAsset_ConsultUserWalletAsset) ProtoMessage() {}

func (x *ConsultSourceAsset_ConsultUserWalletAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultSourceAsset_ConsultUserWalletAsset.ProtoReflect.Descriptor instead.
func (*ConsultSourceAsset_ConsultUserWalletAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ConsultSourceAsset_ConsultUserWalletAsset) GetWallet() *UserWalletAsset {
	if x != nil {
		return x.Wallet
	}
	return nil
}

func (x *ConsultSourceAsset_ConsultUserWalletAsset) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type ConsultSourceAsset_ConsultUserBankAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bank           *UserBankAsset    `protobuf:"bytes,1,opt,name=bank,proto3" json:"bank,omitempty"`
	DeprecatedData map[string]string `protobuf:"bytes,99,rep,name=deprecated_data,json=deprecatedData,proto3" json:"deprecated_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ConsultSourceAsset_ConsultUserBankAsset) Reset() {
	*x = ConsultSourceAsset_ConsultUserBankAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultSourceAsset_ConsultUserBankAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultSourceAsset_ConsultUserBankAsset) ProtoMessage() {}

func (x *ConsultSourceAsset_ConsultUserBankAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultSourceAsset_ConsultUserBankAsset.ProtoReflect.Descriptor instead.
func (*ConsultSourceAsset_ConsultUserBankAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{14, 1}
}

func (x *ConsultSourceAsset_ConsultUserBankAsset) GetBank() *UserBankAsset {
	if x != nil {
		return x.Bank
	}
	return nil
}

func (x *ConsultSourceAsset_ConsultUserBankAsset) GetDeprecatedData() map[string]string {
	if x != nil {
		return x.DeprecatedData
	}
	return nil
}

type ConsultSourceAsset_ConsultUserFinancialAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Finance *UserFinancialAsset `protobuf:"bytes,1,opt,name=finance,proto3" json:"finance,omitempty"`
	Balance int64               `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *ConsultSourceAsset_ConsultUserFinancialAsset) Reset() {
	*x = ConsultSourceAsset_ConsultUserFinancialAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsultSourceAsset_ConsultUserFinancialAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsultSourceAsset_ConsultUserFinancialAsset) ProtoMessage() {}

func (x *ConsultSourceAsset_ConsultUserFinancialAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsultSourceAsset_ConsultUserFinancialAsset.ProtoReflect.Descriptor instead.
func (*ConsultSourceAsset_ConsultUserFinancialAsset) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{14, 2}
}

func (x *ConsultSourceAsset_ConsultUserFinancialAsset) GetFinance() *UserFinancialAsset {
	if x != nil {
		return x.Finance
	}
	return nil
}

func (x *ConsultSourceAsset_ConsultUserFinancialAsset) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type DisplayObject_Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the field
	// @gotags: validate:"required,max=50"
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Value of the field
	// @gotags: validate:"required,max=255"
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// DisplaySystems is the list of systems that the field will be displayed on.
	// Currently, only `cashier` is supported.
	// @gotags: validate:"eq=1,dive,required"
	DisplaySystems []string `protobuf:"bytes,3,rep,name=display_systems,json=displaySystems,proto3" json:"display_systems,omitempty"`
}

func (x *DisplayObject_Field) Reset() {
	*x = DisplayObject_Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayObject_Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayObject_Field) ProtoMessage() {}

func (x *DisplayObject_Field) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_acquiring_core_user_payment_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayObject_Field.ProtoReflect.Descriptor instead.
func (*DisplayObject_Field) Descriptor() ([]byte, []int) {
	return file_external_services_acquiring_core_user_payment_proto_rawDescGZIP(), []int{54, 0}
}

func (x *DisplayObject_Field) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DisplayObject_Field) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *DisplayObject_Field) GetDisplaySystems() []string {
	if x != nil {
		return x.DisplaySystems
	}
	return nil
}

var File_external_services_acquiring_core_user_payment_proto protoreflect.FileDescriptor

var file_external_services_acquiring_core_user_payment_proto_rawDesc = []byte{
	0x0a, 0x33, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbd,
	0x05, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40,
	0x0a, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x64, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x3d, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x4c, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0d,
	0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x45, 0x0a,
	0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x0e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x0d, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x67, 0x0a, 0x0f, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x63, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x41, 0x0a, 0x13, 0x44,
	0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbc,
	0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x61, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x22, 0xcf, 0x02,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x3b,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22,
	0xb6, 0x05, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f,
	0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x65, 0x72, 0x52, 0x05,
	0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12,
	0x3f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x49, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x46, 0x65, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x65, 0x64, 0x46, 0x65, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x4c, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a, 0x0f, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x63, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50,
	0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x64,
	0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x41, 0x0a,
	0x13, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07, 0x4a, 0x04, 0x08, 0x08,
	0x10, 0x09, 0x4a, 0x04, 0x08, 0x09, 0x10, 0x0a, 0x22, 0x8d, 0x02, 0x0a, 0x12, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x22, 0x62, 0x0a, 0x0e, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x11, 0x0a, 0x0f,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2a, 0x0a, 0x0d, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x22, 0x10, 0x0a, 0x0e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x22, 0x39, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x56, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x41, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x22, 0xe2,
	0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x22, 0xd2, 0x03, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x53, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x1a, 0xdf, 0x02, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x4c, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x4d, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a,
	0x0d, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd5, 0x06, 0x0a, 0x12, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x64, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x5e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x69, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x48, 0x00, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x1a, 0x73, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x3f, 0x0a, 0x06, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x06, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x92, 0x02, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x39,
	0x0a, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x12, 0x7c, 0x0a, 0x0f, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x63, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x53, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x41, 0x0a, 0x13, 0x44, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x7b, 0x0a, 0x19, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x44, 0x0a, 0x07, 0x66, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x22, 0xb8, 0x08, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40,
	0x0a, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x64, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a,
	0x0d, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x3d, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x4c, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0d, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x45, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a,
	0x0e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x6f, 0x74, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0d, 0x71, 0x75,
	0x6f, 0x74, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x5a, 0x0a, 0x0f,
	0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x63, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x41, 0x0a, 0x13, 0x44, 0x65, 0x70, 0x72,
	0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xff, 0x09, 0x0a, 0x07,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x0d, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x35, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x52, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x40, 0x0a, 0x09,
	0x66, 0x65, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x66, 0x65, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x53,
	0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x52, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x65, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x66, 0x65, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x4b, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c,
	0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0f,
	0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x63, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x72,
	0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x41, 0x0a, 0x13, 0x44, 0x65,
	0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7e, 0x0a,
	0x0f, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5a, 0x0a,
	0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x47, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x0d, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x0f, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6a,
	0x0a, 0x07, 0x46, 0x65, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x66, 0x65, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x66,
	0x65, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd4, 0x04, 0x0a, 0x0b, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x49, 0x0a, 0x0d, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x64, 0x65, 0x73,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e,
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3c,
	0x0a, 0x09, 0x66, 0x65, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x08, 0x66, 0x65, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x43, 0x0a, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x3a, 0x0a, 0x06, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x39, 0x0a,
	0x0d, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x69, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x69, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0x2a, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x49, 0x64, 0x22, 0xb6, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c,
	0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x3e, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xcb, 0x01,
	0x0a, 0x0c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x4e,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xea, 0x05, 0x0a, 0x09,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x55, 0x73, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x61,
	0x70, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x09, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x4c, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x1c, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0c, 0x73, 0x75, 0x62, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x41, 0x70, 0x70, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x46, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x75, 0x62, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x75, 0x62,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x0e, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x69,
	0x6c, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0c, 0x72,
	0x65, 0x66, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6a, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x26, 0x0a, 0x0f,
	0x73, 0x75, 0x62, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x11, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x0d, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x60, 0x0a, 0x18, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x44, 0x0a, 0x0d,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x0d, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x99, 0x04, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x47, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x52, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5c,
	0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6e, 0x79, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x65, 0x6e, 0x79, 0x41, 0x75, 0x74, 0x68, 0x6e, 0x12, 0x2c,
	0x0a, 0x12, 0x68, 0x61, 0x73, 0x5f, 0x66, 0x75, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x68, 0x61, 0x73, 0x46,
	0x75, 0x74, 0x75, 0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x7e, 0x0a, 0x09,
	0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x12,
	0x2b, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x22, 0x84, 0x01, 0x0a,
	0x05, 0x50, 0x61, 0x79, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x08, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x70, 0x61,
	0x79, 0x65, 0x72, 0x22, 0x53, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x69, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x17, 0x0a, 0x07, 0x7a, 0x61, 0x6c, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x7a, 0x61, 0x6c, 0x6f, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x08, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xef, 0x04, 0x0a, 0x0b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a,
	0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0d, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x4a, 0x0a, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x44, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x62, 0x61, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x48, 0x00, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x4f, 0x0a,
	0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48,
	0x00, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x5d,
	0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69, 0x78, 0x65, 0x64, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x10, 0x75, 0x73, 0x65,
	0x72, 0x46, 0x69, 0x78, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x4a, 0x0a,
	0x0b, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x68, 0x69, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x74,
	0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x11, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x22, 0xea, 0x04, 0x0a, 0x09, 0x44, 0x65, 0x73, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x4d, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52,
	0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0b, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x57, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x5d, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x48, 0x00, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x46, 0x69, 0x78, 0x65, 0x64, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x12, 0x60, 0x0a, 0x13, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x70, 0x61, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x70, 0x61, 0x73, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x48, 0x00, 0x52, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x61, 0x6e,
	0x6b, 0x4e, 0x61, 0x70, 0x61, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x42,
	0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x5c, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x48, 0x00, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x22, 0x28, 0x0a, 0x0f, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x73, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x73, 0x70, 0x49, 0x64, 0x22, 0x62,
	0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x17, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x22, 0xf4, 0x03, 0x0a, 0x0d,
	0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x73, 0x63, 0x72,
	0x6f, 0x77, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x73, 0x63, 0x72, 0x6f, 0x77, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x63, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x69, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x36, 0x6e, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x36, 0x6e, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x34, 0x6e, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x34, 0x6e, 0x6f,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x68, 0x69,
	0x72, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x74, 0x68, 0x69,
	0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xc6, 0x05, 0x0a, 0x11, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x42,
	0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e,
	0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0c, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52,
	0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x6f, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x6f, 0x12, 0x2e, 0x0a,
	0x13, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x61, 0x6e, 0x6b,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x6d, 0x0a,
	0x09, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a,
	0x0e, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x50, 0x53, 0x10,
	0x02, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x03, 0x22, 0x5d, 0x0a, 0x0b,
	0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x52,
	0x4f, 0x55, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x4f, 0x55,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52,
	0x41, 0x57, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x4f, 0x55, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x42, 0x46, 0x54, 0x10, 0x02, 0x22, 0x66, 0x0a, 0x0d, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x30, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc2, 0x02, 0x0a, 0x16, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x70, 0x61, 0x73, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62,
	0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72,
	0x67, 0x5f, 0x61, 0x63, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x67, 0x41, 0x63, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xc8, 0x02,
	0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f, 0x73, 0x69, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x53, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x3b, 0x0a, 0x07, 0x76, 0x6f, 0x75, 0x63, 0x68, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x75, 0x63, 0x68,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x07, 0x76, 0x6f, 0x75, 0x63, 0x68, 0x65, 0x72, 0x12, 0x51, 0x0a,
	0x0f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x0e, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x06, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x9f, 0x01, 0x0a, 0x07, 0x56, 0x6f, 0x75,
	0x63, 0x68, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x07, 0x75, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x6f, 0x0a, 0x0e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x73, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x31, 0x0a, 0x0c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x46, 0x65, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x56,
	0x0a, 0x07, 0x46, 0x65, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9c, 0x02, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x37, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x70, 0x22, 0xb0, 0x01, 0x0a, 0x0d, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x5a, 0x0a, 0x05,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x4b, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x2a, 0x78, 0x0a, 0x0b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b,
	0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x12, 0x0f, 0x0a,
	0x0b, 0x52, 0x45, 0x41, 0x43, 0x48, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x04, 0x12, 0x0f,
	0x0a, 0x0b, 0x4e, 0x45, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x2a,
	0x8c, 0x01, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1c, 0x0a, 0x18, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05,
	0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x06,
	0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x2a, 0x9d,
	0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xb2,
	0x01, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c,
	0x0a, 0x18, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x05, 0x12, 0x10, 0x0a,
	0x0c, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12,
	0x12, 0x0a, 0x0e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45,
	0x44, 0x10, 0x07, 0x2a, 0x63, 0x0a, 0x0d, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x2a, 0xbb, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x57, 0x42,
	0x4c, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x44, 0x43, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03,
	0x42, 0x44, 0x41, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x49, 0x43, 0x10, 0x04, 0x12, 0x07,
	0x0a, 0x03, 0x42, 0x49, 0x44, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x56, 0x43, 0x4f,
	0x10, 0x07, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x4d, 0x46, 0x10, 0x08, 0x12, 0x07, 0x0a, 0x03, 0x50,
	0x4c, 0x54, 0x10, 0x09, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x50, 0x49, 0x10, 0x0a, 0x12, 0x08, 0x0a,
	0x04, 0x49, 0x4e, 0x53, 0x54, 0x10, 0x0b, 0x12, 0x06, 0x0a, 0x02, 0x46, 0x44, 0x10, 0x0c, 0x12,
	0x09, 0x0a, 0x05, 0x45, 0x4d, 0x56, 0x54, 0x46, 0x10, 0x0d, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x48,
	0x49, 0x52, 0x44, 0x50, 0x53, 0x10, 0x0e, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x42, 0x50, 0x10, 0x0f,
	0x22, 0x04, 0x08, 0x06, 0x10, 0x06, 0x2a, 0x8d, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x74, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x53, 0x54,
	0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x45, 0x52,
	0x43, 0x48, 0x41, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x53, 0x45, 0x52, 0x10,
	0x02, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x44, 0x41, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4d,
	0x43, 0x41, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x42, 0x44, 0x41, 0x10, 0x05, 0x12, 0x08,
	0x0a, 0x04, 0x55, 0x42, 0x44, 0x43, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x42, 0x49, 0x43,
	0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x42, 0x49, 0x44, 0x10, 0x08, 0x12, 0x08, 0x0a, 0x04,
	0x4d, 0x4d, 0x42, 0x50, 0x10, 0x09, 0x2a, 0x35, 0x0a, 0x0a, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x89, 0x01,
	0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x18, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x49, 0x54, 0x48,
	0x44, 0x52, 0x41, 0x57, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x4f, 0x50, 0x55, 0x50, 0x10,
	0x03, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x42, 0x46, 0x54, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x49, 0x51,
	0x55, 0x49, 0x44, 0x41, 0x54, 0x45, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x49, 0x53, 0x41,
	0x5f, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x10, 0x07, 0x2a, 0x48, 0x0a, 0x0d, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x41,
	0x4c, 0x4c, 0x45, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x41, 0x54, 0x45, 0x57, 0x41,
	0x59, 0x10, 0x02, 0x2a, 0x51, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x4f, 0x55, 0x43, 0x48, 0x45, 0x52, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0x56, 0x0a, 0x07, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x45, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x50,
	0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x03, 0x2a, 0x45,
	0x0a, 0x06, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x07,
	0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x4f, 0x53, 0x10, 0x63, 0x2a, 0x53, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x5a, 0x50, 0x41, 0x10, 0x01, 0x12, 0x07, 0x0a,
	0x03, 0x5a, 0x50, 0x49, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f,
	0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x63, 0x2a, 0x4c, 0x0a, 0x0c, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4c,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x53,
	0x48, 0x49, 0x45, 0x52, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x63, 0x2a, 0x64, 0x0a, 0x0f, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x4c, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a,
	0x0c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12,
	0x0e, 0x0a, 0x0a, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x02, 0x12,
	0x0d, 0x0a, 0x09, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x03, 0x32, 0x9f,
	0x07, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x66,
	0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x30, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x61, 0x63,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0a, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50, 0x61, 0x79, 0x12, 0x29, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5a, 0x0a, 0x07, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x06, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x25, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x60, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x6a, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x41, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x49, 0x64, 0x12, 0x33, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x41, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x78, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75, 0x70, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x75,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x37, 0x5a, 0x35, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x3b, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_external_services_acquiring_core_user_payment_proto_rawDescOnce sync.Once
	file_external_services_acquiring_core_user_payment_proto_rawDescData = file_external_services_acquiring_core_user_payment_proto_rawDesc
)

func file_external_services_acquiring_core_user_payment_proto_rawDescGZIP() []byte {
	file_external_services_acquiring_core_user_payment_proto_rawDescOnce.Do(func() {
		file_external_services_acquiring_core_user_payment_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_acquiring_core_user_payment_proto_rawDescData)
	})
	return file_external_services_acquiring_core_user_payment_proto_rawDescData
}

var file_external_services_acquiring_core_user_payment_proto_enumTypes = make([]protoimpl.EnumInfo, 18)
var file_external_services_acquiring_core_user_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 70)
var file_external_services_acquiring_core_user_payment_proto_goTypes = []any{
	(AssetStatus)(0),                            // 0: acquiring_core.upay.v1.AssetStatus
	(OrderStatus)(0),                            // 1: acquiring_core.upay.v1.OrderStatus
	(PaymentStatus)(0),                          // 2: acquiring_core.upay.v1.PaymentStatus
	(TransStatus)(0),                            // 3: acquiring_core.upay.v1.TransStatus
	(CaptureMethod)(0),                          // 4: acquiring_core.upay.v1.CaptureMethod
	(PaymentMethod)(0),                          // 5: acquiring_core.upay.v1.PaymentMethod
	(DestAssetType)(0),                          // 6: acquiring_core.upay.v1.DestAssetType
	(WalletType)(0),                             // 7: acquiring_core.upay.v1.WalletType
	(PaymentType)(0),                            // 8: acquiring_core.upay.v1.PaymentType
	(PaymentSystem)(0),                          // 9: acquiring_core.upay.v1.PaymentSystem
	(PromotionType)(0),                          // 10: acquiring_core.upay.v1.PromotionType
	(FeeType)(0),                                // 11: acquiring_core.upay.v1.FeeType
	(OsType)(0),                                 // 12: acquiring_core.upay.v1.OsType
	(PlatformType)(0),                           // 13: acquiring_core.upay.v1.PlatformType
	(ClientSource)(0),                           // 14: acquiring_core.upay.v1.ClientSource
	(PaymentSolution)(0),                        // 15: acquiring_core.upay.v1.PaymentSolution
	(ExternalBankAsset_TokenType)(0),            // 16: acquiring_core.upay.v1.ExternalBankAsset.TokenType
	(ExternalBankAsset_RoutingType)(0),          // 17: acquiring_core.upay.v1.ExternalBankAsset.RoutingType
	(*CreateOrderRequest)(nil),                  // 18: acquiring_core.upay.v1.CreateOrderRequest
	(*CreateLegacyOrderRequest)(nil),            // 19: acquiring_core.upay.v1.CreateLegacyOrderRequest
	(*CreateOrderResponse)(nil),                 // 20: acquiring_core.upay.v1.CreateOrderResponse
	(*ConfirmPayRequest)(nil),                   // 21: acquiring_core.upay.v1.ConfirmPayRequest
	(*ConfirmPayResponse)(nil),                  // 22: acquiring_core.upay.v1.ConfirmPayResponse
	(*CaptureRequest)(nil),                      // 23: acquiring_core.upay.v1.CaptureRequest
	(*CaptureResponse)(nil),                     // 24: acquiring_core.upay.v1.CaptureResponse
	(*CancelRequest)(nil),                       // 25: acquiring_core.upay.v1.CancelRequest
	(*CancelResponse)(nil),                      // 26: acquiring_core.upay.v1.CancelResponse
	(*GetOrderRequest)(nil),                     // 27: acquiring_core.upay.v1.GetOrderRequest
	(*GetOrderByTokenRequest)(nil),              // 28: acquiring_core.upay.v1.GetOrderByTokenRequest
	(*GetOrderByAppTransIdRequest)(nil),         // 29: acquiring_core.upay.v1.GetOrderByAppTransIdRequest
	(*ConsultUserAssetsRequest)(nil),            // 30: acquiring_core.upay.v1.ConsultUserAssetsRequest
	(*ConsultUserAssetsResponse)(nil),           // 31: acquiring_core.upay.v1.ConsultUserAssetsResponse
	(*ConsultSourceAsset)(nil),                  // 32: acquiring_core.upay.v1.ConsultSourceAsset
	(*Order)(nil),                               // 33: acquiring_core.upay.v1.Order
	(*Payment)(nil),                             // 34: acquiring_core.upay.v1.Payment
	(*SourceAssetList)(nil),                     // 35: acquiring_core.upay.v1.SourceAssetList
	(*TransactionList)(nil),                     // 36: acquiring_core.upay.v1.TransactionList
	(*PromotionList)(nil),                       // 37: acquiring_core.upay.v1.PromotionList
	(*FeeList)(nil),                             // 38: acquiring_core.upay.v1.FeeList
	(*Transaction)(nil),                         // 39: acquiring_core.upay.v1.Transaction
	(*Amount)(nil),                              // 40: acquiring_core.upay.v1.Amount
	(*QuoteCurrency)(nil),                       // 41: acquiring_core.upay.v1.QuoteCurrency
	(*Authorization)(nil),                       // 42: acquiring_core.upay.v1.Authorization
	(*ReasonStatus)(nil),                        // 43: acquiring_core.upay.v1.ReasonStatus
	(*ReasonDetail)(nil),                        // 44: acquiring_core.upay.v1.ReasonDetail
	(*OrderMeta)(nil),                           // 45: acquiring_core.upay.v1.OrderMeta
	(*SubMerchant)(nil),                         // 46: acquiring_core.upay.v1.SubMerchant
	(*MerchantPromotion)(nil),                   // 47: acquiring_core.upay.v1.MerchantPromotion
	(*MerchantPromotionDetails)(nil),            // 48: acquiring_core.upay.v1.MerchantPromotionDetails
	(*PaymentMeta)(nil),                         // 49: acquiring_core.upay.v1.PaymentMeta
	(*PaymentOption)(nil),                       // 50: acquiring_core.upay.v1.PaymentOption
	(*AppConfig)(nil),                           // 51: acquiring_core.upay.v1.AppConfig
	(*Payer)(nil),                               // 52: acquiring_core.upay.v1.Payer
	(*User)(nil),                                // 53: acquiring_core.upay.v1.User
	(*Merchant)(nil),                            // 54: acquiring_core.upay.v1.Merchant
	(*SourceAsset)(nil),                         // 55: acquiring_core.upay.v1.SourceAsset
	(*DestAsset)(nil),                           // 56: acquiring_core.upay.v1.DestAsset
	(*ThirdPartyAsset)(nil),                     // 57: acquiring_core.upay.v1.ThirdPartyAsset
	(*UserWalletAsset)(nil),                     // 58: acquiring_core.upay.v1.UserWalletAsset
	(*InternalMerchantAsset)(nil),               // 59: acquiring_core.upay.v1.InternalMerchantAsset
	(*UserBankAsset)(nil),                       // 60: acquiring_core.upay.v1.UserBankAsset
	(*ExternalBankAsset)(nil),                   // 61: acquiring_core.upay.v1.ExternalBankAsset
	(*MerchantAsset)(nil),                       // 62: acquiring_core.upay.v1.MerchantAsset
	(*UserFinancialAsset)(nil),                  // 63: acquiring_core.upay.v1.UserFinancialAsset
	(*UserFixedDepositAsset)(nil),               // 64: acquiring_core.upay.v1.UserFixedDepositAsset
	(*MerchantBankNapasAsset)(nil),              // 65: acquiring_core.upay.v1.MerchantBankNapasAsset
	(*PromotionItem)(nil),                       // 66: acquiring_core.upay.v1.PromotionItem
	(*Voucher)(nil),                             // 67: acquiring_core.upay.v1.Voucher
	(*DirectDiscount)(nil),                      // 68: acquiring_core.upay.v1.DirectDiscount
	(*ConfirmedFee)(nil),                        // 69: acquiring_core.upay.v1.ConfirmedFee
	(*FeeItem)(nil),                             // 70: acquiring_core.upay.v1.FeeItem
	(*Device)(nil),                              // 71: acquiring_core.upay.v1.Device
	(*DisplayObject)(nil),                       // 72: acquiring_core.upay.v1.DisplayObject
	(*TransactionMeta)(nil),                     // 73: acquiring_core.upay.v1.TransactionMeta
	nil,                                         // 74: acquiring_core.upay.v1.CreateOrderRequest.DeprecatedDataEntry
	nil,                                         // 75: acquiring_core.upay.v1.ConfirmPayRequest.DeprecatedDataEntry
	(*ConsultUserAssetsResponse_UserAsset)(nil), // 76: acquiring_core.upay.v1.ConsultUserAssetsResponse.UserAsset
	(*ConsultSourceAsset_ConsultUserWalletAsset)(nil),    // 77: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserWalletAsset
	(*ConsultSourceAsset_ConsultUserBankAsset)(nil),      // 78: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserBankAsset
	(*ConsultSourceAsset_ConsultUserFinancialAsset)(nil), // 79: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserFinancialAsset
	nil,                           // 80: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserBankAsset.DeprecatedDataEntry
	nil,                           // 81: acquiring_core.upay.v1.Order.DeprecatedDataEntry
	nil,                           // 82: acquiring_core.upay.v1.Payment.DeprecatedDataEntry
	nil,                           // 83: acquiring_core.upay.v1.ReasonStatus.MetadataEntry
	nil,                           // 84: acquiring_core.upay.v1.ReasonDetail.MetadataEntry
	nil,                           // 85: acquiring_core.upay.v1.OrderMeta.LabelsEntry
	nil,                           // 86: acquiring_core.upay.v1.PaymentMeta.LabelsEntry
	(*DisplayObject_Field)(nil),   // 87: acquiring_core.upay.v1.DisplayObject.Field
	(*timestamppb.Timestamp)(nil), // 88: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 89: google.protobuf.Duration
}
var file_external_services_acquiring_core_user_payment_proto_depIdxs = []int32{
	8,   // 0: acquiring_core.upay.v1.CreateOrderRequest.payment_type:type_name -> acquiring_core.upay.v1.PaymentType
	56,  // 1: acquiring_core.upay.v1.CreateOrderRequest.dest_asset:type_name -> acquiring_core.upay.v1.DestAsset
	45,  // 2: acquiring_core.upay.v1.CreateOrderRequest.metadata:type_name -> acquiring_core.upay.v1.OrderMeta
	4,   // 3: acquiring_core.upay.v1.CreateOrderRequest.capture_method:type_name -> acquiring_core.upay.v1.CaptureMethod
	40,  // 4: acquiring_core.upay.v1.CreateOrderRequest.payment_amount:type_name -> acquiring_core.upay.v1.Amount
	41,  // 5: acquiring_core.upay.v1.CreateOrderRequest.quote_currency:type_name -> acquiring_core.upay.v1.QuoteCurrency
	74,  // 6: acquiring_core.upay.v1.CreateOrderRequest.deprecated_data:type_name -> acquiring_core.upay.v1.CreateOrderRequest.DeprecatedDataEntry
	45,  // 7: acquiring_core.upay.v1.CreateLegacyOrderRequest.metadata:type_name -> acquiring_core.upay.v1.OrderMeta
	1,   // 8: acquiring_core.upay.v1.CreateOrderResponse.status:type_name -> acquiring_core.upay.v1.OrderStatus
	43,  // 9: acquiring_core.upay.v1.CreateOrderResponse.reason_status:type_name -> acquiring_core.upay.v1.ReasonStatus
	88,  // 10: acquiring_core.upay.v1.CreateOrderResponse.create_time:type_name -> google.protobuf.Timestamp
	52,  // 11: acquiring_core.upay.v1.ConfirmPayRequest.payer:type_name -> acquiring_core.upay.v1.Payer
	55,  // 12: acquiring_core.upay.v1.ConfirmPayRequest.source_assets:type_name -> acquiring_core.upay.v1.SourceAsset
	49,  // 13: acquiring_core.upay.v1.ConfirmPayRequest.metadata:type_name -> acquiring_core.upay.v1.PaymentMeta
	69,  // 14: acquiring_core.upay.v1.ConfirmPayRequest.confirmed_fee:type_name -> acquiring_core.upay.v1.ConfirmedFee
	66,  // 15: acquiring_core.upay.v1.ConfirmPayRequest.promotion_items:type_name -> acquiring_core.upay.v1.PromotionItem
	50,  // 16: acquiring_core.upay.v1.ConfirmPayRequest.payment_option:type_name -> acquiring_core.upay.v1.PaymentOption
	75,  // 17: acquiring_core.upay.v1.ConfirmPayRequest.deprecated_data:type_name -> acquiring_core.upay.v1.ConfirmPayRequest.DeprecatedDataEntry
	1,   // 18: acquiring_core.upay.v1.ConfirmPayResponse.status:type_name -> acquiring_core.upay.v1.OrderStatus
	43,  // 19: acquiring_core.upay.v1.ConfirmPayResponse.reason_status:type_name -> acquiring_core.upay.v1.ReasonStatus
	88,  // 20: acquiring_core.upay.v1.ConfirmPayResponse.pay_time:type_name -> google.protobuf.Timestamp
	8,   // 21: acquiring_core.upay.v1.ConsultUserAssetsRequest.payment_type:type_name -> acquiring_core.upay.v1.PaymentType
	71,  // 22: acquiring_core.upay.v1.ConsultUserAssetsRequest.device:type_name -> acquiring_core.upay.v1.Device
	76,  // 23: acquiring_core.upay.v1.ConsultUserAssetsResponse.assets:type_name -> acquiring_core.upay.v1.ConsultUserAssetsResponse.UserAsset
	77,  // 24: acquiring_core.upay.v1.ConsultSourceAsset.user_wallet:type_name -> acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserWalletAsset
	78,  // 25: acquiring_core.upay.v1.ConsultSourceAsset.user_bank:type_name -> acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserBankAsset
	79,  // 26: acquiring_core.upay.v1.ConsultSourceAsset.user_finance:type_name -> acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserFinancialAsset
	8,   // 27: acquiring_core.upay.v1.Order.payment_type:type_name -> acquiring_core.upay.v1.PaymentType
	56,  // 28: acquiring_core.upay.v1.Order.dest_asset:type_name -> acquiring_core.upay.v1.DestAsset
	1,   // 29: acquiring_core.upay.v1.Order.status:type_name -> acquiring_core.upay.v1.OrderStatus
	43,  // 30: acquiring_core.upay.v1.Order.reason_status:type_name -> acquiring_core.upay.v1.ReasonStatus
	88,  // 31: acquiring_core.upay.v1.Order.create_time:type_name -> google.protobuf.Timestamp
	34,  // 32: acquiring_core.upay.v1.Order.payment:type_name -> acquiring_core.upay.v1.Payment
	45,  // 33: acquiring_core.upay.v1.Order.metadata:type_name -> acquiring_core.upay.v1.OrderMeta
	88,  // 34: acquiring_core.upay.v1.Order.expire_time:type_name -> google.protobuf.Timestamp
	4,   // 35: acquiring_core.upay.v1.Order.capture_method:type_name -> acquiring_core.upay.v1.CaptureMethod
	40,  // 36: acquiring_core.upay.v1.Order.payment_amount:type_name -> acquiring_core.upay.v1.Amount
	41,  // 37: acquiring_core.upay.v1.Order.quote_currency:type_name -> acquiring_core.upay.v1.QuoteCurrency
	81,  // 38: acquiring_core.upay.v1.Order.deprecated_data:type_name -> acquiring_core.upay.v1.Order.DeprecatedDataEntry
	52,  // 39: acquiring_core.upay.v1.Payment.payer:type_name -> acquiring_core.upay.v1.Payer
	55,  // 40: acquiring_core.upay.v1.Payment.source_assets:type_name -> acquiring_core.upay.v1.SourceAsset
	2,   // 41: acquiring_core.upay.v1.Payment.status:type_name -> acquiring_core.upay.v1.PaymentStatus
	43,  // 42: acquiring_core.upay.v1.Payment.reason_status:type_name -> acquiring_core.upay.v1.ReasonStatus
	39,  // 43: acquiring_core.upay.v1.Payment.transactions:type_name -> acquiring_core.upay.v1.Transaction
	88,  // 44: acquiring_core.upay.v1.Payment.pay_time:type_name -> google.protobuf.Timestamp
	49,  // 45: acquiring_core.upay.v1.Payment.metadata:type_name -> acquiring_core.upay.v1.PaymentMeta
	66,  // 46: acquiring_core.upay.v1.Payment.promotion_items:type_name -> acquiring_core.upay.v1.PromotionItem
	70,  // 47: acquiring_core.upay.v1.Payment.fee_items:type_name -> acquiring_core.upay.v1.FeeItem
	35,  // 48: acquiring_core.upay.v1.Payment.source_asset_list:type_name -> acquiring_core.upay.v1.SourceAssetList
	36,  // 49: acquiring_core.upay.v1.Payment.transaction_list:type_name -> acquiring_core.upay.v1.TransactionList
	37,  // 50: acquiring_core.upay.v1.Payment.promotion_list:type_name -> acquiring_core.upay.v1.PromotionList
	38,  // 51: acquiring_core.upay.v1.Payment.fee_list:type_name -> acquiring_core.upay.v1.FeeList
	42,  // 52: acquiring_core.upay.v1.Payment.authorization:type_name -> acquiring_core.upay.v1.Authorization
	50,  // 53: acquiring_core.upay.v1.Payment.payment_option:type_name -> acquiring_core.upay.v1.PaymentOption
	82,  // 54: acquiring_core.upay.v1.Payment.deprecated_data:type_name -> acquiring_core.upay.v1.Payment.DeprecatedDataEntry
	55,  // 55: acquiring_core.upay.v1.SourceAssetList.source_assets:type_name -> acquiring_core.upay.v1.SourceAsset
	39,  // 56: acquiring_core.upay.v1.TransactionList.transactions:type_name -> acquiring_core.upay.v1.Transaction
	66,  // 57: acquiring_core.upay.v1.PromotionList.promotion_items:type_name -> acquiring_core.upay.v1.PromotionItem
	70,  // 58: acquiring_core.upay.v1.FeeList.fee_items:type_name -> acquiring_core.upay.v1.FeeItem
	3,   // 59: acquiring_core.upay.v1.Transaction.trans_status:type_name -> acquiring_core.upay.v1.TransStatus
	43,  // 60: acquiring_core.upay.v1.Transaction.reason_status:type_name -> acquiring_core.upay.v1.ReasonStatus
	55,  // 61: acquiring_core.upay.v1.Transaction.source_assets:type_name -> acquiring_core.upay.v1.SourceAsset
	56,  // 62: acquiring_core.upay.v1.Transaction.dest_asset:type_name -> acquiring_core.upay.v1.DestAsset
	66,  // 63: acquiring_core.upay.v1.Transaction.promotion_items:type_name -> acquiring_core.upay.v1.PromotionItem
	70,  // 64: acquiring_core.upay.v1.Transaction.fee_items:type_name -> acquiring_core.upay.v1.FeeItem
	73,  // 65: acquiring_core.upay.v1.Transaction.metadata:type_name -> acquiring_core.upay.v1.TransactionMeta
	83,  // 66: acquiring_core.upay.v1.ReasonStatus.metadata:type_name -> acquiring_core.upay.v1.ReasonStatus.MetadataEntry
	44,  // 67: acquiring_core.upay.v1.ReasonStatus.details:type_name -> acquiring_core.upay.v1.ReasonDetail
	84,  // 68: acquiring_core.upay.v1.ReasonDetail.metadata:type_name -> acquiring_core.upay.v1.ReasonDetail.MetadataEntry
	85,  // 69: acquiring_core.upay.v1.OrderMeta.labels:type_name -> acquiring_core.upay.v1.OrderMeta.LabelsEntry
	51,  // 70: acquiring_core.upay.v1.OrderMeta.app_config:type_name -> acquiring_core.upay.v1.AppConfig
	72,  // 71: acquiring_core.upay.v1.OrderMeta.display_object:type_name -> acquiring_core.upay.v1.DisplayObject
	46,  // 72: acquiring_core.upay.v1.OrderMeta.sub_merchant:type_name -> acquiring_core.upay.v1.SubMerchant
	47,  // 73: acquiring_core.upay.v1.OrderMeta.merchant_promo:type_name -> acquiring_core.upay.v1.MerchantPromotion
	48,  // 74: acquiring_core.upay.v1.MerchantPromotion.promo_details:type_name -> acquiring_core.upay.v1.MerchantPromotionDetails
	40,  // 75: acquiring_core.upay.v1.MerchantPromotionDetails.savingsAmount:type_name -> acquiring_core.upay.v1.Amount
	86,  // 76: acquiring_core.upay.v1.PaymentMeta.labels:type_name -> acquiring_core.upay.v1.PaymentMeta.LabelsEntry
	9,   // 77: acquiring_core.upay.v1.PaymentMeta.payment_system:type_name -> acquiring_core.upay.v1.PaymentSystem
	71,  // 78: acquiring_core.upay.v1.PaymentMeta.device:type_name -> acquiring_core.upay.v1.Device
	14,  // 79: acquiring_core.upay.v1.PaymentMeta.client_source:type_name -> acquiring_core.upay.v1.ClientSource
	15,  // 80: acquiring_core.upay.v1.PaymentMeta.payment_solution:type_name -> acquiring_core.upay.v1.PaymentSolution
	89,  // 81: acquiring_core.upay.v1.AppConfig.ttl:type_name -> google.protobuf.Duration
	53,  // 82: acquiring_core.upay.v1.Payer.user:type_name -> acquiring_core.upay.v1.User
	54,  // 83: acquiring_core.upay.v1.Payer.merchant:type_name -> acquiring_core.upay.v1.Merchant
	5,   // 84: acquiring_core.upay.v1.SourceAsset.payment_method:type_name -> acquiring_core.upay.v1.PaymentMethod
	58,  // 85: acquiring_core.upay.v1.SourceAsset.user_wallet:type_name -> acquiring_core.upay.v1.UserWalletAsset
	60,  // 86: acquiring_core.upay.v1.SourceAsset.user_bank:type_name -> acquiring_core.upay.v1.UserBankAsset
	63,  // 87: acquiring_core.upay.v1.SourceAsset.user_finance:type_name -> acquiring_core.upay.v1.UserFinancialAsset
	64,  // 88: acquiring_core.upay.v1.SourceAsset.user_fixed_deposit:type_name -> acquiring_core.upay.v1.UserFixedDepositAsset
	57,  // 89: acquiring_core.upay.v1.SourceAsset.third_party:type_name -> acquiring_core.upay.v1.ThirdPartyAsset
	59,  // 90: acquiring_core.upay.v1.SourceAsset.internal_merchant:type_name -> acquiring_core.upay.v1.InternalMerchantAsset
	6,   // 91: acquiring_core.upay.v1.DestAsset.dest_asset_type:type_name -> acquiring_core.upay.v1.DestAssetType
	62,  // 92: acquiring_core.upay.v1.DestAsset.merchant:type_name -> acquiring_core.upay.v1.MerchantAsset
	58,  // 93: acquiring_core.upay.v1.DestAsset.user_wallet:type_name -> acquiring_core.upay.v1.UserWalletAsset
	64,  // 94: acquiring_core.upay.v1.DestAsset.user_fixed_deposit:type_name -> acquiring_core.upay.v1.UserFixedDepositAsset
	65,  // 95: acquiring_core.upay.v1.DestAsset.merchant_bank_napas:type_name -> acquiring_core.upay.v1.MerchantBankNapasAsset
	61,  // 96: acquiring_core.upay.v1.DestAsset.external_bank:type_name -> acquiring_core.upay.v1.ExternalBankAsset
	59,  // 97: acquiring_core.upay.v1.DestAsset.internal_merchant:type_name -> acquiring_core.upay.v1.InternalMerchantAsset
	7,   // 98: acquiring_core.upay.v1.UserWalletAsset.type:type_name -> acquiring_core.upay.v1.WalletType
	17,  // 99: acquiring_core.upay.v1.ExternalBankAsset.routing_type:type_name -> acquiring_core.upay.v1.ExternalBankAsset.RoutingType
	16,  // 100: acquiring_core.upay.v1.ExternalBankAsset.token_type:type_name -> acquiring_core.upay.v1.ExternalBankAsset.TokenType
	10,  // 101: acquiring_core.upay.v1.PromotionItem.type:type_name -> acquiring_core.upay.v1.PromotionType
	67,  // 102: acquiring_core.upay.v1.PromotionItem.voucher:type_name -> acquiring_core.upay.v1.Voucher
	68,  // 103: acquiring_core.upay.v1.PromotionItem.direct_discount:type_name -> acquiring_core.upay.v1.DirectDiscount
	88,  // 104: acquiring_core.upay.v1.Voucher.use_time:type_name -> google.protobuf.Timestamp
	11,  // 105: acquiring_core.upay.v1.FeeItem.type:type_name -> acquiring_core.upay.v1.FeeType
	12,  // 106: acquiring_core.upay.v1.Device.os_type:type_name -> acquiring_core.upay.v1.OsType
	13,  // 107: acquiring_core.upay.v1.Device.platform:type_name -> acquiring_core.upay.v1.PlatformType
	87,  // 108: acquiring_core.upay.v1.DisplayObject.fields:type_name -> acquiring_core.upay.v1.DisplayObject.Field
	5,   // 109: acquiring_core.upay.v1.ConsultUserAssetsResponse.UserAsset.payment_method:type_name -> acquiring_core.upay.v1.PaymentMethod
	32,  // 110: acquiring_core.upay.v1.ConsultUserAssetsResponse.UserAsset.source_asset:type_name -> acquiring_core.upay.v1.ConsultSourceAsset
	0,   // 111: acquiring_core.upay.v1.ConsultUserAssetsResponse.UserAsset.status:type_name -> acquiring_core.upay.v1.AssetStatus
	43,  // 112: acquiring_core.upay.v1.ConsultUserAssetsResponse.UserAsset.reason_status:type_name -> acquiring_core.upay.v1.ReasonStatus
	58,  // 113: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserWalletAsset.wallet:type_name -> acquiring_core.upay.v1.UserWalletAsset
	60,  // 114: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserBankAsset.bank:type_name -> acquiring_core.upay.v1.UserBankAsset
	80,  // 115: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserBankAsset.deprecated_data:type_name -> acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserBankAsset.DeprecatedDataEntry
	63,  // 116: acquiring_core.upay.v1.ConsultSourceAsset.ConsultUserFinancialAsset.finance:type_name -> acquiring_core.upay.v1.UserFinancialAsset
	18,  // 117: acquiring_core.upay.v1.UserPayment.CreateOrder:input_type -> acquiring_core.upay.v1.CreateOrderRequest
	19,  // 118: acquiring_core.upay.v1.UserPayment.CreateLegacyOrder:input_type -> acquiring_core.upay.v1.CreateLegacyOrderRequest
	21,  // 119: acquiring_core.upay.v1.UserPayment.ConfirmPay:input_type -> acquiring_core.upay.v1.ConfirmPayRequest
	23,  // 120: acquiring_core.upay.v1.UserPayment.Capture:input_type -> acquiring_core.upay.v1.CaptureRequest
	25,  // 121: acquiring_core.upay.v1.UserPayment.Cancel:input_type -> acquiring_core.upay.v1.CancelRequest
	27,  // 122: acquiring_core.upay.v1.UserPayment.GetOrder:input_type -> acquiring_core.upay.v1.GetOrderRequest
	28,  // 123: acquiring_core.upay.v1.UserPayment.GetOrderByToken:input_type -> acquiring_core.upay.v1.GetOrderByTokenRequest
	29,  // 124: acquiring_core.upay.v1.UserPayment.GetOrderByAppTransId:input_type -> acquiring_core.upay.v1.GetOrderByAppTransIdRequest
	30,  // 125: acquiring_core.upay.v1.UserPayment.ConsultUserAssets:input_type -> acquiring_core.upay.v1.ConsultUserAssetsRequest
	20,  // 126: acquiring_core.upay.v1.UserPayment.CreateOrder:output_type -> acquiring_core.upay.v1.CreateOrderResponse
	20,  // 127: acquiring_core.upay.v1.UserPayment.CreateLegacyOrder:output_type -> acquiring_core.upay.v1.CreateOrderResponse
	22,  // 128: acquiring_core.upay.v1.UserPayment.ConfirmPay:output_type -> acquiring_core.upay.v1.ConfirmPayResponse
	24,  // 129: acquiring_core.upay.v1.UserPayment.Capture:output_type -> acquiring_core.upay.v1.CaptureResponse
	26,  // 130: acquiring_core.upay.v1.UserPayment.Cancel:output_type -> acquiring_core.upay.v1.CancelResponse
	33,  // 131: acquiring_core.upay.v1.UserPayment.GetOrder:output_type -> acquiring_core.upay.v1.Order
	33,  // 132: acquiring_core.upay.v1.UserPayment.GetOrderByToken:output_type -> acquiring_core.upay.v1.Order
	33,  // 133: acquiring_core.upay.v1.UserPayment.GetOrderByAppTransId:output_type -> acquiring_core.upay.v1.Order
	31,  // 134: acquiring_core.upay.v1.UserPayment.ConsultUserAssets:output_type -> acquiring_core.upay.v1.ConsultUserAssetsResponse
	126, // [126:135] is the sub-list for method output_type
	117, // [117:126] is the sub-list for method input_type
	117, // [117:117] is the sub-list for extension type_name
	117, // [117:117] is the sub-list for extension extendee
	0,   // [0:117] is the sub-list for field type_name
}

func init() { file_external_services_acquiring_core_user_payment_proto_init() }
func file_external_services_acquiring_core_user_payment_proto_init() {
	if File_external_services_acquiring_core_user_payment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_acquiring_core_user_payment_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CreateLegacyOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ConfirmPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ConfirmPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CaptureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CaptureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*CancelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CancelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*GetOrderByTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetOrderByAppTransIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultUserAssetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultUserAssetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultSourceAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*Order); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Payment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*SourceAssetList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*TransactionList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*PromotionList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*FeeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*Transaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*Amount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*QuoteCurrency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*Authorization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*ReasonStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*ReasonDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*OrderMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*SubMerchant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*MerchantPromotion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*MerchantPromotionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*PaymentMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*PaymentOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*AppConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*Payer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*Merchant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*SourceAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*DestAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*ThirdPartyAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*UserWalletAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*InternalMerchantAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*UserBankAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*ExternalBankAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*MerchantAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*UserFinancialAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*UserFixedDepositAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*MerchantBankNapasAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*PromotionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*Voucher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*DirectDiscount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*ConfirmedFee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*FeeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*DisplayObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*TransactionMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultUserAssetsResponse_UserAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultSourceAsset_ConsultUserWalletAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultSourceAsset_ConsultUserBankAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*ConsultSourceAsset_ConsultUserFinancialAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_acquiring_core_user_payment_proto_msgTypes[69].Exporter = func(v any, i int) any {
			switch v := v.(*DisplayObject_Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_external_services_acquiring_core_user_payment_proto_msgTypes[14].OneofWrappers = []any{
		(*ConsultSourceAsset_UserWallet)(nil),
		(*ConsultSourceAsset_UserBank)(nil),
		(*ConsultSourceAsset_UserFinance)(nil),
	}
	file_external_services_acquiring_core_user_payment_proto_msgTypes[34].OneofWrappers = []any{
		(*Payer_User)(nil),
		(*Payer_Merchant)(nil),
	}
	file_external_services_acquiring_core_user_payment_proto_msgTypes[37].OneofWrappers = []any{
		(*SourceAsset_UserWallet)(nil),
		(*SourceAsset_UserBank)(nil),
		(*SourceAsset_UserFinance)(nil),
		(*SourceAsset_UserFixedDeposit)(nil),
		(*SourceAsset_ThirdParty)(nil),
		(*SourceAsset_InternalMerchant)(nil),
	}
	file_external_services_acquiring_core_user_payment_proto_msgTypes[38].OneofWrappers = []any{
		(*DestAsset_Merchant)(nil),
		(*DestAsset_UserWallet)(nil),
		(*DestAsset_UserFixedDeposit)(nil),
		(*DestAsset_MerchantBankNapas)(nil),
		(*DestAsset_ExternalBank)(nil),
		(*DestAsset_InternalMerchant)(nil),
	}
	file_external_services_acquiring_core_user_payment_proto_msgTypes[48].OneofWrappers = []any{
		(*PromotionItem_Voucher)(nil),
		(*PromotionItem_DirectDiscount)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_acquiring_core_user_payment_proto_rawDesc,
			NumEnums:      18,
			NumMessages:   70,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_acquiring_core_user_payment_proto_goTypes,
		DependencyIndexes: file_external_services_acquiring_core_user_payment_proto_depIdxs,
		EnumInfos:         file_external_services_acquiring_core_user_payment_proto_enumTypes,
		MessageInfos:      file_external_services_acquiring_core_user_payment_proto_msgTypes,
	}.Build()
	File_external_services_acquiring_core_user_payment_proto = out.File
	file_external_services_acquiring_core_user_payment_proto_rawDesc = nil
	file_external_services_acquiring_core_user_payment_proto_goTypes = nil
	file_external_services_acquiring_core_user_payment_proto_depIdxs = nil
}
