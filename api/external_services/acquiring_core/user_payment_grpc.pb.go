// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/acquiring_core/user_payment.proto

package acquiring_core

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserPayment_CreateOrder_FullMethodName          = "/acquiring_core.upay.v1.UserPayment/CreateOrder"
	UserPayment_CreateLegacyOrder_FullMethodName    = "/acquiring_core.upay.v1.UserPayment/CreateLegacyOrder"
	UserPayment_ConfirmPay_FullMethodName           = "/acquiring_core.upay.v1.UserPayment/ConfirmPay"
	UserPayment_Capture_FullMethodName              = "/acquiring_core.upay.v1.UserPayment/Capture"
	UserPayment_Cancel_FullMethodName               = "/acquiring_core.upay.v1.UserPayment/Cancel"
	UserPayment_GetOrder_FullMethodName             = "/acquiring_core.upay.v1.UserPayment/GetOrder"
	UserPayment_GetOrderByToken_FullMethodName      = "/acquiring_core.upay.v1.UserPayment/GetOrderByToken"
	UserPayment_GetOrderByAppTransId_FullMethodName = "/acquiring_core.upay.v1.UserPayment/GetOrderByAppTransId"
	UserPayment_ConsultUserAssets_FullMethodName    = "/acquiring_core.upay.v1.UserPayment/ConsultUserAssets"
)

// UserPaymentClient is the client API for UserPayment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserPaymentClient interface {
	// `CreateOrder` creates a new order for collecting payment from the `Payer`.
	// If the order is created successfully, the `CreateOrderResponse` be returned with `OrderStatus` is `CREATED`.
	// Otherwise, a gRPC error will be returned. Possible errors are:
	// * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
	// * `ALREADY_EXISTS`: The order with the same `app_id` and `app_trans_id` already exists.
	// * `INTERNAL`: System error.
	// * `UNKNOWN`: Any other error occurred during the process, this error should never be returned unless there is a bug in the system.
	CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*CreateOrderResponse, error)
	// `CreateLegacyOrder` initializes a new payment order for the `Payer`.
	// In some older flows, the Product opens Cashier with order details before the order is created on Acquiring Core.
	// Since we can't require the Product to create the order first, this function allows Cashier to create it on Acquiring Core before confirming the payment.
	// Initially, this applies to app game (15), with gradual enablement planned for others such as internal app (1), etc.
	// We need observe order details data before implementing the appropriate flow for each app.
	// If the app's flow cannot be determined, the function will return a PERMISSION_DENIED error.
	CreateLegacyOrder(ctx context.Context, in *CreateLegacyOrderRequest, opts ...grpc.CallOption) (*CreateOrderResponse, error)
	// `ConfirmPay` confirms the payment of an newly `CREATED` order, or an order `FAILED` before.
	// If the payment is accepted, the `ConfirmPayResponse` will be returned with `OrderStatus` is `PROCESSING`,
	// and the order will be processed asynchronously.
	// Otherwise, a gRPC error will be returned. Possible errors are:
	//   - `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
	//   - `NOT_FOUND`: The requested order is not found.
	//   - `ALREADY_EXISTS`: The order is already confirmed, now it's `PROCESSING` or completed with status different from `FAILED` (e.g. `SUCCESS`, `PENDING`).
	//   - `PERMISSION_DENIED`: The order is not allowed to be confirmed. If the reason is determined, a gRPC ErrorInfo will be returned.
	//     Possible reasons for Domain `ACQUIRING` are:
	//   - `ORDER_EXPIRED`: The order is expired. The order can be confirmed only within 15 minutes after it's created.
	//   - `INTERNAL`: System error, and the order not be processed.
	//   - `UNKNOWN`: Any other error occurred during the process, can't not sure the order is processed or not, need to manually check the order status.
	ConfirmPay(ctx context.Context, in *ConfirmPayRequest, opts ...grpc.CallOption) (*ConfirmPayResponse, error)
	Capture(ctx context.Context, in *CaptureRequest, opts ...grpc.CallOption) (*CaptureResponse, error)
	Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error)
	// `GetOrder` get the Order with the given order_no.
	// If the order is found, the `Order` will be returned with the order information.
	// Otherwise, a gRPC error will be returned. Possible errors are:
	// * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
	// * `NOT_FOUND`: The requested order is not found.
	// * `INTERNAL`: System error.
	// * `UNKNOWN`: Any other error occurred during the process, this error should never be returned unless there is a bug in the system.
	GetOrder(ctx context.Context, in *GetOrderRequest, opts ...grpc.CallOption) (*Order, error)
	// `GetOrderByToken` get the Order with the given order_token.
	// The response is the same as `GetOrder`.
	GetOrderByToken(ctx context.Context, in *GetOrderByTokenRequest, opts ...grpc.CallOption) (*Order, error)
	// `GetOrderByAppTransId` get the Order with the given app_id and app_trans_id.
	// The response is the same as `GetOrder`.
	GetOrderByAppTransId(ctx context.Context, in *GetOrderByAppTransIdRequest, opts ...grpc.CallOption) (*Order, error)
	ConsultUserAssets(ctx context.Context, in *ConsultUserAssetsRequest, opts ...grpc.CallOption) (*ConsultUserAssetsResponse, error)
}

type userPaymentClient struct {
	cc grpc.ClientConnInterface
}

func NewUserPaymentClient(cc grpc.ClientConnInterface) UserPaymentClient {
	return &userPaymentClient{cc}
}

func (c *userPaymentClient) CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*CreateOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrderResponse)
	err := c.cc.Invoke(ctx, UserPayment_CreateOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) CreateLegacyOrder(ctx context.Context, in *CreateLegacyOrderRequest, opts ...grpc.CallOption) (*CreateOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrderResponse)
	err := c.cc.Invoke(ctx, UserPayment_CreateLegacyOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) ConfirmPay(ctx context.Context, in *ConfirmPayRequest, opts ...grpc.CallOption) (*ConfirmPayResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfirmPayResponse)
	err := c.cc.Invoke(ctx, UserPayment_ConfirmPay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) Capture(ctx context.Context, in *CaptureRequest, opts ...grpc.CallOption) (*CaptureResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CaptureResponse)
	err := c.cc.Invoke(ctx, UserPayment_Capture_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, UserPayment_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) GetOrder(ctx context.Context, in *GetOrderRequest, opts ...grpc.CallOption) (*Order, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Order)
	err := c.cc.Invoke(ctx, UserPayment_GetOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) GetOrderByToken(ctx context.Context, in *GetOrderByTokenRequest, opts ...grpc.CallOption) (*Order, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Order)
	err := c.cc.Invoke(ctx, UserPayment_GetOrderByToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) GetOrderByAppTransId(ctx context.Context, in *GetOrderByAppTransIdRequest, opts ...grpc.CallOption) (*Order, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Order)
	err := c.cc.Invoke(ctx, UserPayment_GetOrderByAppTransId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPaymentClient) ConsultUserAssets(ctx context.Context, in *ConsultUserAssetsRequest, opts ...grpc.CallOption) (*ConsultUserAssetsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConsultUserAssetsResponse)
	err := c.cc.Invoke(ctx, UserPayment_ConsultUserAssets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserPaymentServer is the server API for UserPayment service.
// All implementations must embed UnimplementedUserPaymentServer
// for forward compatibility.
type UserPaymentServer interface {
	// `CreateOrder` creates a new order for collecting payment from the `Payer`.
	// If the order is created successfully, the `CreateOrderResponse` be returned with `OrderStatus` is `CREATED`.
	// Otherwise, a gRPC error will be returned. Possible errors are:
	// * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
	// * `ALREADY_EXISTS`: The order with the same `app_id` and `app_trans_id` already exists.
	// * `INTERNAL`: System error.
	// * `UNKNOWN`: Any other error occurred during the process, this error should never be returned unless there is a bug in the system.
	CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderResponse, error)
	// `CreateLegacyOrder` initializes a new payment order for the `Payer`.
	// In some older flows, the Product opens Cashier with order details before the order is created on Acquiring Core.
	// Since we can't require the Product to create the order first, this function allows Cashier to create it on Acquiring Core before confirming the payment.
	// Initially, this applies to app game (15), with gradual enablement planned for others such as internal app (1), etc.
	// We need observe order details data before implementing the appropriate flow for each app.
	// If the app's flow cannot be determined, the function will return a PERMISSION_DENIED error.
	CreateLegacyOrder(context.Context, *CreateLegacyOrderRequest) (*CreateOrderResponse, error)
	// `ConfirmPay` confirms the payment of an newly `CREATED` order, or an order `FAILED` before.
	// If the payment is accepted, the `ConfirmPayResponse` will be returned with `OrderStatus` is `PROCESSING`,
	// and the order will be processed asynchronously.
	// Otherwise, a gRPC error will be returned. Possible errors are:
	//   - `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
	//   - `NOT_FOUND`: The requested order is not found.
	//   - `ALREADY_EXISTS`: The order is already confirmed, now it's `PROCESSING` or completed with status different from `FAILED` (e.g. `SUCCESS`, `PENDING`).
	//   - `PERMISSION_DENIED`: The order is not allowed to be confirmed. If the reason is determined, a gRPC ErrorInfo will be returned.
	//     Possible reasons for Domain `ACQUIRING` are:
	//   - `ORDER_EXPIRED`: The order is expired. The order can be confirmed only within 15 minutes after it's created.
	//   - `INTERNAL`: System error, and the order not be processed.
	//   - `UNKNOWN`: Any other error occurred during the process, can't not sure the order is processed or not, need to manually check the order status.
	ConfirmPay(context.Context, *ConfirmPayRequest) (*ConfirmPayResponse, error)
	Capture(context.Context, *CaptureRequest) (*CaptureResponse, error)
	Cancel(context.Context, *CancelRequest) (*CancelResponse, error)
	// `GetOrder` get the Order with the given order_no.
	// If the order is found, the `Order` will be returned with the order information.
	// Otherwise, a gRPC error will be returned. Possible errors are:
	// * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
	// * `NOT_FOUND`: The requested order is not found.
	// * `INTERNAL`: System error.
	// * `UNKNOWN`: Any other error occurred during the process, this error should never be returned unless there is a bug in the system.
	GetOrder(context.Context, *GetOrderRequest) (*Order, error)
	// `GetOrderByToken` get the Order with the given order_token.
	// The response is the same as `GetOrder`.
	GetOrderByToken(context.Context, *GetOrderByTokenRequest) (*Order, error)
	// `GetOrderByAppTransId` get the Order with the given app_id and app_trans_id.
	// The response is the same as `GetOrder`.
	GetOrderByAppTransId(context.Context, *GetOrderByAppTransIdRequest) (*Order, error)
	ConsultUserAssets(context.Context, *ConsultUserAssetsRequest) (*ConsultUserAssetsResponse, error)
	mustEmbedUnimplementedUserPaymentServer()
}

// UnimplementedUserPaymentServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserPaymentServer struct{}

func (UnimplementedUserPaymentServer) CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrder not implemented")
}
func (UnimplementedUserPaymentServer) CreateLegacyOrder(context.Context, *CreateLegacyOrderRequest) (*CreateOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLegacyOrder not implemented")
}
func (UnimplementedUserPaymentServer) ConfirmPay(context.Context, *ConfirmPayRequest) (*ConfirmPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmPay not implemented")
}
func (UnimplementedUserPaymentServer) Capture(context.Context, *CaptureRequest) (*CaptureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Capture not implemented")
}
func (UnimplementedUserPaymentServer) Cancel(context.Context, *CancelRequest) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedUserPaymentServer) GetOrder(context.Context, *GetOrderRequest) (*Order, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrder not implemented")
}
func (UnimplementedUserPaymentServer) GetOrderByToken(context.Context, *GetOrderByTokenRequest) (*Order, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderByToken not implemented")
}
func (UnimplementedUserPaymentServer) GetOrderByAppTransId(context.Context, *GetOrderByAppTransIdRequest) (*Order, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderByAppTransId not implemented")
}
func (UnimplementedUserPaymentServer) ConsultUserAssets(context.Context, *ConsultUserAssetsRequest) (*ConsultUserAssetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsultUserAssets not implemented")
}
func (UnimplementedUserPaymentServer) mustEmbedUnimplementedUserPaymentServer() {}
func (UnimplementedUserPaymentServer) testEmbeddedByValue()                     {}

// UnsafeUserPaymentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserPaymentServer will
// result in compilation errors.
type UnsafeUserPaymentServer interface {
	mustEmbedUnimplementedUserPaymentServer()
}

func RegisterUserPaymentServer(s grpc.ServiceRegistrar, srv UserPaymentServer) {
	// If the following call pancis, it indicates UnimplementedUserPaymentServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserPayment_ServiceDesc, srv)
}

func _UserPayment_CreateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).CreateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_CreateOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).CreateOrder(ctx, req.(*CreateOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_CreateLegacyOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLegacyOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).CreateLegacyOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_CreateLegacyOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).CreateLegacyOrder(ctx, req.(*CreateLegacyOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_ConfirmPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).ConfirmPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_ConfirmPay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).ConfirmPay(ctx, req.(*ConfirmPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_Capture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CaptureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).Capture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_Capture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).Capture(ctx, req.(*CaptureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).Cancel(ctx, req.(*CancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_GetOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).GetOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_GetOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).GetOrder(ctx, req.(*GetOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_GetOrderByToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderByTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).GetOrderByToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_GetOrderByToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).GetOrderByToken(ctx, req.(*GetOrderByTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_GetOrderByAppTransId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderByAppTransIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).GetOrderByAppTransId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_GetOrderByAppTransId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).GetOrderByAppTransId(ctx, req.(*GetOrderByAppTransIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPayment_ConsultUserAssets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsultUserAssetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPaymentServer).ConsultUserAssets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPayment_ConsultUserAssets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPaymentServer).ConsultUserAssets(ctx, req.(*ConsultUserAssetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserPayment_ServiceDesc is the grpc.ServiceDesc for UserPayment service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserPayment_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "acquiring_core.upay.v1.UserPayment",
	HandlerType: (*UserPaymentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrder",
			Handler:    _UserPayment_CreateOrder_Handler,
		},
		{
			MethodName: "CreateLegacyOrder",
			Handler:    _UserPayment_CreateLegacyOrder_Handler,
		},
		{
			MethodName: "ConfirmPay",
			Handler:    _UserPayment_ConfirmPay_Handler,
		},
		{
			MethodName: "Capture",
			Handler:    _UserPayment_Capture_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _UserPayment_Cancel_Handler,
		},
		{
			MethodName: "GetOrder",
			Handler:    _UserPayment_GetOrder_Handler,
		},
		{
			MethodName: "GetOrderByToken",
			Handler:    _UserPayment_GetOrderByToken_Handler,
		},
		{
			MethodName: "GetOrderByAppTransId",
			Handler:    _UserPayment_GetOrderByAppTransId_Handler,
		},
		{
			MethodName: "ConsultUserAssets",
			Handler:    _UserPayment_ConsultUserAssets_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/acquiring_core/user_payment.proto",
}
