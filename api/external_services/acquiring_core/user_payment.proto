syntax = "proto3";

package acquiring_core.upay.v1;
option go_package = "installment-service/api/acquiring_core;acquiring_core";

import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";

service UserPayment {
  // `CreateOrder` creates a new order for collecting payment from the `Payer`.
  // If the order is created successfully, the `CreateOrderResponse` be returned with `OrderStatus` is `CREATED`.
  // Otherwise, a gRPC error will be returned. Possible errors are:
  // * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
  // * `ALREADY_EXISTS`: The order with the same `app_id` and `app_trans_id` already exists.
  // * `INTERNAL`: System error.
  // * `UNKNOWN`: Any other error occurred during the process, this error should never be returned unless there is a bug in the system.
  rpc CreateOrder (CreateOrderRequest) returns (CreateOrderResponse);

  // `CreateLegacyOrder` initializes a new payment order for the `Payer`.
  // In some older flows, the Product opens Cashier with order details before the order is created on Acquiring Core.
  // Since we can't require the Product to create the order first, this function allows Cashier to create it on Acquiring Core before confirming the payment.
  // Initially, this applies to app game (15), with gradual enablement planned for others such as internal app (1), etc.
  // We need observe order details data before implementing the appropriate flow for each app.
  // If the app's flow cannot be determined, the function will return a PERMISSION_DENIED error.
  rpc CreateLegacyOrder (CreateLegacyOrderRequest) returns (CreateOrderResponse);

  // `ConfirmPay` confirms the payment of an newly `CREATED` order, or an order `FAILED` before.
  // If the payment is accepted, the `ConfirmPayResponse` will be returned with `OrderStatus` is `PROCESSING`,
  // and the order will be processed asynchronously.
  // Otherwise, a gRPC error will be returned. Possible errors are:
  // * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
  // * `NOT_FOUND`: The requested order is not found.
  // * `ALREADY_EXISTS`: The order is already confirmed, now it's `PROCESSING` or completed with status different from `FAILED` (e.g. `SUCCESS`, `PENDING`).
  // * `PERMISSION_DENIED`: The order is not allowed to be confirmed. If the reason is determined, a gRPC ErrorInfo will be returned.
  //  Possible reasons for Domain `ACQUIRING` are:
  // * `ORDER_EXPIRED`: The order is expired. The order can be confirmed only within 15 minutes after it's created.
  // * `INTERNAL`: System error, and the order not be processed.
  // * `UNKNOWN`: Any other error occurred during the process, can't not sure the order is processed or not, need to manually check the order status.
  rpc ConfirmPay (ConfirmPayRequest) returns (ConfirmPayResponse);

  rpc Capture(CaptureRequest) returns (CaptureResponse);

  rpc Cancel(CancelRequest) returns (CancelResponse);

  // `GetOrder` get the Order with the given order_no.
  // If the order is found, the `Order` will be returned with the order information.
  // Otherwise, a gRPC error will be returned. Possible errors are:
  // * `INVALID_ARGUMENT`: The request is malformed. Client should check the return message or `@gotags: validate` in the proto file for more details.
  // * `NOT_FOUND`: The requested order is not found.
  // * `INTERNAL`: System error.
  // * `UNKNOWN`: Any other error occurred during the process, this error should never be returned unless there is a bug in the system.
  rpc GetOrder (GetOrderRequest) returns (Order);

  // `GetOrderByToken` get the Order with the given order_token.
  // The response is the same as `GetOrder`.
  rpc GetOrderByToken (GetOrderByTokenRequest) returns (Order);

  // `GetOrderByAppTransId` get the Order with the given app_id and app_trans_id.
  // The response is the same as `GetOrder`.
  rpc GetOrderByAppTransId (GetOrderByAppTransIdRequest) returns (Order);

  rpc ConsultUserAssets(ConsultUserAssetsRequest) returns (ConsultUserAssetsResponse);
}

message CreateOrderRequest {
  // @gotags: validate:"min=1"
  int32 app_id = 1;
  // @gotags: validate:"required,prefix_date,max=40"
  string app_trans_id = 2;
  // Pay to amount. The currency is always VND.
  // @gotags: validate:"min=1"
  int64 amount = 7;
  // @gotags: validate:"required,valid"
  PaymentType payment_type = 9;
  // @gotags: validate:"required"
  DestAsset dest_asset = 12;
  // @gotags: validate:"required"
  OrderMeta metadata = 13;
  // Specify when the fund will be captured from the payer.
  // If not set, CAPTURE_METHOD_AUTO is the default; i.e.,
  // the fund will be captured immediately after the order is confirmed successfully.
  CaptureMethod capture_method = 14;
  // The payment amount and currency that merchant requests to receive. 
  Amount payment_amount = 15;
  QuoteCurrency quote_currency = 16;

  map<string, string> deprecated_data = 99;
}

message CreateLegacyOrderRequest {
  // @gotags: validate:"min=1"
  int32 app_id = 1;
  // @gotags: validate:"required,max=100"
  string app_trans_id = 2;
  // @gotags: validate:"min=1"
  int64 amount = 3;
  // @gotags: validate:"required"
  OrderMeta metadata = 4;
  // @gotags: validate:"required"
  string mac = 5;
}

message CreateOrderResponse {
  int64 order_no = 1;
  string order_token = 2;
  string order_url = 3;
  OrderStatus status = 4;
  ReasonStatus reason_status = 5;
  google.protobuf.Timestamp create_time = 6;
  string checksum = 7;
}

/**
 * ConfirmPayRequest contains necessary information to confirm a payment.
 */
message ConfirmPayRequest {
  // The order to be confirmed.
  // @gotags: validate:"min=1"
  int64 order_no = 1;
  // The one who confirms pay for the order.
  // Can be either User or Merchant,
  // or nil in-case of External Payment (for example, user is charged by Napas and not a Zalopay user).
  Payer payer = 3;
  // The assets that will be charged from the payer.
  // @gotags: validate:"eq=1,dive,required"
  repeated SourceAsset source_assets = 4;
  // @gotags: validate:"required"
  PaymentMeta metadata = 7;
  // The checksum of the order,
  // used to verify that the order is not modified before confirming pay.
  // TODO: require after client update to pass this field
  string checksum = 10;

  // The fee information that is confirmed by the user,
  // this field is only required if the client wants to validate the confirmed fee.
  // Otherwise, the fee will be auto calculated by the system.
  ConfirmedFee confirmed_fee = 11;

  // @gotags: validate:"dive"
  repeated PromotionItem promotion_items = 12;

  PaymentOption payment_option = 13;

  // Theses data is used for compatibility with old version.
  // Basically, theses include the same data as the current Kafka TransLog of TPE.
  // This map will be merge with the embed_data in the CreateOrderRequest, existing data will be overwritten.
  map<string, string> deprecated_data = 99;

  reserved 2, 6, 8, 9;
}

message ConfirmPayResponse {
  int64 order_no = 1;
  OrderStatus status = 2;
  ReasonStatus reason_status = 3;
  google.protobuf.Timestamp pay_time = 4;
  int64 payment_no = 5;
}

message CaptureRequest {
  // A UUID to identify the request.
  // @gotags: validate:"required,uuid"
  string request_id = 1;
  // The order to be captured.
  // @gotags: validate:"min=1"
  int64 order_no = 2;
  // The amount to be captured.
  // @gotags: validate:"min=1"
  int64 amount = 3;
}

message CaptureResponse {
  // empty, but open for extension
}

message CancelRequest {
  // The order to be canceled.
  // @gotags: validate:"min=1"
  int64 order_no = 1;
}

message CancelResponse {
  // empty, but open for extension
}

message GetOrderRequest {
  // @gotags: validate:"min=1"
  int64 order_no = 1;
}

message GetOrderByTokenRequest {
  // @gotags: validate:"required"
  string order_token = 1;
}

message GetOrderByAppTransIdRequest {
  // @gotags: validate:"min=1"
  int32 app_id = 1;
  // @gotags: validate:"required,prefix_date,max=100"
  string app_trans_id = 2;
}

message ConsultUserAssetsRequest {
  // @gotags: validate:"required"
  string user_id = 1;
  // @gotags: validate:"min=1"
  int32 app_id = 2;
  // @gotags: validate:"min=1"
  int64 amount = 3;
  // @gotags: validate:"required,valid"
  PaymentType payment_type = 4;
  Device device = 5;
}

message ConsultUserAssetsResponse {
  message UserAsset {
    string name = 1;
    string icon_url = 2;
    PaymentMethod payment_method = 3;
    ConsultSourceAsset source_asset = 4;
    AssetStatus status = 5;
    ReasonStatus reason_status = 6;
  }

  repeated UserAsset assets = 1;
}

message ConsultSourceAsset {
  message ConsultUserWalletAsset {
    UserWalletAsset wallet = 1;
    int64 balance = 2;
  }

  message ConsultUserBankAsset {
    UserBankAsset bank = 1;
    map<string, string> deprecated_data = 99;
  }

  message ConsultUserFinancialAsset {
    UserFinancialAsset finance = 1;
    int64 balance = 2;
  }

  oneof asset {
    ConsultUserWalletAsset user_wallet = 1;
    ConsultUserBankAsset user_bank = 2;
    ConsultUserFinancialAsset user_finance = 3;
  };
}

enum AssetStatus {
  ASSET_STATUS_UNSPECIFIED = 0;

  ACTIVE = 1;
  INACTIVE = 2;
  MAINTENANCE = 3;
  REACH_LIMIT = 4;
  NEED_ACTION = 5;
}

/**
  * `Order` presents the order information.
  *
  * An order must be created for collecting payment from a `Payer`.
  * After the creation, `Payer` can confirm the payment using one or more payment solutions (e.g. Cashier, Agreement, Napas, etc.),
  * which will end up calling the method `ConfirmPay`.
  *
  * Once the payment is confirmed, the order will change to `PROCESSING` status, and the payment will be processed asynchronously.
  * When the processing is done, the order will change to `SUCCESS`, `FAILED` or `PENDING` status.
  *
  * Each time the OrderStatus is changed, an Order message will be sent to Kafka.
  * Clients SHOULD listen to Kafka to get the latest status instead of polling using `GetOrder`.
  * When producing the Kafka message, we use the library `google.golang.org/protobuf/encoding/protojson`,
  * which follow this standard: https://developers.google.com/protocol-buffers/docs/proto3#json.
  * Clients SHOULD make sure to enable the option DiscardUnknown when unmarshal the message for forward compatibility.
  * For example, in Golang, the code should be:
  * ```go
  * unmarshaler := protojson.UnmarshalOptions{
  *   DiscardUnknown: true
  * }
  * ```
  *
  * Clients SHOULD schedule a job for getting the latest status of the order to prevent missing Kafka messages.
  *
  * Possible `OrderStatus`(s) are:
  * * `CREATED`: The order is created, but the payment is not confirmed yet.
  * * `PROCESSING`: The payment is confirmed, and the payment is being processed.
  * * `SUCCESS`: The payment is completed successfully.
  * * `FAILED`: The payment is completed unsuccessfully.
  * * `PENDING`: The payment is processed, but the system can't sure about the result. Manual intervention is required.
  *
  * When an order is completed which the status `FAILED` or `PENDING`, the `reason_status` will be set to indicate the reason.
  * See `ReasonStatus` for more details about the message structure.
  *
  * If the failure reason is defined by Acquiring Core, the value of field `domain` will be `ACQUIRING`,
  * and the possible values of field `reason` are:
  * * `SUBMIT_PAYMENT_ENGINE_FAILED`: Submit transaction to Payment Engine failed due to network issue.
  * * `ORDER_EXPIRED`: The order is expired. The order can be confirmed pay only within the TTL.
  * * `INTERNAL`: System error.
  * * `SOF_BALANCE_INSUFFICIENT`: The balance of provided `SourceAssets` is not enough to pay for the order.
  * * `FRAUD_REJECTED`: The order is rejected by the risk engine because of fraud.
  * * `RISK_REJECTED`: The order is rejected by the risk engine because of risk.
  * * `RISK_CHALLENGED`: The order is challenged by the risk engine, payer can do some actions and retry the payment.
  * * `PAYER_EXCEED_LIMIT`: The payer has exceeded the limit.
  * * `PAYMENT_EXCEED_LIMIT`: The payment has exceeded the limit.
  * * `PERMISSION_DENIED`: The payer does not have permission to pay for the order with the provided `SourceAssets`.
  * * `PAYER_AUTHENTICATION_FAILED`: Input wrong bank OTP.
  * * `SBV_2345_AUTHENTICATION_FAILED`: The SBV 2345 authentication failed.
  * Other values of `reason` are possible.
  */
message Order {
  int64 order_no = 1;
  int32 app_id = 2;
  string app_trans_id = 3;
  int64 amount = 8;
  PaymentType payment_type = 10;
  DestAsset dest_asset = 15;
  OrderStatus status = 16;
  ReasonStatus reason_status = 17;
  google.protobuf.Timestamp create_time = 19;
  Payment payment = 21;
  OrderMeta metadata = 22;
  google.protobuf.Timestamp expire_time = 23;
  string checksum = 24;
  CaptureMethod capture_method = 25;
  Amount payment_amount = 26;
  QuoteCurrency quote_currency = 27;
  string order_token = 28;

  map<string, string> deprecated_data = 99;
}

message Payment {
  int64 payment_no = 1;
  Payer payer = 2;
  // Deprecated: use `source_asset_list.source_assets` instead.
  repeated SourceAsset source_assets = 3 [deprecated = true];
  PaymentStatus status = 4;
  ReasonStatus reason_status = 5;
  // Deprecated: use `transaction_list.transactions` instead.
  repeated Transaction transactions = 6 [deprecated = true];
  google.protobuf.Timestamp pay_time = 7;
  PaymentMeta metadata = 8;
  // Deprecated: use `promotion_list.promotion_items` instead.
  repeated PromotionItem promotion_items = 9 [deprecated = true];
  // Deprecated: use `fee_list.fee_items` instead.
  repeated FeeItem fee_items = 10 [deprecated = true];
  SourceAssetList source_asset_list = 11;
  TransactionList transaction_list = 12;
  PromotionList promotion_list = 13;
  FeeList fee_list = 14;

  // Only has meaning when order capture method is MANUAL.
  Authorization authorization = 15;
  PaymentOption payment_option = 16;

  map<string, string> deprecated_data = 99;
}

message SourceAssetList {
  repeated SourceAsset source_assets = 1;
  int64 total_amount = 2;
}

message TransactionList {
  repeated Transaction transactions = 1;
}

message PromotionList {
  repeated PromotionItem promotion_items = 1;
  int64 total_amount = 2;
}

message FeeList {
  repeated FeeItem fee_items = 1;
  int64 total_amount = 2;
}

message Transaction {
  int64 trans_id = 1;
  int64 order_pay_id = 2;
  TransStatus trans_status = 3;
  ReasonStatus reason_status = 4;
  repeated SourceAsset source_assets = 5;
  DestAsset dest_asset = 6;
  int64 amount = 7;
  repeated PromotionItem promotion_items = 8;
  repeated FeeItem fee_items = 9;
  TransactionMeta metadata = 10;
}

enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;

  CREATED = 1;
  PROCESSING = 2;
  PENDING = 3;
  SUCCESS = 4;
  FAILED = 5;
  AUTHORIZED = 6;
  CANCELED = 7;
}

enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;

  PAYMENT_PROCESSING = 1;
  PAYMENT_PENDING = 2;
  PAYMENT_SUCCESS = 3;
  PAYMENT_FAILED = 4;
  PAYMENT_AUTHORIZED = 5;
}

enum TransStatus {
  TRANS_STATUS_UNSPECIFIED = 0;

  TRANS_INIT = 1;
  TRANS_REQUESTED = 2;
  TRANS_PROCESSING = 3;
  TRANS_PENDING = 4;
  TRANS_SUCCESS = 5;
  TRANS_FAILED = 6;
  TRANS_CANCELED = 7;
}

enum CaptureMethod {
  CAPTURE_METHOD_UNSPECIFIED = 0;

  CAPTURE_METHOD_AUTO = 1;
  CAPTURE_METHOD_MANUAL = 2;
}

message Amount {
  // The currency code of the amount. The value of this field must be an alphabetic code that follows the ISO 4217 standard.
  // For example, "VND" for Vietnamese dong, "USD" for US dollar.
  // @gotags: validate:"required,iso4217"
  string currency = 1;
  // The value of the amount as a natural number. By default, the value of this field is in the smallest currency unit.
  // For example, if the currency is USD and the amount is $1.00, set the value of this field to 100 (cent); or if the 
  // currency is JPY and the amount is ¥1, set the value of this field to 1 (yen).
  // @gotags: validate:"min=1"
  int64 value = 2;
}

message QuoteCurrency {
  // A currency pair, of which the first listed currency (also called base currency) is quoted against the second
  // currency (also called quote currency). The value of this field is in format of {base_currency}/{quote_currency},
  // where {base_currency} and {quote_currency} are alphabetic codes that follow the ISO 4217 standard. For example,
  // if the base currency is Vietnamese dong and the quote currency is US dollar, the value of this field should be "VND/USD".
  // @gotags: validate:"required,max=7"
  string pair = 1;
  // The quotation of the exchange rate between the currency pair.
  // @gotags: validate:"required"
  double price = 2;
}

message Authorization {
  int64 trans_id = 1;
}

/**
  * `ReasonStatus` presents the reason of a error status.
  * The status can be `OrderStatus`, `TransStatus` or `AssetStatus`...
  */
message ReasonStatus {
  // The domain defines the reason of the error. For example: 'ACQUIRING', 'DOMAIN_PAYMENT_ENGINE'...
  string domain = 1;
  // The reason of the error. This is a constant value that identifies the cause of the error.
  // Error reasons are unique within a particular domain of errors.
  // For example: 'PERMISSION_DENIED', 'INTERNAL'...
  string reason = 2;
  // Additional information about the error.
  map<string, string> metadata = 3;
  // A human-readable description of the error, it is safe to return for the end-user.
  string friendly_message = 4;
  // The details of the error from other domains.
  repeated ReasonDetail details = 5;
}

/**
  * `ReasonDetail` presents the detail of a error from a domain.
  */
message ReasonDetail {
  // The domain defines the reason. For example: 'DOMAIN_PAYMENT_ENGINE'...
  string domain = 1;
  // The reason of the error. This is a constant value that identifies the cause of the error.
  // Error reasons are unique within a particular domain of errors.
  string reason = 2;
  // Additional information about the error.
  map<string, string> metadata = 3;
}

/**
  * `OrderMeta` contains extra information about an Order. For example: AppConfig, Merchant information...
 */
message OrderMeta {
  // @gotags: validate:"max=5,dive,keys,label,min=1,max=50,endkeys,label,min=1,max=50"
  map<string, string> labels = 1;
  // @gotags: validate:"request_time_within=-15m 20m"
  int64 app_time = 2;
  // @gotags: validate:"omitempty,max=50"
  string app_user = 3;
  // @gotags: validate:"omitempty,max=256"
  string description = 4;
  // @gotags: validate:"omitempty,max=20"
  string product_code = 5;
  AppConfig app_config = 6;
  // @gotags: validate:"omitempty,max=2048"
  string items = 7;
  // @gotags: validate:"omitempty,max=2048"
  string embed_data = 8;
  // DisplayObject is used to display the order information on some UI systems like: cashier, merchant tool...
  DisplayObject display_object = 9;
  string sub_app_id = 10;
  string sub_app_user = 11;
  SubMerchant sub_merchant = 12;
  MerchantPromotion merchant_promo = 13;
  string bill_number = 14;
  int64 ref_trans_id = 15;
}

/**
  * `SubMerchant` contains information about the merchant that is not in the Zalopay system. Such as merchants of A+, Napas...
 */
message SubMerchant {
  string display_name = 1;
  string mcc = 2;
  string sub_merchant_id = 3;
}

/**
  * `MerchantPromotion` contains information about the promotion that is not in the Zalopay system. Such as promotions of A+, Napas...  
 */
message MerchantPromotion {
  // @gotags: validate:"min=1,dive"
  repeated MerchantPromotionDetails promo_details = 1;
}

message MerchantPromotionDetails {
  // The amount that is saved by be applied the promotion.
  // @gotags: validate:"required"
  Amount savingsAmount = 2;
}

/**
  * `PaymentMeta` contains extra information about a Payment. For example: PaymentSystem, device information...
 */
message PaymentMeta {
  // @gotags: validate:"max=5,dive,keys,label,min=1,max=50,endkeys,label,min=1,max=50"
  map<string, string> labels = 1;
  // PaymentSystem is used to identify the source of payment
  // This field will override the value of `zpSystem` in `deprecated_data`
  // @gotags: validate:"required,valid"
  PaymentSystem payment_system = 2;
  // @gotags: validate:"omitempty,max=20"
  string product_code = 3;
  // Device information of the payer who makes the payment.
  Device device = 4;
  // RedirectURL is used to redirect the payer back to a specific URL in some cases. For example: Bank app redirects back to Zalopay app.
  string redirect_url = 5;
  string auth_info = 6;
  ClientSource client_source = 7;
  PaymentSolution payment_solution = 8;
}

/**
  * `PaymentOption` contains flags that define payment behaviors.
 */
message PaymentOption {
  // Indicates whether authentication is denied.
  // Transaction will be marked as 'FAILED' if that payment requires authentication.
  bool deny_authn = 1;

  // Indicates whether the order has a future payment.
  // Merchant won't see the final status of the order through openAPI unless the payment is 'SUCCESS' or this flag is set to false.
  bool has_future_payment = 2;
}

message AppConfig {
  string callback_url = 1;
  string redirect_url = 2;
  // TTL is the time-to-live of the order. It is valid in range [5m, 30d]
  google.protobuf.Duration ttl = 3;
}

message Payer {
  oneof payer {
    User user = 14;
    Merchant merchant = 15;
  }
}

message User {
  // @gotags: validate:"required"
  string user_id = 1;
  string pin_hash = 2;
  string zalo_id = 3;
}

message Merchant {
  // @gotags: validate:"required"
  string merchant_id = 1;
}

message SourceAsset {
  // @gotags: validate:"min=0"
  int64 amount = 1;
  // @gotags: validate:"required"
  PaymentMethod payment_method = 2;
  // @gotags: validate:"required"
  oneof source_asset {
    UserWalletAsset user_wallet = 3;
    UserBankAsset user_bank = 4;
    UserFinancialAsset user_finance = 5;
    UserFixedDepositAsset user_fixed_deposit = 6;
    ThirdPartyAsset third_party = 7;
    InternalMerchantAsset internal_merchant = 8;
  };
}

enum PaymentMethod {
  PAYMENT_METHOD_UNSPECIFIED = 0;

  WBL = 1;    // Wallet balance
  BDC = 2;    // Bank domestic card
  BDA = 3;    // Bank account
  BIC = 4;    // Bank international card
  BID = 5;    // Bank international debit card
  EMVCO = 7;  // Bank transfer by VietQR
  MMF = 8;    // Micro investment
  PLT = 9;    // Buy now pay later
  UPI = 10;   // Bank transfer by UPIQR 
  INST = 11;  // Installment
  FD = 12;    // User fixed deposit account
  EMVTF = 13; // Bank transfer by VietQR for Transfer
  THIRDPS = 14; // Third party sof
  MBP = 15; // Multi bill

  reserved 6;
}

message DestAsset {
  // @gotags: validate:"required,valid"
  DestAssetType dest_asset_type = 1;
  // @gotags: validate:"required"
  oneof dest_asset {
    MerchantAsset merchant = 2;
    UserWalletAsset user_wallet = 3;
    UserFixedDepositAsset user_fixed_deposit = 4;
    MerchantBankNapasAsset merchant_bank_napas = 5;
    ExternalBankAsset external_bank = 6;
    InternalMerchantAsset internal_merchant = 7;
  }
}

enum DestAssetType {
  DEST_ASSET_TYPE_UNSPECIFIED = 0;

  MERCHANT = 1; // Merchant payable
  USER = 2; // User wallet
  FDA = 3;  // User fixed deposit account
  NMCA = 4; // Merchant Bank Account Napas
  UBDA = 5; // User Bank Domestic Account
  UBDC = 6; // User Bank Domestic Card
  UBIC = 7; // User Bank International Credit Card
  UBID = 8; // User Bank International Debit Card
  MMBP = 9; // Multi Bill
}

enum WalletType {
  MAIN = 0;
  TEMPORARY = 1;
  RESTRICTED = 2;
}

message ThirdPartyAsset {
  // @gotags: validate:"required"
  string psp_id = 1;
}

message UserWalletAsset {
  // @gotags: validate:"required,number"
  string user_id = 1;
  // @gotags: validate:"valid"
  WalletType type = 2;
}

message InternalMerchantAsset {
}

message UserBankAsset {
  // BindingID represents a linked bank account/card/etc.
  string binding_id = 1;
  string binding_system_id = 2;
  string escrow_account_no = 3;
  string bc_trans_id = 4;
  // Issuing bank
  string bank_code = 5;
  string bank_connector_code = 6;
  // BimID represents a bank account/card/etc. It can be either linked or unlinked.
  string bim_id = 7;
  string f6no = 8;
  string l4no = 9;
  string card_scheme = 10;  // e.g. VISA, MASTERCARD, JCB
  string first_account_no = 11;
  string last_account_no = 12;
  string token = 13; //Payment Solution CARD_ON_FILE, GOOGLE_PAY FPAN
  string third_party_payment_info = 14; //Payment Solution APPLE_PAY, GOOGLE_PAY DPAN
}

message ExternalBankAsset {
  // @gotags: validate:"required"
  string bank_code = 1;
  string full_name = 2;
  string account_no = 3;
  string card_no = 4;

  string inquiry_trans_id = 5;
  RoutingType routing_type = 6;
  TokenType token_type = 7;
  string token = 8;
  string sender_name = 9; // use for IBFT
  string first_no = 10; // First account/card no
  string last_no = 11; // Last account/card no
  string bank_connector_code = 12; // use for VISA_DIRECT

  enum TokenType {
    TOKEN_TYPE_UNSPECIFIED = 0;
    TOKEN_TYPE_LINKED = 1;
    TOKEN_TYPE_CPS = 2;
    TOKEN_TYPE_CARD_TOKEN = 3;
  }

  enum RoutingType {
    ROUTING_TYPE_UNSPECIFIED = 0;
    ROUTING_TYPE_WITHDRAW = 1;
    ROUTING_TYPE_IBFT = 2;
  }
}

message MerchantAsset {
  string merchant_id = 1;
  // @gotags: validate:"min=1"
  int32 app_id = 2;
  string payment_id = 3;
}

message UserFinancialAsset {
  string user_id = 1;
  string partner_code = 2;
  string charge_info = 3;
}

message UserFixedDepositAsset {
  // @gotags: validate:"required,number"
  string user_id = 1;
}

message MerchantBankNapasAsset {
  // @gotags: validate:"required"
  string bank_code = 1;
  // @gotags: validate:"required"
  string bank_connector_code = 2;
  // @gotags: validate:"required"
  string inquiry_trans_id = 3;
  // @gotags: validate:"required"
  string account_no = 4;
  // @gotags: validate:"required"
  string holder_name = 5;
  // @gotags: validate:"required"
  string org_acc_type = 6;
  // @gotags: validate:"required"
  string target_acc_type = 7;
  // @gotags: validate:"required"
  string trans_reference = 8;
}

enum PaymentType {
  PAYMENT_TYPE_UNSPECIFIED = 0;

  PAYMENT = 1;
  WITHDRAW = 2;
  TOPUP = 3;
  IBFT = 4;
  TRANSFER = 5;
  LIQUIDATE = 6;
  VISA_DIRECT = 7;
}

enum PaymentSystem {
  PAYMENT_SYSTEM_UNSPECIFIED = 0;

  WALLET = 1;
  GATEWAY = 2;
}

enum PromotionType {
  PROMOTION_TYPE_UNSPECIFIED = 0;

  VOUCHER = 1;
  DIRECT_DISCOUNT = 2;
}

message PromotionItem {
  PromotionType type = 1 [deprecated = true];
  string campaign_id = 2 [deprecated = true];
  string promo_sig = 3 [deprecated = true];
  int64 amount = 4 [deprecated = true];

  oneof item {
    Voucher voucher = 5;
    DirectDiscount direct_discount = 6;
  }
}

message Voucher {
  // @gotags: validate:"required"
  string campaign_id = 1;
  // @gotags: validate:"required"
  string code = 2;
  // @gotags: validate:"required"
  string sig = 3;
  // @gotags: validate:"min=1"
  int64 amount = 4;
  google.protobuf.Timestamp use_time = 5;
}

message DirectDiscount {
  // @gotags: validate:"required"
  string campaign_id = 1;
  // @gotags: validate:"required"
  string code = 2;
  // @gotags: validate:"required"
  string sig = 3;
  // @gotags: validate:"min=1"
  int64 amount = 4;
}

// ConfirmedFee is used to validate the fee that is confirmed by the user.
message ConfirmedFee {
  // @gotags: validate:"min=0"
  int64 total_amount = 1;
}

enum FeeType {
  FEE_TYPE_UNSPECIFIED = 0;

  SERVICE_FEE = 1;
  TRANS_FEE = 2;
  PROMOTION_FEE = 3;
}

message FeeItem {
  FeeType type = 1;
  int64 amount = 2;
}

message Device {
  string device_id = 1;
  string device_model = 2; // e.g. iPhone11,2 or Xiaomi M2003J15SC
  OsType os_type = 3;
  string os_version = 4; // e.g. 15.6.1
  PlatformType platform = 5;
  string app_version = 6; // e.g. 7.20.0
  string user_ip = 7;
}

enum OsType {
  OS_TYPE_UNSPECIFIED = 0;

  ANDROID = 1;
  IOS = 2;

  OTHER_OS = 99;
}

enum PlatformType {
  PLATFORM_TYPE_UNSPECIFIED = 0;

  ZPA = 1;
  ZPI = 2;

  OTHER_PLATFORM = 99;
}

enum ClientSource {
  CLIENT_SOURCE_UNSPECIFIED = 0;

  CASHIER = 1;

  OTHER_SOURCE = 99;
}

enum PaymentSolution {
  PAYMENT_SOLUTION_UNSPECIFIED = 0;

  CARD_ON_FILE = 1;
  GOOGLE_PAY = 2;
  APPLE_PAY = 3;
}

message DisplayObject {
  message Field {
    // Name of the field
    // @gotags: validate:"required,max=50"
    string name = 1;
    // Value of the field
    // @gotags: validate:"required,max=255"
    string value = 2;
    // DisplaySystems is the list of systems that the field will be displayed on.
    // Currently, only `cashier` is supported.
    // @gotags: validate:"eq=1,dive,required"
    repeated string display_systems = 3;
  }

  // @gotags: validate:"min=1,max=10,dive,required"
  repeated Field fields = 1;
}

message TransactionMeta {
  int32 app_id = 1;
  string product_code = 2;
}
