// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/payment_auth/payment_authentication.proto

package payment_auth

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PaymentAuthenticationService_PreparePaymentAuth_FullMethodName    = "/authentication.v3.PaymentAuthenticationService/PreparePaymentAuth"
	PaymentAuthenticationService_ProcessNextAuthAction_FullMethodName = "/authentication.v3.PaymentAuthenticationService/ProcessNextAuthAction"
	PaymentAuthenticationService_TriggerAuthByFund_FullMethodName     = "/authentication.v3.PaymentAuthenticationService/TriggerAuthByFund"
	PaymentAuthenticationService_SubmitAuthValue_FullMethodName       = "/authentication.v3.PaymentAuthenticationService/SubmitAuthValue"
	PaymentAuthenticationService_GetAuthSessionStatus_FullMethodName  = "/authentication.v3.PaymentAuthenticationService/GetAuthSessionStatus"
	PaymentAuthenticationService_GetProcessingAuth_FullMethodName     = "/authentication.v3.PaymentAuthenticationService/GetProcessingAuth"
	PaymentAuthenticationService_GetAuthContext_FullMethodName        = "/authentication.v3.PaymentAuthenticationService/GetAuthContext"
)

// PaymentAuthenticationServiceClient is the client API for PaymentAuthenticationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentAuthenticationServiceClient interface {
	PreparePaymentAuth(ctx context.Context, in *PreparePaymentAuthRequest, opts ...grpc.CallOption) (*PreparePaymentAuthResponse, error)
	ProcessNextAuthAction(ctx context.Context, in *ProcessNextAuthActionRequest, opts ...grpc.CallOption) (*ProcessNextAuthActionResponse, error)
	TriggerAuthByFund(ctx context.Context, in *TriggerAuthByFundRequest, opts ...grpc.CallOption) (*TriggerAuthByFundResponse, error)
	SubmitAuthValue(ctx context.Context, in *SubmitAuthValueRequest, opts ...grpc.CallOption) (*SubmitAuthValueResponse, error)
	GetAuthSessionStatus(ctx context.Context, in *GetAuthSessionStatusRequest, opts ...grpc.CallOption) (*GetAuthSessionStatusResponse, error)
	GetProcessingAuth(ctx context.Context, in *GetProcessingAuthRequest, opts ...grpc.CallOption) (*GetProcessingAuthResponse, error)
	GetAuthContext(ctx context.Context, in *GetAuthContextRequest, opts ...grpc.CallOption) (*GetAuthContextResponse, error)
}

type paymentAuthenticationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentAuthenticationServiceClient(cc grpc.ClientConnInterface) PaymentAuthenticationServiceClient {
	return &paymentAuthenticationServiceClient{cc}
}

func (c *paymentAuthenticationServiceClient) PreparePaymentAuth(ctx context.Context, in *PreparePaymentAuthRequest, opts ...grpc.CallOption) (*PreparePaymentAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreparePaymentAuthResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_PreparePaymentAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAuthenticationServiceClient) ProcessNextAuthAction(ctx context.Context, in *ProcessNextAuthActionRequest, opts ...grpc.CallOption) (*ProcessNextAuthActionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessNextAuthActionResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_ProcessNextAuthAction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAuthenticationServiceClient) TriggerAuthByFund(ctx context.Context, in *TriggerAuthByFundRequest, opts ...grpc.CallOption) (*TriggerAuthByFundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerAuthByFundResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_TriggerAuthByFund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAuthenticationServiceClient) SubmitAuthValue(ctx context.Context, in *SubmitAuthValueRequest, opts ...grpc.CallOption) (*SubmitAuthValueResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubmitAuthValueResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_SubmitAuthValue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAuthenticationServiceClient) GetAuthSessionStatus(ctx context.Context, in *GetAuthSessionStatusRequest, opts ...grpc.CallOption) (*GetAuthSessionStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuthSessionStatusResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_GetAuthSessionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAuthenticationServiceClient) GetProcessingAuth(ctx context.Context, in *GetProcessingAuthRequest, opts ...grpc.CallOption) (*GetProcessingAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProcessingAuthResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_GetProcessingAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAuthenticationServiceClient) GetAuthContext(ctx context.Context, in *GetAuthContextRequest, opts ...grpc.CallOption) (*GetAuthContextResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuthContextResponse)
	err := c.cc.Invoke(ctx, PaymentAuthenticationService_GetAuthContext_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentAuthenticationServiceServer is the server API for PaymentAuthenticationService service.
// All implementations must embed UnimplementedPaymentAuthenticationServiceServer
// for forward compatibility.
type PaymentAuthenticationServiceServer interface {
	PreparePaymentAuth(context.Context, *PreparePaymentAuthRequest) (*PreparePaymentAuthResponse, error)
	ProcessNextAuthAction(context.Context, *ProcessNextAuthActionRequest) (*ProcessNextAuthActionResponse, error)
	TriggerAuthByFund(context.Context, *TriggerAuthByFundRequest) (*TriggerAuthByFundResponse, error)
	SubmitAuthValue(context.Context, *SubmitAuthValueRequest) (*SubmitAuthValueResponse, error)
	GetAuthSessionStatus(context.Context, *GetAuthSessionStatusRequest) (*GetAuthSessionStatusResponse, error)
	GetProcessingAuth(context.Context, *GetProcessingAuthRequest) (*GetProcessingAuthResponse, error)
	GetAuthContext(context.Context, *GetAuthContextRequest) (*GetAuthContextResponse, error)
	mustEmbedUnimplementedPaymentAuthenticationServiceServer()
}

// UnimplementedPaymentAuthenticationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPaymentAuthenticationServiceServer struct{}

func (UnimplementedPaymentAuthenticationServiceServer) PreparePaymentAuth(context.Context, *PreparePaymentAuthRequest) (*PreparePaymentAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreparePaymentAuth not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) ProcessNextAuthAction(context.Context, *ProcessNextAuthActionRequest) (*ProcessNextAuthActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessNextAuthAction not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) TriggerAuthByFund(context.Context, *TriggerAuthByFundRequest) (*TriggerAuthByFundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerAuthByFund not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) SubmitAuthValue(context.Context, *SubmitAuthValueRequest) (*SubmitAuthValueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitAuthValue not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) GetAuthSessionStatus(context.Context, *GetAuthSessionStatusRequest) (*GetAuthSessionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthSessionStatus not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) GetProcessingAuth(context.Context, *GetProcessingAuthRequest) (*GetProcessingAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProcessingAuth not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) GetAuthContext(context.Context, *GetAuthContextRequest) (*GetAuthContextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthContext not implemented")
}
func (UnimplementedPaymentAuthenticationServiceServer) mustEmbedUnimplementedPaymentAuthenticationServiceServer() {
}
func (UnimplementedPaymentAuthenticationServiceServer) testEmbeddedByValue() {}

// UnsafePaymentAuthenticationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentAuthenticationServiceServer will
// result in compilation errors.
type UnsafePaymentAuthenticationServiceServer interface {
	mustEmbedUnimplementedPaymentAuthenticationServiceServer()
}

func RegisterPaymentAuthenticationServiceServer(s grpc.ServiceRegistrar, srv PaymentAuthenticationServiceServer) {
	// If the following call pancis, it indicates UnimplementedPaymentAuthenticationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PaymentAuthenticationService_ServiceDesc, srv)
}

func _PaymentAuthenticationService_PreparePaymentAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreparePaymentAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).PreparePaymentAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_PreparePaymentAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).PreparePaymentAuth(ctx, req.(*PreparePaymentAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAuthenticationService_ProcessNextAuthAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessNextAuthActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).ProcessNextAuthAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_ProcessNextAuthAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).ProcessNextAuthAction(ctx, req.(*ProcessNextAuthActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAuthenticationService_TriggerAuthByFund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerAuthByFundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).TriggerAuthByFund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_TriggerAuthByFund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).TriggerAuthByFund(ctx, req.(*TriggerAuthByFundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAuthenticationService_SubmitAuthValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitAuthValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).SubmitAuthValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_SubmitAuthValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).SubmitAuthValue(ctx, req.(*SubmitAuthValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAuthenticationService_GetAuthSessionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthSessionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).GetAuthSessionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_GetAuthSessionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).GetAuthSessionStatus(ctx, req.(*GetAuthSessionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAuthenticationService_GetProcessingAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProcessingAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).GetProcessingAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_GetProcessingAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).GetProcessingAuth(ctx, req.(*GetProcessingAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAuthenticationService_GetAuthContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAuthenticationServiceServer).GetAuthContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAuthenticationService_GetAuthContext_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAuthenticationServiceServer).GetAuthContext(ctx, req.(*GetAuthContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentAuthenticationService_ServiceDesc is the grpc.ServiceDesc for PaymentAuthenticationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentAuthenticationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "authentication.v3.PaymentAuthenticationService",
	HandlerType: (*PaymentAuthenticationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PreparePaymentAuth",
			Handler:    _PaymentAuthenticationService_PreparePaymentAuth_Handler,
		},
		{
			MethodName: "ProcessNextAuthAction",
			Handler:    _PaymentAuthenticationService_ProcessNextAuthAction_Handler,
		},
		{
			MethodName: "TriggerAuthByFund",
			Handler:    _PaymentAuthenticationService_TriggerAuthByFund_Handler,
		},
		{
			MethodName: "SubmitAuthValue",
			Handler:    _PaymentAuthenticationService_SubmitAuthValue_Handler,
		},
		{
			MethodName: "GetAuthSessionStatus",
			Handler:    _PaymentAuthenticationService_GetAuthSessionStatus_Handler,
		},
		{
			MethodName: "GetProcessingAuth",
			Handler:    _PaymentAuthenticationService_GetProcessingAuth_Handler,
		},
		{
			MethodName: "GetAuthContext",
			Handler:    _PaymentAuthenticationService_GetAuthContext_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/payment_auth/payment_authentication.proto",
}
