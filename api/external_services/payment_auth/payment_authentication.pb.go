// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/payment_auth/payment_authentication.proto

package payment_auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BankTransLevel int32

const (
	BankTransLevel_LVL_UNSPECIFIED BankTransLevel = 0
	BankTransLevel_LVL_A           BankTransLevel = 1
	BankTransLevel_LVL_B           BankTransLevel = 2
	BankTransLevel_LVL_C           BankTransLevel = 3
	BankTransLevel_LVL_D           BankTransLevel = 4
)

// Enum value maps for BankTransLevel.
var (
	BankTransLevel_name = map[int32]string{
		0: "LVL_UNSPECIFIED",
		1: "LVL_A",
		2: "LVL_B",
		3: "LVL_C",
		4: "LVL_D",
	}
	BankTransLevel_value = map[string]int32{
		"LVL_UNSPECIFIED": 0,
		"LVL_A":           1,
		"LVL_B":           2,
		"LVL_C":           3,
		"LVL_D":           4,
	}
)

func (x BankTransLevel) Enum() *BankTransLevel {
	p := new(BankTransLevel)
	*p = x
	return p
}

func (x BankTransLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankTransLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_payment_authentication_proto_enumTypes[0].Descriptor()
}

func (BankTransLevel) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_payment_authentication_proto_enumTypes[0]
}

func (x BankTransLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankTransLevel.Descriptor instead.
func (BankTransLevel) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{0}
}

type BankAuthType int32

const (
	BankAuthType_BANK_AUTH_TYPE_UNSPECIFIED BankAuthType = 0
	BankAuthType_BANK_AUTH_TYPE_DOTP        BankAuthType = 1
	BankAuthType_BANK_AUTH_TYPE_REDIRECT    BankAuthType = 2
	BankAuthType_BANK_AUTH_TYPE_ROP         BankAuthType = 3
	BankAuthType_BANK_AUTH_TYPE_AUTHORIZED  BankAuthType = 4
	BankAuthType_BANK_AUTH_TYPE_APP_TO_APP  BankAuthType = 5
)

// Enum value maps for BankAuthType.
var (
	BankAuthType_name = map[int32]string{
		0: "BANK_AUTH_TYPE_UNSPECIFIED",
		1: "BANK_AUTH_TYPE_DOTP",
		2: "BANK_AUTH_TYPE_REDIRECT",
		3: "BANK_AUTH_TYPE_ROP",
		4: "BANK_AUTH_TYPE_AUTHORIZED",
		5: "BANK_AUTH_TYPE_APP_TO_APP",
	}
	BankAuthType_value = map[string]int32{
		"BANK_AUTH_TYPE_UNSPECIFIED": 0,
		"BANK_AUTH_TYPE_DOTP":        1,
		"BANK_AUTH_TYPE_REDIRECT":    2,
		"BANK_AUTH_TYPE_ROP":         3,
		"BANK_AUTH_TYPE_AUTHORIZED":  4,
		"BANK_AUTH_TYPE_APP_TO_APP":  5,
	}
)

func (x BankAuthType) Enum() *BankAuthType {
	p := new(BankAuthType)
	*p = x
	return p
}

func (x BankAuthType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankAuthType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_payment_authentication_proto_enumTypes[1].Descriptor()
}

func (BankAuthType) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_payment_authentication_proto_enumTypes[1]
}

func (x BankAuthType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankAuthType.Descriptor instead.
func (BankAuthType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{1}
}

type Platform int32

const (
	Platform_PLATFORM_UNSPECIFIED Platform = 0
	Platform_PLATFORM_ZPA         Platform = 1
	Platform_PLATFORM_ZPI         Platform = 2
)

// Enum value maps for Platform.
var (
	Platform_name = map[int32]string{
		0: "PLATFORM_UNSPECIFIED",
		1: "PLATFORM_ZPA",
		2: "PLATFORM_ZPI",
	}
	Platform_value = map[string]int32{
		"PLATFORM_UNSPECIFIED": 0,
		"PLATFORM_ZPA":         1,
		"PLATFORM_ZPI":         2,
	}
)

func (x Platform) Enum() *Platform {
	p := new(Platform)
	*p = x
	return p
}

func (x Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_payment_authentication_proto_enumTypes[2].Descriptor()
}

func (Platform) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_payment_authentication_proto_enumTypes[2]
}

func (x Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Platform.Descriptor instead.
func (Platform) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{2}
}

type DeviceOS int32

const (
	DeviceOS_DEVICE_OS_UNSPECIFIED DeviceOS = 0
	DeviceOS_DEVICE_OS_ANDROID     DeviceOS = 1
	DeviceOS_DEVICE_OS_IOS         DeviceOS = 2
)

// Enum value maps for DeviceOS.
var (
	DeviceOS_name = map[int32]string{
		0: "DEVICE_OS_UNSPECIFIED",
		1: "DEVICE_OS_ANDROID",
		2: "DEVICE_OS_IOS",
	}
	DeviceOS_value = map[string]int32{
		"DEVICE_OS_UNSPECIFIED": 0,
		"DEVICE_OS_ANDROID":     1,
		"DEVICE_OS_IOS":         2,
	}
)

func (x DeviceOS) Enum() *DeviceOS {
	p := new(DeviceOS)
	*p = x
	return p
}

func (x DeviceOS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceOS) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_payment_authentication_proto_enumTypes[3].Descriptor()
}

func (DeviceOS) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_payment_authentication_proto_enumTypes[3]
}

func (x DeviceOS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceOS.Descriptor instead.
func (DeviceOS) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{3}
}

type GetProcessingAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required
	PaymentNo string `protobuf:"bytes,1,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
}

func (x *GetProcessingAuthRequest) Reset() {
	*x = GetProcessingAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProcessingAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProcessingAuthRequest) ProtoMessage() {}

func (x *GetProcessingAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProcessingAuthRequest.ProtoReflect.Descriptor instead.
func (*GetProcessingAuthRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{0}
}

func (x *GetProcessingAuthRequest) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

type GetAuthContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required
	PaymentNo string `protobuf:"bytes,1,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
}

func (x *GetAuthContextRequest) Reset() {
	*x = GetAuthContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthContextRequest) ProtoMessage() {}

func (x *GetAuthContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthContextRequest.ProtoReflect.Descriptor instead.
func (*GetAuthContextRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{1}
}

func (x *GetAuthContextRequest) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

type GetAuthContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Required
	AuthSessions []*AuthSession `protobuf:"bytes,2,rep,name=auth_sessions,json=authSessions,proto3" json:"auth_sessions,omitempty"`
}

func (x *GetAuthContextResponse) Reset() {
	*x = GetAuthContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthContextResponse) ProtoMessage() {}

func (x *GetAuthContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthContextResponse.ProtoReflect.Descriptor instead.
func (*GetAuthContextResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{2}
}

func (x *GetAuthContextResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetAuthContextResponse) GetAuthSessions() []*AuthSession {
	if x != nil {
		return x.AuthSessions
	}
	return nil
}

type AuthSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerAuthType AuthType   `protobuf:"varint,1,opt,name=trigger_auth_type,json=triggerAuthType,proto3,enum=authentication.v3.AuthType" json:"trigger_auth_type,omitempty"`
	FinalAuthTypes  []AuthType `protobuf:"varint,2,rep,packed,name=final_auth_types,json=finalAuthTypes,proto3,enum=authentication.v3.AuthType" json:"final_auth_types,omitempty"`
	Status          AuthStatus `protobuf:"varint,3,opt,name=status,proto3,enum=authentication.v3.AuthStatus" json:"status,omitempty"`
}

func (x *AuthSession) Reset() {
	*x = AuthSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthSession) ProtoMessage() {}

func (x *AuthSession) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthSession.ProtoReflect.Descriptor instead.
func (*AuthSession) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{3}
}

func (x *AuthSession) GetTriggerAuthType() AuthType {
	if x != nil {
		return x.TriggerAuthType
	}
	return AuthType_AUTH_TYPE_UNSPECIFIED
}

func (x *AuthSession) GetFinalAuthTypes() []AuthType {
	if x != nil {
		return x.FinalAuthTypes
	}
	return nil
}

func (x *AuthSession) GetStatus() AuthStatus {
	if x != nil {
		return x.Status
	}
	return AuthStatus_AUTH_SESSION_STATUS_UNSPECIFIED
}

type GetProcessingAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Optional
	AuthSessionId string `protobuf:"bytes,2,opt,name=auth_session_id,json=authSessionId,proto3" json:"auth_session_id,omitempty"`
	// Optional
	AuthProvider Provider `protobuf:"varint,3,opt,name=auth_provider,json=authProvider,proto3,enum=authentication.v3.Provider" json:"auth_provider,omitempty"`
	// Optional
	AuthType AuthType `protobuf:"varint,4,opt,name=auth_type,json=authType,proto3,enum=authentication.v3.AuthType" json:"auth_type,omitempty"`
	// Optional
	ExtraData map[string]string `protobuf:"bytes,5,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetProcessingAuthResponse) Reset() {
	*x = GetProcessingAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProcessingAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProcessingAuthResponse) ProtoMessage() {}

func (x *GetProcessingAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProcessingAuthResponse.ProtoReflect.Descriptor instead.
func (*GetProcessingAuthResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{4}
}

func (x *GetProcessingAuthResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetProcessingAuthResponse) GetAuthSessionId() string {
	if x != nil {
		return x.AuthSessionId
	}
	return ""
}

func (x *GetProcessingAuthResponse) GetAuthProvider() Provider {
	if x != nil {
		return x.AuthProvider
	}
	return Provider_PROVIDER_UNSPECIFIED
}

func (x *GetProcessingAuthResponse) GetAuthType() AuthType {
	if x != nil {
		return x.AuthType
	}
	return AuthType_AUTH_TYPE_UNSPECIFIED
}

func (x *GetProcessingAuthResponse) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

type PreparePaymentAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required: User who initiating the payment and owner of all the funds (exception the final destination).
	ZalopayId string `protobuf:"bytes,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// Required:
	OrderNo string `protobuf:"bytes,2,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	// Required
	PaymentNo string `protobuf:"bytes,3,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
	// Required: the authentication method user must have done to submit payment.
	PreAuth *PreAuth `protobuf:"bytes,4,opt,name=pre_auth,json=preAuth,proto3" json:"pre_auth,omitempty"`
	// Required: Total amount charged on initiating user's fund.
	Amount int64 `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// Optional: for order from AgreementPay, otherwise Required.
	ClientInfo *ClientInfo `protobuf:"bytes,6,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	// Required: List of transaction being executed in the payment.
	Transactions []*Transaction `protobuf:"bytes,7,rep,name=transactions,proto3" json:"transactions,omitempty"`
	// Optional: depend on sources of fund
	AdditionalAuthData *AdditionalAuthData `protobuf:"bytes,8,opt,name=additional_auth_data,json=additionalAuthData,proto3" json:"additional_auth_data,omitempty"`
	// Required
	DenyAuthn bool `protobuf:"varint,9,opt,name=deny_authn,json=denyAuthn,proto3" json:"deny_authn,omitempty"`
}

func (x *PreparePaymentAuthRequest) Reset() {
	*x = PreparePaymentAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreparePaymentAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreparePaymentAuthRequest) ProtoMessage() {}

func (x *PreparePaymentAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreparePaymentAuthRequest.ProtoReflect.Descriptor instead.
func (*PreparePaymentAuthRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{5}
}

func (x *PreparePaymentAuthRequest) GetZalopayId() string {
	if x != nil {
		return x.ZalopayId
	}
	return ""
}

func (x *PreparePaymentAuthRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *PreparePaymentAuthRequest) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

func (x *PreparePaymentAuthRequest) GetPreAuth() *PreAuth {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *PreparePaymentAuthRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PreparePaymentAuthRequest) GetClientInfo() *ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

func (x *PreparePaymentAuthRequest) GetTransactions() []*Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *PreparePaymentAuthRequest) GetAdditionalAuthData() *AdditionalAuthData {
	if x != nil {
		return x.AdditionalAuthData
	}
	return nil
}

func (x *PreparePaymentAuthRequest) GetDenyAuthn() bool {
	if x != nil {
		return x.DenyAuthn
	}
	return false
}

type PreAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required
	AuthType AuthType `protobuf:"varint,1,opt,name=auth_type,json=authType,proto3,enum=authentication.v3.AuthType" json:"auth_type,omitempty"`
	// Required
	Pin string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin,omitempty"`
}

func (x *PreAuth) Reset() {
	*x = PreAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAuth) ProtoMessage() {}

func (x *PreAuth) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAuth.ProtoReflect.Descriptor instead.
func (*PreAuth) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{6}
}

func (x *PreAuth) GetAuthType() AuthType {
	if x != nil {
		return x.AuthType
	}
	return AuthType_AUTH_TYPE_UNSPECIFIED
}

func (x *PreAuth) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

type AdditionalAuthData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional: all transaction from Wallet must contain this data.
	AuthInfoEncoded string `protobuf:"bytes,1,opt,name=auth_info_encoded,json=authInfoEncoded,proto3" json:"auth_info_encoded,omitempty"`
	// Optional: Submit to UM in extra_data to trigger some auth methods.
	ClientSource string `protobuf:"bytes,2,opt,name=client_source,json=clientSource,proto3" json:"client_source,omitempty"`
}

func (x *AdditionalAuthData) Reset() {
	*x = AdditionalAuthData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalAuthData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalAuthData) ProtoMessage() {}

func (x *AdditionalAuthData) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalAuthData.ProtoReflect.Descriptor instead.
func (*AdditionalAuthData) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{7}
}

func (x *AdditionalAuthData) GetAuthInfoEncoded() string {
	if x != nil {
		return x.AuthInfoEncoded
	}
	return ""
}

func (x *AdditionalAuthData) GetClientSource() string {
	if x != nil {
		return x.ClientSource
	}
	return ""
}

type PreparePaymentAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Required:
	//   - True: payment can continue processing without authentication.
	//   - False: payment must follow ProcessNextAuthAction before submitting transactions.
	CanBypass bool `protobuf:"varint,2,opt,name=can_bypass,json=canBypass,proto3" json:"can_bypass,omitempty"`
}

func (x *PreparePaymentAuthResponse) Reset() {
	*x = PreparePaymentAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreparePaymentAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreparePaymentAuthResponse) ProtoMessage() {}

func (x *PreparePaymentAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreparePaymentAuthResponse.ProtoReflect.Descriptor instead.
func (*PreparePaymentAuthResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{8}
}

func (x *PreparePaymentAuthResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PreparePaymentAuthResponse) GetCanBypass() bool {
	if x != nil {
		return x.CanBypass
	}
	return false
}

type Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           int32  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ProductCode     string `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	Amount          int64  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	SourceAssetType string `protobuf:"bytes,4,opt,name=source_asset_type,json=sourceAssetType,proto3" json:"source_asset_type,omitempty"`
	// Optional. Data following by source asset_type;
	//
	// Types that are assignable to SourceAssetData:
	//
	//	*Transaction_Bank
	SourceAssetData isTransaction_SourceAssetData `protobuf_oneof:"source_asset_data"`
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{9}
}

func (x *Transaction) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *Transaction) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *Transaction) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Transaction) GetSourceAssetType() string {
	if x != nil {
		return x.SourceAssetType
	}
	return ""
}

func (m *Transaction) GetSourceAssetData() isTransaction_SourceAssetData {
	if m != nil {
		return m.SourceAssetData
	}
	return nil
}

func (x *Transaction) GetBank() *BankAsset {
	if x, ok := x.GetSourceAssetData().(*Transaction_Bank); ok {
		return x.Bank
	}
	return nil
}

type isTransaction_SourceAssetData interface {
	isTransaction_SourceAssetData()
}

type Transaction_Bank struct {
	Bank *BankAsset `protobuf:"bytes,11,opt,name=bank,proto3,oneof"`
}

func (*Transaction_Bank) isTransaction_SourceAssetData() {}

type ProcessNextAuthActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	// Required
	PaymentNo string `protobuf:"bytes,2,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
	// Required: Idempotency control, each request_id corresponding to one execution of one transaction or many transactions (in multibill order).
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Required:
	Transactions []*Transaction `protobuf:"bytes,4,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *ProcessNextAuthActionRequest) Reset() {
	*x = ProcessNextAuthActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessNextAuthActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessNextAuthActionRequest) ProtoMessage() {}

func (x *ProcessNextAuthActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessNextAuthActionRequest.ProtoReflect.Descriptor instead.
func (*ProcessNextAuthActionRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{10}
}

func (x *ProcessNextAuthActionRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *ProcessNextAuthActionRequest) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

func (x *ProcessNextAuthActionRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessNextAuthActionRequest) GetTransactions() []*Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type ProcessNextAuthActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Required:
	NextAction AuthAction `protobuf:"varint,2,opt,name=next_action,json=nextAction,proto3,enum=authentication.v3.AuthAction" json:"next_action,omitempty"`
	// Optional: only available when the next_action is:
	//   - AUTH_ACTION_WAIT_FOR_RESULT
	AuthSessionId string `protobuf:"bytes,3,opt,name=auth_session_id,json=authSessionId,proto3" json:"auth_session_id,omitempty"`
}

func (x *ProcessNextAuthActionResponse) Reset() {
	*x = ProcessNextAuthActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessNextAuthActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessNextAuthActionResponse) ProtoMessage() {}

func (x *ProcessNextAuthActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessNextAuthActionResponse.ProtoReflect.Descriptor instead.
func (*ProcessNextAuthActionResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{11}
}

func (x *ProcessNextAuthActionResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *ProcessNextAuthActionResponse) GetNextAction() AuthAction {
	if x != nil {
		return x.NextAction
	}
	return AuthAction_AUTH_ACTION_UNSPECIFIED
}

func (x *ProcessNextAuthActionResponse) GetAuthSessionId() string {
	if x != nil {
		return x.AuthSessionId
	}
	return ""
}

type GetAuthSessionStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	AuthSessionId string `protobuf:"bytes,1,opt,name=auth_session_id,json=authSessionId,proto3" json:"auth_session_id,omitempty"`
}

func (x *GetAuthSessionStatusRequest) Reset() {
	*x = GetAuthSessionStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthSessionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthSessionStatusRequest) ProtoMessage() {}

func (x *GetAuthSessionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthSessionStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAuthSessionStatusRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{12}
}

func (x *GetAuthSessionStatusRequest) GetAuthSessionId() string {
	if x != nil {
		return x.AuthSessionId
	}
	return ""
}

type GetAuthSessionStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Requried:
	Status AuthStatus `protobuf:"varint,2,opt,name=status,proto3,enum=authentication.v3.AuthStatus" json:"status,omitempty"`
}

func (x *GetAuthSessionStatusResponse) Reset() {
	*x = GetAuthSessionStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthSessionStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthSessionStatusResponse) ProtoMessage() {}

func (x *GetAuthSessionStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthSessionStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAuthSessionStatusResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{13}
}

func (x *GetAuthSessionStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetAuthSessionStatusResponse) GetStatus() AuthStatus {
	if x != nil {
		return x.Status
	}
	return AuthStatus_AUTH_SESSION_STATUS_UNSPECIFIED
}

type TriggerAuthByFundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	PaymentNo string `protobuf:"bytes,1,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
	// Required:
	TransId int64 `protobuf:"varint,2,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Optional: auth_type requested by fund
	AuthType AuthType `protobuf:"varint,3,opt,name=auth_type,json=authType,proto3,enum=authentication.v3.AuthType" json:"auth_type,omitempty"`
	// Optional: some data returned by fund to trigger a new auth session
	//
	// Types that are assignable to FundExtraData:
	//
	//	*TriggerAuthByFundRequest_Bank
	FundExtraData isTriggerAuthByFundRequest_FundExtraData `protobuf_oneof:"fund_extra_data"`
}

func (x *TriggerAuthByFundRequest) Reset() {
	*x = TriggerAuthByFundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerAuthByFundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerAuthByFundRequest) ProtoMessage() {}

func (x *TriggerAuthByFundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerAuthByFundRequest.ProtoReflect.Descriptor instead.
func (*TriggerAuthByFundRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{14}
}

func (x *TriggerAuthByFundRequest) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

func (x *TriggerAuthByFundRequest) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *TriggerAuthByFundRequest) GetAuthType() AuthType {
	if x != nil {
		return x.AuthType
	}
	return AuthType_AUTH_TYPE_UNSPECIFIED
}

func (m *TriggerAuthByFundRequest) GetFundExtraData() isTriggerAuthByFundRequest_FundExtraData {
	if m != nil {
		return m.FundExtraData
	}
	return nil
}

func (x *TriggerAuthByFundRequest) GetBank() *TriggerAuthByFundRequest_BankExtraData {
	if x, ok := x.GetFundExtraData().(*TriggerAuthByFundRequest_Bank); ok {
		return x.Bank
	}
	return nil
}

type isTriggerAuthByFundRequest_FundExtraData interface {
	isTriggerAuthByFundRequest_FundExtraData()
}

type TriggerAuthByFundRequest_Bank struct {
	Bank *TriggerAuthByFundRequest_BankExtraData `protobuf:"bytes,10,opt,name=bank,proto3,oneof"`
}

func (*TriggerAuthByFundRequest_Bank) isTriggerAuthByFundRequest_FundExtraData() {}

type TriggerAuthByFundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Required:
	NextAction AuthAction `protobuf:"varint,2,opt,name=next_action,json=nextAction,proto3,enum=authentication.v3.AuthAction" json:"next_action,omitempty"`
	// Required:
	AuthSessionId string `protobuf:"bytes,3,opt,name=auth_session_id,json=authSessionId,proto3" json:"auth_session_id,omitempty"`
}

func (x *TriggerAuthByFundResponse) Reset() {
	*x = TriggerAuthByFundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerAuthByFundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerAuthByFundResponse) ProtoMessage() {}

func (x *TriggerAuthByFundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerAuthByFundResponse.ProtoReflect.Descriptor instead.
func (*TriggerAuthByFundResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{15}
}

func (x *TriggerAuthByFundResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *TriggerAuthByFundResponse) GetNextAction() AuthAction {
	if x != nil {
		return x.NextAction
	}
	return AuthAction_AUTH_ACTION_UNSPECIFIED
}

func (x *TriggerAuthByFundResponse) GetAuthSessionId() string {
	if x != nil {
		return x.AuthSessionId
	}
	return ""
}

type SubmitAuthValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	AuthSessionId string `protobuf:"bytes,1,opt,name=auth_session_id,json=authSessionId,proto3" json:"auth_session_id,omitempty"`
	// Required:
	AuthValue string `protobuf:"bytes,2,opt,name=auth_value,json=authValue,proto3" json:"auth_value,omitempty"`
	// Optional
	ExtraData map[string]string `protobuf:"bytes,3,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SubmitAuthValueRequest) Reset() {
	*x = SubmitAuthValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitAuthValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitAuthValueRequest) ProtoMessage() {}

func (x *SubmitAuthValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitAuthValueRequest.ProtoReflect.Descriptor instead.
func (*SubmitAuthValueRequest) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{16}
}

func (x *SubmitAuthValueRequest) GetAuthSessionId() string {
	if x != nil {
		return x.AuthSessionId
	}
	return ""
}

func (x *SubmitAuthValueRequest) GetAuthValue() string {
	if x != nil {
		return x.AuthValue
	}
	return ""
}

func (x *SubmitAuthValueRequest) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

type SubmitAuthValueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required:
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SubmitAuthValueResponse) Reset() {
	*x = SubmitAuthValueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitAuthValueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitAuthValueResponse) ProtoMessage() {}

func (x *SubmitAuthValueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitAuthValueResponse.ProtoReflect.Descriptor instead.
func (*SubmitAuthValueResponse) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{17}
}

func (x *SubmitAuthValueResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type BankAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankCode     string `protobuf:"bytes,1,opt,name=bank_code,json=bankCode,proto3" json:"bank_code,omitempty"`
	BankConnCode string `protobuf:"bytes,2,opt,name=bank_conn_code,json=bankConnCode,proto3" json:"bank_conn_code,omitempty"`
	BindingId    string `protobuf:"bytes,3,opt,name=binding_id,json=bindingId,proto3" json:"binding_id,omitempty"`
}

func (x *BankAsset) Reset() {
	*x = BankAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAsset) ProtoMessage() {}

func (x *BankAsset) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAsset.ProtoReflect.Descriptor instead.
func (*BankAsset) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{18}
}

func (x *BankAsset) GetBankCode() string {
	if x != nil {
		return x.BankCode
	}
	return ""
}

func (x *BankAsset) GetBankConnCode() string {
	if x != nil {
		return x.BankConnCode
	}
	return ""
}

func (x *BankAsset) GetBindingId() string {
	if x != nil {
		return x.BindingId
	}
	return ""
}

type ClientInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientIp   string   `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	DeviceId   string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform   Platform `protobuf:"varint,3,opt,name=platform,proto3,enum=authentication.v3.Platform" json:"platform,omitempty"`
	DeviceOs   DeviceOS `protobuf:"varint,4,opt,name=device_os,json=deviceOs,proto3,enum=authentication.v3.DeviceOS" json:"device_os,omitempty"`
	AppVersion string   `protobuf:"bytes,5,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *ClientInfo) Reset() {
	*x = ClientInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientInfo) ProtoMessage() {}

func (x *ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientInfo.ProtoReflect.Descriptor instead.
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{19}
}

func (x *ClientInfo) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *ClientInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ClientInfo) GetPlatform() Platform {
	if x != nil {
		return x.Platform
	}
	return Platform_PLATFORM_UNSPECIFIED
}

func (x *ClientInfo) GetDeviceOs() DeviceOS {
	if x != nil {
		return x.DeviceOs
	}
	return DeviceOS_DEVICE_OS_UNSPECIFIED
}

func (x *ClientInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type TriggerAuthByFundRequest_BankExtraData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional:
	BcTransId int64 `protobuf:"varint,1,opt,name=bc_trans_id,json=bcTransId,proto3" json:"bc_trans_id,omitempty"`
	// Required:
	ConnChannel int32 `protobuf:"varint,2,opt,name=conn_channel,json=connChannel,proto3" json:"conn_channel,omitempty"`
	// Optional:
	BankTransLevel BankTransLevel `protobuf:"varint,3,opt,name=bank_trans_level,json=bankTransLevel,proto3,enum=authentication.v3.BankTransLevel" json:"bank_trans_level,omitempty"`
	// Optional:
	BcAuthMethod string `protobuf:"bytes,4,opt,name=bc_auth_method,json=bcAuthMethod,proto3" json:"bc_auth_method,omitempty"`
	// Optional:
	BankAuthType BankAuthType `protobuf:"varint,5,opt,name=bank_auth_type,json=bankAuthType,proto3,enum=authentication.v3.BankAuthType" json:"bank_auth_type,omitempty"`
	// Optional: only Required when bank_auth_type is in:
	//   - BANK_AUTH_TYPE_REDIRECT,
	//   - BANK_AUTH_TYPE_APP_TO_APP
	RedirectUrl string `protobuf:"bytes,6,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
}

func (x *TriggerAuthByFundRequest_BankExtraData) Reset() {
	*x = TriggerAuthByFundRequest_BankExtraData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerAuthByFundRequest_BankExtraData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerAuthByFundRequest_BankExtraData) ProtoMessage() {}

func (x *TriggerAuthByFundRequest_BankExtraData) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_payment_authentication_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerAuthByFundRequest_BankExtraData.ProtoReflect.Descriptor instead.
func (*TriggerAuthByFundRequest_BankExtraData) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP(), []int{14, 0}
}

func (x *TriggerAuthByFundRequest_BankExtraData) GetBcTransId() int64 {
	if x != nil {
		return x.BcTransId
	}
	return 0
}

func (x *TriggerAuthByFundRequest_BankExtraData) GetConnChannel() int32 {
	if x != nil {
		return x.ConnChannel
	}
	return 0
}

func (x *TriggerAuthByFundRequest_BankExtraData) GetBankTransLevel() BankTransLevel {
	if x != nil {
		return x.BankTransLevel
	}
	return BankTransLevel_LVL_UNSPECIFIED
}

func (x *TriggerAuthByFundRequest_BankExtraData) GetBcAuthMethod() string {
	if x != nil {
		return x.BcAuthMethod
	}
	return ""
}

func (x *TriggerAuthByFundRequest_BankExtraData) GetBankAuthType() BankAuthType {
	if x != nil {
		return x.BankAuthType
	}
	return BankAuthType_BANK_AUTH_TYPE_UNSPECIFIED
}

func (x *TriggerAuthByFundRequest_BankExtraData) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

var File_external_services_payment_auth_payment_authentication_proto protoreflect.FileDescriptor

var file_external_services_payment_auth_payment_authentication_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33,
	0x1a, 0x2b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x22, 0x36, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f,
	0x22, 0x8d, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x43, 0x0a, 0x0d, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0xd4, 0x01, 0x0a, 0x0b, 0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x47, 0x0a, 0x11, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e,
	0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x10, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x89, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x40, 0x0a,
	0x0d, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12,
	0x38, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5a, 0x0a, 0x0a, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x33, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xbf, 0x03, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x35, 0x0a, 0x08, 0x70, 0x72,
	0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33,
	0x2e, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0b, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x33, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x33, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x57, 0x0a,
	0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x75,
	0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6e, 0x79, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x65, 0x6e, 0x79,
	0x41, 0x75, 0x74, 0x68, 0x6e, 0x22, 0x55, 0x0a, 0x07, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x12, 0x38, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x69, 0x6e, 0x22, 0x65, 0x0a, 0x12,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61,
	0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x22, 0x6b, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x6e, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x61, 0x6e, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73,
	0x22, 0xd4, 0x01, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32,
	0x0a, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x04, 0x62, 0x61,
	0x6e, 0x6b, 0x42, 0x13, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0xbb, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x45, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa4,
	0x04, 0x0a, 0x18, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79,
	0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x4f, 0x0a, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x33, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x46,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x62, 0x61, 0x6e, 0x6b,
	0x1a, 0xaf, 0x02, 0x0a, 0x0d, 0x42, 0x61, 0x6e, 0x6b, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x6e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x4b, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x33, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x63, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x63, 0x41, 0x75,
	0x74, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x45, 0x0a, 0x0e, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x42, 0x11, 0x0a, 0x0f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb3, 0x01, 0x0a, 0x19, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75,
	0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xf6, 0x01, 0x0a, 0x16,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x75, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x57, 0x0a,
	0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x38, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x75, 0x74, 0x68,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x49, 0x0a, 0x17, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x33, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22,
	0x6d, 0x0a, 0x09, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0xda,
	0x01, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x38, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x53,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2a, 0x51, 0x0a, 0x0e, 0x42,
	0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x13, 0x0a,
	0x0f, 0x4c, 0x56, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x56, 0x4c, 0x5f, 0x41, 0x10, 0x01, 0x12, 0x09, 0x0a,
	0x05, 0x4c, 0x56, 0x4c, 0x5f, 0x42, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x56, 0x4c, 0x5f,
	0x43, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x56, 0x4c, 0x5f, 0x44, 0x10, 0x04, 0x2a, 0xba,
	0x01, 0x0a, 0x0c, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x1a, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x17, 0x0a, 0x13, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x44, 0x4f, 0x54, 0x50, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x42, 0x41, 0x4e, 0x4b,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x49, 0x52,
	0x45, 0x43, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x4f, 0x50, 0x10, 0x03, 0x12, 0x1d, 0x0a,
	0x19, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x05, 0x2a, 0x48, 0x0a, 0x08, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x4c, 0x41, 0x54, 0x46,
	0x4f, 0x52, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x5a, 0x50,
	0x41, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x5a, 0x50, 0x49, 0x10, 0x02, 0x2a, 0x4f, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f,
	0x53, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x53,
	0x5f, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x32, 0xb7, 0x06, 0x0a, 0x1c, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x70, 0x61,
	0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x68, 0x12, 0x2c, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x33, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e,
	0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x15, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e,
	0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x11, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x2b, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x46, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x41, 0x75, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x77, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x68, 0x12, 0x2b,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x33, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x56, 0x5a, 0x54, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61, 0x6c, 0x6f, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_payment_auth_payment_authentication_proto_rawDescOnce sync.Once
	file_external_services_payment_auth_payment_authentication_proto_rawDescData = file_external_services_payment_auth_payment_authentication_proto_rawDesc
)

func file_external_services_payment_auth_payment_authentication_proto_rawDescGZIP() []byte {
	file_external_services_payment_auth_payment_authentication_proto_rawDescOnce.Do(func() {
		file_external_services_payment_auth_payment_authentication_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_payment_auth_payment_authentication_proto_rawDescData)
	})
	return file_external_services_payment_auth_payment_authentication_proto_rawDescData
}

var file_external_services_payment_auth_payment_authentication_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_external_services_payment_auth_payment_authentication_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_external_services_payment_auth_payment_authentication_proto_goTypes = []any{
	(BankTransLevel)(0),                            // 0: authentication.v3.BankTransLevel
	(BankAuthType)(0),                              // 1: authentication.v3.BankAuthType
	(Platform)(0),                                  // 2: authentication.v3.Platform
	(DeviceOS)(0),                                  // 3: authentication.v3.DeviceOS
	(*GetProcessingAuthRequest)(nil),               // 4: authentication.v3.GetProcessingAuthRequest
	(*GetAuthContextRequest)(nil),                  // 5: authentication.v3.GetAuthContextRequest
	(*GetAuthContextResponse)(nil),                 // 6: authentication.v3.GetAuthContextResponse
	(*AuthSession)(nil),                            // 7: authentication.v3.AuthSession
	(*GetProcessingAuthResponse)(nil),              // 8: authentication.v3.GetProcessingAuthResponse
	(*PreparePaymentAuthRequest)(nil),              // 9: authentication.v3.PreparePaymentAuthRequest
	(*PreAuth)(nil),                                // 10: authentication.v3.PreAuth
	(*AdditionalAuthData)(nil),                     // 11: authentication.v3.AdditionalAuthData
	(*PreparePaymentAuthResponse)(nil),             // 12: authentication.v3.PreparePaymentAuthResponse
	(*Transaction)(nil),                            // 13: authentication.v3.Transaction
	(*ProcessNextAuthActionRequest)(nil),           // 14: authentication.v3.ProcessNextAuthActionRequest
	(*ProcessNextAuthActionResponse)(nil),          // 15: authentication.v3.ProcessNextAuthActionResponse
	(*GetAuthSessionStatusRequest)(nil),            // 16: authentication.v3.GetAuthSessionStatusRequest
	(*GetAuthSessionStatusResponse)(nil),           // 17: authentication.v3.GetAuthSessionStatusResponse
	(*TriggerAuthByFundRequest)(nil),               // 18: authentication.v3.TriggerAuthByFundRequest
	(*TriggerAuthByFundResponse)(nil),              // 19: authentication.v3.TriggerAuthByFundResponse
	(*SubmitAuthValueRequest)(nil),                 // 20: authentication.v3.SubmitAuthValueRequest
	(*SubmitAuthValueResponse)(nil),                // 21: authentication.v3.SubmitAuthValueResponse
	(*BankAsset)(nil),                              // 22: authentication.v3.BankAsset
	(*ClientInfo)(nil),                             // 23: authentication.v3.ClientInfo
	nil,                                            // 24: authentication.v3.GetProcessingAuthResponse.ExtraDataEntry
	(*TriggerAuthByFundRequest_BankExtraData)(nil), // 25: authentication.v3.TriggerAuthByFundRequest.BankExtraData
	nil,             // 26: authentication.v3.SubmitAuthValueRequest.ExtraDataEntry
	(*Error)(nil),   // 27: authentication.v3.Error
	(AuthType)(0),   // 28: authentication.v3.AuthType
	(AuthStatus)(0), // 29: authentication.v3.AuthStatus
	(Provider)(0),   // 30: authentication.v3.Provider
	(AuthAction)(0), // 31: authentication.v3.AuthAction
}
var file_external_services_payment_auth_payment_authentication_proto_depIdxs = []int32{
	27, // 0: authentication.v3.GetAuthContextResponse.error:type_name -> authentication.v3.Error
	7,  // 1: authentication.v3.GetAuthContextResponse.auth_sessions:type_name -> authentication.v3.AuthSession
	28, // 2: authentication.v3.AuthSession.trigger_auth_type:type_name -> authentication.v3.AuthType
	28, // 3: authentication.v3.AuthSession.final_auth_types:type_name -> authentication.v3.AuthType
	29, // 4: authentication.v3.AuthSession.status:type_name -> authentication.v3.AuthStatus
	27, // 5: authentication.v3.GetProcessingAuthResponse.error:type_name -> authentication.v3.Error
	30, // 6: authentication.v3.GetProcessingAuthResponse.auth_provider:type_name -> authentication.v3.Provider
	28, // 7: authentication.v3.GetProcessingAuthResponse.auth_type:type_name -> authentication.v3.AuthType
	24, // 8: authentication.v3.GetProcessingAuthResponse.extra_data:type_name -> authentication.v3.GetProcessingAuthResponse.ExtraDataEntry
	10, // 9: authentication.v3.PreparePaymentAuthRequest.pre_auth:type_name -> authentication.v3.PreAuth
	23, // 10: authentication.v3.PreparePaymentAuthRequest.client_info:type_name -> authentication.v3.ClientInfo
	13, // 11: authentication.v3.PreparePaymentAuthRequest.transactions:type_name -> authentication.v3.Transaction
	11, // 12: authentication.v3.PreparePaymentAuthRequest.additional_auth_data:type_name -> authentication.v3.AdditionalAuthData
	28, // 13: authentication.v3.PreAuth.auth_type:type_name -> authentication.v3.AuthType
	27, // 14: authentication.v3.PreparePaymentAuthResponse.error:type_name -> authentication.v3.Error
	22, // 15: authentication.v3.Transaction.bank:type_name -> authentication.v3.BankAsset
	13, // 16: authentication.v3.ProcessNextAuthActionRequest.transactions:type_name -> authentication.v3.Transaction
	27, // 17: authentication.v3.ProcessNextAuthActionResponse.error:type_name -> authentication.v3.Error
	31, // 18: authentication.v3.ProcessNextAuthActionResponse.next_action:type_name -> authentication.v3.AuthAction
	27, // 19: authentication.v3.GetAuthSessionStatusResponse.error:type_name -> authentication.v3.Error
	29, // 20: authentication.v3.GetAuthSessionStatusResponse.status:type_name -> authentication.v3.AuthStatus
	28, // 21: authentication.v3.TriggerAuthByFundRequest.auth_type:type_name -> authentication.v3.AuthType
	25, // 22: authentication.v3.TriggerAuthByFundRequest.bank:type_name -> authentication.v3.TriggerAuthByFundRequest.BankExtraData
	27, // 23: authentication.v3.TriggerAuthByFundResponse.error:type_name -> authentication.v3.Error
	31, // 24: authentication.v3.TriggerAuthByFundResponse.next_action:type_name -> authentication.v3.AuthAction
	26, // 25: authentication.v3.SubmitAuthValueRequest.extra_data:type_name -> authentication.v3.SubmitAuthValueRequest.ExtraDataEntry
	27, // 26: authentication.v3.SubmitAuthValueResponse.error:type_name -> authentication.v3.Error
	2,  // 27: authentication.v3.ClientInfo.platform:type_name -> authentication.v3.Platform
	3,  // 28: authentication.v3.ClientInfo.device_os:type_name -> authentication.v3.DeviceOS
	0,  // 29: authentication.v3.TriggerAuthByFundRequest.BankExtraData.bank_trans_level:type_name -> authentication.v3.BankTransLevel
	1,  // 30: authentication.v3.TriggerAuthByFundRequest.BankExtraData.bank_auth_type:type_name -> authentication.v3.BankAuthType
	9,  // 31: authentication.v3.PaymentAuthenticationService.PreparePaymentAuth:input_type -> authentication.v3.PreparePaymentAuthRequest
	14, // 32: authentication.v3.PaymentAuthenticationService.ProcessNextAuthAction:input_type -> authentication.v3.ProcessNextAuthActionRequest
	18, // 33: authentication.v3.PaymentAuthenticationService.TriggerAuthByFund:input_type -> authentication.v3.TriggerAuthByFundRequest
	20, // 34: authentication.v3.PaymentAuthenticationService.SubmitAuthValue:input_type -> authentication.v3.SubmitAuthValueRequest
	16, // 35: authentication.v3.PaymentAuthenticationService.GetAuthSessionStatus:input_type -> authentication.v3.GetAuthSessionStatusRequest
	4,  // 36: authentication.v3.PaymentAuthenticationService.GetProcessingAuth:input_type -> authentication.v3.GetProcessingAuthRequest
	5,  // 37: authentication.v3.PaymentAuthenticationService.GetAuthContext:input_type -> authentication.v3.GetAuthContextRequest
	12, // 38: authentication.v3.PaymentAuthenticationService.PreparePaymentAuth:output_type -> authentication.v3.PreparePaymentAuthResponse
	15, // 39: authentication.v3.PaymentAuthenticationService.ProcessNextAuthAction:output_type -> authentication.v3.ProcessNextAuthActionResponse
	19, // 40: authentication.v3.PaymentAuthenticationService.TriggerAuthByFund:output_type -> authentication.v3.TriggerAuthByFundResponse
	21, // 41: authentication.v3.PaymentAuthenticationService.SubmitAuthValue:output_type -> authentication.v3.SubmitAuthValueResponse
	17, // 42: authentication.v3.PaymentAuthenticationService.GetAuthSessionStatus:output_type -> authentication.v3.GetAuthSessionStatusResponse
	8,  // 43: authentication.v3.PaymentAuthenticationService.GetProcessingAuth:output_type -> authentication.v3.GetProcessingAuthResponse
	6,  // 44: authentication.v3.PaymentAuthenticationService.GetAuthContext:output_type -> authentication.v3.GetAuthContextResponse
	38, // [38:45] is the sub-list for method output_type
	31, // [31:38] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_external_services_payment_auth_payment_authentication_proto_init() }
func file_external_services_payment_auth_payment_authentication_proto_init() {
	if File_external_services_payment_auth_payment_authentication_proto != nil {
		return
	}
	file_external_services_payment_auth_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetProcessingAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetAuthContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetAuthContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AuthSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetProcessingAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PreparePaymentAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PreAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*AdditionalAuthData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*PreparePaymentAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Transaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ProcessNextAuthActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ProcessNextAuthActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetAuthSessionStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetAuthSessionStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerAuthByFundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerAuthByFundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*SubmitAuthValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*SubmitAuthValueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*BankAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*ClientInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_auth_payment_authentication_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerAuthByFundRequest_BankExtraData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_external_services_payment_auth_payment_authentication_proto_msgTypes[9].OneofWrappers = []any{
		(*Transaction_Bank)(nil),
	}
	file_external_services_payment_auth_payment_authentication_proto_msgTypes[14].OneofWrappers = []any{
		(*TriggerAuthByFundRequest_Bank)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_payment_auth_payment_authentication_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_payment_auth_payment_authentication_proto_goTypes,
		DependencyIndexes: file_external_services_payment_auth_payment_authentication_proto_depIdxs,
		EnumInfos:         file_external_services_payment_auth_payment_authentication_proto_enumTypes,
		MessageInfos:      file_external_services_payment_auth_payment_authentication_proto_msgTypes,
	}.Build()
	File_external_services_payment_auth_payment_authentication_proto = out.File
	file_external_services_payment_auth_payment_authentication_proto_rawDesc = nil
	file_external_services_payment_auth_payment_authentication_proto_goTypes = nil
	file_external_services_payment_auth_payment_authentication_proto_depIdxs = nil
}
