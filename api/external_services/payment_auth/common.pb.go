// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/payment_auth/common.proto

package payment_auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorCode int32

const (
	ErrorCode_ER_CODE_UNSPECIFIED ErrorCode = 0
	// Business Error
	ErrorCode_ER_CODE_SUCCESS                     ErrorCode = 1
	ErrorCode_ER_CODE_INVALID_DATA                ErrorCode = 2
	ErrorCode_ER_CODE_UNSUPPORTED                 ErrorCode = 3
	ErrorCode_ER_CODE_NOT_FOUND_AUTH_SESSION      ErrorCode = 4
	ErrorCode_ER_CODE_WRONG_OTP_RETRYABLE         ErrorCode = 10
	ErrorCode_ER_CODE_FAILED_TO_AUTHENTICATE_USER ErrorCode = 11
	ErrorCode_ER_CODE_AUTH_EXPIRED                ErrorCode = 12
	// System Error
	ErrorCode_ER_CODE_INTERNAL             ErrorCode = 1000
	ErrorCode_ER_CODE_DEPENDECY_ERROR      ErrorCode = 1001 // Database, Redis, ...
	ErrorCode_ER_CODE_RISK_UNAVAILABLE     ErrorCode = 1002
	ErrorCode_ER_CODE_RISK_FAILED          ErrorCode = 1003
	ErrorCode_ER_CODE_UM_UNAVAILABLE       ErrorCode = 1005
	ErrorCode_ER_CODE_UM_FAILED            ErrorCode = 1006
	ErrorCode_ER_CODE_BANK_SOF_UNAVAILABLE ErrorCode = 1007
	ErrorCode_ER_CODE_BANK_SOF_FAILED      ErrorCode = 1008
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0:    "ER_CODE_UNSPECIFIED",
		1:    "ER_CODE_SUCCESS",
		2:    "ER_CODE_INVALID_DATA",
		3:    "ER_CODE_UNSUPPORTED",
		4:    "ER_CODE_NOT_FOUND_AUTH_SESSION",
		10:   "ER_CODE_WRONG_OTP_RETRYABLE",
		11:   "ER_CODE_FAILED_TO_AUTHENTICATE_USER",
		12:   "ER_CODE_AUTH_EXPIRED",
		1000: "ER_CODE_INTERNAL",
		1001: "ER_CODE_DEPENDECY_ERROR",
		1002: "ER_CODE_RISK_UNAVAILABLE",
		1003: "ER_CODE_RISK_FAILED",
		1005: "ER_CODE_UM_UNAVAILABLE",
		1006: "ER_CODE_UM_FAILED",
		1007: "ER_CODE_BANK_SOF_UNAVAILABLE",
		1008: "ER_CODE_BANK_SOF_FAILED",
	}
	ErrorCode_value = map[string]int32{
		"ER_CODE_UNSPECIFIED":                 0,
		"ER_CODE_SUCCESS":                     1,
		"ER_CODE_INVALID_DATA":                2,
		"ER_CODE_UNSUPPORTED":                 3,
		"ER_CODE_NOT_FOUND_AUTH_SESSION":      4,
		"ER_CODE_WRONG_OTP_RETRYABLE":         10,
		"ER_CODE_FAILED_TO_AUTHENTICATE_USER": 11,
		"ER_CODE_AUTH_EXPIRED":                12,
		"ER_CODE_INTERNAL":                    1000,
		"ER_CODE_DEPENDECY_ERROR":             1001,
		"ER_CODE_RISK_UNAVAILABLE":            1002,
		"ER_CODE_RISK_FAILED":                 1003,
		"ER_CODE_UM_UNAVAILABLE":              1005,
		"ER_CODE_UM_FAILED":                   1006,
		"ER_CODE_BANK_SOF_UNAVAILABLE":        1007,
		"ER_CODE_BANK_SOF_FAILED":             1008,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_common_proto_enumTypes[0].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_common_proto_enumTypes[0]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_common_proto_rawDescGZIP(), []int{0}
}

type AuthType int32

const (
	AuthType_AUTH_TYPE_UNSPECIFIED AuthType = 0
	// Provided by UM
	AuthType_AUTH_TYPE_UM_PIN            AuthType = 1
	AuthType_AUTH_TYPE_UM_BIO            AuthType = 2
	AuthType_AUTH_TYPE_UM_OTP            AuthType = 3
	AuthType_AUTH_TYPE_UM_FACE_AUTH      AuthType = 4
	AuthType_AUTH_TYPE_UM_KYC            AuthType = 5
	AuthType_AUTH_TYPE_UM_ADJUST_KYC     AuthType = 6
	AuthType_AUTH_TYPE_UM_NFC            AuthType = 7
	AuthType_AUTH_TYPE_UM_KYC_NFC        AuthType = 8
	AuthType_AUTH_TYPE_UM_ADJUST_KYC_NFC AuthType = 9
	AuthType_AUTH_TYPE_UM_SMART_OTP      AuthType = 10
	AuthType_AUTH_TYPE_UM_SMART_OTP_FACE AuthType = 11
	AuthType_AUTH_TYPE_UM_OTP_V2         AuthType = 12
	// Provided by Bank
	AuthType_AUTH_TYPE_BANK_SMS_OTP  AuthType = 101
	AuthType_AUTH_TYPE_BANK_D_OTP    AuthType = 102
	AuthType_AUTH_TYPE_BANK_ROP      AuthType = 103
	AuthType_AUTH_TYPE_BANK_REDIRECT AuthType = 104
	AuthType_AUTH_TYPE_BANK_APP      AuthType = 105
)

// Enum value maps for AuthType.
var (
	AuthType_name = map[int32]string{
		0:   "AUTH_TYPE_UNSPECIFIED",
		1:   "AUTH_TYPE_UM_PIN",
		2:   "AUTH_TYPE_UM_BIO",
		3:   "AUTH_TYPE_UM_OTP",
		4:   "AUTH_TYPE_UM_FACE_AUTH",
		5:   "AUTH_TYPE_UM_KYC",
		6:   "AUTH_TYPE_UM_ADJUST_KYC",
		7:   "AUTH_TYPE_UM_NFC",
		8:   "AUTH_TYPE_UM_KYC_NFC",
		9:   "AUTH_TYPE_UM_ADJUST_KYC_NFC",
		10:  "AUTH_TYPE_UM_SMART_OTP",
		11:  "AUTH_TYPE_UM_SMART_OTP_FACE",
		12:  "AUTH_TYPE_UM_OTP_V2",
		101: "AUTH_TYPE_BANK_SMS_OTP",
		102: "AUTH_TYPE_BANK_D_OTP",
		103: "AUTH_TYPE_BANK_ROP",
		104: "AUTH_TYPE_BANK_REDIRECT",
		105: "AUTH_TYPE_BANK_APP",
	}
	AuthType_value = map[string]int32{
		"AUTH_TYPE_UNSPECIFIED":       0,
		"AUTH_TYPE_UM_PIN":            1,
		"AUTH_TYPE_UM_BIO":            2,
		"AUTH_TYPE_UM_OTP":            3,
		"AUTH_TYPE_UM_FACE_AUTH":      4,
		"AUTH_TYPE_UM_KYC":            5,
		"AUTH_TYPE_UM_ADJUST_KYC":     6,
		"AUTH_TYPE_UM_NFC":            7,
		"AUTH_TYPE_UM_KYC_NFC":        8,
		"AUTH_TYPE_UM_ADJUST_KYC_NFC": 9,
		"AUTH_TYPE_UM_SMART_OTP":      10,
		"AUTH_TYPE_UM_SMART_OTP_FACE": 11,
		"AUTH_TYPE_UM_OTP_V2":         12,
		"AUTH_TYPE_BANK_SMS_OTP":      101,
		"AUTH_TYPE_BANK_D_OTP":        102,
		"AUTH_TYPE_BANK_ROP":          103,
		"AUTH_TYPE_BANK_REDIRECT":     104,
		"AUTH_TYPE_BANK_APP":          105,
	}
)

func (x AuthType) Enum() *AuthType {
	p := new(AuthType)
	*p = x
	return p
}

func (x AuthType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_common_proto_enumTypes[1].Descriptor()
}

func (AuthType) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_common_proto_enumTypes[1]
}

func (x AuthType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthType.Descriptor instead.
func (AuthType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_common_proto_rawDescGZIP(), []int{1}
}

type AuthAction int32

const (
	AuthAction_AUTH_ACTION_UNSPECIFIED     AuthAction = 0
	AuthAction_AUTH_ACTION_BYPASS          AuthAction = 1
	AuthAction_AUTH_ACTION_WAIT_FOR_RESULT AuthAction = 2
	AuthAction_AUTH_ACTION_FAIL            AuthAction = 3
)

// Enum value maps for AuthAction.
var (
	AuthAction_name = map[int32]string{
		0: "AUTH_ACTION_UNSPECIFIED",
		1: "AUTH_ACTION_BYPASS",
		2: "AUTH_ACTION_WAIT_FOR_RESULT",
		3: "AUTH_ACTION_FAIL",
	}
	AuthAction_value = map[string]int32{
		"AUTH_ACTION_UNSPECIFIED":     0,
		"AUTH_ACTION_BYPASS":          1,
		"AUTH_ACTION_WAIT_FOR_RESULT": 2,
		"AUTH_ACTION_FAIL":            3,
	}
)

func (x AuthAction) Enum() *AuthAction {
	p := new(AuthAction)
	*p = x
	return p
}

func (x AuthAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthAction) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_common_proto_enumTypes[2].Descriptor()
}

func (AuthAction) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_common_proto_enumTypes[2]
}

func (x AuthAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthAction.Descriptor instead.
func (AuthAction) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_common_proto_rawDescGZIP(), []int{2}
}

type AuthStatus int32

const (
	AuthStatus_AUTH_SESSION_STATUS_UNSPECIFIED AuthStatus = 0
	AuthStatus_AUTH_SESSION_STATUS_PROCESSING  AuthStatus = 1
	AuthStatus_AUTH_SESSION_STATUS_SUCCESS     AuthStatus = 2
	AuthStatus_AUTH_SESSION_STATUS_FAILED      AuthStatus = 3
)

// Enum value maps for AuthStatus.
var (
	AuthStatus_name = map[int32]string{
		0: "AUTH_SESSION_STATUS_UNSPECIFIED",
		1: "AUTH_SESSION_STATUS_PROCESSING",
		2: "AUTH_SESSION_STATUS_SUCCESS",
		3: "AUTH_SESSION_STATUS_FAILED",
	}
	AuthStatus_value = map[string]int32{
		"AUTH_SESSION_STATUS_UNSPECIFIED": 0,
		"AUTH_SESSION_STATUS_PROCESSING":  1,
		"AUTH_SESSION_STATUS_SUCCESS":     2,
		"AUTH_SESSION_STATUS_FAILED":      3,
	}
)

func (x AuthStatus) Enum() *AuthStatus {
	p := new(AuthStatus)
	*p = x
	return p
}

func (x AuthStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_common_proto_enumTypes[3].Descriptor()
}

func (AuthStatus) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_common_proto_enumTypes[3]
}

func (x AuthStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthStatus.Descriptor instead.
func (AuthStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_common_proto_rawDescGZIP(), []int{3}
}

type Provider int32

const (
	Provider_PROVIDER_UNSPECIFIED Provider = 0
	Provider_PROVIDER_UM          Provider = 1
	Provider_PROVIDER_BANK        Provider = 2
)

// Enum value maps for Provider.
var (
	Provider_name = map[int32]string{
		0: "PROVIDER_UNSPECIFIED",
		1: "PROVIDER_UM",
		2: "PROVIDER_BANK",
	}
	Provider_value = map[string]int32{
		"PROVIDER_UNSPECIFIED": 0,
		"PROVIDER_UM":          1,
		"PROVIDER_BANK":        2,
	}
)

func (x Provider) Enum() *Provider {
	p := new(Provider)
	*p = x
	return p
}

func (x Provider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provider) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_auth_common_proto_enumTypes[4].Descriptor()
}

func (Provider) Type() protoreflect.EnumType {
	return &file_external_services_payment_auth_common_proto_enumTypes[4]
}

func (x Provider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provider.Descriptor instead.
func (Provider) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_auth_common_proto_rawDescGZIP(), []int{4}
}

type Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required.
	Code ErrorCode `protobuf:"varint,1,opt,name=code,proto3,enum=authentication.v3.ErrorCode" json:"code,omitempty"`
	// Required. Error description for debugging and monitoring.
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// Optional. User-friendly message which is helpful to user.
	UserReturnMessage string `protobuf:"bytes,3,opt,name=user_return_message,json=userReturnMessage,proto3" json:"user_return_message,omitempty"`
}

func (x *Error) Reset() {
	*x = Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_auth_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_auth_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_external_services_payment_auth_common_proto_rawDescGZIP(), []int{0}
}

func (x *Error) GetCode() ErrorCode {
	if x != nil {
		return x.Code
	}
	return ErrorCode_ER_CODE_UNSPECIFIED
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Error) GetUserReturnMessage() string {
	if x != nil {
		return x.UserReturnMessage
	}
	return ""
}

var File_external_services_payment_auth_common_proto protoreflect.FileDescriptor

var file_external_services_payment_auth_common_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33,
	0x22, 0x83, 0x01, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0xd8, 0x03, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52,
	0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x52, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x52,
	0x45, 0x54, 0x52, 0x59, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x0a, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x52,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x10, 0x0b, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x15, 0x0a,
	0x10, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0xe8, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x44, 0x45, 0x50, 0x45, 0x4e, 0x44, 0x45, 0x43, 0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0xe9, 0x07, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0xea,
	0x07, 0x12, 0x18, 0x0a, 0x13, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xeb, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x45,
	0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49,
	0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0xed, 0x07, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x52, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xee, 0x07,
	0x12, 0x21, 0x0a, 0x1c, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b,
	0x5f, 0x53, 0x4f, 0x46, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0xef, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42,
	0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x4f, 0x46, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xf0,
	0x07, 0x2a, 0xe0, 0x03, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x15, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x01, 0x12,
	0x14, 0x0a, 0x10, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f,
	0x42, 0x49, 0x4f, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x46, 0x41, 0x43, 0x45,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x05, 0x12, 0x1b, 0x0a,
	0x17, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x41, 0x44,
	0x4a, 0x55, 0x53, 0x54, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x4e, 0x46, 0x43, 0x10, 0x07,
	0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d,
	0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x46, 0x43, 0x10, 0x08, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x41, 0x44, 0x4a, 0x55, 0x53,
	0x54, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x46, 0x43, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x53, 0x4d, 0x41, 0x52,
	0x54, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x0a, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x4f, 0x54,
	0x50, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4d, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x32, 0x10,
	0x0c, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42,
	0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x65, 0x12, 0x18, 0x0a,
	0x14, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f,
	0x44, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x66, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x52, 0x4f, 0x50, 0x10, 0x67, 0x12,
	0x1b, 0x0a, 0x17, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e,
	0x4b, 0x5f, 0x52, 0x45, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x10, 0x68, 0x12, 0x16, 0x0a, 0x12,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41,
	0x50, 0x50, 0x10, 0x69, 0x2a, 0x78, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42,
	0x59, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x03, 0x2a, 0x96,
	0x01, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x1f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x45, 0x53, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53,
	0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x48, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a,
	0x0b, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x55, 0x4d, 0x10, 0x01, 0x12, 0x11,
	0x0a, 0x0d, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10,
	0x02, 0x42, 0x56, 0x5a, 0x54, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61, 0x6c, 0x6f,
	0x70, 0x61, 0x79, 0x2e, 0x76, 0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_external_services_payment_auth_common_proto_rawDescOnce sync.Once
	file_external_services_payment_auth_common_proto_rawDescData = file_external_services_payment_auth_common_proto_rawDesc
)

func file_external_services_payment_auth_common_proto_rawDescGZIP() []byte {
	file_external_services_payment_auth_common_proto_rawDescOnce.Do(func() {
		file_external_services_payment_auth_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_payment_auth_common_proto_rawDescData)
	})
	return file_external_services_payment_auth_common_proto_rawDescData
}

var file_external_services_payment_auth_common_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_external_services_payment_auth_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_external_services_payment_auth_common_proto_goTypes = []any{
	(ErrorCode)(0),  // 0: authentication.v3.ErrorCode
	(AuthType)(0),   // 1: authentication.v3.AuthType
	(AuthAction)(0), // 2: authentication.v3.AuthAction
	(AuthStatus)(0), // 3: authentication.v3.AuthStatus
	(Provider)(0),   // 4: authentication.v3.Provider
	(*Error)(nil),   // 5: authentication.v3.Error
}
var file_external_services_payment_auth_common_proto_depIdxs = []int32{
	0, // 0: authentication.v3.Error.code:type_name -> authentication.v3.ErrorCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_external_services_payment_auth_common_proto_init() }
func file_external_services_payment_auth_common_proto_init() {
	if File_external_services_payment_auth_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_payment_auth_common_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_payment_auth_common_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_external_services_payment_auth_common_proto_goTypes,
		DependencyIndexes: file_external_services_payment_auth_common_proto_depIdxs,
		EnumInfos:         file_external_services_payment_auth_common_proto_enumTypes,
		MessageInfos:      file_external_services_payment_auth_common_proto_msgTypes,
	}.Build()
	File_external_services_payment_auth_common_proto = out.File
	file_external_services_payment_auth_common_proto_rawDesc = nil
	file_external_services_payment_auth_common_proto_goTypes = nil
	file_external_services_payment_auth_common_proto_depIdxs = nil
}
