syntax = "proto3";

option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth";

import "external_services/payment_auth/common.proto";

package authentication.v3;

service PaymentAuthenticationService {
  rpc PreparePaymentAuth(PreparePaymentAuthRequest) returns (PreparePaymentAuthResponse);
  rpc ProcessNextAuthAction(ProcessNextAuthActionRequest) returns (ProcessNextAuthActionResponse);
  rpc TriggerAuthByFund(TriggerAuthByFundRequest) returns (TriggerAuthByFundResponse);
  rpc SubmitAuthValue(SubmitAuthValueRequest) returns (SubmitAuthValueResponse);
  rpc GetAuthSessionStatus(GetAuthSessionStatusRequest) returns (GetAuthSessionStatusResponse);
  rpc GetProcessingAuth(GetProcessingAuthRequest) returns (GetProcessingAuthResponse);
  rpc GetAuthContext(GetAuthContextRequest) returns (GetAuthContextResponse);
}

message GetProcessingAuthRequest {
  // Required
  string payment_no = 1;
}

message GetAuthContextRequest {
  // Required
  string payment_no = 1;
}

message GetAuthContextResponse {
  // Required
  Error error = 1;

  // Required
  repeated AuthSession auth_sessions = 2;
}

message AuthSession{
  AuthType trigger_auth_type = 1;
  repeated AuthType final_auth_types = 2;
  AuthStatus status = 3;
}

message GetProcessingAuthResponse {
  // Required
  Error error = 1;

  // Optional
  string auth_session_id = 2;

  // Optional
  Provider auth_provider = 3;

  // Optional
  AuthType auth_type = 4;

  // Optional
  map<string, string> extra_data = 5;
}

message PreparePaymentAuthRequest {
  // Required: User who initiating the payment and owner of all the funds (exception the final destination).
  string zalopay_id = 1;

  // Required:
  string order_no = 2;

  // Required
  string payment_no = 3;

  // Required: the authentication method user must have done to submit payment.
  PreAuth pre_auth = 4;

  // Required: Total amount charged on initiating user's fund.
  int64 amount = 5;

  // Optional: for order from AgreementPay, otherwise Required.
  ClientInfo client_info = 6;

  // Required: List of transaction being executed in the payment.
  repeated Transaction transactions = 7;

  // Optional: depend on sources of fund
  AdditionalAuthData additional_auth_data = 8;
  // Required
  bool deny_authn = 9;
}

message PreAuth {
  // Required
  AuthType auth_type = 1;

  // Required
  string pin = 2;
}

message AdditionalAuthData {
  // Optional: all transaction from Wallet must contain this data.
  string auth_info_encoded = 1;

  // Optional: Submit to UM in extra_data to trigger some auth methods.
  string client_source = 2;
}

message PreparePaymentAuthResponse {
  // Required:
  Error error = 1;

  // Required:
  //  + True: payment can continue processing without authentication.
  //  + False: payment must follow ProcessNextAuthAction before submitting transactions.
  bool can_bypass = 2;
}

message Transaction {
  int32 app_id = 1;
  string product_code = 2;
  int64 amount = 3;
  string source_asset_type = 4;

  // Optional. Data following by source asset_type;
  oneof source_asset_data {
    BankAsset bank = 11;
  }
}

message ProcessNextAuthActionRequest {
  // Required:
  string order_no = 1;

  // Required
  string payment_no = 2;

  // Required: Idempotency control, each request_id corresponding to one execution of one transaction or many transactions (in multibill order).
  string request_id = 3;

  // Required:
  repeated Transaction transactions = 4;
}

message ProcessNextAuthActionResponse {
  // Required:
  Error error = 1;

  // Required:
  AuthAction next_action = 2;

  // Optional: only available when the next_action is:
  //    - AUTH_ACTION_WAIT_FOR_RESULT
  string auth_session_id = 3;
}

message GetAuthSessionStatusRequest {
  // Required:
  string auth_session_id = 1;
}

message GetAuthSessionStatusResponse {
  // Required:
  Error error = 1;

  // Requried:
  AuthStatus status = 2;
}

message TriggerAuthByFundRequest {
  // Required:
  string payment_no = 1;

  // Required:
  int64 trans_id = 2;

  // Optional: auth_type requested by fund
  AuthType auth_type = 3;

  // Optional: some data returned by fund to trigger a new auth session
  oneof fund_extra_data {
    BankExtraData bank = 10;
  }

  message BankExtraData {
    // Optional:
    int64 bc_trans_id = 1;

    // Required:
    int32 conn_channel = 2;

    // Optional:
    BankTransLevel bank_trans_level = 3;

    // Optional:
    string bc_auth_method = 4;

    // Optional:
    BankAuthType bank_auth_type = 5;

    // Optional: only Required when bank_auth_type is in:
    //    - BANK_AUTH_TYPE_REDIRECT,
    //    - BANK_AUTH_TYPE_APP_TO_APP
    string redirect_url = 6;
  }
}

enum BankTransLevel {
  LVL_UNSPECIFIED = 0;
  LVL_A = 1;
  LVL_B = 2;
  LVL_C = 3;
  LVL_D = 4;
}

enum BankAuthType {
  BANK_AUTH_TYPE_UNSPECIFIED = 0;

  BANK_AUTH_TYPE_DOTP = 1;
  BANK_AUTH_TYPE_REDIRECT = 2;
  BANK_AUTH_TYPE_ROP = 3;
  BANK_AUTH_TYPE_AUTHORIZED = 4;
  BANK_AUTH_TYPE_APP_TO_APP = 5;
}

message TriggerAuthByFundResponse {
  // Required:
  Error error = 1;

  // Required:
  AuthAction next_action = 2;

  // Required:
  string auth_session_id = 3;
}

message SubmitAuthValueRequest {
  // Required:
  string auth_session_id = 1;

  // Required:
  string auth_value = 2;

  // Optional
  map<string, string> extra_data = 3;
}

message SubmitAuthValueResponse {
  // Required:
  Error error = 1;
}

message BankAsset {
  string bank_code = 1;
  string bank_conn_code = 2;
  string binding_id = 3;
}

message ClientInfo {
  string client_ip = 1;
  string device_id = 2;
  Platform platform = 3;
  DeviceOS device_os = 4;
  string app_version = 5;
}

enum Platform {
  PLATFORM_UNSPECIFIED = 0;
  PLATFORM_ZPA = 1;
  PLATFORM_ZPI = 2;
}

enum DeviceOS {
  DEVICE_OS_UNSPECIFIED = 0;
  DEVICE_OS_ANDROID = 1;
  DEVICE_OS_IOS = 2;
}