syntax = "proto3";

option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth";

package authentication.v3;

message Error {
  // Required.
  ErrorCode code = 1;
  // Required. Error description for debugging and monitoring.
  string message = 2;
  // Optional. User-friendly message which is helpful to user.
  string user_return_message = 3;
}

enum ErrorCode {
  ER_CODE_UNSPECIFIED = 0;

  // Business Error
  ER_CODE_SUCCESS = 1;
  ER_CODE_INVALID_DATA = 2;
  ER_CODE_UNSUPPORTED = 3;
  ER_CODE_NOT_FOUND_AUTH_SESSION = 4;

  ER_CODE_WRONG_OTP_RETRYABLE = 10;
  ER_CODE_FAILED_TO_AUTHENTICATE_USER = 11;
  ER_CODE_AUTH_EXPIRED = 12;

  // System Error
  ER_CODE_INTERNAL = 1000;
  ER_CODE_DEPENDECY_ERROR = 1001; // Database, Redis, ...
  ER_CODE_RISK_UNAVAILABLE = 1002;
  ER_CODE_RISK_FAILED = 1003;
  ER_CODE_UM_UNAVAILABLE = 1005;
  ER_CODE_UM_FAILED = 1006;
  ER_CODE_BANK_SOF_UNAVAILABLE = 1007;
  ER_CODE_BANK_SOF_FAILED = 1008;
}

enum AuthType {
  AUTH_TYPE_UNSPECIFIED = 0;

  // Provided by UM
  AUTH_TYPE_UM_PIN = 1;
  AUTH_TYPE_UM_BIO = 2;
  AUTH_TYPE_UM_OTP = 3;
  AUTH_TYPE_UM_FACE_AUTH = 4;
  AUTH_TYPE_UM_KYC = 5;
  AUTH_TYPE_UM_ADJUST_KYC = 6;
  AUTH_TYPE_UM_NFC = 7;
  AUTH_TYPE_UM_KYC_NFC = 8;
  AUTH_TYPE_UM_ADJUST_KYC_NFC = 9;
  AUTH_TYPE_UM_SMART_OTP = 10;
  AUTH_TYPE_UM_SMART_OTP_FACE = 11;
  AUTH_TYPE_UM_OTP_V2 = 12;

  // Provided by Bank
  AUTH_TYPE_BANK_SMS_OTP = 101;
  AUTH_TYPE_BANK_D_OTP = 102;
  AUTH_TYPE_BANK_ROP = 103;
  AUTH_TYPE_BANK_REDIRECT = 104;
  AUTH_TYPE_BANK_APP = 105;
}

enum AuthAction {
  AUTH_ACTION_UNSPECIFIED = 0;

  AUTH_ACTION_BYPASS = 1;
  AUTH_ACTION_WAIT_FOR_RESULT = 2;
  AUTH_ACTION_FAIL = 3;
}

enum AuthStatus {
  AUTH_SESSION_STATUS_UNSPECIFIED = 0;

  AUTH_SESSION_STATUS_PROCESSING = 1;
  AUTH_SESSION_STATUS_SUCCESS = 2;
  AUTH_SESSION_STATUS_FAILED = 3;
}

enum Provider {
  PROVIDER_UNSPECIFIED = 0;
  PROVIDER_UM = 1;
  PROVIDER_BANK = 2;
}