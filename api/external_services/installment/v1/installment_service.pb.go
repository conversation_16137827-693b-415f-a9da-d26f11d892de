// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/installment/v1/installment_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RepayStatus int32

const (
	// Repay status is unspecified
	RepayStatus_REPAY_STATUS_UNSPECIFIED RepayStatus = 0
	// Repay status is pending
	RepayStatus_REPAY_STATUS_PENDING RepayStatus = 1
	// Repay status is success
	RepayStatus_REPAY_STATUS_DUE RepayStatus = 2
	// Repay status is failed
	RepayStatus_REPAY_STATUS_PAID RepayStatus = 3
	// Repay status is overdue
	RepayStatus_REPAY_STATUS_OVERDUE RepayStatus = 4
)

// Enum value maps for RepayStatus.
var (
	RepayStatus_name = map[int32]string{
		0: "REPAY_STATUS_UNSPECIFIED",
		1: "REPAY_STATUS_PENDING",
		2: "REPAY_STATUS_DUE",
		3: "REPAY_STATUS_PAID",
		4: "REPAY_STATUS_OVERDUE",
	}
	RepayStatus_value = map[string]int32{
		"REPAY_STATUS_UNSPECIFIED": 0,
		"REPAY_STATUS_PENDING":     1,
		"REPAY_STATUS_DUE":         2,
		"REPAY_STATUS_PAID":        3,
		"REPAY_STATUS_OVERDUE":     4,
	}
)

func (x RepayStatus) Enum() *RepayStatus {
	p := new(RepayStatus)
	*p = x
	return p
}

func (x RepayStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepayStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_installment_v1_installment_service_proto_enumTypes[0].Descriptor()
}

func (RepayStatus) Type() protoreflect.EnumType {
	return &file_external_services_installment_v1_installment_service_proto_enumTypes[0]
}

func (x RepayStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepayStatus.Descriptor instead.
func (RepayStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{0}
}

type InstallmentStatus int32

const (
	// Installment status is unspecified
	InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED InstallmentStatus = 0
	// Installment status is init
	InstallmentStatus_INSTALLMENT_STATUS_INIT InstallmentStatus = 1
	// Installment status is open
	InstallmentStatus_INSTALLMENT_STATUS_OPEN InstallmentStatus = 2
	// Installment status is closed
	InstallmentStatus_INSTALLMENT_STATUS_CLOSED InstallmentStatus = 3
)

// Enum value maps for InstallmentStatus.
var (
	InstallmentStatus_name = map[int32]string{
		0: "INSTALLMENT_STATUS_UNSPECIFIED",
		1: "INSTALLMENT_STATUS_INIT",
		2: "INSTALLMENT_STATUS_OPEN",
		3: "INSTALLMENT_STATUS_CLOSED",
	}
	InstallmentStatus_value = map[string]int32{
		"INSTALLMENT_STATUS_UNSPECIFIED": 0,
		"INSTALLMENT_STATUS_INIT":        1,
		"INSTALLMENT_STATUS_OPEN":        2,
		"INSTALLMENT_STATUS_CLOSED":      3,
	}
)

func (x InstallmentStatus) Enum() *InstallmentStatus {
	p := new(InstallmentStatus)
	*p = x
	return p
}

func (x InstallmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InstallmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_installment_v1_installment_service_proto_enumTypes[1].Descriptor()
}

func (InstallmentStatus) Type() protoreflect.EnumType {
	return &file_external_services_installment_v1_installment_service_proto_enumTypes[1]
}

func (x InstallmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InstallmentStatus.Descriptor instead.
func (InstallmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{1}
}

type EarlyDischargeStatus int32

const (
	// Early discharge status is unspecified
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED EarlyDischargeStatus = 0
	// Early discharge status is init
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_ELIGIBLE EarlyDischargeStatus = 1
	// Early discharge status is open
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_INELIGIBLE EarlyDischargeStatus = 2
	// Early discharge status is closed
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_PROCESSING EarlyDischargeStatus = 3
	// Early discharge status is closed
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED EarlyDischargeStatus = 4
)

// Enum value maps for EarlyDischargeStatus.
var (
	EarlyDischargeStatus_name = map[int32]string{
		0: "EARLY_DISCHARGE_STATUS_UNSPECIFIED",
		1: "EARLY_DISCHARGE_STATUS_ELIGIBLE",
		2: "EARLY_DISCHARGE_STATUS_INELIGIBLE",
		3: "EARLY_DISCHARGE_STATUS_PROCESSING",
		4: "EARLY_DISCHARGE_STATUS_CLOSED",
	}
	EarlyDischargeStatus_value = map[string]int32{
		"EARLY_DISCHARGE_STATUS_UNSPECIFIED": 0,
		"EARLY_DISCHARGE_STATUS_ELIGIBLE":    1,
		"EARLY_DISCHARGE_STATUS_INELIGIBLE":  2,
		"EARLY_DISCHARGE_STATUS_PROCESSING":  3,
		"EARLY_DISCHARGE_STATUS_CLOSED":      4,
	}
)

func (x EarlyDischargeStatus) Enum() *EarlyDischargeStatus {
	p := new(EarlyDischargeStatus)
	*p = x
	return p
}

func (x EarlyDischargeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EarlyDischargeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_installment_v1_installment_service_proto_enumTypes[2].Descriptor()
}

func (EarlyDischargeStatus) Type() protoreflect.EnumType {
	return &file_external_services_installment_v1_installment_service_proto_enumTypes[2]
}

func (x EarlyDischargeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EarlyDischargeStatus.Descriptor instead.
func (EarlyDischargeStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{2}
}

type GetInstallmentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransId   int64 `protobuf:"varint,1,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	ForceLatest bool  `protobuf:"varint,2,opt,name=force_latest,json=forceLatest,proto3" json:"force_latest,omitempty"`
}

func (x *GetInstallmentStatusRequest) Reset() {
	*x = GetInstallmentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstallmentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstallmentStatusRequest) ProtoMessage() {}

func (x *GetInstallmentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstallmentStatusRequest.ProtoReflect.Descriptor instead.
func (*GetInstallmentStatusRequest) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetInstallmentStatusRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *GetInstallmentStatusRequest) GetForceLatest() bool {
	if x != nil {
		return x.ForceLatest
	}
	return false
}

type GetInstallmentStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status InstallmentStatus `protobuf:"varint,1,opt,name=status,proto3,enum=management_service.installment.v1.InstallmentStatus" json:"status,omitempty"`
	Info   *InstallmentBase  `protobuf:"bytes,2,opt,name=info,proto3,oneof" json:"info,omitempty"`
}

func (x *GetInstallmentStatusResponse) Reset() {
	*x = GetInstallmentStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstallmentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstallmentStatusResponse) ProtoMessage() {}

func (x *GetInstallmentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstallmentStatusResponse.ProtoReflect.Descriptor instead.
func (*GetInstallmentStatusResponse) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetInstallmentStatusResponse) GetStatus() InstallmentStatus {
	if x != nil {
		return x.Status
	}
	return InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED
}

func (x *GetInstallmentStatusResponse) GetInfo() *InstallmentBase {
	if x != nil {
		return x.Info
	}
	return nil
}

type GetEarlyDischargeRefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransId   int64 `protobuf:"varint,1,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	ForceLatest *bool `protobuf:"varint,2,opt,name=force_latest,proto3,oneof" json:"force_latest,omitempty"`
}

func (x *GetEarlyDischargeRefundRequest) Reset() {
	*x = GetEarlyDischargeRefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEarlyDischargeRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarlyDischargeRefundRequest) ProtoMessage() {}

func (x *GetEarlyDischargeRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarlyDischargeRefundRequest.ProtoReflect.Descriptor instead.
func (*GetEarlyDischargeRefundRequest) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetEarlyDischargeRefundRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *GetEarlyDischargeRefundRequest) GetForceLatest() bool {
	if x != nil && x.ForceLatest != nil {
		return *x.ForceLatest
	}
	return false
}

type GetEarlyDischargeRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DischargeInfo *EarlyDischargeForRefund `protobuf:"bytes,1,opt,name=discharge_info,proto3" json:"discharge_info,omitempty"`
}

func (x *GetEarlyDischargeRefundResponse) Reset() {
	*x = GetEarlyDischargeRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEarlyDischargeRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarlyDischargeRefundResponse) ProtoMessage() {}

func (x *GetEarlyDischargeRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarlyDischargeRefundResponse.ProtoReflect.Descriptor instead.
func (*GetEarlyDischargeRefundResponse) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetEarlyDischargeRefundResponse) GetDischargeInfo() *EarlyDischargeForRefund {
	if x != nil {
		return x.DischargeInfo
	}
	return nil
}

type NotifyInstallmentRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NotifyInstallmentRefundResponse) Reset() {
	*x = NotifyInstallmentRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyInstallmentRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyInstallmentRefundResponse) ProtoMessage() {}

func (x *NotifyInstallmentRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyInstallmentRefundResponse.ProtoReflect.Descriptor instead.
func (*NotifyInstallmentRefundResponse) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{4}
}

type InstallmentBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tenor           int32   `protobuf:"varint,2,opt,name=tenor,proto3" json:"tenor,omitempty"`
	ZpTransId       int64   `protobuf:"varint,3,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	PartnerCode     string  `protobuf:"bytes,4,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	PartnerInstId   string  `protobuf:"bytes,5,opt,name=partner_inst_id,json=partnerInstId,proto3" json:"partner_inst_id,omitempty"`
	InterestRate    float64 `protobuf:"fixed64,6,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	PrincipalAmount int64   `protobuf:"varint,7,opt,name=principal_amount,json=principalAmount,proto3" json:"principal_amount,omitempty"`
}

func (x *InstallmentBase) Reset() {
	*x = InstallmentBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstallmentBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentBase) ProtoMessage() {}

func (x *InstallmentBase) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentBase.ProtoReflect.Descriptor instead.
func (*InstallmentBase) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{5}
}

func (x *InstallmentBase) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InstallmentBase) GetTenor() int32 {
	if x != nil {
		return x.Tenor
	}
	return 0
}

func (x *InstallmentBase) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *InstallmentBase) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *InstallmentBase) GetPartnerInstId() string {
	if x != nil {
		return x.PartnerInstId
	}
	return ""
}

func (x *InstallmentBase) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *InstallmentBase) GetPrincipalAmount() int64 {
	if x != nil {
		return x.PrincipalAmount
	}
	return 0
}

type InstallmentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   int64             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tenure               int32             `protobuf:"varint,2,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Status               InstallmentStatus `protobuf:"varint,3,opt,name=status,proto3,enum=management_service.installment.v1.InstallmentStatus" json:"status,omitempty"`
	PrincipalAmount      int64             `protobuf:"varint,4,opt,name=principal_amount,proto3" json:"principal_amount,omitempty"`
	InterestAmount       int64             `protobuf:"varint,5,opt,name=interest_amount,proto3" json:"interest_amount,omitempty"`
	PenaltyAmount        int64             `protobuf:"varint,6,opt,name=penalty_amount,proto3" json:"penalty_amount,omitempty"`
	TotalAmountDue       int64             `protobuf:"varint,7,opt,name=total_amount_due,proto3" json:"total_amount_due,omitempty"`
	TotalPaidAmount      int64             `protobuf:"varint,8,opt,name=total_paid_amount,proto3" json:"total_paid_amount,omitempty"`
	TotalRemainingAmount int64             `protobuf:"varint,9,opt,name=total_remaining_amount,proto3" json:"total_remaining_amount,omitempty"`
}

func (x *InstallmentData) Reset() {
	*x = InstallmentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstallmentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentData) ProtoMessage() {}

func (x *InstallmentData) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentData.ProtoReflect.Descriptor instead.
func (*InstallmentData) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{6}
}

func (x *InstallmentData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InstallmentData) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *InstallmentData) GetStatus() InstallmentStatus {
	if x != nil {
		return x.Status
	}
	return InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED
}

func (x *InstallmentData) GetPrincipalAmount() int64 {
	if x != nil {
		return x.PrincipalAmount
	}
	return 0
}

func (x *InstallmentData) GetInterestAmount() int64 {
	if x != nil {
		return x.InterestAmount
	}
	return 0
}

func (x *InstallmentData) GetPenaltyAmount() int64 {
	if x != nil {
		return x.PenaltyAmount
	}
	return 0
}

func (x *InstallmentData) GetTotalAmountDue() int64 {
	if x != nil {
		return x.TotalAmountDue
	}
	return 0
}

func (x *InstallmentData) GetTotalPaidAmount() int64 {
	if x != nil {
		return x.TotalPaidAmount
	}
	return 0
}

func (x *InstallmentData) GetTotalRemainingAmount() int64 {
	if x != nil {
		return x.TotalRemainingAmount
	}
	return 0
}

type InstallmentRefund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransId         int64 `protobuf:"varint,1,opt,name=zp_trans_id,proto3" json:"zp_trans_id,omitempty"`
	Version           int32 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	NetRefundAmount   int64 `protobuf:"varint,3,opt,name=net_refund_amount,proto3" json:"net_refund_amount,omitempty"`
	TotalRefundAmount int64 `protobuf:"varint,4,opt,name=total_refund_amount,proto3" json:"total_refund_amount,omitempty"`
	UserTopupAmount   int64 `protobuf:"varint,5,opt,name=user_topup_amount,proto3" json:"user_topup_amount,omitempty"`
	UserTopupRequired bool  `protobuf:"varint,6,opt,name=user_topup_required,proto3" json:"user_topup_required,omitempty"`
}

func (x *InstallmentRefund) Reset() {
	*x = InstallmentRefund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstallmentRefund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentRefund) ProtoMessage() {}

func (x *InstallmentRefund) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentRefund.ProtoReflect.Descriptor instead.
func (*InstallmentRefund) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{7}
}

func (x *InstallmentRefund) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *InstallmentRefund) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *InstallmentRefund) GetNetRefundAmount() int64 {
	if x != nil {
		return x.NetRefundAmount
	}
	return 0
}

func (x *InstallmentRefund) GetTotalRefundAmount() int64 {
	if x != nil {
		return x.TotalRefundAmount
	}
	return 0
}

func (x *InstallmentRefund) GetUserTopupAmount() int64 {
	if x != nil {
		return x.UserTopupAmount
	}
	return 0
}

func (x *InstallmentRefund) GetUserTopupRequired() bool {
	if x != nil {
		return x.UserTopupRequired
	}
	return false
}

type RepaymentSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeqNo                int32       `protobuf:"varint,1,opt,name=seq_no,proto3" json:"seq_no,omitempty"`
	Status               RepayStatus `protobuf:"varint,2,opt,name=status,proto3,enum=management_service.installment.v1.RepayStatus" json:"status,omitempty"`
	DueDate              string      `protobuf:"bytes,3,opt,name=due_date,proto3" json:"due_date,omitempty"`
	DueAmount            int64       `protobuf:"varint,4,opt,name=due_amount,proto3" json:"due_amount,omitempty"`
	PenaltyAmount        int64       `protobuf:"varint,5,opt,name=penalty_amount,proto3" json:"penalty_amount,omitempty"`
	TotalDueAmount       int64       `protobuf:"varint,6,opt,name=total_due_amount,proto3" json:"total_due_amount,omitempty"`
	TotalPaidAmount      int64       `protobuf:"varint,7,opt,name=total_paid_amount,proto3" json:"total_paid_amount,omitempty"`
	TotalRemainingAmount int64       `protobuf:"varint,8,opt,name=total_remaining_amount,proto3" json:"total_remaining_amount,omitempty"`
}

func (x *RepaymentSchedule) Reset() {
	*x = RepaymentSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepaymentSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepaymentSchedule) ProtoMessage() {}

func (x *RepaymentSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepaymentSchedule.ProtoReflect.Descriptor instead.
func (*RepaymentSchedule) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{8}
}

func (x *RepaymentSchedule) GetSeqNo() int32 {
	if x != nil {
		return x.SeqNo
	}
	return 0
}

func (x *RepaymentSchedule) GetStatus() RepayStatus {
	if x != nil {
		return x.Status
	}
	return RepayStatus_REPAY_STATUS_UNSPECIFIED
}

func (x *RepaymentSchedule) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *RepaymentSchedule) GetDueAmount() int64 {
	if x != nil {
		return x.DueAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetPenaltyAmount() int64 {
	if x != nil {
		return x.PenaltyAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetTotalPaidAmount() int64 {
	if x != nil {
		return x.TotalPaidAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetTotalRemainingAmount() int64 {
	if x != nil {
		return x.TotalRemainingAmount
	}
	return 0
}

type EarlyDischargeForRefund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status EarlyDischargeStatus `protobuf:"varint,1,opt,name=status,proto3,enum=management_service.installment.v1.EarlyDischargeStatus" json:"status,omitempty"`
	// discharge_amount is the raw total amount of the early discharge
	DischargeAmount int64 `protobuf:"varint,2,opt,name=discharge_amount,proto3" json:"discharge_amount,omitempty"`
	// session_available ref to in_session in EarlyDischargeBase
	SessionAvailable bool `protobuf:"varint,3,opt,name=session_available,proto3" json:"session_available,omitempty"`
}

func (x *EarlyDischargeForRefund) Reset() {
	*x = EarlyDischargeForRefund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EarlyDischargeForRefund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarlyDischargeForRefund) ProtoMessage() {}

func (x *EarlyDischargeForRefund) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_installment_v1_installment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarlyDischargeForRefund.ProtoReflect.Descriptor instead.
func (*EarlyDischargeForRefund) Descriptor() ([]byte, []int) {
	return file_external_services_installment_v1_installment_service_proto_rawDescGZIP(), []int{9}
}

func (x *EarlyDischargeForRefund) GetStatus() EarlyDischargeStatus {
	if x != nil {
		return x.Status
	}
	return EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED
}

func (x *EarlyDischargeForRefund) GetDischargeAmount() int64 {
	if x != nil {
		return x.DischargeAmount
	}
	return 0
}

func (x *EarlyDischargeForRefund) GetSessionAvailable() bool {
	if x != nil {
		return x.SessionAvailable
	}
	return false
}

var File_external_services_installment_v1_installment_service_proto protoreflect.FileDescriptor

var file_external_services_installment_v1_installment_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x22, 0xc2, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x4b, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x61, 0x73, 0x65, 0x48, 0x00, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x83, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0b, 0x7a,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x7a, 0x70, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x22, 0x85,
	0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x62, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x21, 0x0a, 0x1f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf2, 0x01, 0x0a, 0x0f, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x65, 0x6e, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x65,
	0x6e, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52,
	0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x97,
	0x03, 0x0a, 0x0f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x4c, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6e,
	0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x75, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x69, 0x64,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x36, 0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8f, 0x02, 0x0a, 0x11, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x6e, 0x65,
	0x74, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6e, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x74, 0x6f, 0x70, 0x75, 0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x70, 0x75,
	0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x70, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x70, 0x75,
	0x70, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0xe9, 0x02, 0x0a, 0x11, 0x52,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x6f, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e,
	0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x75,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2c, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36,
	0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc4, 0x01, 0x0a, 0x17, 0x45, 0x61, 0x72, 0x6c, 0x79,
	0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x12, 0x4f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x64,
	0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2c, 0x0a, 0x11, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x2a, 0x8c, 0x01,
	0x0a, 0x0b, 0x52, 0x65, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a,
	0x18, 0x52, 0x45, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x45, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x50, 0x41, 0x59, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x55, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x52,
	0x45, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x49, 0x44,
	0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x44, 0x55, 0x45, 0x10, 0x04, 0x2a, 0x90, 0x01, 0x0a,
	0x11, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x02,
	0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x2a,
	0xd4, 0x01, 0x0a, 0x14, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x41, 0x52, 0x4c,
	0x59, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x23, 0x0a, 0x1f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x48, 0x41,
	0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49,
	0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x44,
	0x49, 0x53, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21,
	0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x44, 0x49, 0x53,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c,
	0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x32, 0xe6, 0x03, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x99, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0xa2, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44,
	0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x41,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69,
	0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x95, 0x01, 0x0a, 0x17, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x12, 0x34, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x1a, 0x42, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42,
	0x2b, 0x5a, 0x29, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_installment_v1_installment_service_proto_rawDescOnce sync.Once
	file_external_services_installment_v1_installment_service_proto_rawDescData = file_external_services_installment_v1_installment_service_proto_rawDesc
)

func file_external_services_installment_v1_installment_service_proto_rawDescGZIP() []byte {
	file_external_services_installment_v1_installment_service_proto_rawDescOnce.Do(func() {
		file_external_services_installment_v1_installment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_installment_v1_installment_service_proto_rawDescData)
	})
	return file_external_services_installment_v1_installment_service_proto_rawDescData
}

var file_external_services_installment_v1_installment_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_external_services_installment_v1_installment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_external_services_installment_v1_installment_service_proto_goTypes = []any{
	(RepayStatus)(0),                        // 0: management_service.installment.v1.RepayStatus
	(InstallmentStatus)(0),                  // 1: management_service.installment.v1.InstallmentStatus
	(EarlyDischargeStatus)(0),               // 2: management_service.installment.v1.EarlyDischargeStatus
	(*GetInstallmentStatusRequest)(nil),     // 3: management_service.installment.v1.GetInstallmentStatusRequest
	(*GetInstallmentStatusResponse)(nil),    // 4: management_service.installment.v1.GetInstallmentStatusResponse
	(*GetEarlyDischargeRefundRequest)(nil),  // 5: management_service.installment.v1.GetEarlyDischargeRefundRequest
	(*GetEarlyDischargeRefundResponse)(nil), // 6: management_service.installment.v1.GetEarlyDischargeRefundResponse
	(*NotifyInstallmentRefundResponse)(nil), // 7: management_service.installment.v1.NotifyInstallmentRefundResponse
	(*InstallmentBase)(nil),                 // 8: management_service.installment.v1.InstallmentBase
	(*InstallmentData)(nil),                 // 9: management_service.installment.v1.InstallmentData
	(*InstallmentRefund)(nil),               // 10: management_service.installment.v1.InstallmentRefund
	(*RepaymentSchedule)(nil),               // 11: management_service.installment.v1.RepaymentSchedule
	(*EarlyDischargeForRefund)(nil),         // 12: management_service.installment.v1.EarlyDischargeForRefund
}
var file_external_services_installment_v1_installment_service_proto_depIdxs = []int32{
	1,  // 0: management_service.installment.v1.GetInstallmentStatusResponse.status:type_name -> management_service.installment.v1.InstallmentStatus
	8,  // 1: management_service.installment.v1.GetInstallmentStatusResponse.info:type_name -> management_service.installment.v1.InstallmentBase
	12, // 2: management_service.installment.v1.GetEarlyDischargeRefundResponse.discharge_info:type_name -> management_service.installment.v1.EarlyDischargeForRefund
	1,  // 3: management_service.installment.v1.InstallmentData.status:type_name -> management_service.installment.v1.InstallmentStatus
	0,  // 4: management_service.installment.v1.RepaymentSchedule.status:type_name -> management_service.installment.v1.RepayStatus
	2,  // 5: management_service.installment.v1.EarlyDischargeForRefund.status:type_name -> management_service.installment.v1.EarlyDischargeStatus
	3,  // 6: management_service.installment.v1.Installment.GetInstallmentStatus:input_type -> management_service.installment.v1.GetInstallmentStatusRequest
	5,  // 7: management_service.installment.v1.Installment.GetEarlyDischargeRefund:input_type -> management_service.installment.v1.GetEarlyDischargeRefundRequest
	10, // 8: management_service.installment.v1.Installment.NotifyInstallmentRefund:input_type -> management_service.installment.v1.InstallmentRefund
	4,  // 9: management_service.installment.v1.Installment.GetInstallmentStatus:output_type -> management_service.installment.v1.GetInstallmentStatusResponse
	6,  // 10: management_service.installment.v1.Installment.GetEarlyDischargeRefund:output_type -> management_service.installment.v1.GetEarlyDischargeRefundResponse
	7,  // 11: management_service.installment.v1.Installment.NotifyInstallmentRefund:output_type -> management_service.installment.v1.NotifyInstallmentRefundResponse
	9,  // [9:12] is the sub-list for method output_type
	6,  // [6:9] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_external_services_installment_v1_installment_service_proto_init() }
func file_external_services_installment_v1_installment_service_proto_init() {
	if File_external_services_installment_v1_installment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_installment_v1_installment_service_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetInstallmentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetInstallmentStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetEarlyDischargeRefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetEarlyDischargeRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*NotifyInstallmentRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*InstallmentBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*InstallmentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*InstallmentRefund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RepaymentSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_installment_v1_installment_service_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*EarlyDischargeForRefund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_external_services_installment_v1_installment_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_external_services_installment_v1_installment_service_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_installment_v1_installment_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_installment_v1_installment_service_proto_goTypes,
		DependencyIndexes: file_external_services_installment_v1_installment_service_proto_depIdxs,
		EnumInfos:         file_external_services_installment_v1_installment_service_proto_enumTypes,
		MessageInfos:      file_external_services_installment_v1_installment_service_proto_msgTypes,
	}.Build()
	File_external_services_installment_v1_installment_service_proto = out.File
	file_external_services_installment_v1_installment_service_proto_rawDesc = nil
	file_external_services_installment_v1_installment_service_proto_goTypes = nil
	file_external_services_installment_v1_installment_service_proto_depIdxs = nil
}
