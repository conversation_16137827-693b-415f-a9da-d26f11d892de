syntax = "proto3";

package management_service.installment.v1;

option go_package = "installment/api/installment-service/v1;v1";

import "validate/validate.proto";

service Installment {
  rpc GetInstallmentStatus(GetInstallmentStatusRequest) returns (GetInstallmentStatusResponse) {}
  rpc GetEarlyDischargeRefund(GetEarlyDischargeRefundRequest) returns (GetEarlyDischargeRefundResponse) {}
  rpc NotifyInstallmentRefund(InstallmentRefund) returns (NotifyInstallmentRefundResponse) {}
}

message GetInstallmentStatusRequest {
  int64 zp_trans_id = 1 [(validate.rules).int64.gt = 0];
  bool force_latest = 2;
}

message GetInstallmentStatusResponse {
  InstallmentStatus status = 1;
  optional InstallmentBase info = 2;
}

message GetEarlyDischargeRefundRequest {
  int64 zp_trans_id = 1 [(validate.rules).int64.gt = 0];
  optional bool force_latest = 2 [json_name = 'force_latest'];
}

message GetEarlyDischargeRefundResponse {
  EarlyDischargeForRefund discharge_info = 1 [json_name = 'discharge_info'];
}

message NotifyInstallmentRefundResponse {}

message InstallmentBase {
  int64 id = 1;
  int32 tenor = 2;
  int64 zp_trans_id = 3;
  string partner_code = 4;
  string partner_inst_id = 5;
  double interest_rate = 6;
  int64 principal_amount = 7;
}

message InstallmentData {
  int64 id = 1 [json_name = 'id'];
  int32 tenure = 2 [json_name = 'tenure'];
  InstallmentStatus status = 3 [json_name = 'status'];
  int64 principal_amount = 4 [json_name = 'principal_amount'];
  int64 interest_amount = 5 [json_name = 'interest_amount'];
  int64 penalty_amount = 6 [json_name = 'penalty_amount'];
  int64 total_amount_due = 7 [json_name = 'total_amount_due'];
  int64 total_paid_amount = 8 [json_name = 'total_paid_amount'];
  int64 total_remaining_amount = 9 [json_name = 'total_remaining_amount'];
}

message InstallmentRefund {
  int64 zp_trans_id = 1 [json_name = 'zp_trans_id'];
  int32 version = 2 [json_name = 'version'];
  int64 net_refund_amount = 3 [json_name = 'net_refund_amount'];
  int64 total_refund_amount = 4 [json_name = 'total_refund_amount'];
  int64 user_topup_amount = 5 [json_name = 'user_topup_amount'];
  bool user_topup_required = 6 [json_name = 'user_topup_required'];
}

enum RepayStatus {
  // Repay status is unspecified
  REPAY_STATUS_UNSPECIFIED = 0;
  // Repay status is pending
  REPAY_STATUS_PENDING = 1;
  // Repay status is success
  REPAY_STATUS_DUE = 2;
  // Repay status is failed
  REPAY_STATUS_PAID = 3;
  // Repay status is overdue
  REPAY_STATUS_OVERDUE = 4;
}

enum InstallmentStatus {
  // Installment status is unspecified
  INSTALLMENT_STATUS_UNSPECIFIED = 0;
  // Installment status is init
  INSTALLMENT_STATUS_INIT = 1;
  // Installment status is open
  INSTALLMENT_STATUS_OPEN = 2;
  // Installment status is closed
  INSTALLMENT_STATUS_CLOSED = 3;
}

message RepaymentSchedule {
  int32 seq_no = 1 [json_name = 'seq_no'];
  RepayStatus status = 2 [json_name = 'status'];
  string due_date = 3 [json_name = 'due_date'];
  int64 due_amount = 4 [json_name = 'due_amount'];
  int64 penalty_amount = 5 [json_name = 'penalty_amount'];
  int64 total_due_amount = 6 [json_name = 'total_due_amount'];
  int64 total_paid_amount = 7 [json_name = 'total_paid_amount'];
  int64 total_remaining_amount = 8 [json_name = 'total_remaining_amount'];
}

message EarlyDischargeForRefund {
  EarlyDischargeStatus status = 1 [json_name = 'status'];
  // discharge_amount is the raw total amount of the early discharge
  int64 discharge_amount = 2 [json_name = 'discharge_amount'];
  // session_available ref to in_session in EarlyDischargeBase
  bool session_available  = 3 [json_name = 'session_available'];
}

enum EarlyDischargeStatus {
  // Early discharge status is unspecified
  EARLY_DISCHARGE_STATUS_UNSPECIFIED = 0;
  // Early discharge status is init
  EARLY_DISCHARGE_STATUS_ELIGIBLE = 1;
  // Early discharge status is open
  EARLY_DISCHARGE_STATUS_INELIGIBLE = 2;
  // Early discharge status is closed
  EARLY_DISCHARGE_STATUS_PROCESSING = 3;
  // Early discharge status is closed
  EARLY_DISCHARGE_STATUS_CLOSED = 4;
}