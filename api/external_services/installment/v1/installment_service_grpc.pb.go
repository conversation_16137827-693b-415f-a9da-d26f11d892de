// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/installment/v1/installment_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Installment_GetInstallmentStatus_FullMethodName    = "/management_service.installment.v1.Installment/GetInstallmentStatus"
	Installment_GetEarlyDischargeRefund_FullMethodName = "/management_service.installment.v1.Installment/GetEarlyDischargeRefund"
	Installment_NotifyInstallmentRefund_FullMethodName = "/management_service.installment.v1.Installment/NotifyInstallmentRefund"
)

// InstallmentClient is the client API for Installment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type InstallmentClient interface {
	GetInstallmentStatus(ctx context.Context, in *GetInstallmentStatusRequest, opts ...grpc.CallOption) (*GetInstallmentStatusResponse, error)
	GetEarlyDischargeRefund(ctx context.Context, in *GetEarlyDischargeRefundRequest, opts ...grpc.CallOption) (*GetEarlyDischargeRefundResponse, error)
	NotifyInstallmentRefund(ctx context.Context, in *InstallmentRefund, opts ...grpc.CallOption) (*NotifyInstallmentRefundResponse, error)
}

type installmentClient struct {
	cc grpc.ClientConnInterface
}

func NewInstallmentClient(cc grpc.ClientConnInterface) InstallmentClient {
	return &installmentClient{cc}
}

func (c *installmentClient) GetInstallmentStatus(ctx context.Context, in *GetInstallmentStatusRequest, opts ...grpc.CallOption) (*GetInstallmentStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInstallmentStatusResponse)
	err := c.cc.Invoke(ctx, Installment_GetInstallmentStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetEarlyDischargeRefund(ctx context.Context, in *GetEarlyDischargeRefundRequest, opts ...grpc.CallOption) (*GetEarlyDischargeRefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEarlyDischargeRefundResponse)
	err := c.cc.Invoke(ctx, Installment_GetEarlyDischargeRefund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) NotifyInstallmentRefund(ctx context.Context, in *InstallmentRefund, opts ...grpc.CallOption) (*NotifyInstallmentRefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NotifyInstallmentRefundResponse)
	err := c.cc.Invoke(ctx, Installment_NotifyInstallmentRefund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InstallmentServer is the server API for Installment service.
// All implementations must embed UnimplementedInstallmentServer
// for forward compatibility.
type InstallmentServer interface {
	GetInstallmentStatus(context.Context, *GetInstallmentStatusRequest) (*GetInstallmentStatusResponse, error)
	GetEarlyDischargeRefund(context.Context, *GetEarlyDischargeRefundRequest) (*GetEarlyDischargeRefundResponse, error)
	NotifyInstallmentRefund(context.Context, *InstallmentRefund) (*NotifyInstallmentRefundResponse, error)
	mustEmbedUnimplementedInstallmentServer()
}

// UnimplementedInstallmentServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInstallmentServer struct{}

func (UnimplementedInstallmentServer) GetInstallmentStatus(context.Context, *GetInstallmentStatusRequest) (*GetInstallmentStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstallmentStatus not implemented")
}
func (UnimplementedInstallmentServer) GetEarlyDischargeRefund(context.Context, *GetEarlyDischargeRefundRequest) (*GetEarlyDischargeRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEarlyDischargeRefund not implemented")
}
func (UnimplementedInstallmentServer) NotifyInstallmentRefund(context.Context, *InstallmentRefund) (*NotifyInstallmentRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyInstallmentRefund not implemented")
}
func (UnimplementedInstallmentServer) mustEmbedUnimplementedInstallmentServer() {}
func (UnimplementedInstallmentServer) testEmbeddedByValue()                     {}

// UnsafeInstallmentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InstallmentServer will
// result in compilation errors.
type UnsafeInstallmentServer interface {
	mustEmbedUnimplementedInstallmentServer()
}

func RegisterInstallmentServer(s grpc.ServiceRegistrar, srv InstallmentServer) {
	// If the following call pancis, it indicates UnimplementedInstallmentServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Installment_ServiceDesc, srv)
}

func _Installment_GetInstallmentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstallmentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetInstallmentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetInstallmentStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetInstallmentStatus(ctx, req.(*GetInstallmentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetEarlyDischargeRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEarlyDischargeRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetEarlyDischargeRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetEarlyDischargeRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetEarlyDischargeRefund(ctx, req.(*GetEarlyDischargeRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_NotifyInstallmentRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallmentRefund)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).NotifyInstallmentRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_NotifyInstallmentRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).NotifyInstallmentRefund(ctx, req.(*InstallmentRefund))
	}
	return interceptor(ctx, in, info, handler)
}

// Installment_ServiceDesc is the grpc.ServiceDesc for Installment service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Installment_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "management_service.installment.v1.Installment",
	HandlerType: (*InstallmentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetInstallmentStatus",
			Handler:    _Installment_GetInstallmentStatus_Handler,
		},
		{
			MethodName: "GetEarlyDischargeRefund",
			Handler:    _Installment_GetEarlyDischargeRefund_Handler,
		},
		{
			MethodName: "NotifyInstallmentRefund",
			Handler:    _Installment_NotifyInstallmentRefund_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/installment/v1/installment_service.proto",
}
