// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/refund_core/refund.proto

package acquiring_refund

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RefundService_CreateRefund_FullMethodName = "/acquiring_refund.v1.RefundService/CreateRefund"
	RefundService_GetRefund_FullMethodName    = "/acquiring_refund.v1.RefundService/GetRefund"
	RefundService_ListRefunds_FullMethodName  = "/acquiring_refund.v1.RefundService/ListRefunds"
)

// RefundServiceClient is the client API for RefundService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RefundServiceClient interface {
	CreateRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc.CallOption) (*CreateRefundResponse, error)
	GetRefund(ctx context.Context, in *GetRefundRequest, opts ...grpc.CallOption) (*Refund, error)
	ListRefunds(ctx context.Context, in *ListRefundsRequest, opts ...grpc.CallOption) (*ListRefundsResponse, error)
}

type refundServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRefundServiceClient(cc grpc.ClientConnInterface) RefundServiceClient {
	return &refundServiceClient{cc}
}

func (c *refundServiceClient) CreateRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc.CallOption) (*CreateRefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRefundResponse)
	err := c.cc.Invoke(ctx, RefundService_CreateRefund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) GetRefund(ctx context.Context, in *GetRefundRequest, opts ...grpc.CallOption) (*Refund, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Refund)
	err := c.cc.Invoke(ctx, RefundService_GetRefund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) ListRefunds(ctx context.Context, in *ListRefundsRequest, opts ...grpc.CallOption) (*ListRefundsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListRefundsResponse)
	err := c.cc.Invoke(ctx, RefundService_ListRefunds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundServiceServer is the server API for RefundService service.
// All implementations must embed UnimplementedRefundServiceServer
// for forward compatibility.
type RefundServiceServer interface {
	CreateRefund(context.Context, *CreateRefundRequest) (*CreateRefundResponse, error)
	GetRefund(context.Context, *GetRefundRequest) (*Refund, error)
	ListRefunds(context.Context, *ListRefundsRequest) (*ListRefundsResponse, error)
	mustEmbedUnimplementedRefundServiceServer()
}

// UnimplementedRefundServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRefundServiceServer struct{}

func (UnimplementedRefundServiceServer) CreateRefund(context.Context, *CreateRefundRequest) (*CreateRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRefund not implemented")
}
func (UnimplementedRefundServiceServer) GetRefund(context.Context, *GetRefundRequest) (*Refund, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRefund not implemented")
}
func (UnimplementedRefundServiceServer) ListRefunds(context.Context, *ListRefundsRequest) (*ListRefundsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRefunds not implemented")
}
func (UnimplementedRefundServiceServer) mustEmbedUnimplementedRefundServiceServer() {}
func (UnimplementedRefundServiceServer) testEmbeddedByValue()                       {}

// UnsafeRefundServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RefundServiceServer will
// result in compilation errors.
type UnsafeRefundServiceServer interface {
	mustEmbedUnimplementedRefundServiceServer()
}

func RegisterRefundServiceServer(s grpc.ServiceRegistrar, srv RefundServiceServer) {
	// If the following call pancis, it indicates UnimplementedRefundServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RefundService_ServiceDesc, srv)
}

func _RefundService_CreateRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).CreateRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_CreateRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).CreateRefund(ctx, req.(*CreateRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_GetRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).GetRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_GetRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).GetRefund(ctx, req.(*GetRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_ListRefunds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRefundsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).ListRefunds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_ListRefunds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).ListRefunds(ctx, req.(*ListRefundsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RefundService_ServiceDesc is the grpc.ServiceDesc for RefundService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RefundService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "acquiring_refund.v1.RefundService",
	HandlerType: (*RefundServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateRefund",
			Handler:    _RefundService_CreateRefund_Handler,
		},
		{
			MethodName: "GetRefund",
			Handler:    _RefundService_GetRefund_Handler,
		},
		{
			MethodName: "ListRefunds",
			Handler:    _RefundService_ListRefunds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/refund_core/refund.proto",
}
