syntax = "proto3";

package acquiring_refund.v1;

import "google/protobuf/timestamp.proto";

option go_package = "installment-service/api/acquiring_refund;acquiring_refund";

service RefundService {
  rpc CreateRefund(CreateRefundRequest) returns (CreateRefundResponse);
  rpc GetRefund(GetRefundRequest) returns (Refund);
  rpc ListRefunds(ListRefundsRequest) returns (ListRefundsResponse);
}

message CreateRefundRequest {
  // @gotags: validate:"required,min=1"
  int32 app_id = 1;
  // @gotags: validate:"required,prefix_date,max=40"
  string app_trans_id = 2;
  // @gotags: validate:"required,prefix_date,max=45"
  string m_refund_id = 3;
  // @gotags: validate:"required,min=1"
  int64 amount = 4;
  // @gotags: validate:"max=100"
  string description = 5;
}

message CreateRefundResponse {
  int64 refund_id = 1;
}

message GetRefundRequest {
  // @gotags: validate:"required,min=1"
  int32 app_id = 1;
  // @gotags: validate:"required,prefix_date,max=45"
  string m_refund_id = 2;
}

message ListRefundsRequest {
  // @gotags: validate:"required,min=1"
  int32 app_id = 1;
  // @gotags: validate:"required,prefix_date,max=40"
  string app_trans_id = 2;
}

message ListRefundsResponse {
  repeated Refund refunds = 1;
  int64 total_refunded_amount = 2;
}

message Refund {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    CREATED = 1;
    SUCCESS = 2;
    FAILED = 3;
    PENDING = 4;
  }

  int64 refund_id = 1;
  string m_refund_id = 2;
  int32 app_id = 3;
  string app_trans_id = 4;
  int64 amount = 5;
  Status status = 6;
  ReasonStatus reason_status = 7;

  google.protobuf.Timestamp create_time = 8;
  google.protobuf.Timestamp complete_time = 9;
}

message ReasonStatus {
  string domain = 1;
  string reason = 2;
  map<string, string> metadata = 3;
}