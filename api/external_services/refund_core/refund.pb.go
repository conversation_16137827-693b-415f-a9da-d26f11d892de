// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/refund_core/refund.proto

package acquiring_refund

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Refund_Status int32

const (
	Refund_STATUS_UNSPECIFIED Refund_Status = 0
	Refund_CREATED            Refund_Status = 1
	Refund_SUCCESS            Refund_Status = 2
	Refund_FAILED             Refund_Status = 3
	Refund_PENDING            Refund_Status = 4
)

// Enum value maps for Refund_Status.
var (
	Refund_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "SUCCESS",
		3: "FAILED",
		4: "PENDING",
	}
	Refund_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"CREATED":            1,
		"SUCCESS":            2,
		"FAILED":             3,
		"PENDING":            4,
	}
)

func (x Refund_Status) Enum() *Refund_Status {
	p := new(Refund_Status)
	*p = x
	return p
}

func (x Refund_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Refund_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_refund_core_refund_proto_enumTypes[0].Descriptor()
}

func (Refund_Status) Type() protoreflect.EnumType {
	return &file_external_services_refund_core_refund_proto_enumTypes[0]
}

func (x Refund_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Refund_Status.Descriptor instead.
func (Refund_Status) EnumDescriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{5, 0}
}

type CreateRefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required,min=1"
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"required,prefix_date,max=40"
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	// @gotags: validate:"required,prefix_date,max=45"
	MRefundId string `protobuf:"bytes,3,opt,name=m_refund_id,json=mRefundId,proto3" json:"m_refund_id,omitempty"`
	// @gotags: validate:"required,min=1"
	Amount int64 `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// @gotags: validate:"max=100"
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreateRefundRequest) Reset() {
	*x = CreateRefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRefundRequest) ProtoMessage() {}

func (x *CreateRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRefundRequest.ProtoReflect.Descriptor instead.
func (*CreateRefundRequest) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRefundRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *CreateRefundRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *CreateRefundRequest) GetMRefundId() string {
	if x != nil {
		return x.MRefundId
	}
	return ""
}

func (x *CreateRefundRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreateRefundRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId int64 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
}

func (x *CreateRefundResponse) Reset() {
	*x = CreateRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRefundResponse) ProtoMessage() {}

func (x *CreateRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRefundResponse.ProtoReflect.Descriptor instead.
func (*CreateRefundResponse) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{1}
}

func (x *CreateRefundResponse) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

type GetRefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required,min=1"
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"required,prefix_date,max=45"
	MRefundId string `protobuf:"bytes,2,opt,name=m_refund_id,json=mRefundId,proto3" json:"m_refund_id,omitempty"`
}

func (x *GetRefundRequest) Reset() {
	*x = GetRefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundRequest) ProtoMessage() {}

func (x *GetRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundRequest.ProtoReflect.Descriptor instead.
func (*GetRefundRequest) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{2}
}

func (x *GetRefundRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetRefundRequest) GetMRefundId() string {
	if x != nil {
		return x.MRefundId
	}
	return ""
}

type ListRefundsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: validate:"required,min=1"
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: validate:"required,prefix_date,max=40"
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
}

func (x *ListRefundsRequest) Reset() {
	*x = ListRefundsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRefundsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRefundsRequest) ProtoMessage() {}

func (x *ListRefundsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRefundsRequest.ProtoReflect.Descriptor instead.
func (*ListRefundsRequest) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{3}
}

func (x *ListRefundsRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *ListRefundsRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

type ListRefundsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Refunds             []*Refund `protobuf:"bytes,1,rep,name=refunds,proto3" json:"refunds,omitempty"`
	TotalRefundedAmount int64     `protobuf:"varint,2,opt,name=total_refunded_amount,json=totalRefundedAmount,proto3" json:"total_refunded_amount,omitempty"`
}

func (x *ListRefundsResponse) Reset() {
	*x = ListRefundsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRefundsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRefundsResponse) ProtoMessage() {}

func (x *ListRefundsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRefundsResponse.ProtoReflect.Descriptor instead.
func (*ListRefundsResponse) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{4}
}

func (x *ListRefundsResponse) GetRefunds() []*Refund {
	if x != nil {
		return x.Refunds
	}
	return nil
}

func (x *ListRefundsResponse) GetTotalRefundedAmount() int64 {
	if x != nil {
		return x.TotalRefundedAmount
	}
	return 0
}

type Refund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId     int64                  `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	MRefundId    string                 `protobuf:"bytes,2,opt,name=m_refund_id,json=mRefundId,proto3" json:"m_refund_id,omitempty"`
	AppId        int32                  `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppTransId   string                 `protobuf:"bytes,4,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	Amount       int64                  `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Status       Refund_Status          `protobuf:"varint,6,opt,name=status,proto3,enum=acquiring_refund.v1.Refund_Status" json:"status,omitempty"`
	ReasonStatus *ReasonStatus          `protobuf:"bytes,7,opt,name=reason_status,json=reasonStatus,proto3" json:"reason_status,omitempty"`
	CreateTime   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CompleteTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
}

func (x *Refund) Reset() {
	*x = Refund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Refund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Refund) ProtoMessage() {}

func (x *Refund) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Refund.ProtoReflect.Descriptor instead.
func (*Refund) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{5}
}

func (x *Refund) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *Refund) GetMRefundId() string {
	if x != nil {
		return x.MRefundId
	}
	return ""
}

func (x *Refund) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *Refund) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *Refund) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Refund) GetStatus() Refund_Status {
	if x != nil {
		return x.Status
	}
	return Refund_STATUS_UNSPECIFIED
}

func (x *Refund) GetReasonStatus() *ReasonStatus {
	if x != nil {
		return x.ReasonStatus
	}
	return nil
}

func (x *Refund) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Refund) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

type ReasonStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string            `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Reason   string            `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ReasonStatus) Reset() {
	*x = ReasonStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_refund_core_refund_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReasonStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReasonStatus) ProtoMessage() {}

func (x *ReasonStatus) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_refund_core_refund_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReasonStatus.ProtoReflect.Descriptor instead.
func (*ReasonStatus) Descriptor() ([]byte, []int) {
	return file_external_services_refund_core_refund_proto_rawDescGZIP(), []int{6}
}

func (x *ReasonStatus) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ReasonStatus) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReasonStatus) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

var File_external_services_refund_core_refund_proto protoreflect.FileDescriptor

var file_external_services_refund_core_refund_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xa8, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a,
	0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x22, 0x49, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0b, 0x6d, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x4d, 0x0a,
	0x12, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x22, 0x80, 0x01, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x52, 0x07, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xed, 0x03, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x5f, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x0d, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x53, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x22,
	0xc8, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x4b, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a,
	0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xa7, 0x02, 0x0a, 0x0d, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x0c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x28, 0x2e, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4f, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x25,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x12, 0x60, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x73, 0x12, 0x27, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3b, 0x5a, 0x39, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x3b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_refund_core_refund_proto_rawDescOnce sync.Once
	file_external_services_refund_core_refund_proto_rawDescData = file_external_services_refund_core_refund_proto_rawDesc
)

func file_external_services_refund_core_refund_proto_rawDescGZIP() []byte {
	file_external_services_refund_core_refund_proto_rawDescOnce.Do(func() {
		file_external_services_refund_core_refund_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_refund_core_refund_proto_rawDescData)
	})
	return file_external_services_refund_core_refund_proto_rawDescData
}

var file_external_services_refund_core_refund_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_external_services_refund_core_refund_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_external_services_refund_core_refund_proto_goTypes = []any{
	(Refund_Status)(0),            // 0: acquiring_refund.v1.Refund.Status
	(*CreateRefundRequest)(nil),   // 1: acquiring_refund.v1.CreateRefundRequest
	(*CreateRefundResponse)(nil),  // 2: acquiring_refund.v1.CreateRefundResponse
	(*GetRefundRequest)(nil),      // 3: acquiring_refund.v1.GetRefundRequest
	(*ListRefundsRequest)(nil),    // 4: acquiring_refund.v1.ListRefundsRequest
	(*ListRefundsResponse)(nil),   // 5: acquiring_refund.v1.ListRefundsResponse
	(*Refund)(nil),                // 6: acquiring_refund.v1.Refund
	(*ReasonStatus)(nil),          // 7: acquiring_refund.v1.ReasonStatus
	nil,                           // 8: acquiring_refund.v1.ReasonStatus.MetadataEntry
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
}
var file_external_services_refund_core_refund_proto_depIdxs = []int32{
	6, // 0: acquiring_refund.v1.ListRefundsResponse.refunds:type_name -> acquiring_refund.v1.Refund
	0, // 1: acquiring_refund.v1.Refund.status:type_name -> acquiring_refund.v1.Refund.Status
	7, // 2: acquiring_refund.v1.Refund.reason_status:type_name -> acquiring_refund.v1.ReasonStatus
	9, // 3: acquiring_refund.v1.Refund.create_time:type_name -> google.protobuf.Timestamp
	9, // 4: acquiring_refund.v1.Refund.complete_time:type_name -> google.protobuf.Timestamp
	8, // 5: acquiring_refund.v1.ReasonStatus.metadata:type_name -> acquiring_refund.v1.ReasonStatus.MetadataEntry
	1, // 6: acquiring_refund.v1.RefundService.CreateRefund:input_type -> acquiring_refund.v1.CreateRefundRequest
	3, // 7: acquiring_refund.v1.RefundService.GetRefund:input_type -> acquiring_refund.v1.GetRefundRequest
	4, // 8: acquiring_refund.v1.RefundService.ListRefunds:input_type -> acquiring_refund.v1.ListRefundsRequest
	2, // 9: acquiring_refund.v1.RefundService.CreateRefund:output_type -> acquiring_refund.v1.CreateRefundResponse
	6, // 10: acquiring_refund.v1.RefundService.GetRefund:output_type -> acquiring_refund.v1.Refund
	5, // 11: acquiring_refund.v1.RefundService.ListRefunds:output_type -> acquiring_refund.v1.ListRefundsResponse
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_external_services_refund_core_refund_proto_init() }
func file_external_services_refund_core_refund_proto_init() {
	if File_external_services_refund_core_refund_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_refund_core_refund_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CreateRefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_refund_core_refund_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CreateRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_refund_core_refund_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetRefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_refund_core_refund_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ListRefundsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_refund_core_refund_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ListRefundsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_refund_core_refund_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Refund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_refund_core_refund_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ReasonStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_refund_core_refund_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_refund_core_refund_proto_goTypes,
		DependencyIndexes: file_external_services_refund_core_refund_proto_depIdxs,
		EnumInfos:         file_external_services_refund_core_refund_proto_enumTypes,
		MessageInfos:      file_external_services_refund_core_refund_proto_msgTypes,
	}.Build()
	File_external_services_refund_core_refund_proto = out.File
	file_external_services_refund_core_refund_proto_rawDesc = nil
	file_external_services_refund_core_refund_proto_goTypes = nil
	file_external_services_refund_core_refund_proto_depIdxs = nil
}
