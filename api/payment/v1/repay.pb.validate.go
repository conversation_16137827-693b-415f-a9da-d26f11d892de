// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: payment/v1/repay.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrderRequestMultiError, or nil if none found.
func (m *CreateOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAmount() <= 0 {
		err := CreateOrderRequestValidationError{
			field:  "Amount",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PartnerCode

	if m.GetStatementId() <= 0 {
		err := CreateOrderRequestValidationError{
			field:  "StatementId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetStatementDate()) < 1 {
		err := CreateOrderRequestValidationError{
			field:  "StatementDate",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Extra

	if len(errors) > 0 {
		return CreateOrderRequestMultiError(errors)
	}

	return nil
}

// CreateOrderRequestMultiError is an error wrapping multiple validation errors
// returned by CreateOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderRequestMultiError) AllErrors() []error { return m }

// CreateOrderRequestValidationError is the validation error returned by
// CreateOrderRequest.Validate if the designated constraints aren't met.
type CreateOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderRequestValidationError) ErrorName() string {
	return "CreateOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderRequestValidationError{}

// Validate checks the field values on CreateOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrderResponseMultiError, or nil if none found.
func (m *CreateOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppTransId

	// no validation rules for AppId

	// no validation rules for ZpTransToken

	// no validation rules for TransId

	// no validation rules for OrderNo

	if len(errors) > 0 {
		return CreateOrderResponseMultiError(errors)
	}

	return nil
}

// CreateOrderResponseMultiError is an error wrapping multiple validation
// errors returned by CreateOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderResponseMultiError) AllErrors() []error { return m }

// CreateOrderResponseValidationError is the validation error returned by
// CreateOrderResponse.Validate if the designated constraints aren't met.
type CreateOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderResponseValidationError) ErrorName() string {
	return "CreateOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderResponseValidationError{}

// Validate checks the field values on GetFeeRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFeeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFeeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFeeRequestMultiError, or
// nil if none found.
func (m *GetFeeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := GetFeeRequestValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOrderNo()) < 1 {
		err := GetFeeRequestValidationError{
			field:  "OrderNo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFeeRequestMultiError(errors)
	}

	return nil
}

// GetFeeRequestMultiError is an error wrapping multiple validation errors
// returned by GetFeeRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFeeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeeRequestMultiError) AllErrors() []error { return m }

// GetFeeRequestValidationError is the validation error returned by
// GetFeeRequest.Validate if the designated constraints aren't met.
type GetFeeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeeRequestValidationError) ErrorName() string { return "GetFeeRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFeeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeeRequestValidationError{}

// Validate checks the field values on GetFeeResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFeeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFeeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFeeResponseMultiError,
// or nil if none found.
func (m *GetFeeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFeeResponseValidationError{
					field:  "Fee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFeeResponseValidationError{
					field:  "Fee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFeeResponseValidationError{
				field:  "Fee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFeeResponseMultiError(errors)
	}

	return nil
}

// GetFeeResponseMultiError is an error wrapping multiple validation errors
// returned by GetFeeResponse.ValidateAll() if the designated constraints
// aren't met.
type GetFeeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeeResponseMultiError) AllErrors() []error { return m }

// GetFeeResponseValidationError is the validation error returned by
// GetFeeResponse.Validate if the designated constraints aren't met.
type GetFeeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeeResponseValidationError) ErrorName() string { return "GetFeeResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetFeeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeeResponseValidationError{}

// Validate checks the field values on Fee with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Fee) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Fee with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FeeMultiError, or nil if none found.
func (m *Fee) ValidateAll() error {
	return m.validate(true)
}

func (m *Fee) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeeType

	// no validation rules for FeeAmount

	// no validation rules for Message

	// no validation rules for MessageEn

	if len(errors) > 0 {
		return FeeMultiError(errors)
	}

	return nil
}

// FeeMultiError is an error wrapping multiple validation errors returned by
// Fee.ValidateAll() if the designated constraints aren't met.
type FeeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeeMultiError) AllErrors() []error { return m }

// FeeValidationError is the validation error returned by Fee.Validate if the
// designated constraints aren't met.
type FeeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeeValidationError) ErrorName() string { return "FeeValidationError" }

// Error satisfies the builtin error interface
func (e FeeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFee.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeeValidationError{}
