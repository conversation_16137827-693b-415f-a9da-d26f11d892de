// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: payment/v1/repay.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RePaymentService_CreateOrder_FullMethodName = "/api.payment.v1.RePaymentService/CreateOrder"
)

// RePaymentServiceClient is the client API for RePaymentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RePaymentServiceClient interface {
	CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*CreateOrderResponse, error)
}

type rePaymentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRePaymentServiceClient(cc grpc.ClientConnInterface) RePaymentServiceClient {
	return &rePaymentServiceClient{cc}
}

func (c *rePaymentServiceClient) CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*CreateOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrderResponse)
	err := c.cc.Invoke(ctx, RePaymentService_CreateOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RePaymentServiceServer is the server API for RePaymentService service.
// All implementations must embed UnimplementedRePaymentServiceServer
// for forward compatibility.
type RePaymentServiceServer interface {
	CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderResponse, error)
	mustEmbedUnimplementedRePaymentServiceServer()
}

// UnimplementedRePaymentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRePaymentServiceServer struct{}

func (UnimplementedRePaymentServiceServer) CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrder not implemented")
}
func (UnimplementedRePaymentServiceServer) mustEmbedUnimplementedRePaymentServiceServer() {}
func (UnimplementedRePaymentServiceServer) testEmbeddedByValue()                          {}

// UnsafeRePaymentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RePaymentServiceServer will
// result in compilation errors.
type UnsafeRePaymentServiceServer interface {
	mustEmbedUnimplementedRePaymentServiceServer()
}

func RegisterRePaymentServiceServer(s grpc.ServiceRegistrar, srv RePaymentServiceServer) {
	// If the following call pancis, it indicates UnimplementedRePaymentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RePaymentService_ServiceDesc, srv)
}

func _RePaymentService_CreateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RePaymentServiceServer).CreateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RePaymentService_CreateOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RePaymentServiceServer).CreateOrder(ctx, req.(*CreateOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RePaymentService_ServiceDesc is the grpc.ServiceDesc for RePaymentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RePaymentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.payment.v1.RePaymentService",
	HandlerType: (*RePaymentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrder",
			Handler:    _RePaymentService_CreateOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "payment/v1/repay.proto",
}

const (
	FeeService_GetFee_FullMethodName = "/api.payment.v1.FeeService/GetFee"
)

// FeeServiceClient is the client API for FeeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FeeServiceClient interface {
	GetFee(ctx context.Context, in *GetFeeRequest, opts ...grpc.CallOption) (*GetFeeResponse, error)
}

type feeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFeeServiceClient(cc grpc.ClientConnInterface) FeeServiceClient {
	return &feeServiceClient{cc}
}

func (c *feeServiceClient) GetFee(ctx context.Context, in *GetFeeRequest, opts ...grpc.CallOption) (*GetFeeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFeeResponse)
	err := c.cc.Invoke(ctx, FeeService_GetFee_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeeServiceServer is the server API for FeeService service.
// All implementations must embed UnimplementedFeeServiceServer
// for forward compatibility.
type FeeServiceServer interface {
	GetFee(context.Context, *GetFeeRequest) (*GetFeeResponse, error)
	mustEmbedUnimplementedFeeServiceServer()
}

// UnimplementedFeeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFeeServiceServer struct{}

func (UnimplementedFeeServiceServer) GetFee(context.Context, *GetFeeRequest) (*GetFeeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFee not implemented")
}
func (UnimplementedFeeServiceServer) mustEmbedUnimplementedFeeServiceServer() {}
func (UnimplementedFeeServiceServer) testEmbeddedByValue()                    {}

// UnsafeFeeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FeeServiceServer will
// result in compilation errors.
type UnsafeFeeServiceServer interface {
	mustEmbedUnimplementedFeeServiceServer()
}

func RegisterFeeServiceServer(s grpc.ServiceRegistrar, srv FeeServiceServer) {
	// If the following call pancis, it indicates UnimplementedFeeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FeeService_ServiceDesc, srv)
}

func _FeeService_GetFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeeServiceServer).GetFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FeeService_GetFee_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeeServiceServer).GetFee(ctx, req.(*GetFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FeeService_ServiceDesc is the grpc.ServiceDesc for FeeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FeeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.payment.v1.FeeService",
	HandlerType: (*FeeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFee",
			Handler:    _FeeService_GetFee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "payment/v1/repay.proto",
}
