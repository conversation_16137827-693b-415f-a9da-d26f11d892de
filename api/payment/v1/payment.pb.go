// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: payment/v1/payment.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExchangeRequest_Action int32

const (
	ExchangeRequest_UNKNOWN ExchangeRequest_Action = 0
	ExchangeRequest_DEDUCT  ExchangeRequest_Action = 1
	ExchangeRequest_ADD     ExchangeRequest_Action = 2
)

// Enum value maps for ExchangeRequest_Action.
var (
	ExchangeRequest_Action_name = map[int32]string{
		0: "UNKNOWN",
		1: "DEDUCT",
		2: "ADD",
	}
	ExchangeRequest_Action_value = map[string]int32{
		"UNKNOWN": 0,
		"DEDUCT":  1,
		"ADD":     2,
	}
)

func (x ExchangeRequest_Action) Enum() *ExchangeRequest_Action {
	p := new(ExchangeRequest_Action)
	*p = x
	return p
}

func (x ExchangeRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangeRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_payment_v1_payment_proto_enumTypes[0].Descriptor()
}

func (ExchangeRequest_Action) Type() protoreflect.EnumType {
	return &file_payment_v1_payment_proto_enumTypes[0]
}

func (x ExchangeRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangeRequest_Action.Descriptor instead.
func (ExchangeRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{0, 0}
}

type ReturnCode_Status int32

const (
	ReturnCode_STATUS_UNSPECIFIED ReturnCode_Status = 0
	ReturnCode_OK                 ReturnCode_Status = 1
	ReturnCode_SYSTEM_ERROR       ReturnCode_Status = 2
	ReturnCode_RETRYABLE          ReturnCode_Status = 3
	ReturnCode_NOT_FOUND          ReturnCode_Status = 4
)

// Enum value maps for ReturnCode_Status.
var (
	ReturnCode_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "OK",
		2: "SYSTEM_ERROR",
		3: "RETRYABLE",
		4: "NOT_FOUND",
	}
	ReturnCode_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"OK":                 1,
		"SYSTEM_ERROR":       2,
		"RETRYABLE":          3,
		"NOT_FOUND":          4,
	}
)

func (x ReturnCode_Status) Enum() *ReturnCode_Status {
	p := new(ReturnCode_Status)
	*p = x
	return p
}

func (x ReturnCode_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReturnCode_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_payment_v1_payment_proto_enumTypes[1].Descriptor()
}

func (ReturnCode_Status) Type() protoreflect.EnumType {
	return &file_payment_v1_payment_proto_enumTypes[1]
}

func (x ReturnCode_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReturnCode_Status.Descriptor instead.
func (ReturnCode_Status) EnumDescriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{13, 0}
}

type ReturnCode_Code int32

const (
	ReturnCode_CODE_UNSPECIFIED ReturnCode_Code = 0
	ReturnCode_SUCCEEDED        ReturnCode_Code = 1
	ReturnCode_FAILED           ReturnCode_Code = 2
	ReturnCode_SUBMITTED        ReturnCode_Code = 3
)

// Enum value maps for ReturnCode_Code.
var (
	ReturnCode_Code_name = map[int32]string{
		0: "CODE_UNSPECIFIED",
		1: "SUCCEEDED",
		2: "FAILED",
		3: "SUBMITTED",
	}
	ReturnCode_Code_value = map[string]int32{
		"CODE_UNSPECIFIED": 0,
		"SUCCEEDED":        1,
		"FAILED":           2,
		"SUBMITTED":        3,
	}
)

func (x ReturnCode_Code) Enum() *ReturnCode_Code {
	p := new(ReturnCode_Code)
	*p = x
	return p
}

func (x ReturnCode_Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReturnCode_Code) Descriptor() protoreflect.EnumDescriptor {
	return file_payment_v1_payment_proto_enumTypes[2].Descriptor()
}

func (ReturnCode_Code) Type() protoreflect.EnumType {
	return &file_payment_v1_payment_proto_enumTypes[2]
}

func (x ReturnCode_Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReturnCode_Code.Descriptor instead.
func (ReturnCode_Code) EnumDescriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{13, 1}
}

type ExchangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction ID in ZaloPay
	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Request Id is id which reflects between ZaloPay and external clients to idempontent
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Asset Data
	// Include asset_id Field identify asset information eg: bim_token, card_id, user_id, ...
	Asset  *Asset `protobuf:"bytes,3,opt,name=asset,proto3" json:"asset,omitempty"`
	Amount int64  `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// add or deduct
	Action ExchangeRequest_Action `protobuf:"varint,5,opt,name=action,proto3,enum=asset_exchange.v2.ExchangeRequest_Action" json:"action,omitempty"`
	// Extra data which external service need to handle transaction
	ExtraData map[string]string `protobuf:"bytes,6,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ExchangeRequest) Reset() {
	*x = ExchangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRequest) ProtoMessage() {}

func (x *ExchangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRequest.ProtoReflect.Descriptor instead.
func (*ExchangeRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{0}
}

func (x *ExchangeRequest) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *ExchangeRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ExchangeRequest) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

func (x *ExchangeRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ExchangeRequest) GetAction() ExchangeRequest_Action {
	if x != nil {
		return x.Action
	}
	return ExchangeRequest_UNKNOWN
}

func (x *ExchangeRequest) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

type Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[string]string `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Asset) Reset() {
	*x = Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{1}
}

func (x *Asset) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

type ExchangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction ID in ZaloPay
	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Request_id is id which reflects between ZaloPay and external clients to idempontent
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Client reference source of fund transaciton mapping 1:1 sub_trans_id
	RefSofId string `protobuf:"bytes,3,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	// To determine current state of transaction in client systems
	ReturnCode *ReturnCode       `protobuf:"bytes,4,opt,name=return_code,json=returnCode,proto3" json:"return_code,omitempty"`
	ExtraData  map[string]string `protobuf:"bytes,5,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PaymentNo  string            `protobuf:"bytes,6,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
}

func (x *ExchangeResponse) Reset() {
	*x = ExchangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeResponse) ProtoMessage() {}

func (x *ExchangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeResponse.ProtoReflect.Descriptor instead.
func (*ExchangeResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{2}
}

func (x *ExchangeResponse) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *ExchangeResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ExchangeResponse) GetRefSofId() string {
	if x != nil {
		return x.RefSofId
	}
	return ""
}

func (x *ExchangeResponse) GetReturnCode() *ReturnCode {
	if x != nil {
		return x.ReturnCode
	}
	return nil
}

func (x *ExchangeResponse) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

func (x *ExchangeResponse) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

type RevertExchangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction ID in ZaloPay
	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Request ID is id which reflects between ZaloPay and external clients to idempotent
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Like request_id, revert_request_id is id to identify sub-transaction
	RevertRequestId string `protobuf:"bytes,3,opt,name=revert_request_id,json=revertRequestId,proto3" json:"revert_request_id,omitempty"`
	// Client reference source of fund transaciton mapping 1:1 request_id
	RefSofId string `protobuf:"bytes,4,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	// Source of fund code is defined for specificed external service like (Paylater, Installment ...)
	SofCode string `protobuf:"bytes,5,opt,name=sof_code,json=sofCode,proto3" json:"sof_code,omitempty"`
	// Extra data
	ExtraData map[string]string `protobuf:"bytes,6,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RevertExchangeRequest) Reset() {
	*x = RevertExchangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevertExchangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevertExchangeRequest) ProtoMessage() {}

func (x *RevertExchangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevertExchangeRequest.ProtoReflect.Descriptor instead.
func (*RevertExchangeRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{3}
}

func (x *RevertExchangeRequest) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *RevertExchangeRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RevertExchangeRequest) GetRevertRequestId() string {
	if x != nil {
		return x.RevertRequestId
	}
	return ""
}

func (x *RevertExchangeRequest) GetRefSofId() string {
	if x != nil {
		return x.RefSofId
	}
	return ""
}

func (x *RevertExchangeRequest) GetSofCode() string {
	if x != nil {
		return x.SofCode
	}
	return ""
}

func (x *RevertExchangeRequest) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

type RevertExchangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction ID in ZaloPay
	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Request ID is id which reflects between ZaloPay and external clients
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Like request_id, revert_request_id is id to identify sub-transaction
	RevertRequestId string `protobuf:"bytes,3,opt,name=revert_request_id,json=revertRequestId,proto3" json:"revert_request_id,omitempty"`
	// Client reference source of fund transaction mapping 1:1 request_id
	RefSofId string `protobuf:"bytes,4,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	// Client reference transaction mapping 1:1 revert_request_id
	RevertRefSofId string `protobuf:"bytes,5,opt,name=revert_ref_sof_id,json=revertRefSofId,proto3" json:"revert_ref_sof_id,omitempty"`
	// To determine current state of transaction in client systems
	ReturnCode *ReturnCode `protobuf:"bytes,6,opt,name=return_code,json=returnCode,proto3" json:"return_code,omitempty"`
	// Extra data
	ExtraData map[string]string `protobuf:"bytes,7,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RevertExchangeResponse) Reset() {
	*x = RevertExchangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevertExchangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevertExchangeResponse) ProtoMessage() {}

func (x *RevertExchangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevertExchangeResponse.ProtoReflect.Descriptor instead.
func (*RevertExchangeResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{4}
}

func (x *RevertExchangeResponse) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *RevertExchangeResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RevertExchangeResponse) GetRevertRequestId() string {
	if x != nil {
		return x.RevertRequestId
	}
	return ""
}

func (x *RevertExchangeResponse) GetRefSofId() string {
	if x != nil {
		return x.RefSofId
	}
	return ""
}

func (x *RevertExchangeResponse) GetRevertRefSofId() string {
	if x != nil {
		return x.RevertRefSofId
	}
	return ""
}

func (x *RevertExchangeResponse) GetReturnCode() *ReturnCode {
	if x != nil {
		return x.ReturnCode
	}
	return nil
}

func (x *RevertExchangeResponse) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

type QueryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction ID in ZaloPay
	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Request_id is id which reflects between ZaloPay and external clients to idempotent
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *QueryStatusRequest) Reset() {
	*x = QueryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRequest) ProtoMessage() {}

func (x *QueryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRequest.ProtoReflect.Descriptor instead.
func (*QueryStatusRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{5}
}

func (x *QueryStatusRequest) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *QueryStatusRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type QueryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transaction ID in ZaloPay
	TransId int64 `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	// Request Id is id which reflects between ZaloPay and external clients to idempontent
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Client reference source of fund transaciton mapping 1:1 request_id
	RefSofId string `protobuf:"bytes,3,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	// To determine current state of transaction in client systems
	ReturnCode *ReturnCode `protobuf:"bytes,4,opt,name=return_code,json=returnCode,proto3" json:"return_code,omitempty"`
	// Extra data
	ExtraData map[string]string `protobuf:"bytes,5,rep,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PaymentNo string            `protobuf:"bytes,6,opt,name=payment_no,json=paymentNo,proto3" json:"payment_no,omitempty"`
}

func (x *QueryStatusResponse) Reset() {
	*x = QueryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusResponse) ProtoMessage() {}

func (x *QueryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusResponse.ProtoReflect.Descriptor instead.
func (*QueryStatusResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{6}
}

func (x *QueryStatusResponse) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *QueryStatusResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *QueryStatusResponse) GetRefSofId() string {
	if x != nil {
		return x.RefSofId
	}
	return ""
}

func (x *QueryStatusResponse) GetReturnCode() *ReturnCode {
	if x != nil {
		return x.ReturnCode
	}
	return nil
}

func (x *QueryStatusResponse) GetExtraData() map[string]string {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

func (x *QueryStatusResponse) GetPaymentNo() string {
	if x != nil {
		return x.PaymentNo
	}
	return ""
}

type ConfirmAuthenticationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId   string `protobuf:"bytes,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"` //zlp_trans_id
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Types that are assignable to AuthData:
	//
	//	*ConfirmAuthenticationRequest_Otp
	//	*ConfirmAuthenticationRequest_Face
	AuthData isConfirmAuthenticationRequest_AuthData `protobuf_oneof:"auth_data"`
}

func (x *ConfirmAuthenticationRequest) Reset() {
	*x = ConfirmAuthenticationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmAuthenticationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmAuthenticationRequest) ProtoMessage() {}

func (x *ConfirmAuthenticationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmAuthenticationRequest.ProtoReflect.Descriptor instead.
func (*ConfirmAuthenticationRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{7}
}

func (x *ConfirmAuthenticationRequest) GetTransId() string {
	if x != nil {
		return x.TransId
	}
	return ""
}

func (x *ConfirmAuthenticationRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (m *ConfirmAuthenticationRequest) GetAuthData() isConfirmAuthenticationRequest_AuthData {
	if m != nil {
		return m.AuthData
	}
	return nil
}

func (x *ConfirmAuthenticationRequest) GetOtp() *OTPAuthenticationData {
	if x, ok := x.GetAuthData().(*ConfirmAuthenticationRequest_Otp); ok {
		return x.Otp
	}
	return nil
}

func (x *ConfirmAuthenticationRequest) GetFace() *FaceAuthenticationData {
	if x, ok := x.GetAuthData().(*ConfirmAuthenticationRequest_Face); ok {
		return x.Face
	}
	return nil
}

type isConfirmAuthenticationRequest_AuthData interface {
	isConfirmAuthenticationRequest_AuthData()
}

type ConfirmAuthenticationRequest_Otp struct {
	Otp *OTPAuthenticationData `protobuf:"bytes,3,opt,name=otp,proto3,oneof"`
}

type ConfirmAuthenticationRequest_Face struct {
	Face *FaceAuthenticationData `protobuf:"bytes,4,opt,name=face,proto3,oneof"`
}

func (*ConfirmAuthenticationRequest_Otp) isConfirmAuthenticationRequest_AuthData() {}

func (*ConfirmAuthenticationRequest_Face) isConfirmAuthenticationRequest_AuthData() {}

type ConfirmAuthenticationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId    int64       `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	RequestId  string      `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ReturnCode *ReturnCode `protobuf:"bytes,3,opt,name=return_code,json=returnCode,proto3" json:"return_code,omitempty"`
}

func (x *ConfirmAuthenticationResponse) Reset() {
	*x = ConfirmAuthenticationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmAuthenticationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmAuthenticationResponse) ProtoMessage() {}

func (x *ConfirmAuthenticationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmAuthenticationResponse.ProtoReflect.Descriptor instead.
func (*ConfirmAuthenticationResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{8}
}

func (x *ConfirmAuthenticationResponse) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *ConfirmAuthenticationResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ConfirmAuthenticationResponse) GetReturnCode() *ReturnCode {
	if x != nil {
		return x.ReturnCode
	}
	return nil
}

type OTPAuthenticationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Otp string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *OTPAuthenticationData) Reset() {
	*x = OTPAuthenticationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OTPAuthenticationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OTPAuthenticationData) ProtoMessage() {}

func (x *OTPAuthenticationData) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OTPAuthenticationData.ProtoReflect.Descriptor instead.
func (*OTPAuthenticationData) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{9}
}

func (x *OTPAuthenticationData) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type FaceAuthenticationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// face_token is data for identify face authentication session using for get image from UM
	FaceToken string `protobuf:"bytes,1,opt,name=face_token,json=faceToken,proto3" json:"face_token,omitempty"`
}

func (x *FaceAuthenticationData) Reset() {
	*x = FaceAuthenticationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceAuthenticationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceAuthenticationData) ProtoMessage() {}

func (x *FaceAuthenticationData) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceAuthenticationData.ProtoReflect.Descriptor instead.
func (*FaceAuthenticationData) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{10}
}

func (x *FaceAuthenticationData) GetFaceToken() string {
	if x != nil {
		return x.FaceToken
	}
	return ""
}

type ZasIdQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountingCode string `protobuf:"bytes,1,opt,name=accounting_code,json=accountingCode,proto3" json:"accounting_code,omitempty"`
	Asset          *Asset `protobuf:"bytes,2,opt,name=asset,proto3" json:"asset,omitempty"`
}

func (x *ZasIdQueryRequest) Reset() {
	*x = ZasIdQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZasIdQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZasIdQueryRequest) ProtoMessage() {}

func (x *ZasIdQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZasIdQueryRequest.ProtoReflect.Descriptor instead.
func (*ZasIdQueryRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{11}
}

func (x *ZasIdQueryRequest) GetAccountingCode() string {
	if x != nil {
		return x.AccountingCode
	}
	return ""
}

func (x *ZasIdQueryRequest) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type ZasIdQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReturnCode *ReturnCode `protobuf:"bytes,1,opt,name=return_code,json=returnCode,proto3" json:"return_code,omitempty"`
	ZasId      int64       `protobuf:"varint,2,opt,name=zas_id,json=zasId,proto3" json:"zas_id,omitempty"`
}

func (x *ZasIdQueryResponse) Reset() {
	*x = ZasIdQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZasIdQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZasIdQueryResponse) ProtoMessage() {}

func (x *ZasIdQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZasIdQueryResponse.ProtoReflect.Descriptor instead.
func (*ZasIdQueryResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{12}
}

func (x *ZasIdQueryResponse) GetReturnCode() *ReturnCode {
	if x != nil {
		return x.ReturnCode
	}
	return nil
}

func (x *ZasIdQueryResponse) GetZasId() int64 {
	if x != nil {
		return x.ZasId
	}
	return 0
}

type ReturnCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  ReturnCode_Status `protobuf:"varint,1,opt,name=status,proto3,enum=asset_exchange.v2.ReturnCode_Status" json:"status,omitempty"`
	Code    ReturnCode_Code   `protobuf:"varint,2,opt,name=code,proto3,enum=asset_exchange.v2.ReturnCode_Code" json:"code,omitempty"`
	SubCode string            `protobuf:"bytes,3,opt,name=sub_code,json=subCode,proto3" json:"sub_code,omitempty"`
	Message string            `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ReturnCode) Reset() {
	*x = ReturnCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReturnCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnCode) ProtoMessage() {}

func (x *ReturnCode) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnCode.ProtoReflect.Descriptor instead.
func (*ReturnCode) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{13}
}

func (x *ReturnCode) GetStatus() ReturnCode_Status {
	if x != nil {
		return x.Status
	}
	return ReturnCode_STATUS_UNSPECIFIED
}

func (x *ReturnCode) GetCode() ReturnCode_Code {
	if x != nil {
		return x.Code
	}
	return ReturnCode_CODE_UNSPECIFIED
}

func (x *ReturnCode) GetSubCode() string {
	if x != nil {
		return x.SubCode
	}
	return ""
}

func (x *ReturnCode) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CancelTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransId int64 `protobuf:"varint,1,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
}

func (x *CancelTransactionRequest) Reset() {
	*x = CancelTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelTransactionRequest) ProtoMessage() {}

func (x *CancelTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelTransactionRequest.ProtoReflect.Descriptor instead.
func (*CancelTransactionRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{14}
}

func (x *CancelTransactionRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

type CancelTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefSofId       int64  `protobuf:"varint,1,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	PartnerTransId string `protobuf:"bytes,2,opt,name=partner_trans_id,json=partnerTransId,proto3" json:"partner_trans_id,omitempty"`
}

func (x *CancelTransactionResponse) Reset() {
	*x = CancelTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_payment_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelTransactionResponse) ProtoMessage() {}

func (x *CancelTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_payment_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelTransactionResponse.ProtoReflect.Descriptor instead.
func (*CancelTransactionResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_payment_proto_rawDescGZIP(), []int{15}
}

func (x *CancelTransactionResponse) GetRefSofId() int64 {
	if x != nil {
		return x.RefSofId
	}
	return 0
}

func (x *CancelTransactionResponse) GetPartnerTransId() string {
	if x != nil {
		return x.PartnerTransId
	}
	return ""
}

var File_payment_v1_payment_proto protoreflect.FileDescriptor

var file_payment_v1_payment_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x03, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x50, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x2a, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x44, 0x55, 0x43, 0x54,
	0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x44, 0x44, 0x10, 0x02, 0x22, 0x78, 0x0a, 0x05, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xda, 0x02, 0x0a, 0x10, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f, 0x66,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x51, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4e, 0x6f, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xcc, 0x02, 0x0a, 0x15, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f, 0x66, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x6f, 0x66, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x6f, 0x66, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x0a, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x9e, 0x03, 0x0a, 0x16, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f, 0x66, 0x49, 0x64,
	0x12, 0x29, 0x0a, 0x11, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x73,
	0x6f, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x76,
	0x65, 0x72, 0x74, 0x52, 0x65, 0x66, 0x53, 0x6f, 0x66, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0b, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x57, 0x0a, 0x0a, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x44, 0x61, 0x74, 0x61, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x4e, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x22, 0xe0, 0x02, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f, 0x66,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x54, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe4, 0x01, 0x0a, 0x1c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x3c, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x54, 0x50, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x3f,
	0x0a, 0x04, 0x66, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x61, 0x63, 0x65, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x66, 0x61, 0x63, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x99, 0x01, 0x0a,
	0x1d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x29, 0x0a, 0x15, 0x4f, 0x54, 0x50, 0x41,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6f, 0x74, 0x70, 0x22, 0x40, 0x0a, 0x16, 0x46, 0x61, 0x63, 0x65, 0x41, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a,
	0x0a, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x66, 0x61, 0x63, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x6c, 0x0a, 0x11, 0x5a, 0x61, 0x73, 0x49, 0x64, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x22, 0x6b, 0x0a, 0x12, 0x5a, 0x61, 0x73, 0x49, 0x64, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x7a, 0x61, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x7a, 0x61, 0x73, 0x49, 0x64,
	0x22, 0xd9, 0x02, 0x0a, 0x0a, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x3c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x58, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x54, 0x52, 0x59, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x04, 0x22, 0x46, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x03, 0x22, 0x3a, 0x0a, 0x18,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x7a, 0x70, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x7a,
	0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x22, 0x63, 0x0a, 0x19, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f,
	0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x32, 0xee, 0x04,
	0x0a, 0x0d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12,
	0x53, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x22, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x0e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x28, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72,
	0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0b, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x15, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x41, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x41,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x5a, 0x61,
	0x73, 0x12, 0x24, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x5a, 0x61, 0x73, 0x49, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x5a, 0x61, 0x73, 0x49,
	0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e,
	0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x45,
	0x5a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_payment_v1_payment_proto_rawDescOnce sync.Once
	file_payment_v1_payment_proto_rawDescData = file_payment_v1_payment_proto_rawDesc
)

func file_payment_v1_payment_proto_rawDescGZIP() []byte {
	file_payment_v1_payment_proto_rawDescOnce.Do(func() {
		file_payment_v1_payment_proto_rawDescData = protoimpl.X.CompressGZIP(file_payment_v1_payment_proto_rawDescData)
	})
	return file_payment_v1_payment_proto_rawDescData
}

var file_payment_v1_payment_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_payment_v1_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_payment_v1_payment_proto_goTypes = []any{
	(ExchangeRequest_Action)(0),           // 0: asset_exchange.v2.ExchangeRequest.Action
	(ReturnCode_Status)(0),                // 1: asset_exchange.v2.ReturnCode.Status
	(ReturnCode_Code)(0),                  // 2: asset_exchange.v2.ReturnCode.Code
	(*ExchangeRequest)(nil),               // 3: asset_exchange.v2.ExchangeRequest
	(*Asset)(nil),                         // 4: asset_exchange.v2.Asset
	(*ExchangeResponse)(nil),              // 5: asset_exchange.v2.ExchangeResponse
	(*RevertExchangeRequest)(nil),         // 6: asset_exchange.v2.RevertExchangeRequest
	(*RevertExchangeResponse)(nil),        // 7: asset_exchange.v2.RevertExchangeResponse
	(*QueryStatusRequest)(nil),            // 8: asset_exchange.v2.QueryStatusRequest
	(*QueryStatusResponse)(nil),           // 9: asset_exchange.v2.QueryStatusResponse
	(*ConfirmAuthenticationRequest)(nil),  // 10: asset_exchange.v2.ConfirmAuthenticationRequest
	(*ConfirmAuthenticationResponse)(nil), // 11: asset_exchange.v2.ConfirmAuthenticationResponse
	(*OTPAuthenticationData)(nil),         // 12: asset_exchange.v2.OTPAuthenticationData
	(*FaceAuthenticationData)(nil),        // 13: asset_exchange.v2.FaceAuthenticationData
	(*ZasIdQueryRequest)(nil),             // 14: asset_exchange.v2.ZasIdQueryRequest
	(*ZasIdQueryResponse)(nil),            // 15: asset_exchange.v2.ZasIdQueryResponse
	(*ReturnCode)(nil),                    // 16: asset_exchange.v2.ReturnCode
	(*CancelTransactionRequest)(nil),      // 17: asset_exchange.v2.CancelTransactionRequest
	(*CancelTransactionResponse)(nil),     // 18: asset_exchange.v2.CancelTransactionResponse
	nil,                                   // 19: asset_exchange.v2.ExchangeRequest.ExtraDataEntry
	nil,                                   // 20: asset_exchange.v2.Asset.DataEntry
	nil,                                   // 21: asset_exchange.v2.ExchangeResponse.ExtraDataEntry
	nil,                                   // 22: asset_exchange.v2.RevertExchangeRequest.ExtraDataEntry
	nil,                                   // 23: asset_exchange.v2.RevertExchangeResponse.ExtraDataEntry
	nil,                                   // 24: asset_exchange.v2.QueryStatusResponse.ExtraDataEntry
}
var file_payment_v1_payment_proto_depIdxs = []int32{
	4,  // 0: asset_exchange.v2.ExchangeRequest.asset:type_name -> asset_exchange.v2.Asset
	0,  // 1: asset_exchange.v2.ExchangeRequest.action:type_name -> asset_exchange.v2.ExchangeRequest.Action
	19, // 2: asset_exchange.v2.ExchangeRequest.extra_data:type_name -> asset_exchange.v2.ExchangeRequest.ExtraDataEntry
	20, // 3: asset_exchange.v2.Asset.data:type_name -> asset_exchange.v2.Asset.DataEntry
	16, // 4: asset_exchange.v2.ExchangeResponse.return_code:type_name -> asset_exchange.v2.ReturnCode
	21, // 5: asset_exchange.v2.ExchangeResponse.extra_data:type_name -> asset_exchange.v2.ExchangeResponse.ExtraDataEntry
	22, // 6: asset_exchange.v2.RevertExchangeRequest.extra_data:type_name -> asset_exchange.v2.RevertExchangeRequest.ExtraDataEntry
	16, // 7: asset_exchange.v2.RevertExchangeResponse.return_code:type_name -> asset_exchange.v2.ReturnCode
	23, // 8: asset_exchange.v2.RevertExchangeResponse.extra_data:type_name -> asset_exchange.v2.RevertExchangeResponse.ExtraDataEntry
	16, // 9: asset_exchange.v2.QueryStatusResponse.return_code:type_name -> asset_exchange.v2.ReturnCode
	24, // 10: asset_exchange.v2.QueryStatusResponse.extra_data:type_name -> asset_exchange.v2.QueryStatusResponse.ExtraDataEntry
	12, // 11: asset_exchange.v2.ConfirmAuthenticationRequest.otp:type_name -> asset_exchange.v2.OTPAuthenticationData
	13, // 12: asset_exchange.v2.ConfirmAuthenticationRequest.face:type_name -> asset_exchange.v2.FaceAuthenticationData
	16, // 13: asset_exchange.v2.ConfirmAuthenticationResponse.return_code:type_name -> asset_exchange.v2.ReturnCode
	4,  // 14: asset_exchange.v2.ZasIdQueryRequest.asset:type_name -> asset_exchange.v2.Asset
	16, // 15: asset_exchange.v2.ZasIdQueryResponse.return_code:type_name -> asset_exchange.v2.ReturnCode
	1,  // 16: asset_exchange.v2.ReturnCode.status:type_name -> asset_exchange.v2.ReturnCode.Status
	2,  // 17: asset_exchange.v2.ReturnCode.code:type_name -> asset_exchange.v2.ReturnCode.Code
	3,  // 18: asset_exchange.v2.AssetProvider.Exchange:input_type -> asset_exchange.v2.ExchangeRequest
	6,  // 19: asset_exchange.v2.AssetProvider.RevertExchange:input_type -> asset_exchange.v2.RevertExchangeRequest
	8,  // 20: asset_exchange.v2.AssetProvider.QueryStatus:input_type -> asset_exchange.v2.QueryStatusRequest
	10, // 21: asset_exchange.v2.AssetProvider.ConfirmAuthentication:input_type -> asset_exchange.v2.ConfirmAuthenticationRequest
	14, // 22: asset_exchange.v2.AssetProvider.QueryZas:input_type -> asset_exchange.v2.ZasIdQueryRequest
	17, // 23: asset_exchange.v2.AssetProvider.CancelTransaction:input_type -> asset_exchange.v2.CancelTransactionRequest
	5,  // 24: asset_exchange.v2.AssetProvider.Exchange:output_type -> asset_exchange.v2.ExchangeResponse
	7,  // 25: asset_exchange.v2.AssetProvider.RevertExchange:output_type -> asset_exchange.v2.RevertExchangeResponse
	9,  // 26: asset_exchange.v2.AssetProvider.QueryStatus:output_type -> asset_exchange.v2.QueryStatusResponse
	11, // 27: asset_exchange.v2.AssetProvider.ConfirmAuthentication:output_type -> asset_exchange.v2.ConfirmAuthenticationResponse
	15, // 28: asset_exchange.v2.AssetProvider.QueryZas:output_type -> asset_exchange.v2.ZasIdQueryResponse
	18, // 29: asset_exchange.v2.AssetProvider.CancelTransaction:output_type -> asset_exchange.v2.CancelTransactionResponse
	24, // [24:30] is the sub-list for method output_type
	18, // [18:24] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_payment_v1_payment_proto_init() }
func file_payment_v1_payment_proto_init() {
	if File_payment_v1_payment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_payment_v1_payment_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ExchangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ExchangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RevertExchangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RevertExchangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*QueryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*QueryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ConfirmAuthenticationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ConfirmAuthenticationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*OTPAuthenticationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*FaceAuthenticationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ZasIdQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ZasIdQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ReturnCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*CancelTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_payment_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*CancelTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_payment_v1_payment_proto_msgTypes[7].OneofWrappers = []any{
		(*ConfirmAuthenticationRequest_Otp)(nil),
		(*ConfirmAuthenticationRequest_Face)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_payment_v1_payment_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_payment_v1_payment_proto_goTypes,
		DependencyIndexes: file_payment_v1_payment_proto_depIdxs,
		EnumInfos:         file_payment_v1_payment_proto_enumTypes,
		MessageInfos:      file_payment_v1_payment_proto_msgTypes,
	}.Build()
	File_payment_v1_payment_proto = out.File
	file_payment_v1_payment_proto_rawDesc = nil
	file_payment_v1_payment_proto_goTypes = nil
	file_payment_v1_payment_proto_depIdxs = nil
}
