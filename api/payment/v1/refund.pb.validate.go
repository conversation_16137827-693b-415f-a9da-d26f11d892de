// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: payment/v1/refund.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PreCheckRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PreCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreCheckRequestMultiError, or nil if none found.
func (m *PreCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PreCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	if utf8.RuneCountInString(m.GetZpTransId()) < 1 {
		err := PreCheckRequestValidationError{
			field:  "ZpTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() < 1 {
		err := PreCheckRequestValidationError{
			field:  "Amount",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppTransId()) < 1 {
		err := PreCheckRequestValidationError{
			field:  "AppTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppId() < 1 {
		err := PreCheckRequestValidationError{
			field:  "AppId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRefundId() < 1 {
		err := PreCheckRequestValidationError{
			field:  "RefundId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PaymentDescription

	if len(errors) > 0 {
		return PreCheckRequestMultiError(errors)
	}

	return nil
}

// PreCheckRequestMultiError is an error wrapping multiple validation errors
// returned by PreCheckRequest.ValidateAll() if the designated constraints
// aren't met.
type PreCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreCheckRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreCheckRequestMultiError) AllErrors() []error { return m }

// PreCheckRequestValidationError is the validation error returned by
// PreCheckRequest.Validate if the designated constraints aren't met.
type PreCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreCheckRequestValidationError) ErrorName() string { return "PreCheckRequestValidationError" }

// Error satisfies the builtin error interface
func (e PreCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreCheckRequestValidationError{}

// Validate checks the field values on PreCheckResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PreCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreCheckResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreCheckResponseMultiError, or nil if none found.
func (m *PreCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PreCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RefundId

	// no validation rules for RefSofId

	// no validation rules for RefundType

	// no validation rules for SettleStatus

	if len(errors) > 0 {
		return PreCheckResponseMultiError(errors)
	}

	return nil
}

// PreCheckResponseMultiError is an error wrapping multiple validation errors
// returned by PreCheckResponse.ValidateAll() if the designated constraints
// aren't met.
type PreCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreCheckResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreCheckResponseMultiError) AllErrors() []error { return m }

// PreCheckResponseValidationError is the validation error returned by
// PreCheckResponse.Validate if the designated constraints aren't met.
type PreCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreCheckResponseValidationError) ErrorName() string { return "PreCheckResponseValidationError" }

// Error satisfies the builtin error interface
func (e PreCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreCheckResponseValidationError{}

// Validate checks the field values on RefundRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RefundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RefundRequestMultiError, or
// nil if none found.
func (m *RefundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	if utf8.RuneCountInString(m.GetZpTransId()) < 1 {
		err := RefundRequestValidationError{
			field:  "ZpTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() < 1 {
		err := RefundRequestValidationError{
			field:  "Amount",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppTransId()) < 1 {
		err := RefundRequestValidationError{
			field:  "AppTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppId() < 1 {
		err := RefundRequestValidationError{
			field:  "AppId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRefundId() < 1 {
		err := RefundRequestValidationError{
			field:  "RefundId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PaymentDescription

	if len(errors) > 0 {
		return RefundRequestMultiError(errors)
	}

	return nil
}

// RefundRequestMultiError is an error wrapping multiple validation errors
// returned by RefundRequest.ValidateAll() if the designated constraints
// aren't met.
type RefundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundRequestMultiError) AllErrors() []error { return m }

// RefundRequestValidationError is the validation error returned by
// RefundRequest.Validate if the designated constraints aren't met.
type RefundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundRequestValidationError) ErrorName() string { return "RefundRequestValidationError" }

// Error satisfies the builtin error interface
func (e RefundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundRequestValidationError{}

// Validate checks the field values on RefundResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RefundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RefundResponseMultiError,
// or nil if none found.
func (m *RefundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	// no validation rules for RefundId

	// no validation rules for RefSofId

	// no validation rules for RefundType

	// no validation rules for TransStatus

	if all {
		switch v := interface{}(m.GetErrorDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefundResponseValidationError{
					field:  "ErrorDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefundResponseValidationError{
					field:  "ErrorDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefundResponseValidationError{
				field:  "ErrorDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefundResponseMultiError(errors)
	}

	return nil
}

// RefundResponseMultiError is an error wrapping multiple validation errors
// returned by RefundResponse.ValidateAll() if the designated constraints
// aren't met.
type RefundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundResponseMultiError) AllErrors() []error { return m }

// RefundResponseValidationError is the validation error returned by
// RefundResponse.Validate if the designated constraints aren't met.
type RefundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundResponseValidationError) ErrorName() string { return "RefundResponseValidationError" }

// Error satisfies the builtin error interface
func (e RefundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundResponseValidationError{}

// Validate checks the field values on RefundQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefundQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefundQueryRequestMultiError, or nil if none found.
func (m *RefundQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	if m.GetRefundId() < 1 {
		err := RefundQueryRequestValidationError{
			field:  "RefundId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RefundQueryRequestMultiError(errors)
	}

	return nil
}

// RefundQueryRequestMultiError is an error wrapping multiple validation errors
// returned by RefundQueryRequest.ValidateAll() if the designated constraints
// aren't met.
type RefundQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundQueryRequestMultiError) AllErrors() []error { return m }

// RefundQueryRequestValidationError is the validation error returned by
// RefundQueryRequest.Validate if the designated constraints aren't met.
type RefundQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundQueryRequestValidationError) ErrorName() string {
	return "RefundQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RefundQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundQueryRequestValidationError{}

// Validate checks the field values on RefundQueryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefundQueryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundQueryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefundQueryResponseMultiError, or nil if none found.
func (m *RefundQueryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundQueryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EscrowBankAccount

	// no validation rules for TransStatus

	// no validation rules for RefSofId

	// no validation rules for RefundId

	if len(errors) > 0 {
		return RefundQueryResponseMultiError(errors)
	}

	return nil
}

// RefundQueryResponseMultiError is an error wrapping multiple validation
// errors returned by RefundQueryResponse.ValidateAll() if the designated
// constraints aren't met.
type RefundQueryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundQueryResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundQueryResponseMultiError) AllErrors() []error { return m }

// RefundQueryResponseValidationError is the validation error returned by
// RefundQueryResponse.Validate if the designated constraints aren't met.
type RefundQueryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundQueryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundQueryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundQueryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundQueryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundQueryResponseValidationError) ErrorName() string {
	return "RefundQueryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RefundQueryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundQueryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundQueryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundQueryResponseValidationError{}

// Validate checks the field values on CreateTopupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTopupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTopupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTopupRequestMultiError, or nil if none found.
func (m *CreateTopupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTopupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAmount() < 1000 {
		err := CreateTopupRequestValidationError{
			field:  "Amount",
			reason: "value must be greater than or equal to 1000",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetZpTransId() < 1 {
		err := CreateTopupRequestValidationError{
			field:  "ZpTransId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateTopupRequestMultiError(errors)
	}

	return nil
}

// CreateTopupRequestMultiError is an error wrapping multiple validation errors
// returned by CreateTopupRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateTopupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTopupRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTopupRequestMultiError) AllErrors() []error { return m }

// CreateTopupRequestValidationError is the validation error returned by
// CreateTopupRequest.Validate if the designated constraints aren't met.
type CreateTopupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTopupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTopupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTopupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTopupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTopupRequestValidationError) ErrorName() string {
	return "CreateTopupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTopupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTopupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTopupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTopupRequestValidationError{}

// Validate checks the field values on CreateTopupResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTopupResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTopupResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTopupResponseMultiError, or nil if none found.
func (m *CreateTopupResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTopupResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for AppId

	// no validation rules for AppTransId

	// no validation rules for ZpTransToken

	if len(errors) > 0 {
		return CreateTopupResponseMultiError(errors)
	}

	return nil
}

// CreateTopupResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTopupResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateTopupResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTopupResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTopupResponseMultiError) AllErrors() []error { return m }

// CreateTopupResponseValidationError is the validation error returned by
// CreateTopupResponse.Validate if the designated constraints aren't met.
type CreateTopupResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTopupResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTopupResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTopupResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTopupResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTopupResponseValidationError) ErrorName() string {
	return "CreateTopupResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTopupResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTopupResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTopupResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTopupResponseValidationError{}

// Validate checks the field values on CalculateInterestEstimationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CalculateInterestEstimationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateInterestEstimationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CalculateInterestEstimationRequestMultiError, or nil if none found.
func (m *CalculateInterestEstimationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateInterestEstimationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetZpTransId()) < 1 {
		err := CalculateInterestEstimationRequestValidationError{
			field:  "ZpTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEstimationDate()) < 1 {
		err := CalculateInterestEstimationRequestValidationError{
			field:  "EstimationDate",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CalculateInterestEstimationRequestMultiError(errors)
	}

	return nil
}

// CalculateInterestEstimationRequestMultiError is an error wrapping multiple
// validation errors returned by
// CalculateInterestEstimationRequest.ValidateAll() if the designated
// constraints aren't met.
type CalculateInterestEstimationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateInterestEstimationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateInterestEstimationRequestMultiError) AllErrors() []error { return m }

// CalculateInterestEstimationRequestValidationError is the validation error
// returned by CalculateInterestEstimationRequest.Validate if the designated
// constraints aren't met.
type CalculateInterestEstimationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateInterestEstimationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateInterestEstimationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateInterestEstimationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateInterestEstimationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateInterestEstimationRequestValidationError) ErrorName() string {
	return "CalculateInterestEstimationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateInterestEstimationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateInterestEstimationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateInterestEstimationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateInterestEstimationRequestValidationError{}

// Validate checks the field values on CalculateInterestEstimationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CalculateInterestEstimationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateInterestEstimationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CalculateInterestEstimationResponseMultiError, or nil if none found.
func (m *CalculateInterestEstimationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateInterestEstimationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalInterest

	if len(errors) > 0 {
		return CalculateInterestEstimationResponseMultiError(errors)
	}

	return nil
}

// CalculateInterestEstimationResponseMultiError is an error wrapping multiple
// validation errors returned by
// CalculateInterestEstimationResponse.ValidateAll() if the designated
// constraints aren't met.
type CalculateInterestEstimationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateInterestEstimationResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateInterestEstimationResponseMultiError) AllErrors() []error { return m }

// CalculateInterestEstimationResponseValidationError is the validation error
// returned by CalculateInterestEstimationResponse.Validate if the designated
// constraints aren't met.
type CalculateInterestEstimationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateInterestEstimationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateInterestEstimationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateInterestEstimationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateInterestEstimationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateInterestEstimationResponseValidationError) ErrorName() string {
	return "CalculateInterestEstimationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateInterestEstimationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateInterestEstimationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateInterestEstimationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateInterestEstimationResponseValidationError{}

// Validate checks the field values on TriggerReconcileSettleJobsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerReconcileSettleJobsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerReconcileSettleJobsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TriggerReconcileSettleJobsRequestMultiError, or nil if none found.
func (m *TriggerReconcileSettleJobsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerReconcileSettleJobsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerReconcileSettleJobsRequestMultiError(errors)
	}

	return nil
}

// TriggerReconcileSettleJobsRequestMultiError is an error wrapping multiple
// validation errors returned by
// TriggerReconcileSettleJobsRequest.ValidateAll() if the designated
// constraints aren't met.
type TriggerReconcileSettleJobsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerReconcileSettleJobsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerReconcileSettleJobsRequestMultiError) AllErrors() []error { return m }

// TriggerReconcileSettleJobsRequestValidationError is the validation error
// returned by TriggerReconcileSettleJobsRequest.Validate if the designated
// constraints aren't met.
type TriggerReconcileSettleJobsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerReconcileSettleJobsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerReconcileSettleJobsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerReconcileSettleJobsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerReconcileSettleJobsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerReconcileSettleJobsRequestValidationError) ErrorName() string {
	return "TriggerReconcileSettleJobsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerReconcileSettleJobsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerReconcileSettleJobsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerReconcileSettleJobsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerReconcileSettleJobsRequestValidationError{}

// Validate checks the field values on TriggerReconcileSettleJobsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerReconcileSettleJobsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerReconcileSettleJobsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TriggerReconcileSettleJobsResponseMultiError, or nil if none found.
func (m *TriggerReconcileSettleJobsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerReconcileSettleJobsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerReconcileSettleJobsResponseMultiError(errors)
	}

	return nil
}

// TriggerReconcileSettleJobsResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerReconcileSettleJobsResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerReconcileSettleJobsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerReconcileSettleJobsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerReconcileSettleJobsResponseMultiError) AllErrors() []error { return m }

// TriggerReconcileSettleJobsResponseValidationError is the validation error
// returned by TriggerReconcileSettleJobsResponse.Validate if the designated
// constraints aren't met.
type TriggerReconcileSettleJobsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerReconcileSettleJobsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerReconcileSettleJobsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerReconcileSettleJobsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerReconcileSettleJobsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerReconcileSettleJobsResponseValidationError) ErrorName() string {
	return "TriggerReconcileSettleJobsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerReconcileSettleJobsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerReconcileSettleJobsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerReconcileSettleJobsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerReconcileSettleJobsResponseValidationError{}

// Validate checks the field values on TriggerProcessSettleInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerProcessSettleInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerProcessSettleInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerProcessSettleInfoRequestMultiError, or nil if none found.
func (m *TriggerProcessSettleInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerProcessSettleInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerProcessSettleInfoRequestMultiError(errors)
	}

	return nil
}

// TriggerProcessSettleInfoRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerProcessSettleInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type TriggerProcessSettleInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerProcessSettleInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerProcessSettleInfoRequestMultiError) AllErrors() []error { return m }

// TriggerProcessSettleInfoRequestValidationError is the validation error
// returned by TriggerProcessSettleInfoRequest.Validate if the designated
// constraints aren't met.
type TriggerProcessSettleInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerProcessSettleInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerProcessSettleInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerProcessSettleInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerProcessSettleInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerProcessSettleInfoRequestValidationError) ErrorName() string {
	return "TriggerProcessSettleInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerProcessSettleInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerProcessSettleInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerProcessSettleInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerProcessSettleInfoRequestValidationError{}

// Validate checks the field values on TriggerProcessSettleInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerProcessSettleInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerProcessSettleInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerProcessSettleInfoResponseMultiError, or nil if none found.
func (m *TriggerProcessSettleInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerProcessSettleInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessedCount

	if len(errors) > 0 {
		return TriggerProcessSettleInfoResponseMultiError(errors)
	}

	return nil
}

// TriggerProcessSettleInfoResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerProcessSettleInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerProcessSettleInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerProcessSettleInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerProcessSettleInfoResponseMultiError) AllErrors() []error { return m }

// TriggerProcessSettleInfoResponseValidationError is the validation error
// returned by TriggerProcessSettleInfoResponse.Validate if the designated
// constraints aren't met.
type TriggerProcessSettleInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerProcessSettleInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerProcessSettleInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerProcessSettleInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerProcessSettleInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerProcessSettleInfoResponseValidationError) ErrorName() string {
	return "TriggerProcessSettleInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerProcessSettleInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerProcessSettleInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerProcessSettleInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerProcessSettleInfoResponseValidationError{}

// Validate checks the field values on TriggerPollingEarlyDischargeJobsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *TriggerPollingEarlyDischargeJobsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TriggerPollingEarlyDischargeJobsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// TriggerPollingEarlyDischargeJobsRequestMultiError, or nil if none found.
func (m *TriggerPollingEarlyDischargeJobsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerPollingEarlyDischargeJobsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerPollingEarlyDischargeJobsRequestMultiError(errors)
	}

	return nil
}

// TriggerPollingEarlyDischargeJobsRequestMultiError is an error wrapping
// multiple validation errors returned by
// TriggerPollingEarlyDischargeJobsRequest.ValidateAll() if the designated
// constraints aren't met.
type TriggerPollingEarlyDischargeJobsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerPollingEarlyDischargeJobsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerPollingEarlyDischargeJobsRequestMultiError) AllErrors() []error { return m }

// TriggerPollingEarlyDischargeJobsRequestValidationError is the validation
// error returned by TriggerPollingEarlyDischargeJobsRequest.Validate if the
// designated constraints aren't met.
type TriggerPollingEarlyDischargeJobsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerPollingEarlyDischargeJobsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerPollingEarlyDischargeJobsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerPollingEarlyDischargeJobsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerPollingEarlyDischargeJobsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerPollingEarlyDischargeJobsRequestValidationError) ErrorName() string {
	return "TriggerPollingEarlyDischargeJobsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerPollingEarlyDischargeJobsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerPollingEarlyDischargeJobsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerPollingEarlyDischargeJobsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerPollingEarlyDischargeJobsRequestValidationError{}

// Validate checks the field values on TriggerPollingEarlyDischargeJobsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *TriggerPollingEarlyDischargeJobsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TriggerPollingEarlyDischargeJobsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// TriggerPollingEarlyDischargeJobsResponseMultiError, or nil if none found.
func (m *TriggerPollingEarlyDischargeJobsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerPollingEarlyDischargeJobsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessedCount

	if len(errors) > 0 {
		return TriggerPollingEarlyDischargeJobsResponseMultiError(errors)
	}

	return nil
}

// TriggerPollingEarlyDischargeJobsResponseMultiError is an error wrapping
// multiple validation errors returned by
// TriggerPollingEarlyDischargeJobsResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerPollingEarlyDischargeJobsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerPollingEarlyDischargeJobsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerPollingEarlyDischargeJobsResponseMultiError) AllErrors() []error { return m }

// TriggerPollingEarlyDischargeJobsResponseValidationError is the validation
// error returned by TriggerPollingEarlyDischargeJobsResponse.Validate if the
// designated constraints aren't met.
type TriggerPollingEarlyDischargeJobsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerPollingEarlyDischargeJobsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerPollingEarlyDischargeJobsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerPollingEarlyDischargeJobsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerPollingEarlyDischargeJobsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerPollingEarlyDischargeJobsResponseValidationError) ErrorName() string {
	return "TriggerPollingEarlyDischargeJobsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerPollingEarlyDischargeJobsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerPollingEarlyDischargeJobsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerPollingEarlyDischargeJobsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerPollingEarlyDischargeJobsResponseValidationError{}

// Validate checks the field values on RetryRefundSettleOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetryRefundSettleOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryRefundSettleOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RetryRefundSettleOrderRequestMultiError, or nil if none found.
func (m *RetryRefundSettleOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryRefundSettleOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RetryRefundSettleOrderRequestMultiError(errors)
	}

	return nil
}

// RetryRefundSettleOrderRequestMultiError is an error wrapping multiple
// validation errors returned by RetryRefundSettleOrderRequest.ValidateAll()
// if the designated constraints aren't met.
type RetryRefundSettleOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryRefundSettleOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryRefundSettleOrderRequestMultiError) AllErrors() []error { return m }

// RetryRefundSettleOrderRequestValidationError is the validation error
// returned by RetryRefundSettleOrderRequest.Validate if the designated
// constraints aren't met.
type RetryRefundSettleOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryRefundSettleOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryRefundSettleOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryRefundSettleOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryRefundSettleOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryRefundSettleOrderRequestValidationError) ErrorName() string {
	return "RetryRefundSettleOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RetryRefundSettleOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryRefundSettleOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryRefundSettleOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryRefundSettleOrderRequestValidationError{}

// Validate checks the field values on RetryRefundSettleOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetryRefundSettleOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryRefundSettleOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RetryRefundSettleOrderResponseMultiError, or nil if none found.
func (m *RetryRefundSettleOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryRefundSettleOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessedCount

	if len(errors) > 0 {
		return RetryRefundSettleOrderResponseMultiError(errors)
	}

	return nil
}

// RetryRefundSettleOrderResponseMultiError is an error wrapping multiple
// validation errors returned by RetryRefundSettleOrderResponse.ValidateAll()
// if the designated constraints aren't met.
type RetryRefundSettleOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryRefundSettleOrderResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryRefundSettleOrderResponseMultiError) AllErrors() []error { return m }

// RetryRefundSettleOrderResponseValidationError is the validation error
// returned by RetryRefundSettleOrderResponse.Validate if the designated
// constraints aren't met.
type RetryRefundSettleOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryRefundSettleOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryRefundSettleOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryRefundSettleOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryRefundSettleOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryRefundSettleOrderResponseValidationError) ErrorName() string {
	return "RetryRefundSettleOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RetryRefundSettleOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryRefundSettleOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryRefundSettleOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryRefundSettleOrderResponseValidationError{}

// Validate checks the field values on RefundSettleEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RefundSettleEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundSettleEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefundSettleEventMultiError, or nil if none found.
func (m *RefundSettleEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundSettleEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventId

	// no validation rules for EventType

	// no validation rules for SettleStatus

	// no validation rules for RefZpTransId

	// no validation rules for SettleAmount

	if all {
		switch v := interface{}(m.GetSettleResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefundSettleEventValidationError{
					field:  "SettleResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefundSettleEventValidationError{
					field:  "SettleResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSettleResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefundSettleEventValidationError{
				field:  "SettleResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.SettleContext.(type) {
	case *RefundSettleEvent_StandardSettle:
		if v == nil {
			err := RefundSettleEventValidationError{
				field:  "SettleContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStandardSettle()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RefundSettleEventValidationError{
						field:  "StandardSettle",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RefundSettleEventValidationError{
						field:  "StandardSettle",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStandardSettle()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RefundSettleEventValidationError{
					field:  "StandardSettle",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RefundSettleEvent_ExpiredSettle:
		if v == nil {
			err := RefundSettleEventValidationError{
				field:  "SettleContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExpiredSettle()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RefundSettleEventValidationError{
						field:  "ExpiredSettle",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RefundSettleEventValidationError{
						field:  "ExpiredSettle",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExpiredSettle()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RefundSettleEventValidationError{
					field:  "ExpiredSettle",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RefundSettleEventMultiError(errors)
	}

	return nil
}

// RefundSettleEventMultiError is an error wrapping multiple validation errors
// returned by RefundSettleEvent.ValidateAll() if the designated constraints
// aren't met.
type RefundSettleEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundSettleEventMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundSettleEventMultiError) AllErrors() []error { return m }

// RefundSettleEventValidationError is the validation error returned by
// RefundSettleEvent.Validate if the designated constraints aren't met.
type RefundSettleEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundSettleEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundSettleEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundSettleEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundSettleEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundSettleEventValidationError) ErrorName() string {
	return "RefundSettleEventValidationError"
}

// Error satisfies the builtin error interface
func (e RefundSettleEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundSettleEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundSettleEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundSettleEventValidationError{}

// Validate checks the field values on RefundStandardSettle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefundStandardSettle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundStandardSettle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefundStandardSettleMultiError, or nil if none found.
func (m *RefundStandardSettle) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundStandardSettle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SettleId

	if len(errors) > 0 {
		return RefundStandardSettleMultiError(errors)
	}

	return nil
}

// RefundStandardSettleMultiError is an error wrapping multiple validation
// errors returned by RefundStandardSettle.ValidateAll() if the designated
// constraints aren't met.
type RefundStandardSettleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundStandardSettleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundStandardSettleMultiError) AllErrors() []error { return m }

// RefundStandardSettleValidationError is the validation error returned by
// RefundStandardSettle.Validate if the designated constraints aren't met.
type RefundStandardSettleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundStandardSettleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundStandardSettleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundStandardSettleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundStandardSettleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundStandardSettleValidationError) ErrorName() string {
	return "RefundStandardSettleValidationError"
}

// Error satisfies the builtin error interface
func (e RefundStandardSettleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundStandardSettle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundStandardSettleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundStandardSettleValidationError{}

// Validate checks the field values on RefundExpiredSettle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefundExpiredSettle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundExpiredSettle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefundExpiredSettleMultiError, or nil if none found.
func (m *RefundExpiredSettle) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundExpiredSettle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RefundId

	if len(errors) > 0 {
		return RefundExpiredSettleMultiError(errors)
	}

	return nil
}

// RefundExpiredSettleMultiError is an error wrapping multiple validation
// errors returned by RefundExpiredSettle.ValidateAll() if the designated
// constraints aren't met.
type RefundExpiredSettleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundExpiredSettleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundExpiredSettleMultiError) AllErrors() []error { return m }

// RefundExpiredSettleValidationError is the validation error returned by
// RefundExpiredSettle.Validate if the designated constraints aren't met.
type RefundExpiredSettleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundExpiredSettleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundExpiredSettleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundExpiredSettleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundExpiredSettleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundExpiredSettleValidationError) ErrorName() string {
	return "RefundExpiredSettleValidationError"
}

// Error satisfies the builtin error interface
func (e RefundExpiredSettleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundExpiredSettle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundExpiredSettleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundExpiredSettleValidationError{}

// Validate checks the field values on RefundSettleEvent_RefundSettleResult
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RefundSettleEvent_RefundSettleResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefundSettleEvent_RefundSettleResult
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RefundSettleEvent_RefundSettleResultMultiError, or nil if none found.
func (m *RefundSettleEvent_RefundSettleResult) ValidateAll() error {
	return m.validate(true)
}

func (m *RefundSettleEvent_RefundSettleResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for OrderId

	// no validation rules for AppId

	// no validation rules for AppTransId

	// no validation rules for TransStatus

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return RefundSettleEvent_RefundSettleResultMultiError(errors)
	}

	return nil
}

// RefundSettleEvent_RefundSettleResultMultiError is an error wrapping multiple
// validation errors returned by
// RefundSettleEvent_RefundSettleResult.ValidateAll() if the designated
// constraints aren't met.
type RefundSettleEvent_RefundSettleResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefundSettleEvent_RefundSettleResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefundSettleEvent_RefundSettleResultMultiError) AllErrors() []error { return m }

// RefundSettleEvent_RefundSettleResultValidationError is the validation error
// returned by RefundSettleEvent_RefundSettleResult.Validate if the designated
// constraints aren't met.
type RefundSettleEvent_RefundSettleResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefundSettleEvent_RefundSettleResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefundSettleEvent_RefundSettleResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefundSettleEvent_RefundSettleResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefundSettleEvent_RefundSettleResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefundSettleEvent_RefundSettleResultValidationError) ErrorName() string {
	return "RefundSettleEvent_RefundSettleResultValidationError"
}

// Error satisfies the builtin error interface
func (e RefundSettleEvent_RefundSettleResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefundSettleEvent_RefundSettleResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefundSettleEvent_RefundSettleResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefundSettleEvent_RefundSettleResultValidationError{}
