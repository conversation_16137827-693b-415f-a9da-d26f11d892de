// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: payment/v1/transaction.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionRequestMultiError, or nil if none found.
func (m *GetTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZpTransId

	if len(errors) > 0 {
		return GetTransactionRequestMultiError(errors)
	}

	return nil
}

// GetTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by GetTransactionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionRequestMultiError) AllErrors() []error { return m }

// GetTransactionRequestValidationError is the validation error returned by
// GetTransactionRequest.Validate if the designated constraints aren't met.
type GetTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionRequestValidationError) ErrorName() string {
	return "GetTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionRequestValidationError{}

// Validate checks the field values on GetTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionResponseMultiError, or nil if none found.
func (m *GetTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionResponseValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionResponseMultiError(errors)
	}

	return nil
}

// GetTransactionResponseMultiError is an error wrapping multiple validation
// errors returned by GetTransactionResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionResponseMultiError) AllErrors() []error { return m }

// GetTransactionResponseValidationError is the validation error returned by
// GetTransactionResponse.Validate if the designated constraints aren't met.
type GetTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionResponseValidationError) ErrorName() string {
	return "GetTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionResponseValidationError{}

// Validate checks the field values on ListTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTransactionRequestMultiError, or nil if none found.
func (m *ListTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTransactionRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTransactionRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTransactionRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTransactionRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTransactionRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTransactionRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPagination() == nil {
		err := ListTransactionRequestValidationError{
			field:  "Pagination",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTransactionRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTransactionRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTransactionRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListTransactionRequestMultiError(errors)
	}

	return nil
}

// ListTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by ListTransactionRequest.ValidateAll() if the designated
// constraints aren't met.
type ListTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTransactionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTransactionRequestMultiError) AllErrors() []error { return m }

// ListTransactionRequestValidationError is the validation error returned by
// ListTransactionRequest.Validate if the designated constraints aren't met.
type ListTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTransactionRequestValidationError) ErrorName() string {
	return "ListTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTransactionRequestValidationError{}

// Validate checks the field values on ListTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTransactionResponseMultiError, or nil if none found.
func (m *ListTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTransactionResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTransactionResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTransactionResponseValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTransactionResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTransactionResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTransactionResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListTransactionResponseMultiError(errors)
	}

	return nil
}

// ListTransactionResponseMultiError is an error wrapping multiple validation
// errors returned by ListTransactionResponse.ValidateAll() if the designated
// constraints aren't met.
type ListTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTransactionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTransactionResponseMultiError) AllErrors() []error { return m }

// ListTransactionResponseValidationError is the validation error returned by
// ListTransactionResponse.Validate if the designated constraints aren't met.
type ListTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTransactionResponseValidationError) ErrorName() string {
	return "ListTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTransactionResponseValidationError{}

// Validate checks the field values on Transaction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Transaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Transaction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TransactionMultiError, or
// nil if none found.
func (m *Transaction) ValidateAll() error {
	return m.validate(true)
}

func (m *Transaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZpTransId

	// no validation rules for PartnerTransId

	// no validation rules for Type

	// no validation rules for Amount

	// no validation rules for Status

	// no validation rules for Remark

	// no validation rules for ProductIcon

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return TransactionMultiError(errors)
	}

	return nil
}

// TransactionMultiError is an error wrapping multiple validation errors
// returned by Transaction.ValidateAll() if the designated constraints aren't met.
type TransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionMultiError) AllErrors() []error { return m }

// TransactionValidationError is the validation error returned by
// Transaction.Validate if the designated constraints aren't met.
type TransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionValidationError) ErrorName() string { return "TransactionValidationError" }

// Error satisfies the builtin error interface
func (e TransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionValidationError{}

// Validate checks the field values on Pagination with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pagination) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pagination with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaginationMultiError, or
// nil if none found.
func (m *Pagination) ValidateAll() error {
	return m.validate(true)
}

func (m *Pagination) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetLimit() <= 0 {
		err := PaginationValidationError{
			field:  "Limit",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Offset != nil {
		// no validation rules for Offset
	}

	if m.Cursor != nil {
		// no validation rules for Cursor
	}

	if m.Direction != nil {
		// no validation rules for Direction
	}

	if len(errors) > 0 {
		return PaginationMultiError(errors)
	}

	return nil
}

// PaginationMultiError is an error wrapping multiple validation errors
// returned by Pagination.ValidateAll() if the designated constraints aren't met.
type PaginationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginationMultiError) AllErrors() []error { return m }

// PaginationValidationError is the validation error returned by
// Pagination.Validate if the designated constraints aren't met.
type PaginationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginationValidationError) ErrorName() string { return "PaginationValidationError" }

// Error satisfies the builtin error interface
func (e PaginationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPagination.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginationValidationError{}

// Validate checks the field values on PaginationData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PaginationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaginationData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaginationDataMultiError,
// or nil if none found.
func (m *PaginationData) ValidateAll() error {
	return m.validate(true)
}

func (m *PaginationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrevCursor

	// no validation rules for NextCursor

	// no validation rules for HasPrev

	// no validation rules for HasNext

	if len(errors) > 0 {
		return PaginationDataMultiError(errors)
	}

	return nil
}

// PaginationDataMultiError is an error wrapping multiple validation errors
// returned by PaginationData.ValidateAll() if the designated constraints
// aren't met.
type PaginationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginationDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginationDataMultiError) AllErrors() []error { return m }

// PaginationDataValidationError is the validation error returned by
// PaginationData.Validate if the designated constraints aren't met.
type PaginationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginationDataValidationError) ErrorName() string { return "PaginationDataValidationError" }

// Error satisfies the builtin error interface
func (e PaginationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaginationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginationDataValidationError{}
