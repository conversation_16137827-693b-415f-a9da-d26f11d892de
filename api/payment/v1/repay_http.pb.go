// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: payment/v1/repay.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRePaymentServiceCreateOrder = "/api.payment.v1.RePaymentService/CreateOrder"

type RePaymentServiceHTTPServer interface {
	CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderResponse, error)
}

func RegisterRePaymentServiceHTTPServer(s *http.Server, srv RePaymentServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/payment/v1/repay/create-order", _RePaymentService_CreateOrder0_HTTP_Handler(srv))
}

func _RePaymentService_CreateOrder0_HTTP_Handler(srv RePaymentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRePaymentServiceCreateOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrder(ctx, req.(*CreateOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateOrderResponse)
		return ctx.Result(200, reply)
	}
}

type RePaymentServiceHTTPClient interface {
	CreateOrder(ctx context.Context, req *CreateOrderRequest, opts ...http.CallOption) (rsp *CreateOrderResponse, err error)
}

type RePaymentServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewRePaymentServiceHTTPClient(client *http.Client) RePaymentServiceHTTPClient {
	return &RePaymentServiceHTTPClientImpl{client}
}

func (c *RePaymentServiceHTTPClientImpl) CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...http.CallOption) (*CreateOrderResponse, error) {
	var out CreateOrderResponse
	pattern := "/payment/v1/repay/create-order"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRePaymentServiceCreateOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
