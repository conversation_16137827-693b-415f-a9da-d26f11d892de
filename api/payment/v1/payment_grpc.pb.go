// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: payment/v1/payment.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AssetProvider_Exchange_FullMethodName              = "/asset_exchange.v2.AssetProvider/Exchange"
	AssetProvider_RevertExchange_FullMethodName        = "/asset_exchange.v2.AssetProvider/RevertExchange"
	AssetProvider_QueryStatus_FullMethodName           = "/asset_exchange.v2.AssetProvider/QueryStatus"
	AssetProvider_ConfirmAuthentication_FullMethodName = "/asset_exchange.v2.AssetProvider/ConfirmAuthentication"
	AssetProvider_QueryZas_FullMethodName              = "/asset_exchange.v2.AssetProvider/QueryZas"
	AssetProvider_CancelTransaction_FullMethodName     = "/asset_exchange.v2.AssetProvider/CancelTransaction"
)

// AssetProviderClient is the client API for AssetProvider service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssetProviderClient interface {
	Exchange(ctx context.Context, in *ExchangeRequest, opts ...grpc.CallOption) (*ExchangeResponse, error)
	RevertExchange(ctx context.Context, in *RevertExchangeRequest, opts ...grpc.CallOption) (*RevertExchangeResponse, error)
	QueryStatus(ctx context.Context, in *QueryStatusRequest, opts ...grpc.CallOption) (*QueryStatusResponse, error)
	ConfirmAuthentication(ctx context.Context, in *ConfirmAuthenticationRequest, opts ...grpc.CallOption) (*ConfirmAuthenticationResponse, error)
	QueryZas(ctx context.Context, in *ZasIdQueryRequest, opts ...grpc.CallOption) (*ZasIdQueryResponse, error)
	// For OPS
	CancelTransaction(ctx context.Context, in *CancelTransactionRequest, opts ...grpc.CallOption) (*CancelTransactionResponse, error)
}

type assetProviderClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetProviderClient(cc grpc.ClientConnInterface) AssetProviderClient {
	return &assetProviderClient{cc}
}

func (c *assetProviderClient) Exchange(ctx context.Context, in *ExchangeRequest, opts ...grpc.CallOption) (*ExchangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExchangeResponse)
	err := c.cc.Invoke(ctx, AssetProvider_Exchange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetProviderClient) RevertExchange(ctx context.Context, in *RevertExchangeRequest, opts ...grpc.CallOption) (*RevertExchangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RevertExchangeResponse)
	err := c.cc.Invoke(ctx, AssetProvider_RevertExchange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetProviderClient) QueryStatus(ctx context.Context, in *QueryStatusRequest, opts ...grpc.CallOption) (*QueryStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryStatusResponse)
	err := c.cc.Invoke(ctx, AssetProvider_QueryStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetProviderClient) ConfirmAuthentication(ctx context.Context, in *ConfirmAuthenticationRequest, opts ...grpc.CallOption) (*ConfirmAuthenticationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfirmAuthenticationResponse)
	err := c.cc.Invoke(ctx, AssetProvider_ConfirmAuthentication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetProviderClient) QueryZas(ctx context.Context, in *ZasIdQueryRequest, opts ...grpc.CallOption) (*ZasIdQueryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ZasIdQueryResponse)
	err := c.cc.Invoke(ctx, AssetProvider_QueryZas_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetProviderClient) CancelTransaction(ctx context.Context, in *CancelTransactionRequest, opts ...grpc.CallOption) (*CancelTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelTransactionResponse)
	err := c.cc.Invoke(ctx, AssetProvider_CancelTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetProviderServer is the server API for AssetProvider service.
// All implementations must embed UnimplementedAssetProviderServer
// for forward compatibility.
type AssetProviderServer interface {
	Exchange(context.Context, *ExchangeRequest) (*ExchangeResponse, error)
	RevertExchange(context.Context, *RevertExchangeRequest) (*RevertExchangeResponse, error)
	QueryStatus(context.Context, *QueryStatusRequest) (*QueryStatusResponse, error)
	ConfirmAuthentication(context.Context, *ConfirmAuthenticationRequest) (*ConfirmAuthenticationResponse, error)
	QueryZas(context.Context, *ZasIdQueryRequest) (*ZasIdQueryResponse, error)
	// For OPS
	CancelTransaction(context.Context, *CancelTransactionRequest) (*CancelTransactionResponse, error)
	mustEmbedUnimplementedAssetProviderServer()
}

// UnimplementedAssetProviderServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAssetProviderServer struct{}

func (UnimplementedAssetProviderServer) Exchange(context.Context, *ExchangeRequest) (*ExchangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Exchange not implemented")
}
func (UnimplementedAssetProviderServer) RevertExchange(context.Context, *RevertExchangeRequest) (*RevertExchangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevertExchange not implemented")
}
func (UnimplementedAssetProviderServer) QueryStatus(context.Context, *QueryStatusRequest) (*QueryStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStatus not implemented")
}
func (UnimplementedAssetProviderServer) ConfirmAuthentication(context.Context, *ConfirmAuthenticationRequest) (*ConfirmAuthenticationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmAuthentication not implemented")
}
func (UnimplementedAssetProviderServer) QueryZas(context.Context, *ZasIdQueryRequest) (*ZasIdQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryZas not implemented")
}
func (UnimplementedAssetProviderServer) CancelTransaction(context.Context, *CancelTransactionRequest) (*CancelTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTransaction not implemented")
}
func (UnimplementedAssetProviderServer) mustEmbedUnimplementedAssetProviderServer() {}
func (UnimplementedAssetProviderServer) testEmbeddedByValue()                       {}

// UnsafeAssetProviderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetProviderServer will
// result in compilation errors.
type UnsafeAssetProviderServer interface {
	mustEmbedUnimplementedAssetProviderServer()
}

func RegisterAssetProviderServer(s grpc.ServiceRegistrar, srv AssetProviderServer) {
	// If the following call pancis, it indicates UnimplementedAssetProviderServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AssetProvider_ServiceDesc, srv)
}

func _AssetProvider_Exchange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetProviderServer).Exchange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetProvider_Exchange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetProviderServer).Exchange(ctx, req.(*ExchangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetProvider_RevertExchange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevertExchangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetProviderServer).RevertExchange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetProvider_RevertExchange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetProviderServer).RevertExchange(ctx, req.(*RevertExchangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetProvider_QueryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetProviderServer).QueryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetProvider_QueryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetProviderServer).QueryStatus(ctx, req.(*QueryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetProvider_ConfirmAuthentication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmAuthenticationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetProviderServer).ConfirmAuthentication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetProvider_ConfirmAuthentication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetProviderServer).ConfirmAuthentication(ctx, req.(*ConfirmAuthenticationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetProvider_QueryZas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ZasIdQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetProviderServer).QueryZas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetProvider_QueryZas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetProviderServer).QueryZas(ctx, req.(*ZasIdQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetProvider_CancelTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetProviderServer).CancelTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetProvider_CancelTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetProviderServer).CancelTransaction(ctx, req.(*CancelTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AssetProvider_ServiceDesc is the grpc.ServiceDesc for AssetProvider service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AssetProvider_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asset_exchange.v2.AssetProvider",
	HandlerType: (*AssetProviderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Exchange",
			Handler:    _AssetProvider_Exchange_Handler,
		},
		{
			MethodName: "RevertExchange",
			Handler:    _AssetProvider_RevertExchange_Handler,
		},
		{
			MethodName: "QueryStatus",
			Handler:    _AssetProvider_QueryStatus_Handler,
		},
		{
			MethodName: "ConfirmAuthentication",
			Handler:    _AssetProvider_ConfirmAuthentication_Handler,
		},
		{
			MethodName: "QueryZas",
			Handler:    _AssetProvider_QueryZas_Handler,
		},
		{
			MethodName: "CancelTransaction",
			Handler:    _AssetProvider_CancelTransaction_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "payment/v1/payment.proto",
}
