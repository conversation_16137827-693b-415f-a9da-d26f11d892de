syntax = "proto3";

package api.payment.v1;
option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1;v1";

enum TransType {
  TRANS_TYPE_UNSPECIFIED = 0;
  TRANS_TYPE_PAYMENT = 200;
  TRANS_TYPE_REPAYMENT = 201;
}

enum TransStatus {
  TRANS_STATUS_UNSPECIFIED = 0;
  TRANS_STATUS_SUCCESS = 1;
  TRANS_STATUS_FAILED = 2;
  TRANS_STATUS_PENDING = 3;
  TRANS_STATUS_PROCESSING = 4;
}

// SettleStatus is a settle status of loan transaction
// Currently SETTLE_STATUS_UNSETTLED is is includes some status like (PENDING, PROCESSING). Can be extracted to more detail status in the future
enum SettleStatus {
  SETTLE_STATUS_UNSPECIFIED = 0;
  SETTLE_STATUS_SETTLED = 1;
  SETTLE_STATUS_UNSETTLED = 2;
  SETTLE_STATUS_PROCESSING = 3;
}

// ErrorDetail describe the detail of error when the trans_status is TRANS_STATUS_FAILED
message ErrorDetail {
  // Code is the error code when the error occurs in the Installment system
  SubCode code = 1;
  // BankReturnCode is the return code from bank when transaction failed
  string bank_return_code = 2;
  // Message is the error description
  string message = 3;
}

enum SubCode {
  UNKNOWN = 0;
  DATABASE_ERROR = 1;
  BAD_REQUEST = 2;
  CALL_CONNECTOR_FAILED = 3;
  INTERNAL = 4;
  PUBLISH_KAFKA_FAILED = 5;
  ZALOPAY_CORP_BALANCE_NOT_ENOUGH = 6;
  INVALID_PARTNER = 7;

  // Purchase Code
  DUPLICATED_TRANS_ID = 200;
  ACCOUNT_NOT_EXIST = 201;
  BALANCE_NOT_ENOUGH = 202;
  ACCOUNT_LOG_NOT_EXIST = 203;
  ACCOUNT_IS_INACTIVE = 204;
  TRANSACTION_EXPIRED = 205;
  OD_ACCOUNT_IS_BLOCKED = 206;
  ACCOUNT_IS_BLOCKED = 207;
  INVALID_BENEFICIARY = 208;
  INVALID_PARTNER_BENEFICIARY = 209;
  TRANSACTION_NOT_FOUND = 210;
  INVALID_ACCOUNT_INFORMATION = 211;
  TRANSACTION_UNAUTHENTICATED = 212;

  //Repayment
  INSUFFICIENT_DEBIT_BALANCE = 301;
  NOT_SUPPORT_QR = 302;

  //
  VIOLATE_PARTNER_POLICY = 401;
}