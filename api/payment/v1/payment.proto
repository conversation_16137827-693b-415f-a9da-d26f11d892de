syntax = "proto3";

package asset_exchange.v2;
option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1;v1";

import "validate/validate.proto";

service AssetProvider {
  rpc Exchange(ExchangeRequest) returns (ExchangeResponse);
  rpc RevertExchange(RevertExchangeRequest) returns (RevertExchangeResponse);
  rpc QueryStatus(QueryStatusRequest) returns (QueryStatusResponse);
  rpc ConfirmAuthentication(ConfirmAuthenticationRequest) returns (ConfirmAuthenticationResponse);
  rpc QueryZas(ZasIdQueryRequest) returns (ZasIdQueryResponse);

  // For OPS
  rpc CancelTransaction(CancelTransactionRequest) returns (CancelTransactionResponse);
}

message ExchangeRequest {
  //Transaction ID in ZaloPay
  int64 trans_id = 1;
  //Request Id is id which reflects between ZaloPay and external clients to idempontent
  string request_id = 2;
  //Asset Data
  //Include asset_id Field identify asset information eg: bim_token, card_id, user_id, ...
  Asset asset = 3;
  int64 amount = 4;
  //add or deduct
  Action action = 5;
  //Extra data which external service need to handle transaction
  map<string, string> extra_data = 6;
  enum Action {
    UNKNOWN = 0;
    DEDUCT = 1;
    ADD = 2;
  }
}

message Asset {
  map<string, string> data = 1;
}

message ExchangeResponse {
  //Transaction ID in ZaloPay
  int64 trans_id = 1;
  //Request_id is id which reflects between ZaloPay and external clients to idempontent
  string request_id = 2;
  //Client reference source of fund transaciton mapping 1:1 sub_trans_id
  string ref_sof_id = 3;
  //To determine current state of transaction in client systems
  ReturnCode return_code = 4;
  map<string, string> extra_data = 5;
  string payment_no = 6;
}

message RevertExchangeRequest {
  //Transaction ID in ZaloPay
  int64 trans_id = 1;
  //Request ID is id which reflects between ZaloPay and external clients to idempotent
  string request_id = 2;
  //Like request_id, revert_request_id is id to identify sub-transaction
  string revert_request_id = 3;
  //Client reference source of fund transaciton mapping 1:1 request_id
  string ref_sof_id = 4;
  //Source of fund code is defined for specificed external service like (Paylater, Installment ...)
  string sof_code = 5;
  //Extra data
  map<string, string> extra_data = 6;
}

message RevertExchangeResponse {
  //Transaction ID in ZaloPay
  int64 trans_id = 1;
  //Request ID is id which reflects between ZaloPay and external clients
  string request_id = 2;
  //Like request_id, revert_request_id is id to identify sub-transaction
  string revert_request_id = 3;
  //Client reference source of fund transaction mapping 1:1 request_id
  string ref_sof_id = 4;
  //Client reference transaction mapping 1:1 revert_request_id
  string revert_ref_sof_id = 5;
  //To determine current state of transaction in client systems
  ReturnCode return_code = 6;
  //Extra data
  map<string,string> extra_data = 7;
}

message QueryStatusRequest {
  //Transaction ID in ZaloPay
  int64 trans_id = 1;
  //Request_id is id which reflects between ZaloPay and external clients to idempotent
  string request_id = 2;
}

message QueryStatusResponse {
  //Transaction ID in ZaloPay
  int64 trans_id = 1;
  //Request Id is id which reflects between ZaloPay and external clients to idempontent
  string request_id = 2;
  //Client reference source of fund transaciton mapping 1:1 request_id
  string ref_sof_id = 3;
  //To determine current state of transaction in client systems
  ReturnCode return_code = 4;
  //Extra data
  map<string, string> extra_data = 5;
  string payment_no = 6;
}

message ConfirmAuthenticationRequest {
  string trans_id = 1;//zlp_trans_id
  string request_id = 2;
  oneof auth_data {
    OTPAuthenticationData otp = 3;
    FaceAuthenticationData face = 4;
  }
}

message ConfirmAuthenticationResponse {
  int64 trans_id = 1;
  string request_id = 2;
  ReturnCode return_code = 3;
}

message OTPAuthenticationData {
  string otp = 1;
}

message FaceAuthenticationData {
  //face_token is data for identify face authentication session using for get image from UM
  string face_token = 1 [(validate.rules).string.min_len = 1];
}

message ZasIdQueryRequest {
  string accounting_code = 1;
  Asset asset = 2;
}

message ZasIdQueryResponse {
  ReturnCode return_code = 1;
  int64 zas_id = 2;
}

message ReturnCode {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    OK = 1;
    SYSTEM_ERROR = 2;
    RETRYABLE = 3;
    NOT_FOUND = 4;
  }
  enum Code {
    CODE_UNSPECIFIED = 0;
    SUCCEEDED = 1;
    FAILED = 2;
    SUBMITTED = 3;
  }
  Status status = 1;
  Code code = 2;
  string sub_code = 3;
  string message = 4;
}

message CancelTransactionRequest {
  int64 zp_trans_id = 1;
}

message CancelTransactionResponse {
  int64 ref_sof_id = 1;
  string partner_trans_id = 2;
}