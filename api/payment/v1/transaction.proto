syntax = "proto3";

package api.payment.v1;
option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1;v1";

import "validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "payment/v1/common.proto";

service TransactionService {
  rpc ListTransaction(ListTransactionRequest) returns (ListTransactionResponse) {
    option (google.api.http) = {
      get: "/payment/v1/transactions"
    };
  }
  rpc GetTransaction(GetTransactionRequest) returns (GetTransactionResponse) {
    option (google.api.http) = {
      get: "/payment/v1/transaction",
    };
  }
}

message GetTransactionRequest {
  int64 zp_trans_id = 1 [json_name = "zp_trans_id"];
}

message GetTransactionResponse {
  Transaction transaction = 1 [json_name = "transaction"];
}

message ListTransactionRequest {
  int64 account_id = 1 [json_name = "account_id"];
  google.protobuf.Timestamp from_date = 2 [json_name = "from_date"];
  google.protobuf.Timestamp to_date = 3 [json_name = "to_date"];
  repeated TransType trans_types = 4 [json_name = "trans_types"];
  Pagination pagination = 50 [json_name = "pagination", (validate.rules).message.required = true];
}

message ListTransactionResponse {
  repeated Transaction transactions = 1 [json_name = "transactions"];
  PaginationData pagination = 2 [json_name = "pagination"];
}

message Transaction {
  int64 zp_trans_id = 1;
  string partner_trans_id = 2;
  TransType type = 3;
  int64 amount = 4;
  TransStatus status = 5;
  string remark = 6;
  string product_icon = 7;
  string created_at = 8;
  string updated_at = 9;
}

message Pagination {
  int32 limit = 1 [json_name = "limit", (validate.rules).int32.gt = 0];
  optional int32 offset = 2 [json_name = "offset"];
  optional string cursor = 3 [json_name = "cursor"];
  optional int32 direction = 4 [json_name = "direction"];
}

message PaginationData {
  string prev_cursor = 1 [json_name = "prev_cursor"];
  string next_cursor = 2 [json_name = "next_cursor"];
  bool has_prev = 3 [json_name = "has_prev"];
  bool has_next = 4 [json_name = "has_next"];
}