// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: payment/v1/transaction.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTransactionServiceGetTransaction = "/api.payment.v1.TransactionService/GetTransaction"
const OperationTransactionServiceListTransaction = "/api.payment.v1.TransactionService/ListTransaction"

type TransactionServiceHTTPServer interface {
	GetTransaction(context.Context, *GetTransactionRequest) (*GetTransactionResponse, error)
	ListTransaction(context.Context, *ListTransactionRequest) (*ListTransactionResponse, error)
}

func RegisterTransactionServiceHTTPServer(s *http.Server, srv TransactionServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/payment/v1/transactions", _TransactionService_ListTransaction0_HTTP_Handler(srv))
	r.GET("/payment/v1/transaction", _TransactionService_GetTransaction0_HTTP_Handler(srv))
}

func _TransactionService_ListTransaction0_HTTP_Handler(srv TransactionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTransactionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTransactionServiceListTransaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTransaction(ctx, req.(*ListTransactionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTransactionResponse)
		return ctx.Result(200, reply)
	}
}

func _TransactionService_GetTransaction0_HTTP_Handler(srv TransactionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTransactionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTransactionServiceGetTransaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTransaction(ctx, req.(*GetTransactionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTransactionResponse)
		return ctx.Result(200, reply)
	}
}

type TransactionServiceHTTPClient interface {
	GetTransaction(ctx context.Context, req *GetTransactionRequest, opts ...http.CallOption) (rsp *GetTransactionResponse, err error)
	ListTransaction(ctx context.Context, req *ListTransactionRequest, opts ...http.CallOption) (rsp *ListTransactionResponse, err error)
}

type TransactionServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewTransactionServiceHTTPClient(client *http.Client) TransactionServiceHTTPClient {
	return &TransactionServiceHTTPClientImpl{client}
}

func (c *TransactionServiceHTTPClientImpl) GetTransaction(ctx context.Context, in *GetTransactionRequest, opts ...http.CallOption) (*GetTransactionResponse, error) {
	var out GetTransactionResponse
	pattern := "/payment/v1/transaction"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTransactionServiceGetTransaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TransactionServiceHTTPClientImpl) ListTransaction(ctx context.Context, in *ListTransactionRequest, opts ...http.CallOption) (*ListTransactionResponse, error) {
	var out ListTransactionResponse
	pattern := "/payment/v1/transactions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTransactionServiceListTransaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
