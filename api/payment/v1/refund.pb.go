// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: payment/v1/refund.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RefundSettleEventType int32

const (
	RefundSettleEventType_REFUND_SETTLE_EVENT_UNKNOWN  RefundSettleEventType = 0
	RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST  RefundSettleEventType = 1
	RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE RefundSettleEventType = 2
)

// Enum value maps for RefundSettleEventType.
var (
	RefundSettleEventType_name = map[int32]string{
		0: "REFUND_SETTLE_EVENT_UNKNOWN",
		1: "REFUND_SETTLE_EVENT_REQUEST",
		2: "REFUND_SETTLE_EVENT_RESPONSE",
	}
	RefundSettleEventType_value = map[string]int32{
		"REFUND_SETTLE_EVENT_UNKNOWN":  0,
		"REFUND_SETTLE_EVENT_REQUEST":  1,
		"REFUND_SETTLE_EVENT_RESPONSE": 2,
	}
)

func (x RefundSettleEventType) Enum() *RefundSettleEventType {
	p := new(RefundSettleEventType)
	*p = x
	return p
}

func (x RefundSettleEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundSettleEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_payment_v1_refund_proto_enumTypes[0].Descriptor()
}

func (RefundSettleEventType) Type() protoreflect.EnumType {
	return &file_payment_v1_refund_proto_enumTypes[0]
}

func (x RefundSettleEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundSettleEventType.Descriptor instead.
func (RefundSettleEventType) EnumDescriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{0}
}

type RefundType int32

const (
	RefundType_REFUND_TYPE_UNKNOWN RefundType = 0
	RefundType_REFUND_TYPE_AUTO    RefundType = 1
	RefundType_REFUND_TYPE_MANUAL  RefundType = 2
)

// Enum value maps for RefundType.
var (
	RefundType_name = map[int32]string{
		0: "REFUND_TYPE_UNKNOWN",
		1: "REFUND_TYPE_AUTO",
		2: "REFUND_TYPE_MANUAL",
	}
	RefundType_value = map[string]int32{
		"REFUND_TYPE_UNKNOWN": 0,
		"REFUND_TYPE_AUTO":    1,
		"REFUND_TYPE_MANUAL":  2,
	}
)

func (x RefundType) Enum() *RefundType {
	p := new(RefundType)
	*p = x
	return p
}

func (x RefundType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundType) Descriptor() protoreflect.EnumDescriptor {
	return file_payment_v1_refund_proto_enumTypes[1].Descriptor()
}

func (RefundType) Type() protoreflect.EnumType {
	return &file_payment_v1_refund_proto_enumTypes[1]
}

func (x RefundType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundType.Descriptor instead.
func (RefundType) EnumDescriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{1}
}

type PreCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp          int64  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ZpTransId          string `protobuf:"bytes,2,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	Amount             int64  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	AppTransId         string `protobuf:"bytes,4,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	AppId              int32  `protobuf:"varint,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	RefundId           int64  `protobuf:"varint,6,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	PaymentDescription string `protobuf:"bytes,7,opt,name=payment_description,json=paymentDescription,proto3" json:"payment_description,omitempty"`
}

func (x *PreCheckRequest) Reset() {
	*x = PreCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCheckRequest) ProtoMessage() {}

func (x *PreCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCheckRequest.ProtoReflect.Descriptor instead.
func (*PreCheckRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{0}
}

func (x *PreCheckRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *PreCheckRequest) GetZpTransId() string {
	if x != nil {
		return x.ZpTransId
	}
	return ""
}

func (x *PreCheckRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PreCheckRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *PreCheckRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *PreCheckRequest) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *PreCheckRequest) GetPaymentDescription() string {
	if x != nil {
		return x.PaymentDescription
	}
	return ""
}

type PreCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId     int64        `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	RefSofId     int64        `protobuf:"varint,2,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	RefundType   RefundType   `protobuf:"varint,3,opt,name=refund_type,json=refundType,proto3,enum=api.payment.v1.RefundType" json:"refund_type,omitempty"`
	SettleStatus SettleStatus `protobuf:"varint,4,opt,name=settle_status,json=settleStatus,proto3,enum=api.payment.v1.SettleStatus" json:"settle_status,omitempty"`
}

func (x *PreCheckResponse) Reset() {
	*x = PreCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCheckResponse) ProtoMessage() {}

func (x *PreCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCheckResponse.ProtoReflect.Descriptor instead.
func (*PreCheckResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{1}
}

func (x *PreCheckResponse) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *PreCheckResponse) GetRefSofId() int64 {
	if x != nil {
		return x.RefSofId
	}
	return 0
}

func (x *PreCheckResponse) GetRefundType() RefundType {
	if x != nil {
		return x.RefundType
	}
	return RefundType_REFUND_TYPE_UNKNOWN
}

func (x *PreCheckResponse) GetSettleStatus() SettleStatus {
	if x != nil {
		return x.SettleStatus
	}
	return SettleStatus_SETTLE_STATUS_UNSPECIFIED
}

type RefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp          int64  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ZpTransId          string `protobuf:"bytes,2,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	Amount             int64  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	AppTransId         string `protobuf:"bytes,4,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	AppId              int32  `protobuf:"varint,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	RefundId           int64  `protobuf:"varint,6,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	PaymentDescription string `protobuf:"bytes,7,opt,name=payment_description,json=paymentDescription,proto3" json:"payment_description,omitempty"`
}

func (x *RefundRequest) Reset() {
	*x = RefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundRequest) ProtoMessage() {}

func (x *RefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundRequest.ProtoReflect.Descriptor instead.
func (*RefundRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{2}
}

func (x *RefundRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RefundRequest) GetZpTransId() string {
	if x != nil {
		return x.ZpTransId
	}
	return ""
}

func (x *RefundRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *RefundRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *RefundRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *RefundRequest) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *RefundRequest) GetPaymentDescription() string {
	if x != nil {
		return x.PaymentDescription
	}
	return ""
}

type RefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp   int64        `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	RefundId    int64        `protobuf:"varint,2,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	RefSofId    int64        `protobuf:"varint,3,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	RefundType  RefundType   `protobuf:"varint,4,opt,name=refund_type,json=refundType,proto3,enum=api.payment.v1.RefundType" json:"refund_type,omitempty"`
	TransStatus TransStatus  `protobuf:"varint,5,opt,name=trans_status,json=transStatus,proto3,enum=api.payment.v1.TransStatus" json:"trans_status,omitempty"`
	ErrorDetail *ErrorDetail `protobuf:"bytes,6,opt,name=error_detail,json=errorDetail,proto3" json:"error_detail,omitempty"`
}

func (x *RefundResponse) Reset() {
	*x = RefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundResponse) ProtoMessage() {}

func (x *RefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundResponse.ProtoReflect.Descriptor instead.
func (*RefundResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{3}
}

func (x *RefundResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RefundResponse) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *RefundResponse) GetRefSofId() int64 {
	if x != nil {
		return x.RefSofId
	}
	return 0
}

func (x *RefundResponse) GetRefundType() RefundType {
	if x != nil {
		return x.RefundType
	}
	return RefundType_REFUND_TYPE_UNKNOWN
}

func (x *RefundResponse) GetTransStatus() TransStatus {
	if x != nil {
		return x.TransStatus
	}
	return TransStatus_TRANS_STATUS_UNSPECIFIED
}

func (x *RefundResponse) GetErrorDetail() *ErrorDetail {
	if x != nil {
		return x.ErrorDetail
	}
	return nil
}

type RefundQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	RefundId  int64 `protobuf:"varint,2,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
}

func (x *RefundQueryRequest) Reset() {
	*x = RefundQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundQueryRequest) ProtoMessage() {}

func (x *RefundQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundQueryRequest.ProtoReflect.Descriptor instead.
func (*RefundQueryRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{4}
}

func (x *RefundQueryRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RefundQueryRequest) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

type RefundQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EscrowBankAccount string      `protobuf:"bytes,1,opt,name=escrow_bank_account,json=escrowBankAccount,proto3" json:"escrow_bank_account,omitempty"`
	TransStatus       TransStatus `protobuf:"varint,2,opt,name=trans_status,json=transStatus,proto3,enum=api.payment.v1.TransStatus" json:"trans_status,omitempty"`
	RefSofId          int64       `protobuf:"varint,3,opt,name=ref_sof_id,json=refSofId,proto3" json:"ref_sof_id,omitempty"`
	RefundId          int64       `protobuf:"varint,4,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
}

func (x *RefundQueryResponse) Reset() {
	*x = RefundQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundQueryResponse) ProtoMessage() {}

func (x *RefundQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundQueryResponse.ProtoReflect.Descriptor instead.
func (*RefundQueryResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{5}
}

func (x *RefundQueryResponse) GetEscrowBankAccount() string {
	if x != nil {
		return x.EscrowBankAccount
	}
	return ""
}

func (x *RefundQueryResponse) GetTransStatus() TransStatus {
	if x != nil {
		return x.TransStatus
	}
	return TransStatus_TRANS_STATUS_UNSPECIFIED
}

func (x *RefundQueryResponse) GetRefSofId() int64 {
	if x != nil {
		return x.RefSofId
	}
	return 0
}

func (x *RefundQueryResponse) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

type CreateTopupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount    int64 `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	ZpTransId int64 `protobuf:"varint,2,opt,name=zp_trans_id,proto3" json:"zp_trans_id,omitempty"`
}

func (x *CreateTopupRequest) Reset() {
	*x = CreateTopupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTopupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTopupRequest) ProtoMessage() {}

func (x *CreateTopupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTopupRequest.ProtoReflect.Descriptor instead.
func (*CreateTopupRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{6}
}

func (x *CreateTopupRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreateTopupRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

type CreateTopupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId      int64  `protobuf:"varint,1,opt,name=trans_id,proto3" json:"trans_id,omitempty"`
	AppId        int32  `protobuf:"varint,2,opt,name=app_id,proto3" json:"app_id,omitempty"`
	AppTransId   string `protobuf:"bytes,3,opt,name=app_trans_id,proto3" json:"app_trans_id,omitempty"`
	ZpTransToken string `protobuf:"bytes,4,opt,name=zp_trans_token,proto3" json:"zp_trans_token,omitempty"`
}

func (x *CreateTopupResponse) Reset() {
	*x = CreateTopupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTopupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTopupResponse) ProtoMessage() {}

func (x *CreateTopupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTopupResponse.ProtoReflect.Descriptor instead.
func (*CreateTopupResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{7}
}

func (x *CreateTopupResponse) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *CreateTopupResponse) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *CreateTopupResponse) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *CreateTopupResponse) GetZpTransToken() string {
	if x != nil {
		return x.ZpTransToken
	}
	return ""
}

type CalculateInterestEstimationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransId      string `protobuf:"bytes,1,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	EstimationDate string `protobuf:"bytes,2,opt,name=estimation_date,json=estimationDate,proto3" json:"estimation_date,omitempty"`
}

func (x *CalculateInterestEstimationRequest) Reset() {
	*x = CalculateInterestEstimationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateInterestEstimationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateInterestEstimationRequest) ProtoMessage() {}

func (x *CalculateInterestEstimationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateInterestEstimationRequest.ProtoReflect.Descriptor instead.
func (*CalculateInterestEstimationRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{8}
}

func (x *CalculateInterestEstimationRequest) GetZpTransId() string {
	if x != nil {
		return x.ZpTransId
	}
	return ""
}

func (x *CalculateInterestEstimationRequest) GetEstimationDate() string {
	if x != nil {
		return x.EstimationDate
	}
	return ""
}

type CalculateInterestEstimationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalInterest int64 `protobuf:"varint,1,opt,name=total_interest,json=totalInterest,proto3" json:"total_interest,omitempty"`
}

func (x *CalculateInterestEstimationResponse) Reset() {
	*x = CalculateInterestEstimationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateInterestEstimationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateInterestEstimationResponse) ProtoMessage() {}

func (x *CalculateInterestEstimationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateInterestEstimationResponse.ProtoReflect.Descriptor instead.
func (*CalculateInterestEstimationResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{9}
}

func (x *CalculateInterestEstimationResponse) GetTotalInterest() int64 {
	if x != nil {
		return x.TotalInterest
	}
	return 0
}

type TriggerReconcileSettleJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransIds []int64 `protobuf:"varint,1,rep,packed,name=zp_trans_ids,json=zpTransIds,proto3" json:"zp_trans_ids,omitempty"`
}

func (x *TriggerReconcileSettleJobsRequest) Reset() {
	*x = TriggerReconcileSettleJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerReconcileSettleJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerReconcileSettleJobsRequest) ProtoMessage() {}

func (x *TriggerReconcileSettleJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerReconcileSettleJobsRequest.ProtoReflect.Descriptor instead.
func (*TriggerReconcileSettleJobsRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{10}
}

func (x *TriggerReconcileSettleJobsRequest) GetZpTransIds() []int64 {
	if x != nil {
		return x.ZpTransIds
	}
	return nil
}

type TriggerReconcileSettleJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransIds []int64 `protobuf:"varint,1,rep,packed,name=zp_trans_ids,json=zpTransIds,proto3" json:"zp_trans_ids,omitempty"`
}

func (x *TriggerReconcileSettleJobsResponse) Reset() {
	*x = TriggerReconcileSettleJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerReconcileSettleJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerReconcileSettleJobsResponse) ProtoMessage() {}

func (x *TriggerReconcileSettleJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerReconcileSettleJobsResponse.ProtoReflect.Descriptor instead.
func (*TriggerReconcileSettleJobsResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{11}
}

func (x *TriggerReconcileSettleJobsResponse) GetZpTransIds() []int64 {
	if x != nil {
		return x.ZpTransIds
	}
	return nil
}

type TriggerProcessSettleInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransIds []int64 `protobuf:"varint,1,rep,packed,name=zp_trans_ids,json=zpTransIds,proto3" json:"zp_trans_ids,omitempty"`
}

func (x *TriggerProcessSettleInfoRequest) Reset() {
	*x = TriggerProcessSettleInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerProcessSettleInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerProcessSettleInfoRequest) ProtoMessage() {}

func (x *TriggerProcessSettleInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerProcessSettleInfoRequest.ProtoReflect.Descriptor instead.
func (*TriggerProcessSettleInfoRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{12}
}

func (x *TriggerProcessSettleInfoRequest) GetZpTransIds() []int64 {
	if x != nil {
		return x.ZpTransIds
	}
	return nil
}

type TriggerProcessSettleInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransIds     []int64 `protobuf:"varint,1,rep,packed,name=zp_trans_ids,json=zpTransIds,proto3" json:"zp_trans_ids,omitempty"`
	ProcessedCount int32   `protobuf:"varint,2,opt,name=processed_count,json=processedCount,proto3" json:"processed_count,omitempty"`
}

func (x *TriggerProcessSettleInfoResponse) Reset() {
	*x = TriggerProcessSettleInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerProcessSettleInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerProcessSettleInfoResponse) ProtoMessage() {}

func (x *TriggerProcessSettleInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerProcessSettleInfoResponse.ProtoReflect.Descriptor instead.
func (*TriggerProcessSettleInfoResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{13}
}

func (x *TriggerProcessSettleInfoResponse) GetZpTransIds() []int64 {
	if x != nil {
		return x.ZpTransIds
	}
	return nil
}

func (x *TriggerProcessSettleInfoResponse) GetProcessedCount() int32 {
	if x != nil {
		return x.ProcessedCount
	}
	return 0
}

type TriggerPollingEarlyDischargeJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional list of specific transaction IDs to poll
	PaymentIds []int64 `protobuf:"varint,1,rep,packed,name=payment_ids,json=paymentIds,proto3" json:"payment_ids,omitempty"`
}

func (x *TriggerPollingEarlyDischargeJobsRequest) Reset() {
	*x = TriggerPollingEarlyDischargeJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerPollingEarlyDischargeJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerPollingEarlyDischargeJobsRequest) ProtoMessage() {}

func (x *TriggerPollingEarlyDischargeJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerPollingEarlyDischargeJobsRequest.ProtoReflect.Descriptor instead.
func (*TriggerPollingEarlyDischargeJobsRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{14}
}

func (x *TriggerPollingEarlyDischargeJobsRequest) GetPaymentIds() []int64 {
	if x != nil {
		return x.PaymentIds
	}
	return nil
}

type TriggerPollingEarlyDischargeJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of transaction IDs that were successfully processed
	PaymentIds []int64 `protobuf:"varint,1,rep,packed,name=payment_ids,json=paymentIds,proto3" json:"payment_ids,omitempty"`
	// Total count of processed transactions
	ProcessedCount int32 `protobuf:"varint,2,opt,name=processed_count,json=processedCount,proto3" json:"processed_count,omitempty"`
	// Any errors encountered during processing
	ErrorMessages []string `protobuf:"bytes,3,rep,name=error_messages,json=errorMessages,proto3" json:"error_messages,omitempty"`
}

func (x *TriggerPollingEarlyDischargeJobsResponse) Reset() {
	*x = TriggerPollingEarlyDischargeJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerPollingEarlyDischargeJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerPollingEarlyDischargeJobsResponse) ProtoMessage() {}

func (x *TriggerPollingEarlyDischargeJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerPollingEarlyDischargeJobsResponse.ProtoReflect.Descriptor instead.
func (*TriggerPollingEarlyDischargeJobsResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{15}
}

func (x *TriggerPollingEarlyDischargeJobsResponse) GetPaymentIds() []int64 {
	if x != nil {
		return x.PaymentIds
	}
	return nil
}

func (x *TriggerPollingEarlyDischargeJobsResponse) GetProcessedCount() int32 {
	if x != nil {
		return x.ProcessedCount
	}
	return 0
}

func (x *TriggerPollingEarlyDischargeJobsResponse) GetErrorMessages() []string {
	if x != nil {
		return x.ErrorMessages
	}
	return nil
}

type RetryRefundSettleOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransIds []int64 `protobuf:"varint,1,rep,packed,name=zp_trans_ids,json=zpTransIds,proto3" json:"zp_trans_ids,omitempty"`
}

func (x *RetryRefundSettleOrderRequest) Reset() {
	*x = RetryRefundSettleOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryRefundSettleOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryRefundSettleOrderRequest) ProtoMessage() {}

func (x *RetryRefundSettleOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryRefundSettleOrderRequest.ProtoReflect.Descriptor instead.
func (*RetryRefundSettleOrderRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{16}
}

func (x *RetryRefundSettleOrderRequest) GetZpTransIds() []int64 {
	if x != nil {
		return x.ZpTransIds
	}
	return nil
}

type RetryRefundSettleOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZpTransIds     []int64 `protobuf:"varint,1,rep,packed,name=zp_trans_ids,json=zpTransIds,proto3" json:"zp_trans_ids,omitempty"`
	ProcessedCount int32   `protobuf:"varint,2,opt,name=processed_count,json=processedCount,proto3" json:"processed_count,omitempty"`
}

func (x *RetryRefundSettleOrderResponse) Reset() {
	*x = RetryRefundSettleOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryRefundSettleOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryRefundSettleOrderResponse) ProtoMessage() {}

func (x *RetryRefundSettleOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryRefundSettleOrderResponse.ProtoReflect.Descriptor instead.
func (*RetryRefundSettleOrderResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{17}
}

func (x *RetryRefundSettleOrderResponse) GetZpTransIds() []int64 {
	if x != nil {
		return x.ZpTransIds
	}
	return nil
}

func (x *RetryRefundSettleOrderResponse) GetProcessedCount() int32 {
	if x != nil {
		return x.ProcessedCount
	}
	return 0
}

type RefundSettleEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId      string                `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	EventType    RefundSettleEventType `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=api.payment.v1.RefundSettleEventType" json:"event_type,omitempty"`
	SettleStatus SettleStatus          `protobuf:"varint,3,opt,name=settle_status,json=settleStatus,proto3,enum=api.payment.v1.SettleStatus" json:"settle_status,omitempty"`
	RefZpTransId int64                 `protobuf:"varint,4,opt,name=ref_zp_trans_id,json=refZpTransId,proto3" json:"ref_zp_trans_id,omitempty"`
	SettleAmount int64                 `protobuf:"varint,5,opt,name=settle_amount,json=settleAmount,proto3" json:"settle_amount,omitempty"`
	// The context of the settlement
	//
	// Types that are assignable to SettleContext:
	//
	//	*RefundSettleEvent_StandardSettle
	//	*RefundSettleEvent_ExpiredSettle
	SettleContext isRefundSettleEvent_SettleContext     `protobuf_oneof:"settle_context"`
	SettleResult  *RefundSettleEvent_RefundSettleResult `protobuf:"bytes,50,opt,name=settle_result,json=settleResult,proto3" json:"settle_result,omitempty"`
}

func (x *RefundSettleEvent) Reset() {
	*x = RefundSettleEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundSettleEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundSettleEvent) ProtoMessage() {}

func (x *RefundSettleEvent) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundSettleEvent.ProtoReflect.Descriptor instead.
func (*RefundSettleEvent) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{18}
}

func (x *RefundSettleEvent) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *RefundSettleEvent) GetEventType() RefundSettleEventType {
	if x != nil {
		return x.EventType
	}
	return RefundSettleEventType_REFUND_SETTLE_EVENT_UNKNOWN
}

func (x *RefundSettleEvent) GetSettleStatus() SettleStatus {
	if x != nil {
		return x.SettleStatus
	}
	return SettleStatus_SETTLE_STATUS_UNSPECIFIED
}

func (x *RefundSettleEvent) GetRefZpTransId() int64 {
	if x != nil {
		return x.RefZpTransId
	}
	return 0
}

func (x *RefundSettleEvent) GetSettleAmount() int64 {
	if x != nil {
		return x.SettleAmount
	}
	return 0
}

func (m *RefundSettleEvent) GetSettleContext() isRefundSettleEvent_SettleContext {
	if m != nil {
		return m.SettleContext
	}
	return nil
}

func (x *RefundSettleEvent) GetStandardSettle() *RefundStandardSettle {
	if x, ok := x.GetSettleContext().(*RefundSettleEvent_StandardSettle); ok {
		return x.StandardSettle
	}
	return nil
}

func (x *RefundSettleEvent) GetExpiredSettle() *RefundExpiredSettle {
	if x, ok := x.GetSettleContext().(*RefundSettleEvent_ExpiredSettle); ok {
		return x.ExpiredSettle
	}
	return nil
}

func (x *RefundSettleEvent) GetSettleResult() *RefundSettleEvent_RefundSettleResult {
	if x != nil {
		return x.SettleResult
	}
	return nil
}

type isRefundSettleEvent_SettleContext interface {
	isRefundSettleEvent_SettleContext()
}

type RefundSettleEvent_StandardSettle struct {
	StandardSettle *RefundStandardSettle `protobuf:"bytes,6,opt,name=standard_settle,json=standardSettle,proto3,oneof"`
}

type RefundSettleEvent_ExpiredSettle struct {
	ExpiredSettle *RefundExpiredSettle `protobuf:"bytes,7,opt,name=expired_settle,json=expiredSettle,proto3,oneof"`
}

func (*RefundSettleEvent_StandardSettle) isRefundSettleEvent_SettleContext() {}

func (*RefundSettleEvent_ExpiredSettle) isRefundSettleEvent_SettleContext() {}

// For normal settlement processing
type RefundStandardSettle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SettleId int64 `protobuf:"varint,1,opt,name=settle_id,json=settleId,proto3" json:"settle_id,omitempty"`
}

func (x *RefundStandardSettle) Reset() {
	*x = RefundStandardSettle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundStandardSettle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundStandardSettle) ProtoMessage() {}

func (x *RefundStandardSettle) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundStandardSettle.ProtoReflect.Descriptor instead.
func (*RefundStandardSettle) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{19}
}

func (x *RefundStandardSettle) GetSettleId() int64 {
	if x != nil {
		return x.SettleId
	}
	return 0
}

// For expired refund log settlement
type RefundExpiredSettle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId int64 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"` // The specific log being processed
}

func (x *RefundExpiredSettle) Reset() {
	*x = RefundExpiredSettle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundExpiredSettle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundExpiredSettle) ProtoMessage() {}

func (x *RefundExpiredSettle) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundExpiredSettle.ProtoReflect.Descriptor instead.
func (*RefundExpiredSettle) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{20}
}

func (x *RefundExpiredSettle) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

type RefundSettleEvent_RefundSettleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId      int64       `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	OrderId      int64       `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AppId        int32       `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppTransId   string      `protobuf:"bytes,4,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	TransStatus  TransStatus `protobuf:"varint,5,opt,name=trans_status,json=transStatus,proto3,enum=api.payment.v1.TransStatus" json:"trans_status,omitempty"`
	ErrorMessage string      `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *RefundSettleEvent_RefundSettleResult) Reset() {
	*x = RefundSettleEvent_RefundSettleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_refund_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundSettleEvent_RefundSettleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundSettleEvent_RefundSettleResult) ProtoMessage() {}

func (x *RefundSettleEvent_RefundSettleResult) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_refund_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundSettleEvent_RefundSettleResult.ProtoReflect.Descriptor instead.
func (*RefundSettleEvent_RefundSettleResult) Descriptor() ([]byte, []int) {
	return file_payment_v1_refund_proto_rawDescGZIP(), []int{18, 0}
}

func (x *RefundSettleEvent_RefundSettleResult) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *RefundSettleEvent_RefundSettleResult) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *RefundSettleEvent_RefundSettleResult) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *RefundSettleEvent_RefundSettleResult) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *RefundSettleEvent_RefundSettleResult) GetTransStatus() TransStatus {
	if x != nil {
		return x.TransStatus
	}
	return TransStatus_TRANS_STATUS_UNSPECIFIED
}

func (x *RefundSettleEvent_RefundSettleResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_payment_v1_refund_proto protoreflect.FileDescriptor

var file_payment_v1_refund_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x02, 0x0a, 0x0f, 0x50, 0x72,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x0b, 0x7a,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x7a, 0x70, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x01, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x08, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcd, 0x01, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66,
	0x5f, 0x73, 0x6f, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72,
	0x65, 0x66, 0x53, 0x6f, 0x66, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x99, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x29, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x28, 0x01, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xa6, 0x02, 0x0a, 0x0e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f, 0x66, 0x49, 0x64, 0x12,
	0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0c,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x58, 0x0a, 0x12,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x24, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x08, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0xc0, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x65, 0x73, 0x63, 0x72, 0x6f, 0x77, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x73, 0x63,
	0x72, 0x6f, 0x77, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e,
	0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c,
	0x0a, 0x0a, 0x72, 0x65, 0x66, 0x5f, 0x73, 0x6f, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x53, 0x6f, 0x66, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x61, 0x0a, 0x12, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x22, 0x03, 0x28, 0xe8, 0x07, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x29, 0x0a, 0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52,
	0x0b, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x95, 0x01, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7f, 0x0a, 0x22, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0b, 0x7a, 0x70,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x4c, 0x0a, 0x23, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x22, 0x45, 0x0a, 0x21, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x4a, 0x6f, 0x62,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x7a, 0x70, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a,
	0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x73, 0x22, 0x46, 0x0a, 0x22, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49,
	0x64, 0x73, 0x22, 0x43, 0x0a, 0x1f, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x7a, 0x70, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x73, 0x22, 0x6d, 0x0a, 0x20, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x7a,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4a, 0x0a, 0x27, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x28, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x6f,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x22, 0x41, 0x0a, 0x1d, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x0c, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x7a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x49, 0x64, 0x73, 0x22, 0x6b, 0x0a, 0x1e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x7a, 0x70, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xfa, 0x05, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x44, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x73, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0f, 0x72, 0x65,
	0x66, 0x5f, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x5a, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x72, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72,
	0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x4c, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x53, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x59, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x1a, 0xe8, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x33, 0x0a,
	0x14, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x49, 0x64, 0x22, 0x32, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x2a, 0x7b, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45,
	0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c,
	0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10,
	0x01, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x54, 0x54,
	0x4c, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x10, 0x02, 0x2a, 0x53, 0x0a, 0x0a, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x32, 0x8e, 0x04, 0x0a, 0x0d, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x08, 0x50, 0x72,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x06, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x7c, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x70, 0x75, 0x70, 0x12,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x70, 0x75, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x74, 0x6f, 0x70, 0x75, 0x70, 0x73, 0x12, 0x88,
	0x01, 0x0a, 0x1b, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x32, 0xb0, 0x04, 0x0a, 0x10, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79,
	0x0a, 0x16, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x18, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x1a, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x97, 0x01, 0x0a, 0x20, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x6f,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x45, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4a, 0x6f,
	0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x45, 0x5a, 0x43,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x2e, 0x76,
	0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_payment_v1_refund_proto_rawDescOnce sync.Once
	file_payment_v1_refund_proto_rawDescData = file_payment_v1_refund_proto_rawDesc
)

func file_payment_v1_refund_proto_rawDescGZIP() []byte {
	file_payment_v1_refund_proto_rawDescOnce.Do(func() {
		file_payment_v1_refund_proto_rawDescData = protoimpl.X.CompressGZIP(file_payment_v1_refund_proto_rawDescData)
	})
	return file_payment_v1_refund_proto_rawDescData
}

var file_payment_v1_refund_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_payment_v1_refund_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_payment_v1_refund_proto_goTypes = []any{
	(RefundSettleEventType)(0),                       // 0: api.payment.v1.RefundSettleEventType
	(RefundType)(0),                                  // 1: api.payment.v1.RefundType
	(*PreCheckRequest)(nil),                          // 2: api.payment.v1.PreCheckRequest
	(*PreCheckResponse)(nil),                         // 3: api.payment.v1.PreCheckResponse
	(*RefundRequest)(nil),                            // 4: api.payment.v1.RefundRequest
	(*RefundResponse)(nil),                           // 5: api.payment.v1.RefundResponse
	(*RefundQueryRequest)(nil),                       // 6: api.payment.v1.RefundQueryRequest
	(*RefundQueryResponse)(nil),                      // 7: api.payment.v1.RefundQueryResponse
	(*CreateTopupRequest)(nil),                       // 8: api.payment.v1.CreateTopupRequest
	(*CreateTopupResponse)(nil),                      // 9: api.payment.v1.CreateTopupResponse
	(*CalculateInterestEstimationRequest)(nil),       // 10: api.payment.v1.CalculateInterestEstimationRequest
	(*CalculateInterestEstimationResponse)(nil),      // 11: api.payment.v1.CalculateInterestEstimationResponse
	(*TriggerReconcileSettleJobsRequest)(nil),        // 12: api.payment.v1.TriggerReconcileSettleJobsRequest
	(*TriggerReconcileSettleJobsResponse)(nil),       // 13: api.payment.v1.TriggerReconcileSettleJobsResponse
	(*TriggerProcessSettleInfoRequest)(nil),          // 14: api.payment.v1.TriggerProcessSettleInfoRequest
	(*TriggerProcessSettleInfoResponse)(nil),         // 15: api.payment.v1.TriggerProcessSettleInfoResponse
	(*TriggerPollingEarlyDischargeJobsRequest)(nil),  // 16: api.payment.v1.TriggerPollingEarlyDischargeJobsRequest
	(*TriggerPollingEarlyDischargeJobsResponse)(nil), // 17: api.payment.v1.TriggerPollingEarlyDischargeJobsResponse
	(*RetryRefundSettleOrderRequest)(nil),            // 18: api.payment.v1.RetryRefundSettleOrderRequest
	(*RetryRefundSettleOrderResponse)(nil),           // 19: api.payment.v1.RetryRefundSettleOrderResponse
	(*RefundSettleEvent)(nil),                        // 20: api.payment.v1.RefundSettleEvent
	(*RefundStandardSettle)(nil),                     // 21: api.payment.v1.RefundStandardSettle
	(*RefundExpiredSettle)(nil),                      // 22: api.payment.v1.RefundExpiredSettle
	(*RefundSettleEvent_RefundSettleResult)(nil),     // 23: api.payment.v1.RefundSettleEvent.RefundSettleResult
	(SettleStatus)(0),                                // 24: api.payment.v1.SettleStatus
	(TransStatus)(0),                                 // 25: api.payment.v1.TransStatus
	(*ErrorDetail)(nil),                              // 26: api.payment.v1.ErrorDetail
}
var file_payment_v1_refund_proto_depIdxs = []int32{
	1,  // 0: api.payment.v1.PreCheckResponse.refund_type:type_name -> api.payment.v1.RefundType
	24, // 1: api.payment.v1.PreCheckResponse.settle_status:type_name -> api.payment.v1.SettleStatus
	1,  // 2: api.payment.v1.RefundResponse.refund_type:type_name -> api.payment.v1.RefundType
	25, // 3: api.payment.v1.RefundResponse.trans_status:type_name -> api.payment.v1.TransStatus
	26, // 4: api.payment.v1.RefundResponse.error_detail:type_name -> api.payment.v1.ErrorDetail
	25, // 5: api.payment.v1.RefundQueryResponse.trans_status:type_name -> api.payment.v1.TransStatus
	0,  // 6: api.payment.v1.RefundSettleEvent.event_type:type_name -> api.payment.v1.RefundSettleEventType
	24, // 7: api.payment.v1.RefundSettleEvent.settle_status:type_name -> api.payment.v1.SettleStatus
	21, // 8: api.payment.v1.RefundSettleEvent.standard_settle:type_name -> api.payment.v1.RefundStandardSettle
	22, // 9: api.payment.v1.RefundSettleEvent.expired_settle:type_name -> api.payment.v1.RefundExpiredSettle
	23, // 10: api.payment.v1.RefundSettleEvent.settle_result:type_name -> api.payment.v1.RefundSettleEvent.RefundSettleResult
	25, // 11: api.payment.v1.RefundSettleEvent.RefundSettleResult.trans_status:type_name -> api.payment.v1.TransStatus
	2,  // 12: api.payment.v1.RefundService.PreCheck:input_type -> api.payment.v1.PreCheckRequest
	4,  // 13: api.payment.v1.RefundService.Refund:input_type -> api.payment.v1.RefundRequest
	6,  // 14: api.payment.v1.RefundService.RefundQuery:input_type -> api.payment.v1.RefundQueryRequest
	8,  // 15: api.payment.v1.RefundService.CreateTopup:input_type -> api.payment.v1.CreateTopupRequest
	10, // 16: api.payment.v1.RefundService.CalculateInterestEstimation:input_type -> api.payment.v1.CalculateInterestEstimationRequest
	18, // 17: api.payment.v1.RefundOpsService.RetryRefundSettleOrder:input_type -> api.payment.v1.RetryRefundSettleOrderRequest
	14, // 18: api.payment.v1.RefundOpsService.TriggerProcessSettleInfo:input_type -> api.payment.v1.TriggerProcessSettleInfoRequest
	12, // 19: api.payment.v1.RefundOpsService.TriggerReconcileSettleJobs:input_type -> api.payment.v1.TriggerReconcileSettleJobsRequest
	16, // 20: api.payment.v1.RefundOpsService.TriggerPollingEarlyDischargeJobs:input_type -> api.payment.v1.TriggerPollingEarlyDischargeJobsRequest
	3,  // 21: api.payment.v1.RefundService.PreCheck:output_type -> api.payment.v1.PreCheckResponse
	5,  // 22: api.payment.v1.RefundService.Refund:output_type -> api.payment.v1.RefundResponse
	7,  // 23: api.payment.v1.RefundService.RefundQuery:output_type -> api.payment.v1.RefundQueryResponse
	9,  // 24: api.payment.v1.RefundService.CreateTopup:output_type -> api.payment.v1.CreateTopupResponse
	11, // 25: api.payment.v1.RefundService.CalculateInterestEstimation:output_type -> api.payment.v1.CalculateInterestEstimationResponse
	19, // 26: api.payment.v1.RefundOpsService.RetryRefundSettleOrder:output_type -> api.payment.v1.RetryRefundSettleOrderResponse
	15, // 27: api.payment.v1.RefundOpsService.TriggerProcessSettleInfo:output_type -> api.payment.v1.TriggerProcessSettleInfoResponse
	13, // 28: api.payment.v1.RefundOpsService.TriggerReconcileSettleJobs:output_type -> api.payment.v1.TriggerReconcileSettleJobsResponse
	17, // 29: api.payment.v1.RefundOpsService.TriggerPollingEarlyDischargeJobs:output_type -> api.payment.v1.TriggerPollingEarlyDischargeJobsResponse
	21, // [21:30] is the sub-list for method output_type
	12, // [12:21] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_payment_v1_refund_proto_init() }
func file_payment_v1_refund_proto_init() {
	if File_payment_v1_refund_proto != nil {
		return
	}
	file_payment_v1_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_payment_v1_refund_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PreCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*PreCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*RefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RefundQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*RefundQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTopupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTopupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CalculateInterestEstimationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*CalculateInterestEstimationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerReconcileSettleJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerReconcileSettleJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerProcessSettleInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerProcessSettleInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerPollingEarlyDischargeJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*TriggerPollingEarlyDischargeJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*RetryRefundSettleOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*RetryRefundSettleOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*RefundSettleEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*RefundStandardSettle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*RefundExpiredSettle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_refund_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*RefundSettleEvent_RefundSettleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_payment_v1_refund_proto_msgTypes[18].OneofWrappers = []any{
		(*RefundSettleEvent_StandardSettle)(nil),
		(*RefundSettleEvent_ExpiredSettle)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_payment_v1_refund_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_payment_v1_refund_proto_goTypes,
		DependencyIndexes: file_payment_v1_refund_proto_depIdxs,
		EnumInfos:         file_payment_v1_refund_proto_enumTypes,
		MessageInfos:      file_payment_v1_refund_proto_msgTypes,
	}.Build()
	File_payment_v1_refund_proto = out.File
	file_payment_v1_refund_proto_rawDesc = nil
	file_payment_v1_refund_proto_goTypes = nil
	file_payment_v1_refund_proto_depIdxs = nil
}
