// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: payment/v1/refund.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RefundService_PreCheck_FullMethodName                    = "/api.payment.v1.RefundService/PreCheck"
	RefundService_Refund_FullMethodName                      = "/api.payment.v1.RefundService/Refund"
	RefundService_RefundQuery_FullMethodName                 = "/api.payment.v1.RefundService/RefundQuery"
	RefundService_CreateTopup_FullMethodName                 = "/api.payment.v1.RefundService/CreateTopup"
	RefundService_CalculateInterestEstimation_FullMethodName = "/api.payment.v1.RefundService/CalculateInterestEstimation"
)

// RefundServiceClient is the client API for RefundService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RefundServiceClient interface {
	PreCheck(ctx context.Context, in *PreCheckRequest, opts ...grpc.CallOption) (*PreCheckResponse, error)
	Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error)
	RefundQuery(ctx context.Context, in *RefundQueryRequest, opts ...grpc.CallOption) (*RefundQueryResponse, error)
	CreateTopup(ctx context.Context, in *CreateTopupRequest, opts ...grpc.CallOption) (*CreateTopupResponse, error)
	CalculateInterestEstimation(ctx context.Context, in *CalculateInterestEstimationRequest, opts ...grpc.CallOption) (*CalculateInterestEstimationResponse, error)
}

type refundServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRefundServiceClient(cc grpc.ClientConnInterface) RefundServiceClient {
	return &refundServiceClient{cc}
}

func (c *refundServiceClient) PreCheck(ctx context.Context, in *PreCheckRequest, opts ...grpc.CallOption) (*PreCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreCheckResponse)
	err := c.cc.Invoke(ctx, RefundService_PreCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundResponse)
	err := c.cc.Invoke(ctx, RefundService_Refund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) RefundQuery(ctx context.Context, in *RefundQueryRequest, opts ...grpc.CallOption) (*RefundQueryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundQueryResponse)
	err := c.cc.Invoke(ctx, RefundService_RefundQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) CreateTopup(ctx context.Context, in *CreateTopupRequest, opts ...grpc.CallOption) (*CreateTopupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTopupResponse)
	err := c.cc.Invoke(ctx, RefundService_CreateTopup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) CalculateInterestEstimation(ctx context.Context, in *CalculateInterestEstimationRequest, opts ...grpc.CallOption) (*CalculateInterestEstimationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculateInterestEstimationResponse)
	err := c.cc.Invoke(ctx, RefundService_CalculateInterestEstimation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundServiceServer is the server API for RefundService service.
// All implementations must embed UnimplementedRefundServiceServer
// for forward compatibility.
type RefundServiceServer interface {
	PreCheck(context.Context, *PreCheckRequest) (*PreCheckResponse, error)
	Refund(context.Context, *RefundRequest) (*RefundResponse, error)
	RefundQuery(context.Context, *RefundQueryRequest) (*RefundQueryResponse, error)
	CreateTopup(context.Context, *CreateTopupRequest) (*CreateTopupResponse, error)
	CalculateInterestEstimation(context.Context, *CalculateInterestEstimationRequest) (*CalculateInterestEstimationResponse, error)
	mustEmbedUnimplementedRefundServiceServer()
}

// UnimplementedRefundServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRefundServiceServer struct{}

func (UnimplementedRefundServiceServer) PreCheck(context.Context, *PreCheckRequest) (*PreCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreCheck not implemented")
}
func (UnimplementedRefundServiceServer) Refund(context.Context, *RefundRequest) (*RefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedRefundServiceServer) RefundQuery(context.Context, *RefundQueryRequest) (*RefundQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundQuery not implemented")
}
func (UnimplementedRefundServiceServer) CreateTopup(context.Context, *CreateTopupRequest) (*CreateTopupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTopup not implemented")
}
func (UnimplementedRefundServiceServer) CalculateInterestEstimation(context.Context, *CalculateInterestEstimationRequest) (*CalculateInterestEstimationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateInterestEstimation not implemented")
}
func (UnimplementedRefundServiceServer) mustEmbedUnimplementedRefundServiceServer() {}
func (UnimplementedRefundServiceServer) testEmbeddedByValue()                       {}

// UnsafeRefundServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RefundServiceServer will
// result in compilation errors.
type UnsafeRefundServiceServer interface {
	mustEmbedUnimplementedRefundServiceServer()
}

func RegisterRefundServiceServer(s grpc.ServiceRegistrar, srv RefundServiceServer) {
	// If the following call pancis, it indicates UnimplementedRefundServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RefundService_ServiceDesc, srv)
}

func _RefundService_PreCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).PreCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_PreCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).PreCheck(ctx, req.(*PreCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_Refund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).Refund(ctx, req.(*RefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_RefundQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).RefundQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_RefundQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).RefundQuery(ctx, req.(*RefundQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_CreateTopup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).CreateTopup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_CreateTopup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).CreateTopup(ctx, req.(*CreateTopupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_CalculateInterestEstimation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateInterestEstimationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).CalculateInterestEstimation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundService_CalculateInterestEstimation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).CalculateInterestEstimation(ctx, req.(*CalculateInterestEstimationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RefundService_ServiceDesc is the grpc.ServiceDesc for RefundService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RefundService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.payment.v1.RefundService",
	HandlerType: (*RefundServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PreCheck",
			Handler:    _RefundService_PreCheck_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _RefundService_Refund_Handler,
		},
		{
			MethodName: "RefundQuery",
			Handler:    _RefundService_RefundQuery_Handler,
		},
		{
			MethodName: "CreateTopup",
			Handler:    _RefundService_CreateTopup_Handler,
		},
		{
			MethodName: "CalculateInterestEstimation",
			Handler:    _RefundService_CalculateInterestEstimation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "payment/v1/refund.proto",
}

const (
	RefundOpsService_RetryRefundSettleOrder_FullMethodName           = "/api.payment.v1.RefundOpsService/RetryRefundSettleOrder"
	RefundOpsService_TriggerProcessSettleInfo_FullMethodName         = "/api.payment.v1.RefundOpsService/TriggerProcessSettleInfo"
	RefundOpsService_TriggerReconcileSettleJobs_FullMethodName       = "/api.payment.v1.RefundOpsService/TriggerReconcileSettleJobs"
	RefundOpsService_TriggerPollingEarlyDischargeJobs_FullMethodName = "/api.payment.v1.RefundOpsService/TriggerPollingEarlyDischargeJobs"
)

// RefundOpsServiceClient is the client API for RefundOpsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RefundOpsServiceClient interface {
	RetryRefundSettleOrder(ctx context.Context, in *RetryRefundSettleOrderRequest, opts ...grpc.CallOption) (*RetryRefundSettleOrderResponse, error)
	TriggerProcessSettleInfo(ctx context.Context, in *TriggerProcessSettleInfoRequest, opts ...grpc.CallOption) (*TriggerProcessSettleInfoResponse, error)
	TriggerReconcileSettleJobs(ctx context.Context, in *TriggerReconcileSettleJobsRequest, opts ...grpc.CallOption) (*TriggerReconcileSettleJobsResponse, error)
	TriggerPollingEarlyDischargeJobs(ctx context.Context, in *TriggerPollingEarlyDischargeJobsRequest, opts ...grpc.CallOption) (*TriggerPollingEarlyDischargeJobsResponse, error)
}

type refundOpsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRefundOpsServiceClient(cc grpc.ClientConnInterface) RefundOpsServiceClient {
	return &refundOpsServiceClient{cc}
}

func (c *refundOpsServiceClient) RetryRefundSettleOrder(ctx context.Context, in *RetryRefundSettleOrderRequest, opts ...grpc.CallOption) (*RetryRefundSettleOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RetryRefundSettleOrderResponse)
	err := c.cc.Invoke(ctx, RefundOpsService_RetryRefundSettleOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundOpsServiceClient) TriggerProcessSettleInfo(ctx context.Context, in *TriggerProcessSettleInfoRequest, opts ...grpc.CallOption) (*TriggerProcessSettleInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerProcessSettleInfoResponse)
	err := c.cc.Invoke(ctx, RefundOpsService_TriggerProcessSettleInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundOpsServiceClient) TriggerReconcileSettleJobs(ctx context.Context, in *TriggerReconcileSettleJobsRequest, opts ...grpc.CallOption) (*TriggerReconcileSettleJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerReconcileSettleJobsResponse)
	err := c.cc.Invoke(ctx, RefundOpsService_TriggerReconcileSettleJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundOpsServiceClient) TriggerPollingEarlyDischargeJobs(ctx context.Context, in *TriggerPollingEarlyDischargeJobsRequest, opts ...grpc.CallOption) (*TriggerPollingEarlyDischargeJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerPollingEarlyDischargeJobsResponse)
	err := c.cc.Invoke(ctx, RefundOpsService_TriggerPollingEarlyDischargeJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundOpsServiceServer is the server API for RefundOpsService service.
// All implementations must embed UnimplementedRefundOpsServiceServer
// for forward compatibility.
type RefundOpsServiceServer interface {
	RetryRefundSettleOrder(context.Context, *RetryRefundSettleOrderRequest) (*RetryRefundSettleOrderResponse, error)
	TriggerProcessSettleInfo(context.Context, *TriggerProcessSettleInfoRequest) (*TriggerProcessSettleInfoResponse, error)
	TriggerReconcileSettleJobs(context.Context, *TriggerReconcileSettleJobsRequest) (*TriggerReconcileSettleJobsResponse, error)
	TriggerPollingEarlyDischargeJobs(context.Context, *TriggerPollingEarlyDischargeJobsRequest) (*TriggerPollingEarlyDischargeJobsResponse, error)
	mustEmbedUnimplementedRefundOpsServiceServer()
}

// UnimplementedRefundOpsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRefundOpsServiceServer struct{}

func (UnimplementedRefundOpsServiceServer) RetryRefundSettleOrder(context.Context, *RetryRefundSettleOrderRequest) (*RetryRefundSettleOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryRefundSettleOrder not implemented")
}
func (UnimplementedRefundOpsServiceServer) TriggerProcessSettleInfo(context.Context, *TriggerProcessSettleInfoRequest) (*TriggerProcessSettleInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerProcessSettleInfo not implemented")
}
func (UnimplementedRefundOpsServiceServer) TriggerReconcileSettleJobs(context.Context, *TriggerReconcileSettleJobsRequest) (*TriggerReconcileSettleJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerReconcileSettleJobs not implemented")
}
func (UnimplementedRefundOpsServiceServer) TriggerPollingEarlyDischargeJobs(context.Context, *TriggerPollingEarlyDischargeJobsRequest) (*TriggerPollingEarlyDischargeJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerPollingEarlyDischargeJobs not implemented")
}
func (UnimplementedRefundOpsServiceServer) mustEmbedUnimplementedRefundOpsServiceServer() {}
func (UnimplementedRefundOpsServiceServer) testEmbeddedByValue()                          {}

// UnsafeRefundOpsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RefundOpsServiceServer will
// result in compilation errors.
type UnsafeRefundOpsServiceServer interface {
	mustEmbedUnimplementedRefundOpsServiceServer()
}

func RegisterRefundOpsServiceServer(s grpc.ServiceRegistrar, srv RefundOpsServiceServer) {
	// If the following call pancis, it indicates UnimplementedRefundOpsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RefundOpsService_ServiceDesc, srv)
}

func _RefundOpsService_RetryRefundSettleOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryRefundSettleOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundOpsServiceServer).RetryRefundSettleOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundOpsService_RetryRefundSettleOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundOpsServiceServer).RetryRefundSettleOrder(ctx, req.(*RetryRefundSettleOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundOpsService_TriggerProcessSettleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerProcessSettleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundOpsServiceServer).TriggerProcessSettleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundOpsService_TriggerProcessSettleInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundOpsServiceServer).TriggerProcessSettleInfo(ctx, req.(*TriggerProcessSettleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundOpsService_TriggerReconcileSettleJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerReconcileSettleJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundOpsServiceServer).TriggerReconcileSettleJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundOpsService_TriggerReconcileSettleJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundOpsServiceServer).TriggerReconcileSettleJobs(ctx, req.(*TriggerReconcileSettleJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundOpsService_TriggerPollingEarlyDischargeJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerPollingEarlyDischargeJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundOpsServiceServer).TriggerPollingEarlyDischargeJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RefundOpsService_TriggerPollingEarlyDischargeJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundOpsServiceServer).TriggerPollingEarlyDischargeJobs(ctx, req.(*TriggerPollingEarlyDischargeJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RefundOpsService_ServiceDesc is the grpc.ServiceDesc for RefundOpsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RefundOpsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.payment.v1.RefundOpsService",
	HandlerType: (*RefundOpsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RetryRefundSettleOrder",
			Handler:    _RefundOpsService_RetryRefundSettleOrder_Handler,
		},
		{
			MethodName: "TriggerProcessSettleInfo",
			Handler:    _RefundOpsService_TriggerProcessSettleInfo_Handler,
		},
		{
			MethodName: "TriggerReconcileSettleJobs",
			Handler:    _RefundOpsService_TriggerReconcileSettleJobs_Handler,
		},
		{
			MethodName: "TriggerPollingEarlyDischargeJobs",
			Handler:    _RefundOpsService_TriggerPollingEarlyDischargeJobs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "payment/v1/refund.proto",
}
