// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: payment/v1/refund.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRefundServiceCreateTopup = "/api.payment.v1.RefundService/CreateTopup"

type RefundServiceHTTPServer interface {
	CreateTopup(context.Context, *CreateTopupRequest) (*CreateTopupResponse, error)
}

func RegisterRefundServiceHTTPServer(s *http.Server, srv RefundServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/payment/v1/refund/topups", _RefundService_CreateTopup0_HTTP_Handler(srv))
}

func _RefundService_CreateTopup0_HTTP_Handler(srv RefundServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTopupRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRefundServiceCreateTopup)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTopup(ctx, req.(*CreateTopupRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateTopupResponse)
		return ctx.Result(200, reply)
	}
}

type RefundServiceHTTPClient interface {
	CreateTopup(ctx context.Context, req *CreateTopupRequest, opts ...http.CallOption) (rsp *CreateTopupResponse, err error)
}

type RefundServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewRefundServiceHTTPClient(client *http.Client) RefundServiceHTTPClient {
	return &RefundServiceHTTPClientImpl{client}
}

func (c *RefundServiceHTTPClientImpl) CreateTopup(ctx context.Context, in *CreateTopupRequest, opts ...http.CallOption) (*CreateTopupResponse, error) {
	var out CreateTopupResponse
	pattern := "/payment/v1/refund/topups"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRefundServiceCreateTopup))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
