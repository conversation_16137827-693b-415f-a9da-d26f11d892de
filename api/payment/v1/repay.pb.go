// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: payment/v1/repay.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FeeType int32

const (
	FeeType_FEE_TYPE_INVALID    FeeType = 0
	FeeType_FEE_TYPE_CONVERSION FeeType = 1
	FeeType_FEE_TYPE_PLATFORM   FeeType = 2
)

// Enum value maps for FeeType.
var (
	FeeType_name = map[int32]string{
		0: "FEE_TYPE_INVALID",
		1: "FEE_TYPE_CONVERSION",
		2: "FEE_TYPE_PLATFORM",
	}
	FeeType_value = map[string]int32{
		"FEE_TYPE_INVALID":    0,
		"FEE_TYPE_CONVERSION": 1,
		"FEE_TYPE_PLATFORM":   2,
	}
)

func (x FeeType) Enum() *FeeType {
	p := new(FeeType)
	*p = x
	return p
}

func (x FeeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeeType) Descriptor() protoreflect.EnumDescriptor {
	return file_payment_v1_repay_proto_enumTypes[0].Descriptor()
}

func (FeeType) Type() protoreflect.EnumType {
	return &file_payment_v1_repay_proto_enumTypes[0]
}

func (x FeeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeeType.Descriptor instead.
func (FeeType) EnumDescriptor() ([]byte, []int) {
	return file_payment_v1_repay_proto_rawDescGZIP(), []int{0}
}

type CreateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount        int64             `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	PartnerCode   string            `protobuf:"bytes,2,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	StatementId   int64             `protobuf:"varint,3,opt,name=statement_id,proto3" json:"statement_id,omitempty"`
	StatementDate string            `protobuf:"bytes,4,opt,name=statement_date,proto3" json:"statement_date,omitempty"`
	Extra         map[string]string `protobuf:"bytes,5,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //  bool generate_viet_qr = 5 [json_name = "generate_viet_qr"];
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_repay_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_repay_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_repay_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOrderRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreateOrderRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *CreateOrderRequest) GetStatementId() int64 {
	if x != nil {
		return x.StatementId
	}
	return 0
}

func (x *CreateOrderRequest) GetStatementDate() string {
	if x != nil {
		return x.StatementDate
	}
	return ""
}

func (x *CreateOrderRequest) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

type CreateOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppTransId   string `protobuf:"bytes,1,opt,name=app_trans_id,proto3" json:"app_trans_id,omitempty"`
	AppId        int32  `protobuf:"varint,2,opt,name=app_id,proto3" json:"app_id,omitempty"`
	ZpTransToken string `protobuf:"bytes,3,opt,name=zp_trans_token,proto3" json:"zp_trans_token,omitempty"`
	TransId      int64  `protobuf:"varint,4,opt,name=trans_id,proto3" json:"trans_id,omitempty"`
	OrderNo      int64  `protobuf:"varint,5,opt,name=order_no,proto3" json:"order_no,omitempty"` //  string qr_code = 6 [json_name = "qr_code"];
}

func (x *CreateOrderResponse) Reset() {
	*x = CreateOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_repay_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResponse) ProtoMessage() {}

func (x *CreateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_repay_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateOrderResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_repay_proto_rawDescGZIP(), []int{1}
}

func (x *CreateOrderResponse) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *CreateOrderResponse) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *CreateOrderResponse) GetZpTransToken() string {
	if x != nil {
		return x.ZpTransToken
	}
	return ""
}

func (x *CreateOrderResponse) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *CreateOrderResponse) GetOrderNo() int64 {
	if x != nil {
		return x.OrderNo
	}
	return 0
}

type GetFeeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId  int64  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderNo string `protobuf:"bytes,2,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
}

func (x *GetFeeRequest) Reset() {
	*x = GetFeeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_repay_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeeRequest) ProtoMessage() {}

func (x *GetFeeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_repay_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeeRequest.ProtoReflect.Descriptor instead.
func (*GetFeeRequest) Descriptor() ([]byte, []int) {
	return file_payment_v1_repay_proto_rawDescGZIP(), []int{2}
}

func (x *GetFeeRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetFeeRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

type GetFeeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fee *Fee `protobuf:"bytes,1,opt,name=fee,proto3" json:"fee,omitempty"`
}

func (x *GetFeeResponse) Reset() {
	*x = GetFeeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_repay_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeeResponse) ProtoMessage() {}

func (x *GetFeeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_repay_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeeResponse.ProtoReflect.Descriptor instead.
func (*GetFeeResponse) Descriptor() ([]byte, []int) {
	return file_payment_v1_repay_proto_rawDescGZIP(), []int{3}
}

func (x *GetFeeResponse) GetFee() *Fee {
	if x != nil {
		return x.Fee
	}
	return nil
}

type Fee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeeType   FeeType `protobuf:"varint,1,opt,name=fee_type,json=feeType,proto3,enum=api.payment.v1.FeeType" json:"fee_type,omitempty"`
	FeeAmount int64   `protobuf:"varint,2,opt,name=fee_amount,json=feeAmount,proto3" json:"fee_amount,omitempty"`
	Message   string  `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	MessageEn string  `protobuf:"bytes,4,opt,name=message_en,json=messageEn,proto3" json:"message_en,omitempty"`
}

func (x *Fee) Reset() {
	*x = Fee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_v1_repay_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fee) ProtoMessage() {}

func (x *Fee) ProtoReflect() protoreflect.Message {
	mi := &file_payment_v1_repay_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fee.ProtoReflect.Descriptor instead.
func (*Fee) Descriptor() ([]byte, []int) {
	return file_payment_v1_repay_proto_rawDescGZIP(), []int{4}
}

func (x *Fee) GetFeeType() FeeType {
	if x != nil {
		return x.FeeType
	}
	return FeeType_FEE_TYPE_INVALID
}

func (x *Fee) GetFeeAmount() int64 {
	if x != nil {
		return x.FeeAmount
	}
	return 0
}

func (x *Fee) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Fee) GetMessageEn() string {
	if x != nil {
		return x.MessageEn
	}
	return ""
}

var File_payment_v1_repay_proto protoreflect.FileDescriptor

var file_payment_v1_repay_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70,
	0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb6, 0x02, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x0c, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x1a, 0x38,
	0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb1, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x22, 0x55, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x22, 0x37, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x52, 0x03, 0x66, 0x65, 0x65, 0x22, 0x91, 0x01, 0x0a,
	0x03, 0x46, 0x65, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x66, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x65, 0x65, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x65,
	0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e,
	0x2a, 0x4f, 0x0a, 0x07, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x46,
	0x45, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10,
	0x00, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x45, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x45,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x10,
	0x02, 0x32, 0x96, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x2f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x2d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x32, 0x57, 0x0a, 0x0a, 0x46, 0x65,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61,
	0x6c, 0x6f, 0x70, 0x61, 0x79, 0x2e, 0x76, 0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_payment_v1_repay_proto_rawDescOnce sync.Once
	file_payment_v1_repay_proto_rawDescData = file_payment_v1_repay_proto_rawDesc
)

func file_payment_v1_repay_proto_rawDescGZIP() []byte {
	file_payment_v1_repay_proto_rawDescOnce.Do(func() {
		file_payment_v1_repay_proto_rawDescData = protoimpl.X.CompressGZIP(file_payment_v1_repay_proto_rawDescData)
	})
	return file_payment_v1_repay_proto_rawDescData
}

var file_payment_v1_repay_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_payment_v1_repay_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_payment_v1_repay_proto_goTypes = []any{
	(FeeType)(0),                // 0: api.payment.v1.FeeType
	(*CreateOrderRequest)(nil),  // 1: api.payment.v1.CreateOrderRequest
	(*CreateOrderResponse)(nil), // 2: api.payment.v1.CreateOrderResponse
	(*GetFeeRequest)(nil),       // 3: api.payment.v1.GetFeeRequest
	(*GetFeeResponse)(nil),      // 4: api.payment.v1.GetFeeResponse
	(*Fee)(nil),                 // 5: api.payment.v1.Fee
	nil,                         // 6: api.payment.v1.CreateOrderRequest.ExtraEntry
}
var file_payment_v1_repay_proto_depIdxs = []int32{
	6, // 0: api.payment.v1.CreateOrderRequest.extra:type_name -> api.payment.v1.CreateOrderRequest.ExtraEntry
	5, // 1: api.payment.v1.GetFeeResponse.fee:type_name -> api.payment.v1.Fee
	0, // 2: api.payment.v1.Fee.fee_type:type_name -> api.payment.v1.FeeType
	1, // 3: api.payment.v1.RePaymentService.CreateOrder:input_type -> api.payment.v1.CreateOrderRequest
	3, // 4: api.payment.v1.FeeService.GetFee:input_type -> api.payment.v1.GetFeeRequest
	2, // 5: api.payment.v1.RePaymentService.CreateOrder:output_type -> api.payment.v1.CreateOrderResponse
	4, // 6: api.payment.v1.FeeService.GetFee:output_type -> api.payment.v1.GetFeeResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_payment_v1_repay_proto_init() }
func file_payment_v1_repay_proto_init() {
	if File_payment_v1_repay_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_payment_v1_repay_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_repay_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_repay_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetFeeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_repay_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetFeeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_v1_repay_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Fee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_payment_v1_repay_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_payment_v1_repay_proto_goTypes,
		DependencyIndexes: file_payment_v1_repay_proto_depIdxs,
		EnumInfos:         file_payment_v1_repay_proto_enumTypes,
		MessageInfos:      file_payment_v1_repay_proto_msgTypes,
	}.Build()
	File_payment_v1_repay_proto = out.File
	file_payment_v1_repay_proto_rawDesc = nil
	file_payment_v1_repay_proto_goTypes = nil
	file_payment_v1_repay_proto_depIdxs = nil
}
