// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: payment/v1/payment.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExchangeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExchangeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangeRequestMultiError, or nil if none found.
func (m *ExchangeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetAsset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangeRequestValidationError{
					field:  "Asset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangeRequestValidationError{
					field:  "Asset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAsset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangeRequestValidationError{
				field:  "Asset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Amount

	// no validation rules for Action

	// no validation rules for ExtraData

	if len(errors) > 0 {
		return ExchangeRequestMultiError(errors)
	}

	return nil
}

// ExchangeRequestMultiError is an error wrapping multiple validation errors
// returned by ExchangeRequest.ValidateAll() if the designated constraints
// aren't met.
type ExchangeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangeRequestMultiError) AllErrors() []error { return m }

// ExchangeRequestValidationError is the validation error returned by
// ExchangeRequest.Validate if the designated constraints aren't met.
type ExchangeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangeRequestValidationError) ErrorName() string { return "ExchangeRequestValidationError" }

// Error satisfies the builtin error interface
func (e ExchangeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangeRequestValidationError{}

// Validate checks the field values on Asset with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Asset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Asset with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AssetMultiError, or nil if none found.
func (m *Asset) ValidateAll() error {
	return m.validate(true)
}

func (m *Asset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return AssetMultiError(errors)
	}

	return nil
}

// AssetMultiError is an error wrapping multiple validation errors returned by
// Asset.ValidateAll() if the designated constraints aren't met.
type AssetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetMultiError) AllErrors() []error { return m }

// AssetValidationError is the validation error returned by Asset.Validate if
// the designated constraints aren't met.
type AssetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetValidationError) ErrorName() string { return "AssetValidationError" }

// Error satisfies the builtin error interface
func (e AssetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetValidationError{}

// Validate checks the field values on ExchangeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExchangeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangeResponseMultiError, or nil if none found.
func (m *ExchangeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	// no validation rules for RefSofId

	if all {
		switch v := interface{}(m.GetReturnCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangeResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangeResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturnCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangeResponseValidationError{
				field:  "ReturnCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExtraData

	// no validation rules for PaymentNo

	if len(errors) > 0 {
		return ExchangeResponseMultiError(errors)
	}

	return nil
}

// ExchangeResponseMultiError is an error wrapping multiple validation errors
// returned by ExchangeResponse.ValidateAll() if the designated constraints
// aren't met.
type ExchangeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangeResponseMultiError) AllErrors() []error { return m }

// ExchangeResponseValidationError is the validation error returned by
// ExchangeResponse.Validate if the designated constraints aren't met.
type ExchangeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangeResponseValidationError) ErrorName() string { return "ExchangeResponseValidationError" }

// Error satisfies the builtin error interface
func (e ExchangeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangeResponseValidationError{}

// Validate checks the field values on RevertExchangeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevertExchangeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevertExchangeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevertExchangeRequestMultiError, or nil if none found.
func (m *RevertExchangeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevertExchangeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	// no validation rules for RevertRequestId

	// no validation rules for RefSofId

	// no validation rules for SofCode

	// no validation rules for ExtraData

	if len(errors) > 0 {
		return RevertExchangeRequestMultiError(errors)
	}

	return nil
}

// RevertExchangeRequestMultiError is an error wrapping multiple validation
// errors returned by RevertExchangeRequest.ValidateAll() if the designated
// constraints aren't met.
type RevertExchangeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevertExchangeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevertExchangeRequestMultiError) AllErrors() []error { return m }

// RevertExchangeRequestValidationError is the validation error returned by
// RevertExchangeRequest.Validate if the designated constraints aren't met.
type RevertExchangeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevertExchangeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevertExchangeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevertExchangeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevertExchangeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevertExchangeRequestValidationError) ErrorName() string {
	return "RevertExchangeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevertExchangeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevertExchangeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevertExchangeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevertExchangeRequestValidationError{}

// Validate checks the field values on RevertExchangeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevertExchangeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevertExchangeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevertExchangeResponseMultiError, or nil if none found.
func (m *RevertExchangeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RevertExchangeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	// no validation rules for RevertRequestId

	// no validation rules for RefSofId

	// no validation rules for RevertRefSofId

	if all {
		switch v := interface{}(m.GetReturnCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevertExchangeResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevertExchangeResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturnCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevertExchangeResponseValidationError{
				field:  "ReturnCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExtraData

	if len(errors) > 0 {
		return RevertExchangeResponseMultiError(errors)
	}

	return nil
}

// RevertExchangeResponseMultiError is an error wrapping multiple validation
// errors returned by RevertExchangeResponse.ValidateAll() if the designated
// constraints aren't met.
type RevertExchangeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevertExchangeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevertExchangeResponseMultiError) AllErrors() []error { return m }

// RevertExchangeResponseValidationError is the validation error returned by
// RevertExchangeResponse.Validate if the designated constraints aren't met.
type RevertExchangeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevertExchangeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevertExchangeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevertExchangeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevertExchangeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevertExchangeResponseValidationError) ErrorName() string {
	return "RevertExchangeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RevertExchangeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevertExchangeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevertExchangeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevertExchangeResponseValidationError{}

// Validate checks the field values on QueryStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryStatusRequestMultiError, or nil if none found.
func (m *QueryStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	if len(errors) > 0 {
		return QueryStatusRequestMultiError(errors)
	}

	return nil
}

// QueryStatusRequestMultiError is an error wrapping multiple validation errors
// returned by QueryStatusRequest.ValidateAll() if the designated constraints
// aren't met.
type QueryStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryStatusRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryStatusRequestMultiError) AllErrors() []error { return m }

// QueryStatusRequestValidationError is the validation error returned by
// QueryStatusRequest.Validate if the designated constraints aren't met.
type QueryStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryStatusRequestValidationError) ErrorName() string {
	return "QueryStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryStatusRequestValidationError{}

// Validate checks the field values on QueryStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryStatusResponseMultiError, or nil if none found.
func (m *QueryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	// no validation rules for RefSofId

	if all {
		switch v := interface{}(m.GetReturnCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryStatusResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryStatusResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturnCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryStatusResponseValidationError{
				field:  "ReturnCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExtraData

	// no validation rules for PaymentNo

	if len(errors) > 0 {
		return QueryStatusResponseMultiError(errors)
	}

	return nil
}

// QueryStatusResponseMultiError is an error wrapping multiple validation
// errors returned by QueryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type QueryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryStatusResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryStatusResponseMultiError) AllErrors() []error { return m }

// QueryStatusResponseValidationError is the validation error returned by
// QueryStatusResponse.Validate if the designated constraints aren't met.
type QueryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryStatusResponseValidationError) ErrorName() string {
	return "QueryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryStatusResponseValidationError{}

// Validate checks the field values on ConfirmAuthenticationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmAuthenticationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmAuthenticationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConfirmAuthenticationRequestMultiError, or nil if none found.
func (m *ConfirmAuthenticationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmAuthenticationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	switch v := m.AuthData.(type) {
	case *ConfirmAuthenticationRequest_Otp:
		if v == nil {
			err := ConfirmAuthenticationRequestValidationError{
				field:  "AuthData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOtp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConfirmAuthenticationRequestValidationError{
						field:  "Otp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConfirmAuthenticationRequestValidationError{
						field:  "Otp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOtp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConfirmAuthenticationRequestValidationError{
					field:  "Otp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ConfirmAuthenticationRequest_Face:
		if v == nil {
			err := ConfirmAuthenticationRequestValidationError{
				field:  "AuthData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFace()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConfirmAuthenticationRequestValidationError{
						field:  "Face",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConfirmAuthenticationRequestValidationError{
						field:  "Face",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFace()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConfirmAuthenticationRequestValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ConfirmAuthenticationRequestMultiError(errors)
	}

	return nil
}

// ConfirmAuthenticationRequestMultiError is an error wrapping multiple
// validation errors returned by ConfirmAuthenticationRequest.ValidateAll() if
// the designated constraints aren't met.
type ConfirmAuthenticationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmAuthenticationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmAuthenticationRequestMultiError) AllErrors() []error { return m }

// ConfirmAuthenticationRequestValidationError is the validation error returned
// by ConfirmAuthenticationRequest.Validate if the designated constraints
// aren't met.
type ConfirmAuthenticationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmAuthenticationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmAuthenticationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmAuthenticationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmAuthenticationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmAuthenticationRequestValidationError) ErrorName() string {
	return "ConfirmAuthenticationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmAuthenticationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmAuthenticationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmAuthenticationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmAuthenticationRequestValidationError{}

// Validate checks the field values on ConfirmAuthenticationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmAuthenticationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmAuthenticationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConfirmAuthenticationResponseMultiError, or nil if none found.
func (m *ConfirmAuthenticationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmAuthenticationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransId

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetReturnCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmAuthenticationResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmAuthenticationResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturnCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmAuthenticationResponseValidationError{
				field:  "ReturnCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmAuthenticationResponseMultiError(errors)
	}

	return nil
}

// ConfirmAuthenticationResponseMultiError is an error wrapping multiple
// validation errors returned by ConfirmAuthenticationResponse.ValidateAll()
// if the designated constraints aren't met.
type ConfirmAuthenticationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmAuthenticationResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmAuthenticationResponseMultiError) AllErrors() []error { return m }

// ConfirmAuthenticationResponseValidationError is the validation error
// returned by ConfirmAuthenticationResponse.Validate if the designated
// constraints aren't met.
type ConfirmAuthenticationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmAuthenticationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmAuthenticationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmAuthenticationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmAuthenticationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmAuthenticationResponseValidationError) ErrorName() string {
	return "ConfirmAuthenticationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmAuthenticationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmAuthenticationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmAuthenticationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmAuthenticationResponseValidationError{}

// Validate checks the field values on OTPAuthenticationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OTPAuthenticationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OTPAuthenticationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OTPAuthenticationDataMultiError, or nil if none found.
func (m *OTPAuthenticationData) ValidateAll() error {
	return m.validate(true)
}

func (m *OTPAuthenticationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Otp

	if len(errors) > 0 {
		return OTPAuthenticationDataMultiError(errors)
	}

	return nil
}

// OTPAuthenticationDataMultiError is an error wrapping multiple validation
// errors returned by OTPAuthenticationData.ValidateAll() if the designated
// constraints aren't met.
type OTPAuthenticationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OTPAuthenticationDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OTPAuthenticationDataMultiError) AllErrors() []error { return m }

// OTPAuthenticationDataValidationError is the validation error returned by
// OTPAuthenticationData.Validate if the designated constraints aren't met.
type OTPAuthenticationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OTPAuthenticationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OTPAuthenticationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OTPAuthenticationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OTPAuthenticationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OTPAuthenticationDataValidationError) ErrorName() string {
	return "OTPAuthenticationDataValidationError"
}

// Error satisfies the builtin error interface
func (e OTPAuthenticationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOTPAuthenticationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OTPAuthenticationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OTPAuthenticationDataValidationError{}

// Validate checks the field values on FaceAuthenticationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FaceAuthenticationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FaceAuthenticationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FaceAuthenticationDataMultiError, or nil if none found.
func (m *FaceAuthenticationData) ValidateAll() error {
	return m.validate(true)
}

func (m *FaceAuthenticationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetFaceToken()) < 1 {
		err := FaceAuthenticationDataValidationError{
			field:  "FaceToken",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FaceAuthenticationDataMultiError(errors)
	}

	return nil
}

// FaceAuthenticationDataMultiError is an error wrapping multiple validation
// errors returned by FaceAuthenticationData.ValidateAll() if the designated
// constraints aren't met.
type FaceAuthenticationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FaceAuthenticationDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FaceAuthenticationDataMultiError) AllErrors() []error { return m }

// FaceAuthenticationDataValidationError is the validation error returned by
// FaceAuthenticationData.Validate if the designated constraints aren't met.
type FaceAuthenticationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FaceAuthenticationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FaceAuthenticationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FaceAuthenticationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FaceAuthenticationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FaceAuthenticationDataValidationError) ErrorName() string {
	return "FaceAuthenticationDataValidationError"
}

// Error satisfies the builtin error interface
func (e FaceAuthenticationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFaceAuthenticationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FaceAuthenticationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FaceAuthenticationDataValidationError{}

// Validate checks the field values on ZasIdQueryRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ZasIdQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ZasIdQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ZasIdQueryRequestMultiError, or nil if none found.
func (m *ZasIdQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ZasIdQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountingCode

	if all {
		switch v := interface{}(m.GetAsset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ZasIdQueryRequestValidationError{
					field:  "Asset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ZasIdQueryRequestValidationError{
					field:  "Asset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAsset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ZasIdQueryRequestValidationError{
				field:  "Asset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ZasIdQueryRequestMultiError(errors)
	}

	return nil
}

// ZasIdQueryRequestMultiError is an error wrapping multiple validation errors
// returned by ZasIdQueryRequest.ValidateAll() if the designated constraints
// aren't met.
type ZasIdQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ZasIdQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ZasIdQueryRequestMultiError) AllErrors() []error { return m }

// ZasIdQueryRequestValidationError is the validation error returned by
// ZasIdQueryRequest.Validate if the designated constraints aren't met.
type ZasIdQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ZasIdQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ZasIdQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ZasIdQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ZasIdQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ZasIdQueryRequestValidationError) ErrorName() string {
	return "ZasIdQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ZasIdQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sZasIdQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ZasIdQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ZasIdQueryRequestValidationError{}

// Validate checks the field values on ZasIdQueryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ZasIdQueryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ZasIdQueryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ZasIdQueryResponseMultiError, or nil if none found.
func (m *ZasIdQueryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ZasIdQueryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReturnCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ZasIdQueryResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ZasIdQueryResponseValidationError{
					field:  "ReturnCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReturnCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ZasIdQueryResponseValidationError{
				field:  "ReturnCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ZasId

	if len(errors) > 0 {
		return ZasIdQueryResponseMultiError(errors)
	}

	return nil
}

// ZasIdQueryResponseMultiError is an error wrapping multiple validation errors
// returned by ZasIdQueryResponse.ValidateAll() if the designated constraints
// aren't met.
type ZasIdQueryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ZasIdQueryResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ZasIdQueryResponseMultiError) AllErrors() []error { return m }

// ZasIdQueryResponseValidationError is the validation error returned by
// ZasIdQueryResponse.Validate if the designated constraints aren't met.
type ZasIdQueryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ZasIdQueryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ZasIdQueryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ZasIdQueryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ZasIdQueryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ZasIdQueryResponseValidationError) ErrorName() string {
	return "ZasIdQueryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ZasIdQueryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sZasIdQueryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ZasIdQueryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ZasIdQueryResponseValidationError{}

// Validate checks the field values on ReturnCode with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReturnCode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReturnCode with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReturnCodeMultiError, or
// nil if none found.
func (m *ReturnCode) ValidateAll() error {
	return m.validate(true)
}

func (m *ReturnCode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Code

	// no validation rules for SubCode

	// no validation rules for Message

	if len(errors) > 0 {
		return ReturnCodeMultiError(errors)
	}

	return nil
}

// ReturnCodeMultiError is an error wrapping multiple validation errors
// returned by ReturnCode.ValidateAll() if the designated constraints aren't met.
type ReturnCodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReturnCodeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReturnCodeMultiError) AllErrors() []error { return m }

// ReturnCodeValidationError is the validation error returned by
// ReturnCode.Validate if the designated constraints aren't met.
type ReturnCodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReturnCodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReturnCodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReturnCodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReturnCodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReturnCodeValidationError) ErrorName() string { return "ReturnCodeValidationError" }

// Error satisfies the builtin error interface
func (e ReturnCodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReturnCode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReturnCodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReturnCodeValidationError{}

// Validate checks the field values on CancelTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelTransactionRequestMultiError, or nil if none found.
func (m *CancelTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZpTransId

	if len(errors) > 0 {
		return CancelTransactionRequestMultiError(errors)
	}

	return nil
}

// CancelTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by CancelTransactionRequest.ValidateAll() if the designated
// constraints aren't met.
type CancelTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelTransactionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelTransactionRequestMultiError) AllErrors() []error { return m }

// CancelTransactionRequestValidationError is the validation error returned by
// CancelTransactionRequest.Validate if the designated constraints aren't met.
type CancelTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelTransactionRequestValidationError) ErrorName() string {
	return "CancelTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelTransactionRequestValidationError{}

// Validate checks the field values on CancelTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelTransactionResponseMultiError, or nil if none found.
func (m *CancelTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RefSofId

	// no validation rules for PartnerTransId

	if len(errors) > 0 {
		return CancelTransactionResponseMultiError(errors)
	}

	return nil
}

// CancelTransactionResponseMultiError is an error wrapping multiple validation
// errors returned by CancelTransactionResponse.ValidateAll() if the
// designated constraints aren't met.
type CancelTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelTransactionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelTransactionResponseMultiError) AllErrors() []error { return m }

// CancelTransactionResponseValidationError is the validation error returned by
// CancelTransactionResponse.Validate if the designated constraints aren't met.
type CancelTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelTransactionResponseValidationError) ErrorName() string {
	return "CancelTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelTransactionResponseValidationError{}
