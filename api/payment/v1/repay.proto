syntax = "proto3";

package api.payment.v1;
option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1;v1";

import "google/api/annotations.proto";
import "validate/validate.proto";

service RePaymentService {
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse) {
    option (google.api.http) = {
      post: "/payment/v1/repay/create-order",
      body: "*"
    };
  }
}

service FeeService {
  rpc GetFee(GetFeeRequest) returns (GetFeeResponse) {}
}

message CreateOrderRequest {
  int64 amount = 1 [json_name = "amount", (validate.rules).int64.gt = 0];
  string partner_code = 2 [json_name = "partner_code"];
  int64 statement_id = 3 [json_name = "statement_id", (validate.rules).int64.gt = 0];
  string statement_date = 4 [json_name = "statement_date", (validate.rules).string.min_len = 1];
  map<string,string> extra = 5 [json_name = "extra"];
  //  bool generate_viet_qr = 5 [json_name = "generate_viet_qr"];
}

message CreateOrderResponse {
  string app_trans_id = 1 [json_name = "app_trans_id"];
  int32 app_id = 2 [json_name = "app_id"];
  string zp_trans_token = 3 [json_name = "zp_trans_token"];
  int64 trans_id = 4 [json_name = "trans_id"];
  int64 order_no = 5 [json_name = "order_no"];
//  string qr_code = 6 [json_name = "qr_code"];
}

message GetFeeRequest {
  int64 user_id = 1 [(validate.rules).int64.gt = 0];
  string order_no = 2 [(validate.rules).string.min_len = 1];
}

message GetFeeResponse {
    Fee fee = 1;
}

message Fee {
  FeeType fee_type = 1;
  int64 fee_amount = 2;
  string message = 3;
  string message_en = 4;
}

enum FeeType {
  FEE_TYPE_INVALID = 0;
  FEE_TYPE_CONVERSION = 1;
  FEE_TYPE_PLATFORM = 2;
}