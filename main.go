package main

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cobra"
	paymentCmd "gitlab.zalopay.vn/fin/installment/payment-service/payment/cmd"
)

var rootCmd = cobra.Command{
	RunE:             runCmd,
	TraverseChildren: true,
}

func init() {
	rootCmd.PersistentFlags().String("config", "/apps/config/config.yaml", "config file (default is empty)")
}

func main() {
	rootCmd.AddCommand(paymentCmd.NewMainAppCmd())
	rootCmd.AddCommand(paymentCmd.NewConsumerCmd())

	if err := rootCmd.Execute(); err != nil {
		log.Fatal("Execute program failed: ", err)
	}
}

// runCmd is the entry point for the main command
func runCmd(cmd *cobra.Command, args []string) error {
	// When no subcommand is provided, run the main app command
	defaultCmd := paymentCmd.NewMainAppCmd()
	return defaultCmd.RunE(cmd, args)
}
