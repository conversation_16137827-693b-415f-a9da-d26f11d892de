sonarqube:
  quality_check: true  # jen<PERSON> job will fail if sonar step fails, otherwise it will run sonar as background, and upload result to sonar if success.
  tag:
    sonarqube_portfolio: customer-management-and-funding-technology
    sonarqube_squad: financial-services
service_metadata:
  tier: 3
devClusters: "zlp-k8s-dev-1" # values: zlp-k8s-dev-1 (onprem), aws-k8s-nonprod-1 (aws)
qcClusters: "zlp-k8s-dev-1" # values: zlp-k8s-dev-1 (onprem), aws-k8s-nonprod-1 (aws)
stagingClusters: "zlp-k8s-prod-3" # optional, default is "zlp-k8s-prod-2", if has multiple values, separate by comma, e.g: "zlp-k8s-prod-2,aws-k8s-prod-1"
productionClusters: "zlp-k8s-prod-3" # optional, default is "zlp-k8s-prod-2", if has multiple values, separate by comma, e.g: "zlp-k8s-prod-2,aws-k8s-prod-1,aws-k8s-prod-2"
serviceProtocols: "http,grpc" # optional, default is "http", for grpc please specify both "http,grpc" to scrape metrics
configType: "yaml" # optional, default is "yaml"
language: "GO" # required, define program language here, if this value not exists, can't init pipeline. Supported languages ["GO", "JAVA", "NODEJS", "OTHER"]