# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /payment/v1/refund/topups:
        post:
            tags:
                - RefundService
            operationId: RefundService_CreateTopup
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.payment.v1.CreateTopupRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.payment.v1.CreateTopupResponse'
    /payment/v1/repay/create-order:
        post:
            tags:
                - RePaymentService
            operationId: RePaymentService_CreateOrder
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.payment.v1.CreateOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.payment.v1.CreateOrderResponse'
    /payment/v1/transaction:
        get:
            tags:
                - TransactionService
            operationId: TransactionService_GetTransaction
            parameters:
                - name: zp_trans_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.payment.v1.GetTransactionResponse'
    /payment/v1/transactions:
        get:
            tags:
                - TransactionService
            operationId: TransactionService_ListTransaction
            parameters:
                - name: account_id
                  in: query
                  schema:
                    type: string
                - name: from_date
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: to_date
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: trans_types
                  in: query
                  schema:
                    type: array
                    items:
                        enum:
                            - TRANS_TYPE_UNSPECIFIED
                            - TRANS_TYPE_PAYMENT
                            - TRANS_TYPE_REPAYMENT
                        type: string
                        format: enum
                - name: pagination.limit
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagination.offset
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagination.cursor
                  in: query
                  schema:
                    type: string
                - name: pagination.direction
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.payment.v1.ListTransactionResponse'
components:
    schemas:
        api.payment.v1.CreateOrderRequest:
            type: object
            properties:
                amount:
                    type: string
                partner_code:
                    type: string
                statement_id:
                    type: string
                statement_date:
                    type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
        api.payment.v1.CreateOrderResponse:
            type: object
            properties:
                app_trans_id:
                    type: string
                app_id:
                    type: integer
                    format: int32
                zp_trans_token:
                    type: string
                trans_id:
                    type: string
                order_no:
                    type: string
        api.payment.v1.CreateTopupRequest:
            type: object
            properties:
                amount:
                    type: string
                zp_trans_id:
                    type: string
        api.payment.v1.CreateTopupResponse:
            type: object
            properties:
                trans_id:
                    type: string
                app_id:
                    type: integer
                    format: int32
                app_trans_id:
                    type: string
                zp_trans_token:
                    type: string
        api.payment.v1.GetTransactionResponse:
            type: object
            properties:
                transaction:
                    $ref: '#/components/schemas/api.payment.v1.Transaction'
        api.payment.v1.ListTransactionResponse:
            type: object
            properties:
                transactions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.payment.v1.Transaction'
                pagination:
                    $ref: '#/components/schemas/api.payment.v1.PaginationData'
        api.payment.v1.PaginationData:
            type: object
            properties:
                prev_cursor:
                    type: string
                next_cursor:
                    type: string
                has_prev:
                    type: boolean
                has_next:
                    type: boolean
        api.payment.v1.Transaction:
            type: object
            properties:
                zpTransId:
                    type: string
                partnerTransId:
                    type: string
                type:
                    enum:
                        - TRANS_TYPE_UNSPECIFIED
                        - TRANS_TYPE_PAYMENT
                        - TRANS_TYPE_REPAYMENT
                    type: string
                    format: enum
                amount:
                    type: string
                status:
                    enum:
                        - TRANS_STATUS_UNSPECIFIED
                        - TRANS_STATUS_SUCCESS
                        - TRANS_STATUS_FAILED
                        - TRANS_STATUS_PENDING
                        - TRANS_STATUS_PROCESSING
                    type: string
                    format: enum
                remark:
                    type: string
                productIcon:
                    type: string
                createdAt:
                    type: string
                updatedAt:
                    type: string
tags:
    - name: RePaymentService
    - name: RefundService
    - name: TransactionService
