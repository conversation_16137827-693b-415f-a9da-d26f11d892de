package configs

import "gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"

// This file is a custom file that is not generated by kratos tool. It is used to store the configuration of the service.
// OrderConfigsWrapper provide more information about the configuration about OrderConfigsInfo
type OrderConfigsHelper struct {
	orderConfigs *OrderConfigs
}

func NewOrderConfigs(configs *OrderConfigs) *OrderConfigsHelper {
	return &OrderConfigsHelper{orderConfigs: configs}
}

func (w *OrderConfigsHelper) GetConfigRepayment(partnerCode partner.PartnerCode) (*OrderConfig, bool) {
	return w.getConfigByPartner(w.orderConfigs.GetRepayment(), partnerCode)
}

func (w *OrderConfigsHelper) GetConfigRefundTopup(partnerCode partner.PartnerCode) (*OrderConfig, bool) {
	return w.getConfigByPartner(w.orderConfigs.GetRefundTopup(), partnerCode)
}

func (w *OrderConfigsHelper) GetConfigRefundSettle(partnerCode partner.PartnerCode) (*OrderConfig, bool) {
	return w.getConfigByPartner(w.orderConfigs.GetRefundSettle(), partnerCode)
}

func (w *OrderConfigsHelper) GetConfigRefundFundback(partnerCode partner.PartnerCode) (*OrderConfig, bool) {
	return w.getConfigByPartner(w.orderConfigs.GetRefundFundback(), partnerCode)
}

func (w *OrderConfigsHelper) getConfigByPartner(configs []*OrderConfig, partnerCode partner.PartnerCode) (*OrderConfig, bool) {
	if len(configs) == 0 {
		return nil, false
	}

	// First try to find exact match for partner code
	for _, config := range configs {
		configPartnerCode := config.GetPartnerCode()
		if configPartnerCode == partnerCode.String() {
			return config, true
		}
	}

	// If no partner-specific config found, look for common config
	for _, config := range configs {
		configPartnerCode := config.GetPartnerCode()
		if configPartnerCode == "" {
			return config, true
		}
	}

	return nil, false
}
