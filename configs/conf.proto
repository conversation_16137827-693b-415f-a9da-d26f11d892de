syntax = "proto3";
package kratos.api;

option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/configs;configs";

import "google/protobuf/duration.proto";

message Bootstrap {
  Payment payment = 1;
}

message Payment {
  Server server = 1;
  Data data = 2;
  Adapters adapters = 3;
  Publishers publishers = 4;
  Temporal temporal = 5;
  Schedulers schedulers = 6;
  Application app = 7;
  Tracing tracer = 8;
  OrderConfigs order_configs = 9;
  Refund refund = 10;
}

message Schedulers {
  TemporalTask payment_status = 1;
  TemporalTask refund_settle_recon = 2;
  TemporalTask refund_discharge_poll = 3;
  TemporalTask refund_fundback_process = 4;
  TemporalTask refund_expired_process = 5;
  TemporalTask refund_expired_repay_poll = 6;
}

message Publishers {
  message Kafka {
    string brokers = 1;
    string topic = 2;
  }
  Kafka purchase_initiated_event = 1;
  Kafka purchase_authenticated_event = 2;
  Kafka pe_update_exchange_status_event = 3;
  Kafka refund_settle_process = 4;
}

message Adapters {
  message GrpcService {
    string address = 1;
    google.protobuf.Duration timeout = 2;
    bool secured = 3;
    string client_id = 4;
    string client_key = 5;
  }

  message AcquiringCoreService {
      message PartnerConfig {
        string callback_url = 1;
        int32 app_id = 2;
        string mac_key = 3;
        string callback_key = 4;
        string partner_code = 5;
      }
    GrpcService grpc = 1;
    repeated PartnerConfig partners = 2;
  }

  GrpcService cimb_connector = 1;
  GrpcService account_service = 2;
  AcquiringCoreService acquiring_core_service = 3;
  GrpcService session = 4;
  GrpcService payment_auth = 5;
  GrpcService installment_service = 6;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
    string api_keys = 4;
  }

  HTTP http = 1;
  GRPC grpc = 2;
  Consumer consumer = 3;
}

message Consumer {
  message Kafka {
    string brokers = 1;
    string topic = 2;
    string group_id = 3;
    int32 num_workers = 4;
    bool use_first_offset = 5;
  }
  Kafka purchase_initiated = 1;
  Kafka purchase_authenticated = 2;
  Kafka ac_order_status_updated = 3;
  Kafka purchase_commission_updated = 4;
  Kafka payment_auth_session_status = 5;
  Kafka ac_refund_info_updated = 6;
  Kafka refund_settle_process = 7;
  Kafka refund_settle_request = 8;
  Kafka refund_settle_response = 9;
  Kafka topup_order_status_updated = 10;
  Kafka settle_order_status_updated = 11;
  Kafka fundback_order_status_updated = 12;
}

message Data {
  message Database {
    string driver = 1;
    string host = 2;
    int64 port = 3;
    string username = 4;
    string password = 5;
    string db_name = 6;
    int64 max_open_conn = 7;
    int64 max_idle_conn = 8;
    google.protobuf.Duration conn_max_life_time = 9;
    bool allow_native_passwords = 10;
  }
  Database database = 1;
  RedisCache redis_cache = 2;
}

message Temporal {
  string address = 1;
  string namespace = 2;
  string enable_tls = 3;
}

message TemporalTask {
  string queue_name = 1;
  string workflow_type = 2;
  google.protobuf.Duration timeout = 3;
  google.protobuf.Duration time_interval = 4;
  google.protobuf.Duration max_time_retry = 5;
  google.protobuf.Duration max_time_interval = 6;
  TemporalTask sub_task = 30;
}

message Application {
  string env = 1;
  string name = 2;
  int64 cimb_zas_id = 3;
}

message Tracing {
  bool enabled = 1;
  string service_name = 2;
  string agent_host = 3;
  int32 agent_port = 4;
}

message RedisCache {
  string address = 1;
  string username = 2;
  string password = 3;
  string master_name = 4;
  int32 pool_size = 5;
  int32 max_retries = 6;
  int32 min_idle_conn = 7;
  google.protobuf.Duration pool_timeout = 8;
  google.protobuf.Duration idle_timeout = 9;
  google.protobuf.Duration max_conn_age = 10;
}

message OrderConfigs {
  repeated OrderConfig repayment = 1;
  repeated OrderConfig refund_topup = 2;
  repeated OrderConfig refund_settle = 3;
  repeated OrderConfig refund_fundback = 4;
}

message OrderConfig {
  int32 app_id = 1;
  string mac_key = 2;
  string callback_key = 3;
  string callback_url = 4;
  optional string partner_code = 5;
  optional string merchant_id = 6;
  optional string product_code = 7;
}

message Refund {
  google.protobuf.Duration trans_alive_in = 1;
}