// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: conf.proto

package configs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Payment       *Payment               `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetPayment() *Payment {
	if x != nil {
		return x.Payment
	}
	return nil
}

type Payment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Server        *Server                `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data          *Data                  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Adapters      *Adapters              `protobuf:"bytes,3,opt,name=adapters,proto3" json:"adapters,omitempty"`
	Publishers    *Publishers            `protobuf:"bytes,4,opt,name=publishers,proto3" json:"publishers,omitempty"`
	Temporal      *Temporal              `protobuf:"bytes,5,opt,name=temporal,proto3" json:"temporal,omitempty"`
	Schedulers    *Schedulers            `protobuf:"bytes,6,opt,name=schedulers,proto3" json:"schedulers,omitempty"`
	App           *Application           `protobuf:"bytes,7,opt,name=app,proto3" json:"app,omitempty"`
	Tracer        *Tracing               `protobuf:"bytes,8,opt,name=tracer,proto3" json:"tracer,omitempty"`
	OrderConfigs  *OrderConfigs          `protobuf:"bytes,9,opt,name=order_configs,json=orderConfigs,proto3" json:"order_configs,omitempty"`
	Refund        *Refund                `protobuf:"bytes,10,opt,name=refund,proto3" json:"refund,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Payment) Reset() {
	*x = Payment{}
	mi := &file_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Payment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Payment) ProtoMessage() {}

func (x *Payment) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Payment.ProtoReflect.Descriptor instead.
func (*Payment) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Payment) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Payment) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Payment) GetAdapters() *Adapters {
	if x != nil {
		return x.Adapters
	}
	return nil
}

func (x *Payment) GetPublishers() *Publishers {
	if x != nil {
		return x.Publishers
	}
	return nil
}

func (x *Payment) GetTemporal() *Temporal {
	if x != nil {
		return x.Temporal
	}
	return nil
}

func (x *Payment) GetSchedulers() *Schedulers {
	if x != nil {
		return x.Schedulers
	}
	return nil
}

func (x *Payment) GetApp() *Application {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Payment) GetTracer() *Tracing {
	if x != nil {
		return x.Tracer
	}
	return nil
}

func (x *Payment) GetOrderConfigs() *OrderConfigs {
	if x != nil {
		return x.OrderConfigs
	}
	return nil
}

func (x *Payment) GetRefund() *Refund {
	if x != nil {
		return x.Refund
	}
	return nil
}

type Schedulers struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	PaymentStatus          *TemporalTask          `protobuf:"bytes,1,opt,name=payment_status,json=paymentStatus,proto3" json:"payment_status,omitempty"`
	RefundSettleRecon      *TemporalTask          `protobuf:"bytes,2,opt,name=refund_settle_recon,json=refundSettleRecon,proto3" json:"refund_settle_recon,omitempty"`
	RefundDischargePoll    *TemporalTask          `protobuf:"bytes,3,opt,name=refund_discharge_poll,json=refundDischargePoll,proto3" json:"refund_discharge_poll,omitempty"`
	RefundFundbackProcess  *TemporalTask          `protobuf:"bytes,4,opt,name=refund_fundback_process,json=refundFundbackProcess,proto3" json:"refund_fundback_process,omitempty"`
	RefundExpiredProcess   *TemporalTask          `protobuf:"bytes,5,opt,name=refund_expired_process,json=refundExpiredProcess,proto3" json:"refund_expired_process,omitempty"`
	RefundExpiredRepayPoll *TemporalTask          `protobuf:"bytes,6,opt,name=refund_expired_repay_poll,json=refundExpiredRepayPoll,proto3" json:"refund_expired_repay_poll,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *Schedulers) Reset() {
	*x = Schedulers{}
	mi := &file_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Schedulers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schedulers) ProtoMessage() {}

func (x *Schedulers) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schedulers.ProtoReflect.Descriptor instead.
func (*Schedulers) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Schedulers) GetPaymentStatus() *TemporalTask {
	if x != nil {
		return x.PaymentStatus
	}
	return nil
}

func (x *Schedulers) GetRefundSettleRecon() *TemporalTask {
	if x != nil {
		return x.RefundSettleRecon
	}
	return nil
}

func (x *Schedulers) GetRefundDischargePoll() *TemporalTask {
	if x != nil {
		return x.RefundDischargePoll
	}
	return nil
}

func (x *Schedulers) GetRefundFundbackProcess() *TemporalTask {
	if x != nil {
		return x.RefundFundbackProcess
	}
	return nil
}

func (x *Schedulers) GetRefundExpiredProcess() *TemporalTask {
	if x != nil {
		return x.RefundExpiredProcess
	}
	return nil
}

func (x *Schedulers) GetRefundExpiredRepayPoll() *TemporalTask {
	if x != nil {
		return x.RefundExpiredRepayPoll
	}
	return nil
}

type Publishers struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	PurchaseInitiatedEvent      *Publishers_Kafka      `protobuf:"bytes,1,opt,name=purchase_initiated_event,json=purchaseInitiatedEvent,proto3" json:"purchase_initiated_event,omitempty"`
	PurchaseAuthenticatedEvent  *Publishers_Kafka      `protobuf:"bytes,2,opt,name=purchase_authenticated_event,json=purchaseAuthenticatedEvent,proto3" json:"purchase_authenticated_event,omitempty"`
	PeUpdateExchangeStatusEvent *Publishers_Kafka      `protobuf:"bytes,3,opt,name=pe_update_exchange_status_event,json=peUpdateExchangeStatusEvent,proto3" json:"pe_update_exchange_status_event,omitempty"`
	RefundSettleProcess         *Publishers_Kafka      `protobuf:"bytes,4,opt,name=refund_settle_process,json=refundSettleProcess,proto3" json:"refund_settle_process,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *Publishers) Reset() {
	*x = Publishers{}
	mi := &file_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Publishers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Publishers) ProtoMessage() {}

func (x *Publishers) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Publishers.ProtoReflect.Descriptor instead.
func (*Publishers) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Publishers) GetPurchaseInitiatedEvent() *Publishers_Kafka {
	if x != nil {
		return x.PurchaseInitiatedEvent
	}
	return nil
}

func (x *Publishers) GetPurchaseAuthenticatedEvent() *Publishers_Kafka {
	if x != nil {
		return x.PurchaseAuthenticatedEvent
	}
	return nil
}

func (x *Publishers) GetPeUpdateExchangeStatusEvent() *Publishers_Kafka {
	if x != nil {
		return x.PeUpdateExchangeStatusEvent
	}
	return nil
}

func (x *Publishers) GetRefundSettleProcess() *Publishers_Kafka {
	if x != nil {
		return x.RefundSettleProcess
	}
	return nil
}

type Adapters struct {
	state                protoimpl.MessageState         `protogen:"open.v1"`
	CimbConnector        *Adapters_GrpcService          `protobuf:"bytes,1,opt,name=cimb_connector,json=cimbConnector,proto3" json:"cimb_connector,omitempty"`
	AccountService       *Adapters_GrpcService          `protobuf:"bytes,2,opt,name=account_service,json=accountService,proto3" json:"account_service,omitempty"`
	AcquiringCoreService *Adapters_AcquiringCoreService `protobuf:"bytes,3,opt,name=acquiring_core_service,json=acquiringCoreService,proto3" json:"acquiring_core_service,omitempty"`
	Session              *Adapters_GrpcService          `protobuf:"bytes,4,opt,name=session,proto3" json:"session,omitempty"`
	PaymentAuth          *Adapters_GrpcService          `protobuf:"bytes,5,opt,name=payment_auth,json=paymentAuth,proto3" json:"payment_auth,omitempty"`
	InstallmentService   *Adapters_GrpcService          `protobuf:"bytes,6,opt,name=installment_service,json=installmentService,proto3" json:"installment_service,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Adapters) Reset() {
	*x = Adapters{}
	mi := &file_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Adapters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adapters) ProtoMessage() {}

func (x *Adapters) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adapters.ProtoReflect.Descriptor instead.
func (*Adapters) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Adapters) GetCimbConnector() *Adapters_GrpcService {
	if x != nil {
		return x.CimbConnector
	}
	return nil
}

func (x *Adapters) GetAccountService() *Adapters_GrpcService {
	if x != nil {
		return x.AccountService
	}
	return nil
}

func (x *Adapters) GetAcquiringCoreService() *Adapters_AcquiringCoreService {
	if x != nil {
		return x.AcquiringCoreService
	}
	return nil
}

func (x *Adapters) GetSession() *Adapters_GrpcService {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *Adapters) GetPaymentAuth() *Adapters_GrpcService {
	if x != nil {
		return x.PaymentAuth
	}
	return nil
}

func (x *Adapters) GetInstallmentService() *Adapters_GrpcService {
	if x != nil {
		return x.InstallmentService
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Http          *Server_HTTP           `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc          *Server_GRPC           `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	Consumer      *Consumer              `protobuf:"bytes,3,opt,name=consumer,proto3" json:"consumer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{5}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Server) GetConsumer() *Consumer {
	if x != nil {
		return x.Consumer
	}
	return nil
}

type Consumer struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	PurchaseInitiated          *Consumer_Kafka        `protobuf:"bytes,1,opt,name=purchase_initiated,json=purchaseInitiated,proto3" json:"purchase_initiated,omitempty"`
	PurchaseAuthenticated      *Consumer_Kafka        `protobuf:"bytes,2,opt,name=purchase_authenticated,json=purchaseAuthenticated,proto3" json:"purchase_authenticated,omitempty"`
	AcOrderStatusUpdated       *Consumer_Kafka        `protobuf:"bytes,3,opt,name=ac_order_status_updated,json=acOrderStatusUpdated,proto3" json:"ac_order_status_updated,omitempty"`
	PurchaseCommissionUpdated  *Consumer_Kafka        `protobuf:"bytes,4,opt,name=purchase_commission_updated,json=purchaseCommissionUpdated,proto3" json:"purchase_commission_updated,omitempty"`
	PaymentAuthSessionStatus   *Consumer_Kafka        `protobuf:"bytes,5,opt,name=payment_auth_session_status,json=paymentAuthSessionStatus,proto3" json:"payment_auth_session_status,omitempty"`
	AcRefundInfoUpdated        *Consumer_Kafka        `protobuf:"bytes,6,opt,name=ac_refund_info_updated,json=acRefundInfoUpdated,proto3" json:"ac_refund_info_updated,omitempty"`
	RefundSettleProcess        *Consumer_Kafka        `protobuf:"bytes,7,opt,name=refund_settle_process,json=refundSettleProcess,proto3" json:"refund_settle_process,omitempty"`
	RefundSettleRequest        *Consumer_Kafka        `protobuf:"bytes,8,opt,name=refund_settle_request,json=refundSettleRequest,proto3" json:"refund_settle_request,omitempty"`
	RefundSettleResponse       *Consumer_Kafka        `protobuf:"bytes,9,opt,name=refund_settle_response,json=refundSettleResponse,proto3" json:"refund_settle_response,omitempty"`
	TopupOrderStatusUpdated    *Consumer_Kafka        `protobuf:"bytes,10,opt,name=topup_order_status_updated,json=topupOrderStatusUpdated,proto3" json:"topup_order_status_updated,omitempty"`
	SettleOrderStatusUpdated   *Consumer_Kafka        `protobuf:"bytes,11,opt,name=settle_order_status_updated,json=settleOrderStatusUpdated,proto3" json:"settle_order_status_updated,omitempty"`
	FundbackOrderStatusUpdated *Consumer_Kafka        `protobuf:"bytes,12,opt,name=fundback_order_status_updated,json=fundbackOrderStatusUpdated,proto3" json:"fundback_order_status_updated,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *Consumer) Reset() {
	*x = Consumer{}
	mi := &file_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Consumer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Consumer) ProtoMessage() {}

func (x *Consumer) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Consumer.ProtoReflect.Descriptor instead.
func (*Consumer) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{6}
}

func (x *Consumer) GetPurchaseInitiated() *Consumer_Kafka {
	if x != nil {
		return x.PurchaseInitiated
	}
	return nil
}

func (x *Consumer) GetPurchaseAuthenticated() *Consumer_Kafka {
	if x != nil {
		return x.PurchaseAuthenticated
	}
	return nil
}

func (x *Consumer) GetAcOrderStatusUpdated() *Consumer_Kafka {
	if x != nil {
		return x.AcOrderStatusUpdated
	}
	return nil
}

func (x *Consumer) GetPurchaseCommissionUpdated() *Consumer_Kafka {
	if x != nil {
		return x.PurchaseCommissionUpdated
	}
	return nil
}

func (x *Consumer) GetPaymentAuthSessionStatus() *Consumer_Kafka {
	if x != nil {
		return x.PaymentAuthSessionStatus
	}
	return nil
}

func (x *Consumer) GetAcRefundInfoUpdated() *Consumer_Kafka {
	if x != nil {
		return x.AcRefundInfoUpdated
	}
	return nil
}

func (x *Consumer) GetRefundSettleProcess() *Consumer_Kafka {
	if x != nil {
		return x.RefundSettleProcess
	}
	return nil
}

func (x *Consumer) GetRefundSettleRequest() *Consumer_Kafka {
	if x != nil {
		return x.RefundSettleRequest
	}
	return nil
}

func (x *Consumer) GetRefundSettleResponse() *Consumer_Kafka {
	if x != nil {
		return x.RefundSettleResponse
	}
	return nil
}

func (x *Consumer) GetTopupOrderStatusUpdated() *Consumer_Kafka {
	if x != nil {
		return x.TopupOrderStatusUpdated
	}
	return nil
}

func (x *Consumer) GetSettleOrderStatusUpdated() *Consumer_Kafka {
	if x != nil {
		return x.SettleOrderStatusUpdated
	}
	return nil
}

func (x *Consumer) GetFundbackOrderStatusUpdated() *Consumer_Kafka {
	if x != nil {
		return x.FundbackOrderStatusUpdated
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Database      *Data_Database         `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	RedisCache    *RedisCache            `protobuf:"bytes,2,opt,name=redis_cache,json=redisCache,proto3" json:"redis_cache,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data) Reset() {
	*x = Data{}
	mi := &file_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{7}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedisCache() *RedisCache {
	if x != nil {
		return x.RedisCache
	}
	return nil
}

type Temporal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Namespace     string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	EnableTls     string                 `protobuf:"bytes,3,opt,name=enable_tls,json=enableTls,proto3" json:"enable_tls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Temporal) Reset() {
	*x = Temporal{}
	mi := &file_conf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Temporal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Temporal) ProtoMessage() {}

func (x *Temporal) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Temporal.ProtoReflect.Descriptor instead.
func (*Temporal) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{8}
}

func (x *Temporal) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Temporal) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Temporal) GetEnableTls() string {
	if x != nil {
		return x.EnableTls
	}
	return ""
}

type TemporalTask struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	QueueName       string                 `protobuf:"bytes,1,opt,name=queue_name,json=queueName,proto3" json:"queue_name,omitempty"`
	WorkflowType    string                 `protobuf:"bytes,2,opt,name=workflow_type,json=workflowType,proto3" json:"workflow_type,omitempty"`
	Timeout         *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	TimeInterval    *durationpb.Duration   `protobuf:"bytes,4,opt,name=time_interval,json=timeInterval,proto3" json:"time_interval,omitempty"`
	MaxTimeRetry    *durationpb.Duration   `protobuf:"bytes,5,opt,name=max_time_retry,json=maxTimeRetry,proto3" json:"max_time_retry,omitempty"`
	MaxTimeInterval *durationpb.Duration   `protobuf:"bytes,6,opt,name=max_time_interval,json=maxTimeInterval,proto3" json:"max_time_interval,omitempty"`
	SubTask         *TemporalTask          `protobuf:"bytes,30,opt,name=sub_task,json=subTask,proto3" json:"sub_task,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TemporalTask) Reset() {
	*x = TemporalTask{}
	mi := &file_conf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemporalTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporalTask) ProtoMessage() {}

func (x *TemporalTask) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporalTask.ProtoReflect.Descriptor instead.
func (*TemporalTask) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{9}
}

func (x *TemporalTask) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *TemporalTask) GetWorkflowType() string {
	if x != nil {
		return x.WorkflowType
	}
	return ""
}

func (x *TemporalTask) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *TemporalTask) GetTimeInterval() *durationpb.Duration {
	if x != nil {
		return x.TimeInterval
	}
	return nil
}

func (x *TemporalTask) GetMaxTimeRetry() *durationpb.Duration {
	if x != nil {
		return x.MaxTimeRetry
	}
	return nil
}

func (x *TemporalTask) GetMaxTimeInterval() *durationpb.Duration {
	if x != nil {
		return x.MaxTimeInterval
	}
	return nil
}

func (x *TemporalTask) GetSubTask() *TemporalTask {
	if x != nil {
		return x.SubTask
	}
	return nil
}

type Application struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Env           string                 `protobuf:"bytes,1,opt,name=env,proto3" json:"env,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CimbZasId     int64                  `protobuf:"varint,3,opt,name=cimb_zas_id,json=cimbZasId,proto3" json:"cimb_zas_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Application) Reset() {
	*x = Application{}
	mi := &file_conf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Application) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application) ProtoMessage() {}

func (x *Application) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application.ProtoReflect.Descriptor instead.
func (*Application) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{10}
}

func (x *Application) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *Application) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Application) GetCimbZasId() int64 {
	if x != nil {
		return x.CimbZasId
	}
	return 0
}

type Tracing struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Enabled       bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	ServiceName   string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	AgentHost     string                 `protobuf:"bytes,3,opt,name=agent_host,json=agentHost,proto3" json:"agent_host,omitempty"`
	AgentPort     int32                  `protobuf:"varint,4,opt,name=agent_port,json=agentPort,proto3" json:"agent_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Tracing) Reset() {
	*x = Tracing{}
	mi := &file_conf_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tracing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tracing) ProtoMessage() {}

func (x *Tracing) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tracing.ProtoReflect.Descriptor instead.
func (*Tracing) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{11}
}

func (x *Tracing) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Tracing) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *Tracing) GetAgentHost() string {
	if x != nil {
		return x.AgentHost
	}
	return ""
}

func (x *Tracing) GetAgentPort() int32 {
	if x != nil {
		return x.AgentPort
	}
	return 0
}

type RedisCache struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	MasterName    string                 `protobuf:"bytes,4,opt,name=master_name,json=masterName,proto3" json:"master_name,omitempty"`
	PoolSize      int32                  `protobuf:"varint,5,opt,name=pool_size,json=poolSize,proto3" json:"pool_size,omitempty"`
	MaxRetries    int32                  `protobuf:"varint,6,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	MinIdleConn   int32                  `protobuf:"varint,7,opt,name=min_idle_conn,json=minIdleConn,proto3" json:"min_idle_conn,omitempty"`
	PoolTimeout   *durationpb.Duration   `protobuf:"bytes,8,opt,name=pool_timeout,json=poolTimeout,proto3" json:"pool_timeout,omitempty"`
	IdleTimeout   *durationpb.Duration   `protobuf:"bytes,9,opt,name=idle_timeout,json=idleTimeout,proto3" json:"idle_timeout,omitempty"`
	MaxConnAge    *durationpb.Duration   `protobuf:"bytes,10,opt,name=max_conn_age,json=maxConnAge,proto3" json:"max_conn_age,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedisCache) Reset() {
	*x = RedisCache{}
	mi := &file_conf_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedisCache) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedisCache) ProtoMessage() {}

func (x *RedisCache) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedisCache.ProtoReflect.Descriptor instead.
func (*RedisCache) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{12}
}

func (x *RedisCache) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *RedisCache) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RedisCache) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RedisCache) GetMasterName() string {
	if x != nil {
		return x.MasterName
	}
	return ""
}

func (x *RedisCache) GetPoolSize() int32 {
	if x != nil {
		return x.PoolSize
	}
	return 0
}

func (x *RedisCache) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *RedisCache) GetMinIdleConn() int32 {
	if x != nil {
		return x.MinIdleConn
	}
	return 0
}

func (x *RedisCache) GetPoolTimeout() *durationpb.Duration {
	if x != nil {
		return x.PoolTimeout
	}
	return nil
}

func (x *RedisCache) GetIdleTimeout() *durationpb.Duration {
	if x != nil {
		return x.IdleTimeout
	}
	return nil
}

func (x *RedisCache) GetMaxConnAge() *durationpb.Duration {
	if x != nil {
		return x.MaxConnAge
	}
	return nil
}

type OrderConfigs struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Repayment      []*OrderConfig         `protobuf:"bytes,1,rep,name=repayment,proto3" json:"repayment,omitempty"`
	RefundTopup    []*OrderConfig         `protobuf:"bytes,2,rep,name=refund_topup,json=refundTopup,proto3" json:"refund_topup,omitempty"`
	RefundSettle   []*OrderConfig         `protobuf:"bytes,3,rep,name=refund_settle,json=refundSettle,proto3" json:"refund_settle,omitempty"`
	RefundFundback []*OrderConfig         `protobuf:"bytes,4,rep,name=refund_fundback,json=refundFundback,proto3" json:"refund_fundback,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *OrderConfigs) Reset() {
	*x = OrderConfigs{}
	mi := &file_conf_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderConfigs) ProtoMessage() {}

func (x *OrderConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderConfigs.ProtoReflect.Descriptor instead.
func (*OrderConfigs) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{13}
}

func (x *OrderConfigs) GetRepayment() []*OrderConfig {
	if x != nil {
		return x.Repayment
	}
	return nil
}

func (x *OrderConfigs) GetRefundTopup() []*OrderConfig {
	if x != nil {
		return x.RefundTopup
	}
	return nil
}

func (x *OrderConfigs) GetRefundSettle() []*OrderConfig {
	if x != nil {
		return x.RefundSettle
	}
	return nil
}

func (x *OrderConfigs) GetRefundFundback() []*OrderConfig {
	if x != nil {
		return x.RefundFundback
	}
	return nil
}

type OrderConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         int32                  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MacKey        string                 `protobuf:"bytes,2,opt,name=mac_key,json=macKey,proto3" json:"mac_key,omitempty"`
	CallbackKey   string                 `protobuf:"bytes,3,opt,name=callback_key,json=callbackKey,proto3" json:"callback_key,omitempty"`
	CallbackUrl   string                 `protobuf:"bytes,4,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	PartnerCode   *string                `protobuf:"bytes,5,opt,name=partner_code,json=partnerCode,proto3,oneof" json:"partner_code,omitempty"`
	MerchantId    *string                `protobuf:"bytes,6,opt,name=merchant_id,json=merchantId,proto3,oneof" json:"merchant_id,omitempty"`
	ProductCode   *string                `protobuf:"bytes,7,opt,name=product_code,json=productCode,proto3,oneof" json:"product_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderConfig) Reset() {
	*x = OrderConfig{}
	mi := &file_conf_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderConfig) ProtoMessage() {}

func (x *OrderConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderConfig.ProtoReflect.Descriptor instead.
func (*OrderConfig) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{14}
}

func (x *OrderConfig) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *OrderConfig) GetMacKey() string {
	if x != nil {
		return x.MacKey
	}
	return ""
}

func (x *OrderConfig) GetCallbackKey() string {
	if x != nil {
		return x.CallbackKey
	}
	return ""
}

func (x *OrderConfig) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *OrderConfig) GetPartnerCode() string {
	if x != nil && x.PartnerCode != nil {
		return *x.PartnerCode
	}
	return ""
}

func (x *OrderConfig) GetMerchantId() string {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return ""
}

func (x *OrderConfig) GetProductCode() string {
	if x != nil && x.ProductCode != nil {
		return *x.ProductCode
	}
	return ""
}

type Refund struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransAliveIn  *durationpb.Duration   `protobuf:"bytes,1,opt,name=trans_alive_in,json=transAliveIn,proto3" json:"trans_alive_in,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Refund) Reset() {
	*x = Refund{}
	mi := &file_conf_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Refund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Refund) ProtoMessage() {}

func (x *Refund) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Refund.ProtoReflect.Descriptor instead.
func (*Refund) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{15}
}

func (x *Refund) GetTransAliveIn() *durationpb.Duration {
	if x != nil {
		return x.TransAliveIn
	}
	return nil
}

type Publishers_Kafka struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Brokers       string                 `protobuf:"bytes,1,opt,name=brokers,proto3" json:"brokers,omitempty"`
	Topic         string                 `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Publishers_Kafka) Reset() {
	*x = Publishers_Kafka{}
	mi := &file_conf_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Publishers_Kafka) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Publishers_Kafka) ProtoMessage() {}

func (x *Publishers_Kafka) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Publishers_Kafka.ProtoReflect.Descriptor instead.
func (*Publishers_Kafka) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Publishers_Kafka) GetBrokers() string {
	if x != nil {
		return x.Brokers
	}
	return ""
}

func (x *Publishers_Kafka) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type Adapters_GrpcService struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Secured       bool                   `protobuf:"varint,3,opt,name=secured,proto3" json:"secured,omitempty"`
	ClientId      string                 `protobuf:"bytes,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey     string                 `protobuf:"bytes,5,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Adapters_GrpcService) Reset() {
	*x = Adapters_GrpcService{}
	mi := &file_conf_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Adapters_GrpcService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adapters_GrpcService) ProtoMessage() {}

func (x *Adapters_GrpcService) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adapters_GrpcService.ProtoReflect.Descriptor instead.
func (*Adapters_GrpcService) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Adapters_GrpcService) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Adapters_GrpcService) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *Adapters_GrpcService) GetSecured() bool {
	if x != nil {
		return x.Secured
	}
	return false
}

func (x *Adapters_GrpcService) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Adapters_GrpcService) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

type Adapters_AcquiringCoreService struct {
	state         protoimpl.MessageState                         `protogen:"open.v1"`
	Grpc          *Adapters_GrpcService                          `protobuf:"bytes,1,opt,name=grpc,proto3" json:"grpc,omitempty"`
	Partners      []*Adapters_AcquiringCoreService_PartnerConfig `protobuf:"bytes,2,rep,name=partners,proto3" json:"partners,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Adapters_AcquiringCoreService) Reset() {
	*x = Adapters_AcquiringCoreService{}
	mi := &file_conf_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Adapters_AcquiringCoreService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adapters_AcquiringCoreService) ProtoMessage() {}

func (x *Adapters_AcquiringCoreService) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adapters_AcquiringCoreService.ProtoReflect.Descriptor instead.
func (*Adapters_AcquiringCoreService) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 1}
}

func (x *Adapters_AcquiringCoreService) GetGrpc() *Adapters_GrpcService {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Adapters_AcquiringCoreService) GetPartners() []*Adapters_AcquiringCoreService_PartnerConfig {
	if x != nil {
		return x.Partners
	}
	return nil
}

type Adapters_AcquiringCoreService_PartnerConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallbackUrl   string                 `protobuf:"bytes,1,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	AppId         int32                  `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MacKey        string                 `protobuf:"bytes,3,opt,name=mac_key,json=macKey,proto3" json:"mac_key,omitempty"`
	CallbackKey   string                 `protobuf:"bytes,4,opt,name=callback_key,json=callbackKey,proto3" json:"callback_key,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,5,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) Reset() {
	*x = Adapters_AcquiringCoreService_PartnerConfig{}
	mi := &file_conf_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adapters_AcquiringCoreService_PartnerConfig) ProtoMessage() {}

func (x *Adapters_AcquiringCoreService_PartnerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adapters_AcquiringCoreService_PartnerConfig.ProtoReflect.Descriptor instead.
func (*Adapters_AcquiringCoreService_PartnerConfig) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 1, 0}
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) GetMacKey() string {
	if x != nil {
		return x.MacKey
	}
	return ""
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) GetCallbackKey() string {
	if x != nil {
		return x.CallbackKey
	}
	return ""
}

func (x *Adapters_AcquiringCoreService_PartnerConfig) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type Server_HTTP struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	mi := &file_conf_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	ApiKeys       string                 `protobuf:"bytes,4,opt,name=api_keys,json=apiKeys,proto3" json:"api_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	mi := &file_conf_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{5, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *Server_GRPC) GetApiKeys() string {
	if x != nil {
		return x.ApiKeys
	}
	return ""
}

type Consumer_Kafka struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Brokers        string                 `protobuf:"bytes,1,opt,name=brokers,proto3" json:"brokers,omitempty"`
	Topic          string                 `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	GroupId        string                 `protobuf:"bytes,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	NumWorkers     int32                  `protobuf:"varint,4,opt,name=num_workers,json=numWorkers,proto3" json:"num_workers,omitempty"`
	UseFirstOffset bool                   `protobuf:"varint,5,opt,name=use_first_offset,json=useFirstOffset,proto3" json:"use_first_offset,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Consumer_Kafka) Reset() {
	*x = Consumer_Kafka{}
	mi := &file_conf_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Consumer_Kafka) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Consumer_Kafka) ProtoMessage() {}

func (x *Consumer_Kafka) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Consumer_Kafka.ProtoReflect.Descriptor instead.
func (*Consumer_Kafka) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{6, 0}
}

func (x *Consumer_Kafka) GetBrokers() string {
	if x != nil {
		return x.Brokers
	}
	return ""
}

func (x *Consumer_Kafka) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *Consumer_Kafka) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Consumer_Kafka) GetNumWorkers() int32 {
	if x != nil {
		return x.NumWorkers
	}
	return 0
}

func (x *Consumer_Kafka) GetUseFirstOffset() bool {
	if x != nil {
		return x.UseFirstOffset
	}
	return false
}

type Data_Database struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Driver               string                 `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Host                 string                 `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Port                 int64                  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Username             string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Password             string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	DbName               string                 `protobuf:"bytes,6,opt,name=db_name,json=dbName,proto3" json:"db_name,omitempty"`
	MaxOpenConn          int64                  `protobuf:"varint,7,opt,name=max_open_conn,json=maxOpenConn,proto3" json:"max_open_conn,omitempty"`
	MaxIdleConn          int64                  `protobuf:"varint,8,opt,name=max_idle_conn,json=maxIdleConn,proto3" json:"max_idle_conn,omitempty"`
	ConnMaxLifeTime      *durationpb.Duration   `protobuf:"bytes,9,opt,name=conn_max_life_time,json=connMaxLifeTime,proto3" json:"conn_max_life_time,omitempty"`
	AllowNativePasswords bool                   `protobuf:"varint,10,opt,name=allow_native_passwords,json=allowNativePasswords,proto3" json:"allow_native_passwords,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	mi := &file_conf_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{7, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Data_Database) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Data_Database) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Data_Database) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Database) GetDbName() string {
	if x != nil {
		return x.DbName
	}
	return ""
}

func (x *Data_Database) GetMaxOpenConn() int64 {
	if x != nil {
		return x.MaxOpenConn
	}
	return 0
}

func (x *Data_Database) GetMaxIdleConn() int64 {
	if x != nil {
		return x.MaxIdleConn
	}
	return 0
}

func (x *Data_Database) GetConnMaxLifeTime() *durationpb.Duration {
	if x != nil {
		return x.ConnMaxLifeTime
	}
	return nil
}

func (x *Data_Database) GetAllowNativePasswords() bool {
	if x != nil {
		return x.AllowNativePasswords
	}
	return false
}

var File_conf_proto protoreflect.FileDescriptor

const file_conf_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"conf.proto\x12\n" +
	"kratos.api\x1a\x1egoogle/protobuf/duration.proto\":\n" +
	"\tBootstrap\x12-\n" +
	"\apayment\x18\x01 \x01(\v2\x13.kratos.api.PaymentR\apayment\"\xf2\x03\n" +
	"\aPayment\x12*\n" +
	"\x06server\x18\x01 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x02 \x01(\v2\x10.kratos.api.DataR\x04data\x120\n" +
	"\badapters\x18\x03 \x01(\v2\x14.kratos.api.AdaptersR\badapters\x126\n" +
	"\n" +
	"publishers\x18\x04 \x01(\v2\x16.kratos.api.PublishersR\n" +
	"publishers\x120\n" +
	"\btemporal\x18\x05 \x01(\v2\x14.kratos.api.TemporalR\btemporal\x126\n" +
	"\n" +
	"schedulers\x18\x06 \x01(\v2\x16.kratos.api.SchedulersR\n" +
	"schedulers\x12)\n" +
	"\x03app\x18\a \x01(\v2\x17.kratos.api.ApplicationR\x03app\x12+\n" +
	"\x06tracer\x18\b \x01(\v2\x13.kratos.api.TracingR\x06tracer\x12=\n" +
	"\rorder_configs\x18\t \x01(\v2\x18.kratos.api.OrderConfigsR\forderConfigs\x12*\n" +
	"\x06refund\x18\n" +
	" \x01(\v2\x12.kratos.api.RefundR\x06refund\"\xdc\x03\n" +
	"\n" +
	"Schedulers\x12?\n" +
	"\x0epayment_status\x18\x01 \x01(\v2\x18.kratos.api.TemporalTaskR\rpaymentStatus\x12H\n" +
	"\x13refund_settle_recon\x18\x02 \x01(\v2\x18.kratos.api.TemporalTaskR\x11refundSettleRecon\x12L\n" +
	"\x15refund_discharge_poll\x18\x03 \x01(\v2\x18.kratos.api.TemporalTaskR\x13refundDischargePoll\x12P\n" +
	"\x17refund_fundback_process\x18\x04 \x01(\v2\x18.kratos.api.TemporalTaskR\x15refundFundbackProcess\x12N\n" +
	"\x16refund_expired_process\x18\x05 \x01(\v2\x18.kratos.api.TemporalTaskR\x14refundExpiredProcess\x12S\n" +
	"\x19refund_expired_repay_poll\x18\x06 \x01(\v2\x18.kratos.api.TemporalTaskR\x16refundExpiredRepayPoll\"\xb3\x03\n" +
	"\n" +
	"Publishers\x12V\n" +
	"\x18purchase_initiated_event\x18\x01 \x01(\v2\x1c.kratos.api.Publishers.KafkaR\x16purchaseInitiatedEvent\x12^\n" +
	"\x1cpurchase_authenticated_event\x18\x02 \x01(\v2\x1c.kratos.api.Publishers.KafkaR\x1apurchaseAuthenticatedEvent\x12b\n" +
	"\x1fpe_update_exchange_status_event\x18\x03 \x01(\v2\x1c.kratos.api.Publishers.KafkaR\x1bpeUpdateExchangeStatusEvent\x12P\n" +
	"\x15refund_settle_process\x18\x04 \x01(\v2\x1c.kratos.api.Publishers.KafkaR\x13refundSettleProcess\x1a7\n" +
	"\x05Kafka\x12\x18\n" +
	"\abrokers\x18\x01 \x01(\tR\abrokers\x12\x14\n" +
	"\x05topic\x18\x02 \x01(\tR\x05topic\"\xd7\a\n" +
	"\bAdapters\x12G\n" +
	"\x0ecimb_connector\x18\x01 \x01(\v2 .kratos.api.Adapters.GrpcServiceR\rcimbConnector\x12I\n" +
	"\x0faccount_service\x18\x02 \x01(\v2 .kratos.api.Adapters.GrpcServiceR\x0eaccountService\x12_\n" +
	"\x16acquiring_core_service\x18\x03 \x01(\v2).kratos.api.Adapters.AcquiringCoreServiceR\x14acquiringCoreService\x12:\n" +
	"\asession\x18\x04 \x01(\v2 .kratos.api.Adapters.GrpcServiceR\asession\x12C\n" +
	"\fpayment_auth\x18\x05 \x01(\v2 .kratos.api.Adapters.GrpcServiceR\vpaymentAuth\x12Q\n" +
	"\x13installment_service\x18\x06 \x01(\v2 .kratos.api.Adapters.GrpcServiceR\x12installmentService\x1a\xb2\x01\n" +
	"\vGrpcService\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x123\n" +
	"\atimeout\x18\x02 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12\x18\n" +
	"\asecured\x18\x03 \x01(\bR\asecured\x12\x1b\n" +
	"\tclient_id\x18\x04 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x05 \x01(\tR\tclientKey\x1a\xcc\x02\n" +
	"\x14AcquiringCoreService\x124\n" +
	"\x04grpc\x18\x01 \x01(\v2 .kratos.api.Adapters.GrpcServiceR\x04grpc\x12S\n" +
	"\bpartners\x18\x02 \x03(\v27.kratos.api.Adapters.AcquiringCoreService.PartnerConfigR\bpartners\x1a\xa8\x01\n" +
	"\rPartnerConfig\x12!\n" +
	"\fcallback_url\x18\x01 \x01(\tR\vcallbackUrl\x12\x15\n" +
	"\x06app_id\x18\x02 \x01(\x05R\x05appId\x12\x17\n" +
	"\amac_key\x18\x03 \x01(\tR\x06macKey\x12!\n" +
	"\fcallback_key\x18\x04 \x01(\tR\vcallbackKey\x12!\n" +
	"\fpartner_code\x18\x05 \x01(\tR\vpartnerCode\"\x86\x03\n" +
	"\x06Server\x12+\n" +
	"\x04http\x18\x01 \x01(\v2\x17.kratos.api.Server.HTTPR\x04http\x12+\n" +
	"\x04grpc\x18\x02 \x01(\v2\x17.kratos.api.Server.GRPCR\x04grpc\x120\n" +
	"\bconsumer\x18\x03 \x01(\v2\x14.kratos.api.ConsumerR\bconsumer\x1ai\n" +
	"\x04HTTP\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x1a\x84\x01\n" +
	"\x04GRPC\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12\x19\n" +
	"\bapi_keys\x18\x04 \x01(\tR\aapiKeys\"\xa8\t\n" +
	"\bConsumer\x12I\n" +
	"\x12purchase_initiated\x18\x01 \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x11purchaseInitiated\x12Q\n" +
	"\x16purchase_authenticated\x18\x02 \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x15purchaseAuthenticated\x12Q\n" +
	"\x17ac_order_status_updated\x18\x03 \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x14acOrderStatusUpdated\x12Z\n" +
	"\x1bpurchase_commission_updated\x18\x04 \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x19purchaseCommissionUpdated\x12Y\n" +
	"\x1bpayment_auth_session_status\x18\x05 \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x18paymentAuthSessionStatus\x12O\n" +
	"\x16ac_refund_info_updated\x18\x06 \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x13acRefundInfoUpdated\x12N\n" +
	"\x15refund_settle_process\x18\a \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x13refundSettleProcess\x12N\n" +
	"\x15refund_settle_request\x18\b \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x13refundSettleRequest\x12P\n" +
	"\x16refund_settle_response\x18\t \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x14refundSettleResponse\x12W\n" +
	"\x1atopup_order_status_updated\x18\n" +
	" \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x17topupOrderStatusUpdated\x12Y\n" +
	"\x1bsettle_order_status_updated\x18\v \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x18settleOrderStatusUpdated\x12]\n" +
	"\x1dfundback_order_status_updated\x18\f \x01(\v2\x1a.kratos.api.Consumer.KafkaR\x1afundbackOrderStatusUpdated\x1a\x9d\x01\n" +
	"\x05Kafka\x12\x18\n" +
	"\abrokers\x18\x01 \x01(\tR\abrokers\x12\x14\n" +
	"\x05topic\x18\x02 \x01(\tR\x05topic\x12\x19\n" +
	"\bgroup_id\x18\x03 \x01(\tR\agroupId\x12\x1f\n" +
	"\vnum_workers\x18\x04 \x01(\x05R\n" +
	"numWorkers\x12(\n" +
	"\x10use_first_offset\x18\x05 \x01(\bR\x0euseFirstOffset\"\xda\x03\n" +
	"\x04Data\x125\n" +
	"\bdatabase\x18\x01 \x01(\v2\x19.kratos.api.Data.DatabaseR\bdatabase\x127\n" +
	"\vredis_cache\x18\x02 \x01(\v2\x16.kratos.api.RedisCacheR\n" +
	"redisCache\x1a\xe1\x02\n" +
	"\bDatabase\x12\x16\n" +
	"\x06driver\x18\x01 \x01(\tR\x06driver\x12\x12\n" +
	"\x04host\x18\x02 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x03 \x01(\x03R\x04port\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\x12\x17\n" +
	"\adb_name\x18\x06 \x01(\tR\x06dbName\x12\"\n" +
	"\rmax_open_conn\x18\a \x01(\x03R\vmaxOpenConn\x12\"\n" +
	"\rmax_idle_conn\x18\b \x01(\x03R\vmaxIdleConn\x12F\n" +
	"\x12conn_max_life_time\x18\t \x01(\v2\x19.google.protobuf.DurationR\x0fconnMaxLifeTime\x124\n" +
	"\x16allow_native_passwords\x18\n" +
	" \x01(\bR\x14allowNativePasswords\"a\n" +
	"\bTemporal\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x12\x1c\n" +
	"\tnamespace\x18\x02 \x01(\tR\tnamespace\x12\x1d\n" +
	"\n" +
	"enable_tls\x18\x03 \x01(\tR\tenableTls\"\x84\x03\n" +
	"\fTemporalTask\x12\x1d\n" +
	"\n" +
	"queue_name\x18\x01 \x01(\tR\tqueueName\x12#\n" +
	"\rworkflow_type\x18\x02 \x01(\tR\fworkflowType\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12>\n" +
	"\rtime_interval\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\ftimeInterval\x12?\n" +
	"\x0emax_time_retry\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\fmaxTimeRetry\x12E\n" +
	"\x11max_time_interval\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\x0fmaxTimeInterval\x123\n" +
	"\bsub_task\x18\x1e \x01(\v2\x18.kratos.api.TemporalTaskR\asubTask\"S\n" +
	"\vApplication\x12\x10\n" +
	"\x03env\x18\x01 \x01(\tR\x03env\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1e\n" +
	"\vcimb_zas_id\x18\x03 \x01(\x03R\tcimbZasId\"\x84\x01\n" +
	"\aTracing\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12!\n" +
	"\fservice_name\x18\x02 \x01(\tR\vserviceName\x12\x1d\n" +
	"\n" +
	"agent_host\x18\x03 \x01(\tR\tagentHost\x12\x1d\n" +
	"\n" +
	"agent_port\x18\x04 \x01(\x05R\tagentPort\"\x9a\x03\n" +
	"\n" +
	"RedisCache\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1f\n" +
	"\vmaster_name\x18\x04 \x01(\tR\n" +
	"masterName\x12\x1b\n" +
	"\tpool_size\x18\x05 \x01(\x05R\bpoolSize\x12\x1f\n" +
	"\vmax_retries\x18\x06 \x01(\x05R\n" +
	"maxRetries\x12\"\n" +
	"\rmin_idle_conn\x18\a \x01(\x05R\vminIdleConn\x12<\n" +
	"\fpool_timeout\x18\b \x01(\v2\x19.google.protobuf.DurationR\vpoolTimeout\x12<\n" +
	"\fidle_timeout\x18\t \x01(\v2\x19.google.protobuf.DurationR\vidleTimeout\x12;\n" +
	"\fmax_conn_age\x18\n" +
	" \x01(\v2\x19.google.protobuf.DurationR\n" +
	"maxConnAge\"\x81\x02\n" +
	"\fOrderConfigs\x125\n" +
	"\trepayment\x18\x01 \x03(\v2\x17.kratos.api.OrderConfigR\trepayment\x12:\n" +
	"\frefund_topup\x18\x02 \x03(\v2\x17.kratos.api.OrderConfigR\vrefundTopup\x12<\n" +
	"\rrefund_settle\x18\x03 \x03(\v2\x17.kratos.api.OrderConfigR\frefundSettle\x12@\n" +
	"\x0frefund_fundback\x18\x04 \x03(\v2\x17.kratos.api.OrderConfigR\x0erefundFundback\"\xab\x02\n" +
	"\vOrderConfig\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\x05R\x05appId\x12\x17\n" +
	"\amac_key\x18\x02 \x01(\tR\x06macKey\x12!\n" +
	"\fcallback_key\x18\x03 \x01(\tR\vcallbackKey\x12!\n" +
	"\fcallback_url\x18\x04 \x01(\tR\vcallbackUrl\x12&\n" +
	"\fpartner_code\x18\x05 \x01(\tH\x00R\vpartnerCode\x88\x01\x01\x12$\n" +
	"\vmerchant_id\x18\x06 \x01(\tH\x01R\n" +
	"merchantId\x88\x01\x01\x12&\n" +
	"\fproduct_code\x18\a \x01(\tH\x02R\vproductCode\x88\x01\x01B\x0f\n" +
	"\r_partner_codeB\x0e\n" +
	"\f_merchant_idB\x0f\n" +
	"\r_product_code\"I\n" +
	"\x06Refund\x12?\n" +
	"\x0etrans_alive_in\x18\x01 \x01(\v2\x19.google.protobuf.DurationR\ftransAliveInBCZAgitlab.zalopay.vn/fin/installment/payment-service/configs;configsb\x06proto3"

var (
	file_conf_proto_rawDescOnce sync.Once
	file_conf_proto_rawDescData []byte
)

func file_conf_proto_rawDescGZIP() []byte {
	file_conf_proto_rawDescOnce.Do(func() {
		file_conf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_conf_proto_rawDesc), len(file_conf_proto_rawDesc)))
	})
	return file_conf_proto_rawDescData
}

var file_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_conf_proto_goTypes = []any{
	(*Bootstrap)(nil),                     // 0: kratos.api.Bootstrap
	(*Payment)(nil),                       // 1: kratos.api.Payment
	(*Schedulers)(nil),                    // 2: kratos.api.Schedulers
	(*Publishers)(nil),                    // 3: kratos.api.Publishers
	(*Adapters)(nil),                      // 4: kratos.api.Adapters
	(*Server)(nil),                        // 5: kratos.api.Server
	(*Consumer)(nil),                      // 6: kratos.api.Consumer
	(*Data)(nil),                          // 7: kratos.api.Data
	(*Temporal)(nil),                      // 8: kratos.api.Temporal
	(*TemporalTask)(nil),                  // 9: kratos.api.TemporalTask
	(*Application)(nil),                   // 10: kratos.api.Application
	(*Tracing)(nil),                       // 11: kratos.api.Tracing
	(*RedisCache)(nil),                    // 12: kratos.api.RedisCache
	(*OrderConfigs)(nil),                  // 13: kratos.api.OrderConfigs
	(*OrderConfig)(nil),                   // 14: kratos.api.OrderConfig
	(*Refund)(nil),                        // 15: kratos.api.Refund
	(*Publishers_Kafka)(nil),              // 16: kratos.api.Publishers.Kafka
	(*Adapters_GrpcService)(nil),          // 17: kratos.api.Adapters.GrpcService
	(*Adapters_AcquiringCoreService)(nil), // 18: kratos.api.Adapters.AcquiringCoreService
	(*Adapters_AcquiringCoreService_PartnerConfig)(nil), // 19: kratos.api.Adapters.AcquiringCoreService.PartnerConfig
	(*Server_HTTP)(nil),         // 20: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),         // 21: kratos.api.Server.GRPC
	(*Consumer_Kafka)(nil),      // 22: kratos.api.Consumer.Kafka
	(*Data_Database)(nil),       // 23: kratos.api.Data.Database
	(*durationpb.Duration)(nil), // 24: google.protobuf.Duration
}
var file_conf_proto_depIdxs = []int32{
	1,  // 0: kratos.api.Bootstrap.payment:type_name -> kratos.api.Payment
	5,  // 1: kratos.api.Payment.server:type_name -> kratos.api.Server
	7,  // 2: kratos.api.Payment.data:type_name -> kratos.api.Data
	4,  // 3: kratos.api.Payment.adapters:type_name -> kratos.api.Adapters
	3,  // 4: kratos.api.Payment.publishers:type_name -> kratos.api.Publishers
	8,  // 5: kratos.api.Payment.temporal:type_name -> kratos.api.Temporal
	2,  // 6: kratos.api.Payment.schedulers:type_name -> kratos.api.Schedulers
	10, // 7: kratos.api.Payment.app:type_name -> kratos.api.Application
	11, // 8: kratos.api.Payment.tracer:type_name -> kratos.api.Tracing
	13, // 9: kratos.api.Payment.order_configs:type_name -> kratos.api.OrderConfigs
	15, // 10: kratos.api.Payment.refund:type_name -> kratos.api.Refund
	9,  // 11: kratos.api.Schedulers.payment_status:type_name -> kratos.api.TemporalTask
	9,  // 12: kratos.api.Schedulers.refund_settle_recon:type_name -> kratos.api.TemporalTask
	9,  // 13: kratos.api.Schedulers.refund_discharge_poll:type_name -> kratos.api.TemporalTask
	9,  // 14: kratos.api.Schedulers.refund_fundback_process:type_name -> kratos.api.TemporalTask
	9,  // 15: kratos.api.Schedulers.refund_expired_process:type_name -> kratos.api.TemporalTask
	9,  // 16: kratos.api.Schedulers.refund_expired_repay_poll:type_name -> kratos.api.TemporalTask
	16, // 17: kratos.api.Publishers.purchase_initiated_event:type_name -> kratos.api.Publishers.Kafka
	16, // 18: kratos.api.Publishers.purchase_authenticated_event:type_name -> kratos.api.Publishers.Kafka
	16, // 19: kratos.api.Publishers.pe_update_exchange_status_event:type_name -> kratos.api.Publishers.Kafka
	16, // 20: kratos.api.Publishers.refund_settle_process:type_name -> kratos.api.Publishers.Kafka
	17, // 21: kratos.api.Adapters.cimb_connector:type_name -> kratos.api.Adapters.GrpcService
	17, // 22: kratos.api.Adapters.account_service:type_name -> kratos.api.Adapters.GrpcService
	18, // 23: kratos.api.Adapters.acquiring_core_service:type_name -> kratos.api.Adapters.AcquiringCoreService
	17, // 24: kratos.api.Adapters.session:type_name -> kratos.api.Adapters.GrpcService
	17, // 25: kratos.api.Adapters.payment_auth:type_name -> kratos.api.Adapters.GrpcService
	17, // 26: kratos.api.Adapters.installment_service:type_name -> kratos.api.Adapters.GrpcService
	20, // 27: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	21, // 28: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	6,  // 29: kratos.api.Server.consumer:type_name -> kratos.api.Consumer
	22, // 30: kratos.api.Consumer.purchase_initiated:type_name -> kratos.api.Consumer.Kafka
	22, // 31: kratos.api.Consumer.purchase_authenticated:type_name -> kratos.api.Consumer.Kafka
	22, // 32: kratos.api.Consumer.ac_order_status_updated:type_name -> kratos.api.Consumer.Kafka
	22, // 33: kratos.api.Consumer.purchase_commission_updated:type_name -> kratos.api.Consumer.Kafka
	22, // 34: kratos.api.Consumer.payment_auth_session_status:type_name -> kratos.api.Consumer.Kafka
	22, // 35: kratos.api.Consumer.ac_refund_info_updated:type_name -> kratos.api.Consumer.Kafka
	22, // 36: kratos.api.Consumer.refund_settle_process:type_name -> kratos.api.Consumer.Kafka
	22, // 37: kratos.api.Consumer.refund_settle_request:type_name -> kratos.api.Consumer.Kafka
	22, // 38: kratos.api.Consumer.refund_settle_response:type_name -> kratos.api.Consumer.Kafka
	22, // 39: kratos.api.Consumer.topup_order_status_updated:type_name -> kratos.api.Consumer.Kafka
	22, // 40: kratos.api.Consumer.settle_order_status_updated:type_name -> kratos.api.Consumer.Kafka
	22, // 41: kratos.api.Consumer.fundback_order_status_updated:type_name -> kratos.api.Consumer.Kafka
	23, // 42: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	12, // 43: kratos.api.Data.redis_cache:type_name -> kratos.api.RedisCache
	24, // 44: kratos.api.TemporalTask.timeout:type_name -> google.protobuf.Duration
	24, // 45: kratos.api.TemporalTask.time_interval:type_name -> google.protobuf.Duration
	24, // 46: kratos.api.TemporalTask.max_time_retry:type_name -> google.protobuf.Duration
	24, // 47: kratos.api.TemporalTask.max_time_interval:type_name -> google.protobuf.Duration
	9,  // 48: kratos.api.TemporalTask.sub_task:type_name -> kratos.api.TemporalTask
	24, // 49: kratos.api.RedisCache.pool_timeout:type_name -> google.protobuf.Duration
	24, // 50: kratos.api.RedisCache.idle_timeout:type_name -> google.protobuf.Duration
	24, // 51: kratos.api.RedisCache.max_conn_age:type_name -> google.protobuf.Duration
	14, // 52: kratos.api.OrderConfigs.repayment:type_name -> kratos.api.OrderConfig
	14, // 53: kratos.api.OrderConfigs.refund_topup:type_name -> kratos.api.OrderConfig
	14, // 54: kratos.api.OrderConfigs.refund_settle:type_name -> kratos.api.OrderConfig
	14, // 55: kratos.api.OrderConfigs.refund_fundback:type_name -> kratos.api.OrderConfig
	24, // 56: kratos.api.Refund.trans_alive_in:type_name -> google.protobuf.Duration
	24, // 57: kratos.api.Adapters.GrpcService.timeout:type_name -> google.protobuf.Duration
	17, // 58: kratos.api.Adapters.AcquiringCoreService.grpc:type_name -> kratos.api.Adapters.GrpcService
	19, // 59: kratos.api.Adapters.AcquiringCoreService.partners:type_name -> kratos.api.Adapters.AcquiringCoreService.PartnerConfig
	24, // 60: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	24, // 61: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	24, // 62: kratos.api.Data.Database.conn_max_life_time:type_name -> google.protobuf.Duration
	63, // [63:63] is the sub-list for method output_type
	63, // [63:63] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_conf_proto_init() }
func file_conf_proto_init() {
	if File_conf_proto != nil {
		return
	}
	file_conf_proto_msgTypes[14].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_conf_proto_rawDesc), len(file_conf_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_proto_goTypes,
		DependencyIndexes: file_conf_proto_depIdxs,
		MessageInfos:      file_conf_proto_msgTypes,
	}.Build()
	File_conf_proto = out.File
	file_conf_proto_goTypes = nil
	file_conf_proto_depIdxs = nil
}
