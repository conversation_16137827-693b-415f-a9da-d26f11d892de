FROM registry-gitlab.zalopay.vn/docker/images/golang:1.23.4 as builder

ENV GO111MODULE=on
ENV GONOSUMDB="gitlab.zalopay.vn"
ENV GONOPROXY="gitlab.zalopay.vn"
ENV GOPRIVATE="gitlab.zalopay.vn"

# Working directory
WORKDIR /build

# your command here, below is an example
COPY go.mod .
COPY go.sum .

# Install app dependencies
RUN go mod download

# Add src app
COPY . .

# Build app
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./app

# final stage
FROM registry-gitlab.zalopay.vn/docker/images/alpine:3.13
# Copy binary from builder
COPY --from=builder /build/app /app

# Run server command
ENV TZ Asia/Saigon

# expose some necessary port
EXPOSE 8080 9090
ENTRYPOINT ["/app"]