package model

import "github.com/go-kratos/kratos/v2/log"

type CIMBTransaction struct {
	PaymentStatus             CIMBPaymentStatus
	CreatedTime               string
	BankTransactionSequenceID string
}

type CIMBPaymentStatus string

func ParseCIMBPaymentStatusFromString(value string) CIMBPaymentStatus {
	switch value {
	case CIMBPaymentStatusNew.String():
		return CIMBPaymentStatusNew
	case CIMBPaymentStatusVerifiedPendingConfirmation.String():
		return CIMBPaymentStatusVerifiedPendingConfirmation
	case CIMBPaymentStatusProcessing.String():
		return CIMBPaymentStatusProcessing
	case CIMBPaymentStatusComplete.String():
		return CIMBPaymentStatusComplete
	case CIMBPaymentStatusTransferFailed.String():
		return CIMBPaymentStatusTransferFailed
	case CIMBPaymentStatusExpired.String():
		return CIMBPaymentStatusExpired
	case CIMBPaymentStatusManualReconRequired.String():
		return CIMBPaymentStatusManualReconRequired
	case CIMBPaymentStatusCancelled.String():
		return CIMBPaymentStatusCancelled
	default:
		log.Warnf("[Alert], parse status from string, unknown status: %s", value)
		return CIMBPaymentStatus(value)
	}
}

func (c CIMBPaymentStatus) String() string {
	return string(c)
}

func (c CIMBPaymentStatus) IsValid() bool {
	switch c {
	case CIMBPaymentStatusNew,
		CIMBPaymentStatusVerifiedPendingConfirmation,
		CIMBPaymentStatusProcessing,
		CIMBPaymentStatusManualReconRequired,
		CIMBPaymentStatusComplete, CIMBPaymentStatusTransferFailed, CIMBPaymentStatusExpired, CIMBPaymentStatusCancelled:
		return true
	}
	return false
}

func (c CIMBPaymentStatus) IsFinalStatus() bool {
	switch c {
	case CIMBPaymentStatusComplete,
		CIMBPaymentStatusTransferFailed,
		CIMBPaymentStatusExpired,
		CIMBPaymentStatusCancelled,
		CIMBPaymentStatusManualReconRequired:
		return true
	}
	return false
}

const (
	CIMBPaymentStatusNew                         CIMBPaymentStatus = "NEW"
	CIMBPaymentStatusVerifiedPendingConfirmation CIMBPaymentStatus = "VERIFIED_PENDING_CONFIRMATION"
	CIMBPaymentStatusProcessing                  CIMBPaymentStatus = "PROCESSING"
	CIMBPaymentStatusComplete                    CIMBPaymentStatus = "COMPLETED"
	CIMBPaymentStatusFailed                      CIMBPaymentStatus = "FAILED"
	CIMBPaymentStatusTransferFailed              CIMBPaymentStatus = "TRANSFER_FAILED"
	CIMBPaymentStatusExpired                     CIMBPaymentStatus = "TRANSACTION_EXPIRED"
	CIMBPaymentStatusManualReconRequired         CIMBPaymentStatus = "MANUAL_RECON_REQUIRED"
	CIMBPaymentStatusCancelled                   CIMBPaymentStatus = "CANCELLED"
)

type AdvanceStepUpMethod string

const (
	//AdvanceStepUpMethodPartnerPin     AdvanceStepUpMethod = "PARTNER_PIN" //PARTNER_PIN
	AdvanceStepUpMethodPartnerPinPass        AdvanceStepUpMethod = "PARTNER_PIN_PASS"
	AdvanceStepUpMethodPartnerOTP            AdvanceStepUpMethod = "PARTNER_OTP_OR_BIO"
	AdvanceStepUpMethodPartnerSelfie         AdvanceStepUpMethod = "PARTNER_SELFIE"
	AdvanceStepUpMethodCIMBSelfie            AdvanceStepUpMethod = "CIMB_SELFIE"
	AdvanceStepUpMethodPartnerSelfieWithOTP  AdvanceStepUpMethod = "PARTNER_SELFIE_WITH_OTP"
	AdvanceStepUpMethodNoAdvanceAuthRequired AdvanceStepUpMethod = "NO_ADVANCE_AUTH_REQUIRED"
)

func (r AdvanceStepUpMethod) String() string {
	return string(r)
}

func ParseAdvanceStepUpMethodFromString(value string) AdvanceStepUpMethod {
	switch value {
	case AdvanceStepUpMethodPartnerPinPass.String():
		return AdvanceStepUpMethodPartnerPinPass
	case AdvanceStepUpMethodPartnerOTP.String():
		return AdvanceStepUpMethodPartnerOTP
	case AdvanceStepUpMethodPartnerSelfie.String():
		return AdvanceStepUpMethodPartnerSelfie
	case AdvanceStepUpMethodCIMBSelfie.String():
		return AdvanceStepUpMethodCIMBSelfie
	case AdvanceStepUpMethodPartnerSelfieWithOTP.String():
		return AdvanceStepUpMethodPartnerSelfieWithOTP
	case AdvanceStepUpMethodNoAdvanceAuthRequired.String():
		return AdvanceStepUpMethodNoAdvanceAuthRequired
	}
	return AdvanceStepUpMethod(value)
}

type StepUpMethod string

const (
	StepUpMethodPinPass  = StepUpMethod("PARTNER_PIN_PASS")
	StepUpMethodPtpOrBio = StepUpMethod("PARTNER_OTP_OR_BIO")
)

func (r StepUpMethod) String() string {
	return string(r)
}

func ParseStepUpMethodFromString(s string) StepUpMethod {
	switch s {
	case StepUpMethodPinPass.String():
		return StepUpMethodPinPass
	case StepUpMethodPtpOrBio.String():
		return StepUpMethodPtpOrBio
	}
	return StepUpMethod(s)
}

func FromCIMBPaymentStatusToPaymentStatus(status CIMBPaymentStatus) PaymentStatus {
	switch status {
	case CIMBPaymentStatusComplete:
		return PaymentStatusSucceeded
	case
		CIMBPaymentStatusFailed,
		CIMBPaymentStatusTransferFailed,
		CIMBPaymentStatusCancelled,
		CIMBPaymentStatusExpired:
		return PaymentStatusFailed
	case CIMBPaymentStatusProcessing:
		return PaymentStatusProcessing
	default:
		return PaymentStatusPending
	}
}
