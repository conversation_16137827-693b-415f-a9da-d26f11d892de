package model

type AuthSessionStatusEvent struct {
	OrderNo       string   `json:"order_no"`
	PaymentNo     string   `json:"payment_no"`
	AuthSessionId string   `json:"auth_session_id"`
	Status        string   `json:"status"` //PROCESSING,FAILED,SUCCESS
	AuthData      AuthData `json:"auth_data"`
	TransId       int64    `json:"trans_id"`
	Timestamp     int64    `json:"timestamp"`
	FundType      string   `json:"fund_type"` //UNKNOWN,BANK,INSTALLMENT,PAY_LATER
}

type AuthData struct {
	AuthProvider string `json:"auth_provider"` //BANK,ZALOPAY
	AuthType     int32  `json:"auth_type"`
	AuthValue    string `json:"auth_value"`
}
