package model

import (
	"fmt"
	"time"

	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

type TransactionInfo struct {
	InstallmentTransactionID int64
	PartnerTransactionID     string
	TransactionAmount        int64
	BeneficiaryAmount        int64
	BeneficiaryDetails       *BeneficiaryDetails
	TransactionStatus        string
	AvailableBalance         int64
}

type BeneficiaryDetails struct {
	BankCode          string
	CorpAccountNumber string
	CorpAccountName   string
}

type InstallmentInstruction struct {
	Tenor        int32
	InterestRate float64
}

type ListPaymentQuery struct {
	ZalopayID    int64
	AccountID    int64
	TransTypes   []TransType
	PartnerCodes []partner.PartnerCode
	TimeRange    *TimeRangeFilter
	Pagination   *Pagination
}

func (q *ListPaymentQuery) Validate(maxLimit int) error {
	if q.Pagination == nil {
		return fmt.Errorf("pagination is required")
	}
	if q.Pagination.Limit > maxLimit {
		return fmt.Errorf("limit must be less than %d", maxLimit)
	}
	return nil
}

func (q *ListPaymentQuery) IsQueryNext() bool {
	return q.Pagination.IsNext()
}

func (q *ListPaymentQuery) IsQueryPrev() bool {
	return q.Pagination.IsPrev()
}

func (q *ListPaymentQuery) HasPagingByOffset() bool {
	return q.Pagination.HasOffsetQuery()
}

func (q *ListPaymentQuery) HasPagingByCursor() bool {
	return q.Pagination.HasCursorQuery()
}

func (q *ListPaymentQuery) InitPagination() *ListPaymentQuery {
	if q.Pagination == nil {
		q.Pagination = &Pagination{}
	}
	if q.Pagination.Limit == 0 {
		q.Pagination.Limit = 100
	}
	if q.Pagination.Direct == 0 {
		q.Pagination.Direct = CursorDirectionNext
	}
	return q
}

func (q *ListPaymentQuery) SetLimit(limit int) *ListPaymentQuery {
	q.Pagination.Limit = limit
	return q
}

// Transaction is a representation of a payment log in the system.
// It is a child domain of the payment domain.
// It includes some basic information of a payment log item
// It can be converted from PurchaseOrder
type Transaction struct {
	TransID        int64
	ZPTransID      string
	PartnerTransID string
	AccountInfo    Account
	Amount         int64
	Type           TransType
	Remark         string
	Status         TransStatus
	Product        TransProduct
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

type TransProduct struct {
	IconUrl string
}

type TransStatus string

const (
	TransStatusSucceeded  TransStatus = "SUCCEEDED"
	TransStatusFailed     TransStatus = "FAILED"
	TransStatusPending    TransStatus = "PENDING"
	TransStatusProcessing TransStatus = "PROCESSING"
)
