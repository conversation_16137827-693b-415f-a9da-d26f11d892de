package model

import "fmt"

const (
	ErrDuplicateTransaction = "duplicate_transaction"
)

var (
	ErrOrderNotFound                  = fmt.Errorf("order not found")
	ErrPartnerNotFound                = fmt.Errorf("partner not found")
	ErrRefundNotFound                 = fmt.Errorf("refund not found")
	ErrRefundSettleNotFound           = fmt.<PERSON><PERSON><PERSON>("refund settle not found")
	ErrRefundTopupNotRequired         = fmt.<PERSON><PERSON><PERSON>("refund topup not required")
	ErrRefundTopupOrderAmountMismatch = fmt.Errorf("refund topup order amount mismatch")
	ErrPartnerTransactionNotFound     = fmt.<PERSON><PERSON><PERSON>("partner transaction not found")
	ErrInsufficientSettlementFunds    = fmt.Errorf("insufficient funds for settlement")
	ErrSettlementAmountMismatch       = fmt.Errorf("settlement amount mismatch with source transaction")
)
