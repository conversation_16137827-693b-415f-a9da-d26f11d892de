package model

type Pagination struct {
	Offset *int      `json:"page"`
	Limit  int       `json:"limit"`
	Cursor any       `json:"cursor"`
	Direct Direction `json:"direct"`
}

func (p *Pagination) HasOffsetQuery() bool {
	return p != nil && p.Offset != nil
}

func (p *Pagination) HasCursorQuery() bool {
	return p != nil && p.Cursor != nil && p.Offset == nil
}

func (p *Pagination) IsNext() bool {
	return p != nil && p.Direct == CursorDirectionNext
}

func (p *Pagination) IsPrev() bool {
	return p != nil && p.Direct == CursorDirectionPrev
}

type PaginationResult struct {
	HasNext    bool   `json:"has_next"`
	HasPrev    bool   `json:"has_prev"`
	NextCursor string `json:"next_cursor"`
	PrevCursor string `json:"prev_cursor"`
}

type Direction int32

const (
	CursorDirectionNext Direction = iota
	CursorDirectionPrev
)
