package model

type OrderStatus string

const (
	OrderStatusInit       OrderStatus = "init"
	OrderStatusPending    OrderStatus = "pending"
	OrderStatusSucceeded  OrderStatus = "succeeded"
	OrderStatusFailed     OrderStatus = "failed"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusCancelled  OrderStatus = "cancelled"
)

type OrderType string

const (
	OrderTypeRefund         OrderType = "refund"
	OrderTypeRepayment      OrderType = "repayment"
	OrderTypeEarlyDischarge OrderType = "early_discharge"
)

type OrderStage string

const (
	OrderStageTopup    OrderStage = "topup"
	OrderStageSettle   OrderStage = "settle"
	OrderStageFundback OrderStage = "fundback"
)

func (o OrderStage) String() string {
	return string(o)
}

type OrderProgressUpdate struct {
	OrderID   int64
	Status    OrderStatus
	ZpTransID int64
	ExtraData any
}
