package model

import (
	"time"

	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

type PaymentStatusWorkflowRequest struct {
	PaymentID int64 `json:"payment_id"`
	ZalopayID int64 `json:"zalopay_id"`
}

type PaymentStatusWorkflowResponse struct {
	PaymentStatus          string `json:"payment_status"`
	PartnerPaymentStatus   string `json:"partner_payment_status"`
	AwaitingPartnerTransID bool   `json:"awaiting_partner_trans_id"`
}

// InstallmentCreateParams for Create Loan Event
type InstallmentCreateParams struct {
	ZalopayID   int64                  `json:"zalopay_id"`
	AccountID   int64                  `json:"account_id"`
	PartnerCode partner.PartnerCode    `json:"partner_code"`
	Installment InstallmentPlanParams  `json:"installment"`
	Transaction InstallmentTransParams `json:"transaction"`
}

type InstallmentTransParams struct {
	TransID   int64  `json:"trans_id"`
	TransDesc string `json:"trans_desc"`
	ZpTransID int64  `json:"zp_trans_id"`
}

type InstallmentPlanParams struct {
	Tenure         int32       `json:"tenure"`
	EmiAmount      int64       `json:"emi_amount"`
	InterestRate   float64     `json:"interest_rate"`
	InterestAmount int64       `json:"interest_amount"`
	DisburseAmount int64       `json:"disburse_amount"`
	TotalFeeAmount int64       `json:"total_fee_amount"`
	TotalDueAmount int64       `json:"total_due_amount"`
	FeeDetails     []FeeDetail `json:"fee_details"`
}

type FeeDetail struct {
	Type    FeeType `json:"type"`
	Rate    float64 `json:"rate"` // Total fee rate
	Amount  int64   `json:"amount"`
	Explain string  `json:"explain"`
}

type FeeType string

const (
	UnknownFee    FeeType = "unknown_fee"
	PlatformFee   FeeType = "platform_fee"
	ConversionFee FeeType = "conversion_fee"
)

type SyncAccountBalanceParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	AccountID int64 `json:"account_id"`
	Polling   bool  `json:"polling"`
}

type SyncLoanRepaymentWorkflow struct {
	ZalopayID     int64          `json:"zalopay_id"`
	AccountID     int64          `json:"account_id"`
	StatementID   int64          `json:"statement_id"`
	StatementDate time.Time      `json:"statement_date"`
	EventData     map[string]any `json:"event_data"`
}
