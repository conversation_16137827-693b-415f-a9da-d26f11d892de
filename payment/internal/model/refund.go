package model

import (
	"math"
	"reflect"
	"slices"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

type CreateRefundOrderRequest struct {
	Amount       int64
	ZaloPayID    int64
	AppTransID   string
	Description  string
	RefZPTransID int64
	PartnerCode  partner.PartnerCode
}

type CreateRefundOrderResponse struct {
	AppID        int32
	Amount       int64
	AppTransID   string
	ZpTransToken string
	OrderNo      int64
	OrderStatus  int32
	Description  string
	DataChecksum string
}

type RefundOrder struct {
	ID                 int64
	ZPTransID          int64
	RefundID           int64
	Amount             int64
	AppTransID         string
	AppID              int32
	PaymentDescription string
	RefundType         RefundType
	Status             RefundStatus
	ProcessType        RefundProcessType
	DeadlineAt         time.Time
	BankStatus         string
	ErrorCode          string
	ErrorMessage       string
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

type RefundOrders []*RefundOrder

func (r RefundOrders) SumTotalRefundAmount() int64 {
	var total int64
	for _, refund := range r {
		if refund.Status != RefundStatusSuccess {
			continue
		}
		total += refund.Amount
	}
	return total
}

func (r RefundOrders) SumNetRefundAmount() int64 {
	var total int64
	for _, refund := range r {
		if refund.Status != RefundStatusSuccess ||
			refund.ProcessType != RefundProcessTypeSettlement {
			continue
		}
		total += refund.Amount
	}
	return total
}

func (r RefundOrders) SumTotalExpiredAmount() int64 {
	var total int64
	for _, refund := range r {
		if refund.Status != RefundStatusSuccess {
			continue
		}
		if refund.ProcessType != RefundProcessTypeSettlement {
			continue
		}
		total += refund.Amount
	}
	return total
}

func (r RefundOrders) GetIDs() []int64 {
	var ids []int64
	for _, refund := range r {
		ids = append(ids, refund.ID)
	}
	return ids
}

func (r *RefundOrder) IsComplete() bool {
	statuses := []RefundStatus{RefundStatusSuccess, RefundStatusFailed}
	return slices.Contains(statuses, r.Status)
}

func (r *RefundOrder) IsSuccess() bool {
	return r.Status == RefundStatusSuccess
}

type RefundType string

const (
	RefundTypeAuto   RefundType = "AUTO"
	RefundTypeManual RefundType = "MANUAL"
)

type RefundStatus string

const (
	RefundStatusUnknown    RefundStatus = "UNKNOWN"
	RefundStatusInit       RefundStatus = "INIT"
	RefundStatusSuccess    RefundStatus = "SUCCESS"
	RefundStatusFailed     RefundStatus = "FAILED"
	RefundStatusPending    RefundStatus = "PENDING"
	RefundStatusProcessing RefundStatus = "PROCESSING"
)

func (s RefundStatus) IsFinal() bool {
	statuses := []RefundStatus{RefundStatusSuccess, RefundStatusFailed}
	return slices.Contains(statuses, s)
}

type RefundSettleStatus string

const (
	RefundSettleStatusInit       RefundSettleStatus = "INIT"
	RefundSettleStatusPending    RefundSettleStatus = "PENDING"
	RefundSettleStatusProcessing RefundSettleStatus = "PROCESSING"
	RefundSettleStatusSettled    RefundSettleStatus = "SETTLED"
	RefundSettleStatusCompleted  RefundSettleStatus = "COMPLETED"
)

type RefundProcessType string

const (
	RefundProcessTypeManual     RefundProcessType = "MANUAL"
	RefundProcessTypeFundback   RefundProcessType = "FUNDBACK"
	RefundProcessTypeRepayment  RefundProcessType = "REPAYMENT"
	RefundProcessTypeSettlement RefundProcessType = "SETTLEMENT"
)

func FromInstallmentToSettleStatus(status InstallmentStatus) SettleStatus {
	switch status {
	case InstallmentStatusOpen:
		return SettleStatusUnsettled
	case InstallmentStatusClosed:
		return SettleStatusSettled
	default:
		return SettleStatusUnsettled
	}
}

type RefundSettle struct {
	ID                int64
	Status            RefundSettleStatus
	ZPTransID         int64
	EventVersion      int32
	NetRefundAmount   int64
	TotalRefundAmount int64
	SettlementAmount  int64
	UserTopupAmount   int64
	UserPaybackAmount int64
	ExtraData         RefundSettleExtra
}

type RefundSettleRecon struct {
	NetRefundAmount   int64
	TotalRefundAmount int64
	SettlementAmount  int64
}

type RefundSettleExtra struct {
	EventSnapshot   *RefundSettleSnapshot  `json:"event_snapshot"`
	OriginTransInfo *RefundOriginTransInfo `json:"origin_trans_info"`
}

func (r *RefundSettleExtra) IsEmpty() bool {
	return reflect.DeepEqual(r, RefundSettle{})
}

func (r *RefundSettleExtra) IsSnapshotEmpty() bool {
	return r.EventSnapshot == nil
}

type RefundSettleSnapshot struct {
	Status            RefundSettleStatus `json:"status"`
	EventVersion      int32              `json:"event_version"`
	UserTopupAmount   int64              `json:"user_topup_amount"`
	NetRefundAmount   int64              `json:"net_refund_amount"`
	TotalRefundAmount int64              `json:"total_refund_amount"`
}

type RefundOriginTransInfo struct {
	ZalopayID   int64
	AccountID   int64
	PartnerCode string
}

func NewRefundSettle(zpTransID int64, amounts *RefundSettleRecon) *RefundSettle {
	return &RefundSettle{
		ZPTransID:         zpTransID,
		NetRefundAmount:   amounts.NetRefundAmount,
		TotalRefundAmount: amounts.TotalRefundAmount,
		SettlementAmount:  amounts.SettlementAmount,
	}
}

func (r *RefundSettle) HasSnapshotChanged() bool {
	if r.Status == RefundSettleStatusInit &&
		(r.ExtraData.IsEmpty() || r.ExtraData.IsSnapshotEmpty()) {
		return true
	}
	return r.Status != r.ExtraData.EventSnapshot.Status ||
		r.EventVersion != r.ExtraData.EventSnapshot.EventVersion ||
		r.UserTopupAmount != r.ExtraData.EventSnapshot.UserTopupAmount ||
		r.NetRefundAmount != r.ExtraData.EventSnapshot.NetRefundAmount ||
		r.TotalRefundAmount != r.ExtraData.EventSnapshot.TotalRefundAmount
}

func (r *RefundSettle) RefreshSnapshotData() int32 {
	originVersion := r.EventVersion
	r.EventVersion = r.EventVersion + 1
	r.ExtraData.EventSnapshot = &RefundSettleSnapshot{
		Status:            r.Status,
		EventVersion:      r.EventVersion,
		UserTopupAmount:   r.UserTopupAmount,
		NetRefundAmount:   r.NetRefundAmount,
		TotalRefundAmount: r.TotalRefundAmount,
	}
	return originVersion
}

func (r *RefundSettle) SyncReconAmounts(params *RefundSettleRecon) *RefundSettle {
	r.NetRefundAmount = params.NetRefundAmount
	r.TotalRefundAmount = params.TotalRefundAmount
	r.SettlementAmount = params.SettlementAmount
	return r
}

func (r *RefundSettle) StorePurchaseSnapshot(purchase *PurchaseOrder) *RefundSettle {
	if r.ExtraData.OriginTransInfo == nil {
		r.ExtraData.OriginTransInfo = &RefundOriginTransInfo{}
	}
	r.ExtraData.OriginTransInfo.AccountID = purchase.AccountInfo.ID
	r.ExtraData.OriginTransInfo.ZalopayID = purchase.AccountInfo.ZalopayID
	r.ExtraData.OriginTransInfo.PartnerCode = purchase.AccountInfo.PartnerCode
	return r
}

func (r *RefundSettle) SetSettlementAmount(amount int64) *RefundSettle {
	r.SettlementAmount = amount
	return r
}

func (r *RefundSettle) HasReconAmountsChanged(params *RefundSettleRecon) bool {
	return r.NetRefundAmount != params.NetRefundAmount ||
		r.TotalRefundAmount != params.TotalRefundAmount ||
		r.SettlementAmount != params.SettlementAmount
}

func (r *RefundSettle) EvaluateStatusByPayback(totalPayback int64) RefundSettleStatus {
	if totalPayback >= r.GetPaybackObligationAmount() {
		return RefundSettleStatusCompleted
	}
	return r.Status
}

func (r *RefundSettle) CanStartSettlement() bool {
	return r.Status == RefundSettleStatusPending
}

func (r *RefundSettle) IsUserTopupRequired() bool {
	statuses := []RefundSettleStatus{
		RefundSettleStatusInit,
		RefundSettleStatusPending,
	}
	return slices.Contains(statuses, r.Status) && r.GetTotalCollectAmount() < r.SettlementAmount
}

func (r *RefundSettle) ShouldTriggerSettlement() bool {
	statuses := []RefundSettleStatus{
		RefundSettleStatusInit,
		RefundSettleStatusPending,
	}
	return slices.Contains(statuses, r.Status) && r.GetTotalCollectAmount() >= r.SettlementAmount
}

func (r *RefundSettle) HasSufficientFunds() bool {
	return r.GetTotalCollectAmount() >= r.SettlementAmount
}

func (r *RefundSettle) GetTotalCollectAmount() int64 {
	return r.NetRefundAmount + r.UserTopupAmount
}

func (r *RefundSettle) GetTopupObligationAmount() int64 {
	if r.GetTotalCollectAmount() >= r.SettlementAmount {
		return 0
	}
	return r.SettlementAmount - r.GetTotalCollectAmount()
}

func (r *RefundSettle) GetPaybackObligationAmount() int64 {
	value := r.GetTotalCollectAmount() + r.UserPaybackAmount - r.SettlementAmount
	return int64(math.Max(0, float64(value)))
}

func (r *RefundSettle) ValidateTopupOrderAmount(orderAmount int64) error {
	if orderAmount < 0 {
		return errors.New("request amount cannot be negative")
	}
	if orderAmount != r.GetTopupObligationAmount() {
		return errors.Wrapf(ErrRefundTopupOrderAmountMismatch, "orderAmount: %d, obligationAmount: %d", orderAmount, r.GetTopupObligationAmount())
	}
	return nil
}

// RefundSettleReconParams represents parameters for refund settlement reconciliation
type RefundSettleReconParams struct {
	ZPTransID int64
}

// SubmitRefundOrderResponse represents the response from submitting a refund order
type SubmitRefundOrderResponse struct {
	OrderID       int64             `json:"order_id"`
	AppTransID    string            `json:"app_trans_id"`
	Status        OrderStatus       `json:"status"`
	PaymentStatus PaymentStatus     `json:"payment_status"`
	ZPTransID     int64             `json:"zp_trans_id"`
	ExtraData     map[string]string `json:"extra_data,omitempty"`
	CreatedAt     time.Time         `json:"created_at"`
}

type RefundSettleMode int

const (
	RefundSettleModeStandard RefundSettleMode = iota + 1
	RefundSettleModeExpired
)
