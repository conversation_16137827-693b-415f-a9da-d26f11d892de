package model

import (
	"time"

	"github.com/pkg/errors"
)

type InstallmentDetail struct {
	PaymentID            int64
	LoanId               string
	SystemReportingDate  time.Time
	OutstandingPrincipal int64
	InterestRate         float64
	FirstSchedule        *InstRepaySchedule
	EarlyDischarge       *EarlyDischargeData
}

type InstRepaySchedule struct {
	PeriodNo          string
	PeriodStart       time.Time
	PeriodEnd         time.Time
	OutstandingAmount int64
	EmiAmount         int64
}

type EarlyDischargeData struct {
	EarlyDischargeFee    int64
	OutstandingPrincipal int64
	InterestAmount       int64
	PenaltyInterest      int64
	TotalDischargeAmount int64
}

type InstallmentStatus string

const (
	InstallmentStatusUnknown    InstallmentStatus = "unknown"
	InstallmentStatusOpen       InstallmentStatus = "open"
	InstallmentStatusClosed     InstallmentStatus = "closed"
	InstallmentStatusInCreation InstallmentStatus = "in_creation"
)

func (i InstallmentStatus) IsValid() bool {
	return i != InstallmentStatusUnknown
}

type DischargeStatus string

const (
	DischargeStatusUnknown  DischargeStatus = "unknown"
	DischargeStatusCharged  DischargeStatus = "charged"
	DischargeStatusEligible DischargeStatus = "eligible"
)

type GetInstallmentStatusResponse struct {
	Status InstallmentStatus
	LoanID string
}

type EarlyDischarge struct {
	IsSettled   bool
	IsEligible  bool
	TotalAmount int64
	InSession   bool // Renamed from SessionAvailable for better clarity
}

func (e *EarlyDischarge) Validate() error {
	if e.TotalAmount <= 0 && e.IsEligible {
		return errors.Errorf("invalid total amount when eligible: %d", e.TotalAmount)
	}
	if !e.IsSettled && !e.IsEligible {
		return errors.Errorf("invalid status, isSettled: %v, isEligible: %v", e.IsSettled, e.IsEligible)
	}
	return nil
}

// SettleStatus is the status of a loan item
type SettleStatus string

const (
	SettleStatusSettled   SettleStatus = "SETTLED"
	SettleStatusUnsettled SettleStatus = "UNSETTLED"
)
