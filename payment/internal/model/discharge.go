package model

import (
	"time"

	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

type EarlyDischargeLog struct {
	ID           int64                `json:"id"`
	AccountInfo  Account              `json:"account_info"`
	BankRoute    BankRoute            `json:"bank_route"`
	Order        Order                `json:"order"`
	ZpTransID    int64                `json:"zp_trans_id"`
	TransType    TransType            `json:"trans_type"`
	Status       PaymentStatus        `json:"status"`
	PartnerData  PartnerRepaymentData `json:"partner_data"`
	PartnerReqID string               `json:"partner_req_id"`
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`
}

type EarlyDischargeRequest struct {
	RequestID          string `json:"request_id"`
	PaymentID          int64  `json:"payment_id"`
	LoanID             string `json:"loan_id"`
	Amount             int64  `json:"amount"`
	PaymentDescription string `json:"payment_description"`
	CorpAccountNumber  string `json:"corp_account_number"`
	CorpAccountName    string `json:"corp_account_name"`
	PartnerRemark      string `json:"partner_remark"`
}

type EarlyDischargeResult struct {
	MonitorTime
	CIMBError
	PartnerTransID    string            `json:"partner_trans_id"`
	TransactionStatus CIMBPaymentStatus `json:"transaction_status"`
	NeedReconcilation bool              `json:"need_reconciliation"`

	// ErrorCode is the code make the transaction failed
	// It is not the same as CIMBError.ErrorCode (which is the error from rpc request)
	ErrorCode string `json:"error_code"`
}

type EarlyDischargeStatusWorkflowRequest struct {
	PaymentID int64 `json:"payment_id"`
	ZalopayID int64 `json:"zalopay_id"`
}

type EarlyDischargeStatusWorkflowResponse struct {
	DischargeStatus        string `json:"discharge_status"`
	ErrorDescription       string `json:"error_description"`
	PartnerDischargeStatus string `json:"partner_discharge_status"`
}

func NewEarlyDischargeLog(order *RefundSettleOrder, bankRoute *BankRoute, account *Account) *EarlyDischargeLog {
	return &EarlyDischargeLog{
		AccountInfo: *account,
		BankRoute:   *bankRoute,
		Order: Order{
			ID:          order.ID,
			AppTransID:  order.AppTransID,
			AppID:       order.AppID,
			Amount:      order.Amount,
			Description: order.Description,
			TransID:     order.ZPTransID,
		},
		ZpTransID:    order.ZPTransID,
		TransType:    TransTypeEarlyDischarge,
		Status:       PaymentStatusInit,
		PartnerData:  PartnerRepaymentData{},
		PartnerReqID: idgen.GenPaymentTransID(),
	}
}

func (e *EarlyDischargeLog) WithRefundSettleOrder(order *RefundSettleOrder) *EarlyDischargeLog {
	e.Order.ID = order.ID
	e.Order.AppID = order.AppID
	e.Order.Amount = order.Amount
	e.Order.TransID = order.ZPTransID
	e.Order.AppTransID = order.AppTransID
	e.Order.Description = order.Description
	return e
}
