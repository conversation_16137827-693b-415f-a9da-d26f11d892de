package model

import (
	"fmt"
	"reflect"
	"slices"
	"strings"
	"time"
)

type Order struct {
	ID          int64
	TransID     int64
	AppID       int32
	AppTransID  string
	Amount      int64
	Description string
}

func (o *Order) IsEmpty() bool {
	return reflect.DeepEqual(o, Order{})
}

type PaymentStatus string

const (
	// Payment status
	PaymentStatusInit       PaymentStatus = "INIT"
	PaymentStatusFailed     PaymentStatus = "FAILED"
	PaymentStatusPending    PaymentStatus = "PENDING"
	PaymentStatusProcessing PaymentStatus = "PROCESSING"
	PaymentStatusSucceeded  PaymentStatus = "SUCCEEDED"
)

func (s *PaymentStatus) String() string {
	return string(*s)
}

func (s *PaymentStatus) IsInitStatus() bool {
	return *s == PaymentStatusInit
}

func (s *PaymentStatus) IsFailedStatus() bool {
	return *s == PaymentStatusFailed
}

func (s *PaymentStatus) IsSucceededStatus() bool {
	return *s == PaymentStatusSucceeded
}

func (s *PaymentStatus) IsFinalStatus() bool {
	return slices.Contains([]PaymentStatus{
		PaymentStatusFailed,
		PaymentStatusSucceeded,
	}, *s)
}

func (s *PaymentStatus) ToTransStatus() TransStatus {
	switch *s {
	case PaymentStatusSucceeded:
		return TransStatusSucceeded
	case PaymentStatusProcessing:
		return TransStatusProcessing
	case PaymentStatusPending:
		return TransStatusPending
	case PaymentStatusFailed:
		return TransStatusFailed
	default:
		return TransStatusPending
	}
}

type PurchaseOrder struct {
	ID                     int64                  `json:"id"`
	PERequestID            string                 `json:"pe_request_id"`
	AccountInfo            Account                `json:"account_info"`
	BankRoute              BankRoute              `json:"bank_route"`
	Order                  Order                  `json:"order"`
	ZpTransID              string                 `json:"zp_trans_id"`
	PaymentNo              string                 `json:"payment_no"`
	TransType              TransType              `json:"trans_type"`
	Status                 PaymentStatus          `json:"status"`
	ErrorCode              string                 `json:"error_sub_code"`
	ErrorMessage           string                 `json:"error_message"`
	PartnerData            PurchaseOrderData      `json:"partner_data"`
	InstallmentInstruction InstallmentInstruction `json:"installment_instruction"`
	FSChargeInfo           string                 `json:"fs_charge_info"`
	DeviceID               string                 `json:"device_id"`
	UserIP                 string                 `json:"user_ip"`
	CreatedAt              time.Time              `json:"created_at"`
	UpdatedAt              time.Time              `json:"updated_at"`
}

// SetAccount set account for purchase order
func (po *PurchaseOrder) SetAccount(account Account) {
	po.AccountInfo = account
}

// SetStatus set status for purchase order
func (po *PurchaseOrder) SetStatus(status PaymentStatus) {
	po.Status = status
}

func (po *PurchaseOrder) SetError(errCode string, description string) {
	po.Status = PaymentStatusFailed
	po.ErrorCode = errCode
	po.ErrorMessage = description
}

// SetBankRoute set bank route for purchase order
func (po *PurchaseOrder) SetBankRoute(bankRoute BankRoute) {
	po.BankRoute = bankRoute
}

func (po *PurchaseOrder) ToTransaction() *Transaction {
	accountInfo := po.AccountInfo
	partnerData := po.PartnerData
	partnerTransID := partnerData.BankTransactionSequenceID
	return &Transaction{
		TransID:        po.ID,
		ZPTransID:      po.ZpTransID,
		PartnerTransID: partnerTransID,
		AccountInfo:    accountInfo,
		Amount:         po.Order.Amount,
		Type:           po.TransType,
		Remark:         po.Order.Description,
		Status:         po.Status.ToTransStatus(),
		CreatedAt:      po.CreatedAt,
		UpdatedAt:      po.UpdatedAt,
	}
}

//
//type PartnerData interface {
//}

type PurchaseOrderData struct {
	//CorpAccountNumber  string `json:"corp_account_number"` //for this data specific, we can put to adapter connect with partner
	//CorpAccountName    string `json:"corp_account_name"`
	CustomerRemark string `json:"customer_remark"`
	//PaymentDescription string `json:"payment_description"`
	DrawdownPurpose string `json:"drawdown_purpose"`

	//UserBankAccountNumber string `json:"user_bank_account_number"`
	//UserBankAccountName   string `json:"user_bank_account_name"`
	//PartnerTransID string `json:"partner_trans_id"`
	//ErrorCode string `json:"error_code"`

	Status                    CIMBPaymentStatus `json:"status"`
	BankTransactionSequenceID string            `json:"bank_transaction_sequence_id"`
	PurchaseResults           PurchaseResult    `json:"purchase_results"`
	//InstallmentInstruction InstallmentInstruction `json:"installment_instruction"`
	InitPurchasingResult      InitPurchasingResult      `json:"init_purchasing_result"`
	ConfirmPurchaseResult     ConfirmPurchaseResult     `json:"confirm_purchase_result"`
	ConfirmDisbursementResult ConfirmDisbursementResult `json:"confirm_disbursement_result"`
	PaymentAuth               PaymentAuth               `json:"payment_auth"`
}

type InitPurchasingResult struct {
	MonitorTime
	CIMBError
	TransactionStatus    CIMBPaymentStatus   `json:"transaction_status"`
	CIMBTransID          string              `json:"cimb_trans_id"`
	RequiredStepupMethod AdvanceStepUpMethod `json:"required_stepup_method"`
}

type ConfirmPurchaseResult struct {
	MonitorTime
	CIMBError
	TransactionStatus       CIMBPaymentStatus `json:"transaction_status"`
	AvailableOdLimit        int64             `json:"available_od_limit"`
	RepaymentAccountBalance int64             `json:"repayment_account_balance"`
	//"transactionStatus": "COMPLETED",
	//"cimbTransactionSequenceId": "cim_seq_00001",
	//"partnerReferenceRequestId": "partner_ref_req_123456",
	//"availableOdLimit": 4000000,
	//"repaymentAccountBalance": 30000
}

type ConfirmDisbursementResult struct {
	MonitorTime
	CIMBError
	TransactionStatus CIMBPaymentStatus `json:"transaction_status"`
}

type MonitorTime struct {
	SendRequestTime     time.Time `json:"send_request_time"`
	ReceiveResponseTime time.Time `json:"receive_response_time"`
}

func (m *MonitorTime) Start() {
	m.SendRequestTime = time.Now()
}

func (m *MonitorTime) End() {
	m.ReceiveResponseTime = time.Now()
}

type CIMBError struct {
	ErrorCode   string
	Description string
}

func (r CIMBError) Error() string {
	if r.Description != "" {
		return fmt.Sprintf("%s:%s", r.ErrorCode, r.Description)
	}
	return r.ErrorCode
}

func (r *CIMBError) IsError() bool {
	return r.ErrorCode != ""
}

func (r *CIMBError) SetErrorCodeIfNotExist(errCode string) *CIMBError {
	if r.ErrorCode == "" {
		r.ErrorCode = errCode
	}
	return r
}

func (r *CIMBError) AppendErrorDescription(description string) *CIMBError {
	if r.Description == "" {
		r.Description = description
		return r
	}
	if description == "" {
		return r
	}
	r.Description = strings.Join([]string{r.Description, description}, " -> ")
	return r
}

type PurchaseResult struct {
	SendRequestDatetime     string
	ReceiveResponseDatetime string
	BankTransStatus         string
	BankTransID             string
	ErrorCode               string
	Description             string
	RequiredStepupMethod    AdvanceStepUpMethod
	AvailableOdLimit        int64
	RepaymentAccountBalance int64
}

func (r *PurchaseResult) SetErrorCode(errCode string, description string) {
	r.ErrorCode = errCode
	r.Description = description
}

type ChargeInfo struct {
	PartnerCode string `json:"partner_code"`
	//AccountID    string `json:"account_id"`
	Tenor        int32
	InterestRate float64
}

// FSChargeInfo is the charge info from Payment Engine
type FSChargeInfo struct {
	PlanKey        string  `json:"plan_key"`
	EmiAmount      int64   `json:"emi_amount"`
	PlanTenure     int32   `json:"plan_tenure"`
	PartnerCode    string  `json:"partner_code"`
	InterestRate   float64 `json:"interest_rate"`
	InterestAmount int64   `json:"interest_amount"`
	TotalDueAmount int64   `json:"total_due_amount"`
	TotalFeeAmount int64   `json:"total_fee_amount"`
}

type PaymentAuth struct {
	AuthSessionID     string            `json:"auth_session_id"`
	AuthSessionStatus AuthSessionStatus `json:"auth_session_status"`
}

type AuthSessionStatus string

func (a AuthSessionStatus) String() string {
	return string(a)
}

func (a AuthSessionStatus) IsSuccess() bool {
	return a == AuthSessionStatusSuccess
}

func (a AuthSessionStatus) IsFailed() bool {
	return a == AuthSessionStatusFailed
}

func (a AuthSessionStatus) IsCompleted() bool {
	return a == AuthSessionStatusSuccess || a == AuthSessionStatusFailed
}

const (
	AuthSessionStatusEmpty      AuthSessionStatus = ""
	AuthSessionStatusProcessing AuthSessionStatus = "PROCESSING"
	AuthSessionStatusFailed     AuthSessionStatus = "FAILED"
	AuthSessionStatusSuccess    AuthSessionStatus = "SUCCESS"
	//AuthSessionStatusTriggerFailed AuthSessionStatus = "TRIGGER_FAILED"
)

func FromStringToAuthSessionStatus(value string) AuthSessionStatus {
	switch value {
	case AuthSessionStatusProcessing.String():
		return AuthSessionStatusProcessing
	case AuthSessionStatusFailed.String():
		return AuthSessionStatusFailed
	case AuthSessionStatusSuccess.String():
		return AuthSessionStatusSuccess
	//case AuthSessionStatusTriggerFailed.String():
	//	return AuthSessionStatusTriggerFailed
	default:
		return AuthSessionStatusEmpty
	}
}
