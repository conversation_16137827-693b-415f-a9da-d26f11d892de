package model

import (
	"slices"
)

type RefundOrderExtra struct {
	RefZPTransID   int64  `json:"ref_zp_trans_id"`
	ExtOrderNo     int64  `json:"ext_order_no,omitempty"`
	ExtChecksum    string `json:"ext_checksum,omitempty"`
	ExtErrorReason string `json:"ext_error_reason,omitempty"`
}

func (r *RefundOrderExtra) GetRefZPTransID() int64 {
	if r == nil {
		return 0
	}
	return r.RefZPTransID
}

type RefundSettleOrder struct {
	ID             int64
	ZalopayID      int64
	AppTransID     string
	AppID          int32
	Amount         int64
	ZPTransToken   string
	Type           OrderType
	Status         OrderStatus
	Stage          OrderStage
	Description    string
	RefundID       int64
	RefundSettleID int64
	ZPTransID      int64
	PartnerCode    string
	ExtraData      RefundOrderExtra
}

type RefundSettleOrders []*RefundSettleOrder

func NewRefundSettleOrder(zalopayID int64, partnerCode string, refZpTransID, settleAmount int64) *RefundSettleOrder {
	return &RefundSettleOrder{
		ZalopayID:   zalopayID,
		PartnerCode: partnerCode,
		Amount:      settleAmount,
		Status:      OrderStatusInit,
		Type:        OrderTypeRefund,
		Stage:       OrderStageSettle,
		ExtraData:   RefundOrderExtra{RefZPTransID: refZpTransID},
	}
}

func (r *RefundSettleOrder) IsFinal() bool {
	return r.Status == OrderStatusSucceeded ||
		r.Status == OrderStatusFailed ||
		r.Status == OrderStatusCancelled
}

func (r *RefundSettleOrder) IsSuccess() bool {
	if r == nil {
		return false
	}
	return r.Status == OrderStatusSucceeded
}

func (r *RefundSettleOrder) IsFailedOrCancelled() bool {
	if r == nil {
		return false
	}
	return r.Status == OrderStatusFailed || r.Status == OrderStatusCancelled
}

func (ref *RefundSettleOrder) AttachOrderNo(orderNo int64) {
	if ref == nil {
		return
	}
	ref.ExtraData.ExtOrderNo = orderNo
}

func (ref *RefundSettleOrder) CancelOrder() {
	if ref == nil {
		return
	}
	ref.Status = OrderStatusCancelled
}

func (r *RefundSettleOrder) HandleOrderUpdate(zpTransID int64, orderStatus OrderStatus, reasonStatus string) {
	r.Status = orderStatus
	r.ZPTransID = zpTransID
	if orderStatus == OrderStatusFailed {
		r.ExtraData.ExtErrorReason = reasonStatus
	}
}

func (r *RefundSettleOrder) GetRefundSettleMode() RefundSettleMode {
	if r == nil || r.Type != OrderTypeRefund {
		return 0
	}
	switch {
	case r.RefundID != 0:
		return RefundSettleModeExpired
	case r.RefundSettleID != 0:
		return RefundSettleModeStandard
	default:
		return 0
	}
}

func (r *RefundSettleOrder) FulfillSettleOrder(order *CreateRefundOrderResponse) *RefundSettleOrder {
	r.AppID = order.AppID
	r.Amount = order.Amount
	r.AppTransID = order.AppTransID
	r.ZPTransToken = order.ZpTransToken
	r.Description = order.Description
	r.ExtraData.ExtOrderNo = order.OrderNo
	r.ExtraData.ExtChecksum = order.DataChecksum
	return r
}

func (r *RefundSettleOrder) WithStatus(orderStatus OrderStatus) *RefundSettleOrder {
	r.Status = orderStatus
	return r
}

func (r *RefundSettleOrder) WithSettleID(settleID int64) *RefundSettleOrder {
	r.RefundSettleID = settleID
	return r
}

func (r *RefundSettleOrder) WithRefundID(refundID int64) *RefundSettleOrder {
	r.RefundID = refundID
	return r
}

func (r *RefundSettleOrder) GetRefZPTransID() int64 {
	if r == nil {
		return 0
	}
	return r.ExtraData.RefZPTransID
}

func (r *RefundSettleOrder) CloneForResubmission() *RefundSettleOrder {
	if r == nil {
		return nil
	}
	clone := NewRefundSettleOrder(
		r.ZalopayID,
		r.PartnerCode,
		r.GetRefZPTransID(),
		r.Amount,
	)

	clone.AppID = r.AppID
	clone.RefundID = r.RefundID
	clone.RefundSettleID = r.RefundSettleID

	return clone
}

func (r RefundSettleOrders) SortByIDAsc() RefundSettleOrders {
	if len(r) <= 1 {
		return r
	}

	sortedOrders := make(RefundSettleOrders, len(r))
	copy(sortedOrders, r)

	slices.SortFunc(sortedOrders, func(a, b *RefundSettleOrder) int {
		return int(a.ID - b.ID)
	})

	return sortedOrders
}

func (r RefundSettleOrders) MustGetLastOrder() *RefundSettleOrder {
	if len(r) == 0 {
		panic("no order found")
	}
	return r[len(r)-1]
}

func (r RefundSettleOrders) HasLastOrderFailed() bool {
	if len(r) == 0 {
		return false
	}
	lastOrder := r[len(r)-1]
	return lastOrder.Status == OrderStatusFailed || lastOrder.Status == OrderStatusCancelled
}

func (r RefundSettleOrders) EligibleToRetrySubmit() bool {
	statusNotAllowed := []OrderStatus{
		OrderStatusPending,
		OrderStatusSucceeded,
		OrderStatusProcessing,
	}
	failedTime := 0
	statusValid := true
	for _, v := range r {
		if slices.Contains(statusNotAllowed, v.Status) {
			statusValid = false
		}
		if v.Status == OrderStatusFailed {
			failedTime++
		}
	}
	return statusValid && failedTime < 3
}

type RefundTopupOrder struct {
	ID             int64
	ZalopayID      int64
	AppTransID     string
	AppID          int32
	Amount         int64
	ZPTransToken   string
	Type           OrderType
	Status         OrderStatus
	Stage          OrderStage
	Description    string
	RefundSettleID int64
	ZPTransID      int64
	PartnerCode    string
	ExtraData      RefundOrderExtra
}

func NewRefundTopupOrder(zalopayID, refZpTransID, topupAmount int64) *RefundTopupOrder {
	return &RefundTopupOrder{
		ZalopayID: zalopayID,
		Amount:    topupAmount,
		Status:    OrderStatusInit,
		Type:      OrderTypeRefund,
		Stage:     OrderStageTopup,
		ExtraData: RefundOrderExtra{RefZPTransID: refZpTransID},
	}
}

func (r *RefundTopupOrder) EnrichTopupOrder(settle *RefundSettle, payment *PurchaseOrder, description string) *RefundTopupOrder {
	r.RefundSettleID = settle.ID
	r.ZalopayID = payment.AccountInfo.ZalopayID
	r.PartnerCode = payment.AccountInfo.PartnerCode
	r.Description = description
	return r
}

func (r *RefundTopupOrder) FulfillTopupOrder(tdOrder *CreateRefundOrderResponse) *RefundTopupOrder {
	r.AppID = tdOrder.AppID
	r.Amount = tdOrder.Amount
	r.AppTransID = tdOrder.AppTransID
	r.ZPTransToken = tdOrder.ZpTransToken
	r.Description = tdOrder.Description
	r.ExtraData.ExtOrderNo = tdOrder.OrderNo
	r.ExtraData.ExtChecksum = tdOrder.DataChecksum
	return r
}

func (r *RefundTopupOrder) GetRefZPTransID() int64 {
	if r == nil {
		return 0
	}
	return r.ExtraData.RefZPTransID
}

func (r *RefundTopupOrder) HandleOrderUpdate(zpTransID int64, orderStatus OrderStatus, reasonStatus string) {
	r.Status = orderStatus
	r.ZPTransID = zpTransID
	if orderStatus == OrderStatusFailed {
		r.ExtraData.ExtErrorReason = reasonStatus
	}
}

func (r *RefundTopupOrder) IsFinal() bool {
	return r.Status == OrderStatusSucceeded ||
		r.Status == OrderStatusFailed ||
		r.Status == OrderStatusCancelled
}

func (r *RefundTopupOrder) IsSuccess() bool {
	return r.Status == OrderStatusSucceeded
}

type RefundTopupOrders []*RefundTopupOrder

func (r RefundTopupOrders) HasProcessingOrder() bool {
	for _, order := range r {
		if order.Status == OrderStatusPending ||
			order.Status == OrderStatusProcessing {
			return true
		}
	}
	return false
}

func (r RefundTopupOrders) GetTotalAmount() int64 {
	var total int64
	for _, order := range r {
		total += order.Amount
	}
	return total
}

func (r RefundTopupOrders) GetSuccessAmount() int64 {
	var total int64
	for _, order := range r {
		if order.Status == OrderStatusSucceeded {
			total += order.Amount
		}
	}
	return total
}

type RefundFundbackOrder struct {
	ID             int64
	ZalopayID      int64
	AppTransID     string
	AppID          int32
	Amount         int64
	ZPTransToken   string
	Type           OrderType
	Status         OrderStatus
	Stage          OrderStage
	Description    string
	RefundSettleID int64
	ZPTransID      int64
	PartnerCode    string
	ExtraData      RefundOrderExtra
}

func NewRefundFundbackOrder(zalopayID, refZpTransID, fundbackAmount int64) *RefundFundbackOrder {
	return &RefundFundbackOrder{
		ZalopayID: zalopayID,
		Amount:    fundbackAmount,
		Status:    OrderStatusInit,
		Type:      OrderTypeRefund,
		Stage:     OrderStageFundback,
		ExtraData: RefundOrderExtra{RefZPTransID: refZpTransID},
	}
}

func (r *RefundFundbackOrder) EnrichFundbackOrder(settle *RefundSettle, payment *PurchaseOrder) *RefundFundbackOrder {
	r.RefundSettleID = settle.ID
	r.ZalopayID = payment.AccountInfo.ZalopayID
	r.PartnerCode = payment.AccountInfo.PartnerCode
	return r
}

func (r *RefundFundbackOrder) FulfillFundbackOrder(order *CreateRefundOrderResponse) *RefundFundbackOrder {
	r.AppID = order.AppID
	r.Amount = order.Amount
	r.AppTransID = order.AppTransID
	r.ZPTransToken = order.ZpTransToken
	r.Description = order.Description
	r.ExtraData.ExtOrderNo = order.OrderNo
	r.ExtraData.ExtChecksum = order.DataChecksum
	return r
}

func (r *RefundFundbackOrder) GetRefZPTransID() int64 {
	if r == nil {
		return 0
	}
	return r.ExtraData.RefZPTransID
}

func (r *RefundFundbackOrder) HandleOrderUpdate(zpTransID int64, orderStatus OrderStatus, reasonStatus string) {
	r.Status = orderStatus
	r.ZPTransID = zpTransID
	if orderStatus == OrderStatusFailed {
		r.ExtraData.ExtErrorReason = reasonStatus
	}
}

func (r *RefundFundbackOrder) IsFinal() bool {
	return r.Status == OrderStatusSucceeded ||
		r.Status == OrderStatusFailed ||
		r.Status == OrderStatusCancelled
}

func (r *RefundFundbackOrder) IsSuccess() bool {
	return r.Status == OrderStatusSucceeded
}

type RefundFundbackOrders []*RefundFundbackOrder

func (r *RefundFundbackOrders) HasProcessingOrder() bool {
	statuses := []OrderStatus{
		OrderStatusInit,
		OrderStatusPending,
		OrderStatusProcessing,
	}
	for _, v := range *r {
		if slices.Contains(statuses, v.Status) {
			return true
		}
	}
	return false
}

func (r *RefundFundbackOrders) GetTotalSuccessAmount() int64 {
	var total int64
	for _, order := range *r {
		if order.Status == OrderStatusSucceeded {
			total += order.Amount
		}
	}
	return total
}
