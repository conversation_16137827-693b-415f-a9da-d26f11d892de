package model

import "time"

type BankRoute struct {
	ID                int64           `json:"id"`
	InternalBankCode  string          `json:"internal_bank_code"`
	PartnerBankCode   string          `json:"partner_bank_code"`
	BankAccountName   string          `json:"bank_account_name"`
	BankAccountNumber string          `json:"bank_account_number"`
	Status            BankRouteStatus `json:"status"`
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         time.Time       `json:"updated_at"`
}

type BankRouteStatus string

type TransType int32

const (
	TransTypePayment        TransType = 200
	TransTypeRePayment      TransType = 201
	TransTypeEarlyDischarge TransType = 202
)

func ListTransTypeToAnys(transTypes []TransType) []any {
	result := make([]any, 0)
	for _, t := range transTypes {
		result = append(result, t)
	}
	return result
}
