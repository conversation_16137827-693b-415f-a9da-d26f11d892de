package model

type Account struct {
	ID          int64         `json:"id"`
	ZalopayID   int64         `json:"zalopay_id"`
	PartnerCode string        `json:"partner"`
	Status      AccountStatus `json:"status"`
	//TotalLimit       int64                        `json:"total_limit"`
	AvailableBalance   int64 `json:"available_balance"`
	PartnerAccountId   string
	PartnerAccountName string
}

func (a *Account) IsActive() bool {
	if a.Status == AccountStatusActive {
		return true
	}
	return false
}

type AccountStatus string

const (
	AccountStatusActive   = AccountStatus("active")
	AccountStatusInactive = AccountStatus("inactive")
	AccountStatusLocked   = AccountStatus("locked")
)
