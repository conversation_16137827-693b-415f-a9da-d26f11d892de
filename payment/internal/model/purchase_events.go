package model

type PurchaseEventName string

const (
	PurchaseEventCreated   = PurchaseEventName("purchase_created")
	PurchaseEventSucceeded = PurchaseEventName("purchase_succeeded")
	PurchaseEventFailed    = PurchaseEventName("purchase_failed")
)

type PurchaseEvent struct {
	EventName       PurchaseEventName `json:"event_name"`
	PartnerCode     string            `json:"partner_code"`
	AccountID       int64             `json:"account_id"` //identify of account
	PurchaseOrderID int64             `json:"purchase_order_id"`
	ZalopayID       int64             `json:"zalopay_id"`
}

type UpdatePaymentType string

const (
	UpdatePaymentTypeCommission UpdatePaymentType = "COMMISSION"
)

type UpdatePaymentEvent struct {
	Type       UpdatePaymentType        `json:"type"`
	Commission *UpdatePaymentCommission `json:"commission,omitempty"`
}

type UpdatePaymentCommission struct {
	ZPTransID    int64 `json:"zp_trans_id"`
	IsCommission bool  `json:"is_commission"`
}
