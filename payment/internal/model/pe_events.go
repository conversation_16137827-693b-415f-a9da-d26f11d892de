package model

type ExchangeStatusEvent struct {
	TransID         int64             `json:"trans_id"`
	RequestID       string            `json:"request_id"`
	RefSofID        string            `json:"ref_sof_id"`
	RevertRequestID string            `json:"revert_request_id"`
	RevertRefSofID  string            `json:"revert_ref_sof_id"`
	Status          ExchangeStatus    `json:"status"`
	ExtraData       map[string]string `json:"extra_data"`
}

type ExchangeStatus struct {
	Code    string `json:"code"`
	SubCode string `json:"sub_code"`
	Message string `json:"message"`
}
