package model

import "time"

type RepayOrder struct {
	ID            int64
	ZalopayID     int64
	AppTransID    string
	AppID         int32
	Amount        int64
	ZPTransToken  string
	Status        OrderStatus
	Type          OrderType
	Description   string
	ZPTransID     int64
	StatementDate time.Time
	StatementID   int64
	PartnerCode   string
}

// IsCompleted Implement IsCompleted method for RepayOrder
func (r *RepayOrder) IsCompleted() bool {
	return r.Status == OrderStatusSucceeded || r.Status == OrderStatusFailed
}
