package model

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

type RepayOrderRequest struct {
	AppTransID  string
	Amount      int64
	AppTime     int64
	AppUser     string
	Description string
	//CallbackURL string
	EmbedData   string
	ProductCode string
	PartnerCode string
}

type RepayOrderResponse struct {
	OrderNo      int64
	ZpTransToken string
	OrderUrl     string
	OrderStatus  string
	ReasonStatus string
	CreatedTime  time.Time
	AppTransID   string
	AppID        int32
}

type RepaymentResult struct {
	MonitorTime
	CIMBError
	BankTransID       string            `json:"bank_trans_id"`
	RepaymentStatus   string            `json:"repayment_status"`
	AvailableOdLimit  int64             `json:"available_od_limit"`
	TransactionStatus CIMBPaymentStatus `json:"-"`
}

type ODRepaymentRequest struct {
	RefOrderID         int64
	PartnerReqID       string
	Amount             int64
	PaymentDescription string
	ZalopayID          int64
	CorpAccountNumber  string
	CorpAccountName    string
	BankAccountNumber  string
	BankAccountName    string
}

type RepaymentStatusPollingResponse struct {
	RepayStatus      string `json:"repay_status"`
	PartnerStatus    string `json:"partner_status"`
	ErrorDescription string `json:"error_description"`
}

type RepaymentStatusPollingRequest struct {
	OrderID   int64
	PaymentID int64
}

type RepaymentLog struct {
	ID           int64                `json:"id"`
	AccountInfo  Account              `json:"account_info"`
	BankRoute    BankRoute            `json:"bank_route"`
	Order        Order                `json:"order"`
	ZpTransID    string               `json:"zp_trans_id"`
	TransType    TransType            `json:"trans_type"`
	Status       PaymentStatus        `json:"status"`
	PartnerData  PartnerRepaymentData `json:"partner_data"`
	PartnerReqID string               `json:"partner_req_id"`
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`
}

type PartnerRepaymentData struct {
	RepaymentResult RepaymentResult `json:"repayment_result"`
}

func NewRepaymentLog(repayOrder *RepayOrder, account Account, bankRoute *BankRoute) RepaymentLog {
	var repaymentLog RepaymentLog
	repaymentLog.Status = PaymentStatusInit
	repaymentLog.Order = Order{
		ID:          repayOrder.ID,
		TransID:     repayOrder.ZPTransID,
		AppID:       repayOrder.AppID,
		AppTransID:  repayOrder.AppTransID,
		Amount:      repayOrder.Amount,
		Description: repayOrder.Description,
	}
	repaymentLog.AccountInfo = account
	repaymentLog.BankRoute = *bankRoute
	repaymentLog.ZpTransID = cast.ToString(repayOrder.ZPTransID)
	repaymentLog.TransType = TransTypeRePayment

	return repaymentLog
}

func NewRepaymentLogForSettlement(settleOrder *RefundSettleOrder, account *Account, bankRoute *BankRoute) *RepaymentLog {
	var repaymentLog RepaymentLog
	repaymentLog.Status = PaymentStatusInit
	repaymentLog.BankRoute = *bankRoute
	repaymentLog.AccountInfo = *account
	repaymentLog.TransType = TransTypeRePayment
	repaymentLog.ZpTransID = cast.ToString(settleOrder.ZPTransID)
	repaymentLog.PartnerReqID = idgen.GenPaymentTransID()
	repaymentLog.Order = Order{
		ID:          settleOrder.ID,
		TransID:     settleOrder.ZPTransID,
		AppID:       settleOrder.AppID,
		AppTransID:  settleOrder.AppTransID,
		Amount:      settleOrder.Amount,
		Description: settleOrder.Description,
	}
	return &repaymentLog
}

func (r *RepaymentLog) SetStatus(status PaymentStatus) *RepaymentLog {
	r.Status = status
	return r
}

func (r *RepaymentLog) SetSystemError(err error) {
	if err == nil {
		return
	}

	r.PartnerData.RepaymentResult.CIMBError.ErrorCode = "system_error"
	r.PartnerData.RepaymentResult.CIMBError.Description = fmt.Sprintf("%s", err)
}

func (r *RepaymentLog) SetPartnerData(data *RepaymentResult) {
	if data == nil {
		return
	}

	r.PartnerData = PartnerRepaymentData{
		RepaymentResult: *data,
	}
}

func (r *RepaymentLog) HandlePartnerRepayError(err error) *RepaymentLog {
	if err == nil {
		return r
	}

	r.Status = PaymentStatusPending

	var cimbError CIMBError
	if errors.As(err, &cimbError) {
		r.PartnerData.RepaymentResult.CIMBError = cimbError
		return r
	}

	r.PartnerData.RepaymentResult.CIMBError.ErrorCode = "system_error"
	r.PartnerData.RepaymentResult.CIMBError.Description = err.Error()
	return r
}

func (r *RepaymentLog) HandlePartnerRepayResult(data *RepaymentResult) *RepaymentLog {
	if data == nil {
		return r
	}

	r.SetPartnerData(data)

	switch data.TransactionStatus {
	case CIMBPaymentStatusComplete:
		r.Status = PaymentStatusSucceeded
	case CIMBPaymentStatusFailed,
		CIMBPaymentStatusExpired,
		CIMBPaymentStatusTransferFailed:
		r.Status = PaymentStatusFailed
	case CIMBPaymentStatusProcessing:
		r.Status = PaymentStatusProcessing
	default:
		r.Status = PaymentStatusPending
	}
	return r
}

func (r *RepaymentLog) WithRefundSettleOrder(order *RefundSettleOrder) *RepaymentLog {
	r.Order.ID = order.ID
	r.Order.AppID = order.AppID
	r.Order.Amount = order.Amount
	r.Order.TransID = order.ZPTransID
	r.Order.AppTransID = order.AppTransID
	r.Order.Description = order.Description
	return r
}
