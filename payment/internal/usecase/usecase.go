package usecase

import (
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/purchase"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/repay"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/transaction"
)

var ProviderSet = wire.NewSet(
	repay.NewRepayUsecase,
	refund.NewRefundUsecase,
	purchase.NewPurchaseUsecase,
	transaction.NewTransactionUsecase,
)
