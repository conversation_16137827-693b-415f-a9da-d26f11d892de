package repay

import (
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
)

type RepayUsecase struct {
	logger          *log.Helper
	repo            types.PaymentRepo
	orderRepo       types.OrderRepo
	accountAdapter  types.AccountAdapter
	acAdapter       types.AcquiringCoreAdapter
	connector       types.PartnerConnector
	workflowAdapter types.WorkflowAdapter
}

func NewRepayUsecase(logger log.Logger,
	repo types.PaymentRepo,
	accountAdapter types.AccountAdapter,
	orderRepo types.OrderRepo,
	acAdapter types.AcquiringCoreAdapter,
	connector types.PartnerConnector,
	workflowAdapter types.WorkflowAdapter) *RepayUsecase {
	return &RepayUsecase{
		logger:          log.NewHelper(log.With(logger, "adapters", "repay_usecase")),
		repo:            repo,
		orderRepo:       orderRepo,
		accountAdapter:  accountAdapter,
		acAdapter:       acAdapter,
		connector:       connector,
		workflowAdapter: workflowAdapter,
	}
}
