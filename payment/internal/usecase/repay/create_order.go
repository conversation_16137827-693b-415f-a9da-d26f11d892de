package repay

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
	productCodeNatureDeposit = "AC005"
	repaymentMessage         = "Thanh toán dư nợ kỳ sao kê tháng %s" // TODO  sao kê tháng x/xxxx
	DefaultFeeAmount         = 0
)

type CreateOrderRequest struct {
	UserID        int64
	Amount        int64
	PartnerCode   string
	StatementDate time.Time
	StatementID   int64
}

func (uc *RepayUsecase) CreateOrder(ctx context.Context, req CreateOrderRequest) (*model.RepayOrderResponse, error) {
	appTransID := idgen.GenAppTransID()

	//calc amount, fee
	fee := int64(DefaultFeeAmount)
	amount := req.Amount - fee
	description := fmt.Sprintf(repaymentMessage, req.StatementDate.Format("1/2006"))

	resp, err := uc.acAdapter.CreateOrder(ctx, model.RepayOrderRequest{
		AppTransID:  appTransID,
		Amount:      amount,
		AppTime:     time.Now().UnixMilli(),
		AppUser:     cast.ToString(req.UserID),
		Description: description,
		EmbedData:   genEmbedData(fee), // TODO include fee info
		ProductCode: productCodeNatureDeposit,
		PartnerCode: req.PartnerCode,
	})
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "create order failed", "error", err, "req", req)
		return nil, err
	}

	uc.logger.WithContext(ctx).Infow("msg", "create order to AC success", "response", resp, "req", req)

	_, err = uc.orderRepo.CreateRepayOrder(ctx, model.RepayOrder{
		ZalopayID:     req.UserID,
		AppTransID:    appTransID,
		AppID:         resp.AppID,
		Amount:        req.Amount,
		Status:        model.OrderStatusInit,
		ZPTransToken:  resp.ZpTransToken,
		Type:          model.OrderTypeRepayment,
		Description:   description,
		StatementDate: req.StatementDate,
		StatementID:   req.StatementID,
		PartnerCode:   req.PartnerCode,
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func genEmbedData(fee int64) string {
	type EmbedData struct {
		Fee int64 `json:"fee"`
	}
	ed := EmbedData{Fee: fee}
	data, _ := json.Marshal(ed)

	return string(data)

}
