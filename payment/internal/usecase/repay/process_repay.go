package repay

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
)

type ProcessRepayRequest struct {
	OrderNo    string
	AppId      int32
	AppTransId string
	Amount     string
	Status     model.OrderStatus
	ZPTransID  int64
}

type ProcessRepayResponse struct {
}

func (uc *RepayUsecase) ProcessRepay(ctx context.Context, req ProcessRepayRequest) ProcessError {
	// 1. Get order from DB.
	//If completed then return
	repayOrder, err := uc.orderRepo.GetRepayOrder(ctx, req.AppTransId, req.AppId, model.OrderTypeRepayment)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Not exist order in DB
			return NewProcessError(err, false)
		}
		return NewProcessError(err, true)
	}
	if repayOrder.IsCompleted() {
		return NewProcessError(fmt.Errorf("order is already processed"), false)
	}

	repayOrder.ZPTransID = req.ZPTransID

	// 2. Get Installment Account from Adapter
	account, err := uc.accountAdapter.GetAccount(ctx, repayOrder.ZalopayID, "CIMB")
	if err != nil {
		return NewProcessError(err, true)
	}

	// 3. Get order status from AC (TODO)

	// Get bank route
	bankRoute, err := uc.repo.GetBankRoute(ctx, "CIMB", model.TransTypeRePayment)
	if err != nil {
		return NewProcessError(err, true)
	}

	// 4. if order_status = fail
	if req.Status == model.OrderStatusSucceeded {
		// Call ODRepayment to Partner
		repaymentLog := model.NewRepaymentLog(repayOrder, account, bankRoute)
		repaymentLog.Status = model.PaymentStatusSucceeded

		odRepaymentResp, err := uc.connector.ODRepayment(ctx, model.ODRepaymentRequest{
			RefOrderID:         repayOrder.ID,
			PartnerReqID:       idgen.GenPaymentTransID(),
			Amount:             repayOrder.Amount,
			PaymentDescription: repayOrder.Description,
			ZalopayID:          repayOrder.ZalopayID,
			CorpAccountNumber:  bankRoute.BankAccountNumber,
			CorpAccountName:    bankRoute.BankAccountName,
			BankAccountNumber:  account.PartnerAccountId,
			BankAccountName:    account.PartnerAccountName,
		})
		if err != nil {
			uc.logger.WithContext(ctx).Errorw("msg", "OD repayment fail", "error", err)
			repaymentLog.SetSystemError(err)
		} else {
			repaymentLog.SetPartnerData(odRepaymentResp)
		}

		// TODO retry
		_, err = uc.repo.CreateRepaymentLog(ctx, repaymentLog)
		if err != nil {
			uc.logger.WithContext(ctx).Errorw("msg", "failed to create repayment log", "repaymentLog", repaymentLog, "error", err)
		}
	}

	// Update order status
	err = uc.orderRepo.UpdateOrderStatus(ctx, repayOrder.ID, req.Status, repayOrder.ZPTransID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "Failed to update order status",
			"order_id", repayOrder.ID, "status", req.Status, "zp_trans_id", repayOrder.ZPTransID, "error", err)
		return NewProcessError(err, true)
	}

	_ = uc.triggerSyncAccountBalance(ctx, repayOrder.ID, model.SyncAccountBalanceParams{
		ZalopayID: repayOrder.ZalopayID,
		AccountID: account.ID,
		Polling:   true,
	})

	_ = uc.triggerSyncLoanRepaymentWorkflow(ctx, repayOrder.ID, model.SyncLoanRepaymentWorkflow{
		ZalopayID:     repayOrder.ZalopayID,
		AccountID:     account.ID,
		StatementID:   repayOrder.StatementID,
		StatementDate: repayOrder.StatementDate,
	})

	return nil
}

func (uc *RepayUsecase) triggerSyncAccountBalance(ctx context.Context, repayOrderID int64, params model.SyncAccountBalanceParams) error {
	wfType := "PollingAccountBalanceWorkflow"
	wfOption := client.StartWorkflowOptions{
		ID:                    fmt.Sprintf("REPAYMENT-POLLING-ACCOUNT-BALANCE-%d-%d", params.ZalopayID, params.AccountID),
		TaskQueue:             "account.syncing.common",
		WorkflowIDReusePolicy: enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING,
	}

	wfRun, err := uc.workflowAdapter.ExecuteWorkflow(ctx, wfOption, wfType, params)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "[Trigger] after repay, sync account balance fail", "error", err, "params", params, "repayOrderID", repayOrderID)
		return err
	}

	uc.logger.WithContext(ctx).Infow("msg", "[Trigger] after repay, sync account balance success", "runID", wfRun.GetRunID(), "params", params, "repayOrderID", repayOrderID)
	return nil
}

func (uc *RepayUsecase) triggerSyncLoanRepaymentWorkflow(ctx context.Context, repayOrderID int64, params model.SyncLoanRepaymentWorkflow) error {
	wfType := "SyncLoanRepaymentWorkflow" //TODO move config to env
	wfOption := client.StartWorkflowOptions{
		ID:                    fmt.Sprintf("REPAYMENT-POLLING-LOAN-REPAYMENT-%d", params.StatementID),
		TaskQueue:             "management.common", //TODO move config to env
		WorkflowIDReusePolicy: enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING,
	}

	wfRun, err := uc.workflowAdapter.ExecuteWorkflow(ctx, wfOption, wfType, params)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "[Trigger] after repay, sync loan-repayment fail", "error", err, "params", params, "repayOrderID", repayOrderID)
		return err
	}

	uc.logger.WithContext(ctx).Infow("msg", "[Trigger] after repay, sync loan-repayment success", "runID", wfRun.GetRunID(), "params", params, "repayOrderID", repayOrderID)
	return nil
}
