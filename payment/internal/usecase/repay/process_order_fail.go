package repay

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type ProcessRepayFailedRequest struct {
	OrderNo    string
	AppId      int32
	AppTransId string
	Amount     string
	Status     model.OrderStatus
	ZPTransID  int64
}

type ProcessError interface {
	error
	IsRetry() bool
}

type processError struct {
	isRetry bool
	err     error
}

func NewProcessError(err error, isRetry bool) ProcessError {
	return &processError{isRetry: isRetry, err: err}
}

// Implement Error function for processError
func (e processError) Error() string {
	if e.err != nil {
		return e.err.Error()
	}
	return ""
}

func (e processError) IsRetry() bool {
	return e.isRetry
}

func (uc *RepayUsecase) ProcessRepayFailed(ctx context.Context, req ProcessRepayFailedRequest) ProcessError {
	repayOrder, err := uc.orderRepo.GetRepayOrder(ctx, req.AppTransId, req.AppId, model.OrderTypeRepayment)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Not exist order in DB
			return NewProcessError(err, false)
		}
		return NewProcessError(err, true)
	}

	if repayOrder.IsCompleted() {
		// Order is already processed
		uc.logger.WithContext(ctx).Infow("msg", "Order is already processed", "order", repayOrder)
		return NewProcessError(fmt.Errorf("order is already processed"), false)
	}

	// Update order status
	err = uc.orderRepo.UpdateOrderStatus(ctx, repayOrder.ID, req.Status, req.ZPTransID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "Failed to update order status", "order_id", repayOrder.ID, "status", req.Status, "error", err)
		return NewProcessError(err, true)
	}

	return nil
}
