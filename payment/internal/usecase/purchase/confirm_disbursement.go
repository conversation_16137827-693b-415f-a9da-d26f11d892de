package purchase

import (
	"context"
	"fmt"
	"time"

	"github.com/avast/retry-go"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
)

//type ConfirmDisbursementResponse struct{}

func (uc *PurchaseUsecase) ConfirmDisbursement(ctx context.Context, req model.PurchaseEvent) error {
	var purchaseOrder *model.PurchaseOrder
	err := retry.Do(
		func() error {
			var retryErr error
			purchaseOrder, retryErr = uc.repo.GetPayment(ctx, req.PurchaseOrderID, req.ZalopayID)
			if retryErr != nil {
				uc.log.WithContext(ctx).Errorw("msg", "[ConfirmDisbursement] get purchase order fail on retry...", "req", req, "error", retryErr)
				return retryErr
			}

			return nil
		},
		retry.Attempts(3),
		retry.Delay(time.Millisecond*50),
	)

	if err != nil {
		_ = uc.handleFailResult(ctx, purchaseOrder, "[ConfirmDisbursement] get_purchase_fail", err.Error())
		return err
	}

	defer func() {
		_ = uc.triggerSyncAccountBalance(ctx, purchaseOrder.ID, model.SyncAccountBalanceParams{
			ZalopayID: purchaseOrder.AccountInfo.ZalopayID,
			AccountID: purchaseOrder.AccountInfo.ID,
		})
	}()

	var ret *model.ConfirmDisbursementResult
	if purchaseOrder.Status == model.PaymentStatusProcessing &&
		purchaseOrder.PartnerData.Status == model.CIMBPaymentStatusVerifiedPendingConfirmation {
		ret, err = uc.partnerConnector.ConfirmDisbursement(ctx,
			req.PurchaseOrderID, purchaseOrder.PartnerData.InitPurchasingResult.CIMBTransID, req.ZalopayID, false)
		if err != nil {
			uc.log.WithContext(ctx).Errorw("msg", "confirm disbursement to CIMB fail. Continue polling final status",
				"payment_id", purchaseOrder.ID, "error", err)

			_ = uc.triggerPollingAndHandlePaymentStatus(ctx, purchaseOrder.ID, purchaseOrder.AccountInfo.ZalopayID)
			return nil
		}

		purchaseOrder.PartnerData.ConfirmDisbursementResult = *ret
		purchaseOrder.PartnerData.Status = ret.TransactionStatus
		purchaseOrder.Status = model.PaymentStatusProcessing

		_, err = uc.repo.UpdatePayment(ctx, *purchaseOrder)
		if err != nil {
			uc.log.WithContext(ctx).Errorw("msg", "after confirm disbursement success, update purchase fail", "purchase_order", purchaseOrder)
		}

		_ = uc.triggerPollingAndHandlePaymentStatus(ctx, purchaseOrder.ID, purchaseOrder.AccountInfo.ZalopayID)
		return nil
	}

	if purchaseOrder.Status == model.PaymentStatusProcessing &&
		purchaseOrder.PartnerData.Status == model.CIMBPaymentStatusProcessing {
		_ = uc.triggerPollingAndHandlePaymentStatus(ctx, purchaseOrder.ID, purchaseOrder.AccountInfo.ZalopayID)
		return nil
	}

	// Bypass if purchase status is not processing
	uc.log.WithContext(ctx).Infow("ConfirmDisbursement, purchase status is not processing", "purchase_order", purchaseOrder, "req", req)
	return nil
}

func (uc *PurchaseUsecase) triggerPollingAndHandlePaymentStatus(ctx context.Context, paymentID int64, zalopayID int64) error {
	wfType := "PollingPaymentStatus"
	wfOption := client.StartWorkflowOptions{
		ID:                    fmt.Sprintf("PAYMENT-SYNC-STATUS-%d-%d", zalopayID, paymentID),
		TaskQueue:             "installment.payment",
		WorkflowIDReusePolicy: enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING,
	}

	wfRun, err := uc.workflowAdapter.ExecuteWorkflow(ctx, wfOption, wfType, &model.PaymentStatusWorkflowRequest{
		PaymentID: paymentID,
		ZalopayID: zalopayID,
	})
	if err != nil {
		uc.log.WithContext(ctx).WithContext(ctx).Errorw("msg", "[Trigger] polling payment status fail", "error", err, "zalopayID", zalopayID, "paymentID", paymentID)
		return err
	}

	uc.log.WithContext(ctx).Infow("msg", "[Trigger] polling payment status success", "runID", wfRun.GetRunID(), "zalopayID", zalopayID, "paymentID", paymentID)
	return nil
}

type CancelTransactionResponse struct {
	RefSofId       int64
	PartnerTransId string
}

func (uc *PurchaseUsecase) CancelTransaction(ctx context.Context, zpTransID int64) (*CancelTransactionResponse, error) {
	po, err := uc.repo.GetPaymentByTransID(ctx, zpTransID)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[CancelTransaction] get purchase order by transID fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	cimbTrans, err := uc.partnerConnector.InquiryTransaction(ctx, cast.ToString(po.ID))
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[CancelTransaction] inquiry transaction fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	if cimbTrans.PaymentStatus != model.CIMBPaymentStatusVerifiedPendingConfirmation {
		uc.log.WithContext(ctx).Warnw("msg", "[CancelTransaction] payment status is not VerifiedPendingConfirmation", "zpTransID", zpTransID, "cimbTrans", cimbTrans)
		return nil, fmt.Errorf("payment status is not %s", model.CIMBPaymentStatusVerifiedPendingConfirmation)
	}

	ret, err := uc.partnerConnector.ConfirmDisbursement(ctx,
		po.ID, po.PartnerData.InitPurchasingResult.CIMBTransID, po.AccountInfo.ZalopayID, true)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[CancelTransaction] confirm disbursement fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	if ret.IsError() {
		uc.log.WithContext(ctx).Errorw("msg", "[CancelTransaction] confirm disbursement fail", "zpTransID", zpTransID, "error_code", ret.ErrorCode)
		return nil, fmt.Errorf("confirm disbursement fail, error_code: %s", ret.ErrorCode)
	}

	if ret.TransactionStatus != model.CIMBPaymentStatusCancelled {
		uc.log.WithContext(ctx).Errorw("msg", "[CancelTransaction] confirm disbursement fail",
			"zpTransID", zpTransID, "error", "status is not cancel", "ret", ret)
		return nil, fmt.Errorf("confirm disbursement fail, status is not canceled")
	}

	err = uc.triggerPollingAndHandlePaymentStatus(ctx, po.ID, po.AccountInfo.ZalopayID)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[CancelTransaction] triggerPollingAndHandlePaymentStatus fail",
			"zpTransID", zpTransID, "payment_id", po.ID, "error", err)
	}

	return &CancelTransactionResponse{
		RefSofId:       po.ID,
		PartnerTransId: po.PartnerData.InitPurchasingResult.CIMBTransID,
	}, nil
}
