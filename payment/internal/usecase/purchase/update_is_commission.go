package purchase

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type UpdatePaymentCommission struct {
	ZPTransID    int64 `json:"zp_trans_id"`
	IsCommission bool  `json:"is_commission"`
}

func (uc *PurchaseUsecase) UpdateCommission(ctx context.Context, req model.UpdatePaymentEvent) error {
	payment, err := uc.repo.GetPaymentByTransID(ctx, req.Commission.ZPTransID)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "UpdateCommission, GetPaymentByTransID error", "error", err)
		return err
	}

	if payment.Status != model.PaymentStatusSucceeded {
		uc.log.WithContext(ctx).Warnw("msg", "UpdateCommission, payment status is not success",
			"payment.Status", payment.Status, "req", req)
		return fmt.Errorf("payment status is not success: %s", payment.Status)
	}

	err = uc.repo.UpdatePaymentCommission(ctx, req.Commission.ZPTransID, req.Commission.IsCommission)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "UpdateCommission, UpdatePaymentCommission error",
			"error", err, "req", req)
		return err
	}

	return nil
}
