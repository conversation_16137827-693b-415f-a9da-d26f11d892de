package purchase

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type InitPurchaseRequest struct {
	ZalopayID int64
	//PartnerCode string
	Order      model.Order
	ChargeInfo string
	RequestID  string
	PaymentNo  string
	DeviceID   string
	UserIP     string
}

type PlanKey struct {
	Tenor        int
	InterestRate float64
}

type InitPurchaseResponse struct {
	ZPTransID            int64
	TransactionID        int64
	PartnerTransactionID string
	PartnerCode          string
}

func (uc *PurchaseUsecase) InitPurchase(ctx context.Context, req InitPurchaseRequest) (*InitPurchaseResponse, error) {
	//Validate charge_info
	chargeInfo, err := decodeChargeInfo(req.ChargeInfo)
	if err != nil {
		return nil, errors.InternalServer("charge_info_invalid", err.Error())
	}

	uc.log.WithContext(ctx).Infow("msg", "init_purchase, decode charge info", "charge_info", chargeInfo, "req.ChargeInfo", req.ChargeInfo)

	purchaseOrder := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   req.ZalopayID,
			PartnerCode: chargeInfo.PartnerCode,
			//PartnerAccountId: chargeInfo.AccountID,
		},
		BankRoute: model.BankRoute{
			PartnerBankCode: chargeInfo.PartnerCode,
		},
		Order: model.Order{
			TransID:     req.Order.TransID,
			AppID:       req.Order.AppID,
			AppTransID:  req.Order.AppTransID,
			Amount:      req.Order.Amount,
			Description: req.Order.Description,
		},
		TransType:    model.TransTypePayment,
		Status:       model.PaymentStatusInit,
		PERequestID:  req.RequestID,
		FSChargeInfo: req.ChargeInfo,
		PaymentNo:    req.PaymentNo,
		DeviceID:     req.DeviceID,
		UserIP:       req.UserIP,
	}

	purchaseOrder.InstallmentInstruction = model.InstallmentInstruction{
		Tenor:        chargeInfo.Tenor,
		InterestRate: chargeInfo.InterestRate,
	}

	//Init Payment
	purchaseOrder, err = uc.repo.CreatePayment(ctx, *purchaseOrder)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "create payment fail", "purchase_order", purchaseOrder, "error", err)
		return nil, err
	}

	// Publish to kafka
	data, _ := json.Marshal(model.PurchaseEvent{
		EventName:       "",
		PartnerCode:     "CIMB",
		AccountID:       cast.ToInt64(purchaseOrder.AccountInfo.PartnerAccountId),
		PurchaseOrderID: purchaseOrder.ID,
		ZalopayID:       purchaseOrder.AccountInfo.ZalopayID,
	})
	err = uc.purchaseInitiatedPublisher.PublishRaw(ctx, cast.ToString(purchaseOrder.Order.TransID), data)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "publish purchase initiated fail", "purchase_order", purchaseOrder, "error", err)
		return nil, errors.InternalServer("publish_purchase_initiated_fail", err.Error())
	}

	uc.log.WithContext(ctx).Infow("msg", "publish purchase initiated success", "data", data)

	return &InitPurchaseResponse{
		ZPTransID:            purchaseOrder.Order.TransID,
		TransactionID:        purchaseOrder.ID,
		PartnerTransactionID: purchaseOrder.PartnerData.PurchaseResults.BankTransID,
		PartnerCode:          purchaseOrder.AccountInfo.PartnerCode,
	}, nil
}

//type PlanKeyData struct {
//	PlanKey    string
//	ZalopayID  int64
//	Tenure     int64
//	AppID      int32
//	AppTransID string
//}

const (
	planKeyPattern = "%d|%d|%s|%d"
)

func decodeChargeInfo(key string) (model.ChargeInfo, error) {
	var fsci model.FSChargeInfo
	err := json.Unmarshal([]byte(key), &fsci)
	if err != nil {
		return model.ChargeInfo{}, err
	}

	keyDecoded, err := base64.RawURLEncoding.DecodeString(fsci.PlanKey)
	if err != nil {
		return model.ChargeInfo{}, err
	}

	keyString := strings.ReplaceAll(string(keyDecoded), "|", " ")
	keyPattern := strings.ReplaceAll(planKeyPattern, "|", " ")

	var zalopayID int64
	var appID int32
	var tenure int32
	var appTransID string
	_, err = fmt.Sscanf(keyString, keyPattern, &zalopayID, &appID, &appTransID, &tenure)
	if err != nil {
		return model.ChargeInfo{}, err
	}

	return model.ChargeInfo{
		PartnerCode: fsci.PartnerCode,
		//AccountID:    "0",
		Tenor:        fsci.PlanTenure,
		InterestRate: fsci.InterestRate,
	}, nil
}
