package purchase

import (
	"context"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type QueryStatusRequest struct {
	PurchaseOrderID int64
	ZalopayID       int64
	TransID         int64
}

type QueryStatusResponse struct {
	PurchaseOrder *model.PurchaseOrder
}

func (uc *PurchaseUsecase) QueryStatus(ctx context.Context, req QueryStatusRequest) (*QueryStatusResponse, error) {
	purchaseOrder, err := uc.repo.GetPaymentByTransID(ctx, req.TransID)
	if err != nil {
		return nil, err
	}

	return &QueryStatusResponse{
		PurchaseOrder: purchaseOrder,
	}, nil
}
