package purchase

import (
	"context"
	"fmt"
	"time"

	"github.com/avast/retry-go"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
)

//
//type ConfirmPurchaseRequest struct {
//}

//type ConfirmPurchaseResponse struct {
//}

func (uc *PurchaseUsecase) ConfirmPurchase(ctx context.Context, req model.PurchaseEvent) error {
	var purchaseOrder *model.PurchaseOrder
	err := retry.Do(
		func() error {
			var retryErr error
			purchaseOrder, retryErr = uc.repo.GetPayment(ctx, req.PurchaseOrderID, req.ZalopayID)
			if retryErr != nil {
				uc.log.WithContext(ctx).Errorw("msg", "get purchase order fail on retry...", "req", req, "error", retryErr)
				return retryErr
			}

			return nil
		},
		retry.Attempts(3),
		retry.Delay(time.Millisecond*50),
	)

	if err != nil {
		_ = uc.handleFailResult(ctx, purchaseOrder, "get_purchase_fail", err.Error())
		return err
	}

	if purchaseOrder.Status != model.PaymentStatusProcessing ||
		purchaseOrder.PartnerData.Status != model.CIMBPaymentStatusNew {
		// Bypass if purchase status is not processing
		uc.log.WithContext(ctx).Warnw("msg", "purchase status is not processing", "purchase_order", purchaseOrder)
		return nil
	}

	ret, err := uc.partnerConnector.ConfirmPurchasing(ctx, *purchaseOrder)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "confirm purchase to CIMB fail",
			"purchase_order", purchaseOrder, "error", err)

		_ = uc.handleFailResult(ctx, purchaseOrder, "cimb_confirm_purchasing_fail", err.Error())
		return err
	}

	purchaseOrder.PartnerData.ConfirmPurchaseResult = *ret

	if ret.IsError() {
		_ = uc.handleFailResult(ctx, purchaseOrder, "cimb_confirm_purchasing_fail", ret.CIMBError.ErrorCode)
		return fmt.Errorf("cimb error: %s", ret.CIMBError.ErrorCode)
	}

	_ = uc.triggerSyncAccountBalance(ctx, purchaseOrder.ID, model.SyncAccountBalanceParams{
		ZalopayID: purchaseOrder.AccountInfo.ZalopayID,
		AccountID: purchaseOrder.AccountInfo.ID,
	})

	purchaseOrder.PartnerData.Status = ret.TransactionStatus
	purchaseOrder.Status = model.PaymentStatusProcessing

	_, err = uc.repo.UpdatePayment(ctx, *purchaseOrder)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "update purchase fail", "purchase_order", purchaseOrder, "error", err)
		_ = uc.handleFailResult(ctx, purchaseOrder, "zlp_update_payment_fail", err.Error())
		return err
	}

	uc.log.WithContext(ctx).Infow("msg", "update confirm purchase success", "purchase_order", purchaseOrder)

	return nil
}

func (uc *PurchaseUsecase) triggerSyncAccountBalance(ctx context.Context, paymentID int64, params model.SyncAccountBalanceParams) error {
	wfType := "SyncAccountBalanceWorkflow"
	wfOption := client.StartWorkflowOptions{
		ID:                    fmt.Sprintf("PAYMENT-SYNC-ACCOUNT-BALANCE_%d-%d", params.ZalopayID, params.AccountID),
		TaskQueue:             "account.syncing.common",
		WorkflowIDReusePolicy: enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING,
	}

	wfRun, err := uc.workflowAdapter.ExecuteWorkflow(ctx, wfOption, wfType, params)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[Trigger] sync account balance fail", "error", err, "params", params, "paymentID", paymentID)
		return err
	}

	uc.log.WithContext(ctx).Infow("msg", "[Trigger] sync account balance success", "runID", wfRun.GetRunID(), "params", params, "paymentID", paymentID)
	return nil
}
