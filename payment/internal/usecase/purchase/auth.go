package purchase

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/avast/retry-go"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

// AuthProcess is an usecase for auth process
func (uc *PurchaseUsecase) AuthProcess(ctx context.Context, req model.PurchaseEvent) error {
	// Get purchase order from DB
	var purchaseOrder *model.PurchaseOrder
	err := retry.Do(
		func() error {
			var retryErr error
			purchaseOrder, retryErr = uc.repo.GetPayment(ctx, req.PurchaseOrderID, req.ZalopayID)
			if retryErr != nil {
				uc.log.WithContext(ctx).Errorw("msg", "get purchase order fail on retry...", "req", req, "error", retryErr)
				return retryErr
			}

			return nil
		},
		retry.Attempts(3),
		retry.Delay(time.Millisecond*50),
	)

	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "get purchase order fail", "req", req, "error", err)
		_ = uc.handleFailResult(ctx, purchaseOrder, "get_purchase_fail", err.Error())
		return nil
	}

	if !purchaseOrder.Status.IsInitStatus() {
		// Bypass if purchase status is not init status
		uc.log.WithContext(ctx).Infow("msg", "Bypass auth process", "purchase_order", purchaseOrder, "req", req)
		return nil
	}

	partnerCode := purchaseOrder.AccountInfo.PartnerCode

	account, err := uc.accountAdapter.GetAccount(ctx, req.ZalopayID, partnerCode)
	if err != nil {
		_ = uc.handleFailResult(ctx, purchaseOrder, "get_account_fail", err.Error())
		return nil
	}

	purchaseOrder.SetAccount(account)

	if !account.IsActive() {
		_ = uc.handleFailResult(ctx, purchaseOrder, "account_not_active", "account is not active")
		return nil
	}

	bankRoute, err := uc.repo.GetBankRoute(ctx, partnerCode, model.TransTypePayment)
	if err != nil {
		_ = uc.handleFailResult(ctx, purchaseOrder, "get_bank_route_fail", err.Error())
		return nil
	}

	purchaseOrder.SetBankRoute(*bankRoute)

	purchaseOrder.PartnerData.CustomerRemark = buildCustomerRemark(purchaseOrder.Order.AppID, purchaseOrder.Order.TransID)
	purchaseOrder.PartnerData.DrawdownPurpose = buildDescription(purchaseOrder.Order.AppID, purchaseOrder.Order.TransID)

	initPurchasingRet, err := uc.partnerConnector.InitPurchasing(ctx, *purchaseOrder)

	// Need retry?
	if err != nil {
		_ = uc.handleFailResult(ctx, purchaseOrder, "cimb_init_transaction_fail", err.Error())
		return nil
	}

	purchaseOrder.PartnerData.InitPurchasingResult = *initPurchasingRet

	if initPurchasingRet.CIMBError.IsError() {
		_ = uc.handleFailResult(ctx, purchaseOrder, "cimb_init_transaction_fail", initPurchasingRet.CIMBError.ErrorCode)
		return nil
	}

	purchaseOrder.PartnerData.Status = initPurchasingRet.TransactionStatus

	switch initPurchasingRet.RequiredStepupMethod {
	case model.AdvanceStepUpMethodNoAdvanceAuthRequired, model.AdvanceStepUpMethodPartnerPinPass:
		purchaseOrder.Status = model.PaymentStatusProcessing
		_, err = uc.repo.UpdatePayment(ctx, *purchaseOrder)
		if err != nil {
			uc.log.WithContext(ctx).Errorw("msg", "update purchase fail", "purchase_order", purchaseOrder, "error", err)
			_ = uc.handleFailResult(ctx, purchaseOrder, "zlp_update_payment_fail", err.Error())
			return nil
		}

		// Push to Internal Kafka
		_ = uc.pushToPurchaseAuthenticateKafka(ctx, partnerCode, cast.ToInt64(purchaseOrder.AccountInfo.ID),
			purchaseOrder.ID, purchaseOrder.AccountInfo.ZalopayID, purchaseOrder.Order.TransID)
		return nil
	case model.AdvanceStepUpMethodPartnerOTP, model.AdvanceStepUpMethodPartnerSelfie:
		authSessionID, err := uc.handlePaymentAuthenticator(ctx, purchaseOrder, initPurchasingRet)
		if err != nil {
			_ = uc.handleFailResult(ctx, purchaseOrder, "payment_auth", err.Error())
			return nil
		}

		purchaseOrder.Status = model.PaymentStatusProcessing
		purchaseOrder.PartnerData.PaymentAuth.AuthSessionID = authSessionID

		_, err = uc.repo.UpdatePayment(ctx, *purchaseOrder)
		if err != nil {
			uc.log.WithContext(ctx).Errorw("msg", "update purchase fail", "purchase_order", purchaseOrder, "error", err)
			_ = uc.handleFailResult(ctx, purchaseOrder, "zlp_update_payment_fail", err.Error())
			return nil
		}

		return nil
	case model.AdvanceStepUpMethodCIMBSelfie:
		uc.log.WithContext(ctx).Errorw("msg", "not handle auth method", "required_stepup_method", initPurchasingRet.RequiredStepupMethod, "purchase_order", purchaseOrder)
		_ = uc.handleFailResult(ctx, purchaseOrder, "init_transaction_required_selfie", "not handle cimb selfie auth method")
		return nil
	case model.AdvanceStepUpMethodPartnerSelfieWithOTP:
		uc.log.WithContext(ctx).Errorw("msg", "not handle auth method", "required_stepup_method", initPurchasingRet.RequiredStepupMethod, "purchase_order", purchaseOrder)
		_ = uc.handleFailResult(ctx, purchaseOrder, "init_transaction_required_selfie", "not handle cimb selfie auth method")
		return nil
	default:
		// mark fail
		uc.log.WithContext(ctx).Errorw("msg", "not handle selfie auth method", "purchase_order", purchaseOrder)
		_ = uc.handleFailResult(ctx, purchaseOrder, "init_transaction_pay_auth_selfie", "not handle selfie auth method")
		return nil
	}

	return nil
}

func (uc *PurchaseUsecase) handlePaymentAuthenticator(ctx context.Context, po *model.PurchaseOrder, initResult *model.InitPurchasingResult) (string, error) {
	var authType = payment_auth.AuthType_AUTH_TYPE_UNSPECIFIED
	if initResult.RequiredStepupMethod == model.AdvanceStepUpMethodPartnerSelfie {
		authType = payment_auth.AuthType_AUTH_TYPE_UM_FACE_AUTH
	} else if initResult.RequiredStepupMethod == model.AdvanceStepUpMethodPartnerOTP {
		authType = payment_auth.AuthType_AUTH_TYPE_UM_OTP
	}

	if authType == payment_auth.AuthType_AUTH_TYPE_UNSPECIFIED {
		return "", fmt.Errorf("not handle AdvanceStepUpMethod: %s", initResult.RequiredStepupMethod)
	}

	authSessionID, err := uc.paymentAuthAdapter.Authenticate(ctx, *po, authType)
	if err != nil {
		return "", fmt.Errorf("failed to authenticate: %w", err)
	}

	return cast.ToString(authSessionID), nil
}

func (uc *PurchaseUsecase) handleFailResult(ctx context.Context, purchaseOrder *model.PurchaseOrder, errCode string, errMsg string) error {
	//purchaseOrder.SetStatus(model.PurchaseFailed)
	purchaseOrder.SetError(errCode, errMsg)

	_ = uc.pushToPEKafka(ctx, purchaseOrder.Order.TransID, purchaseOrder.PERequestID, purchaseOrder.ID,
		v1.ReturnCode_Code_name[int32(v1.ReturnCode_FAILED)], purchaseOrder.ErrorCode, purchaseOrder.ErrorMessage)

	// Update purchase status to DB, status = fail
	_, err := uc.repo.UpdatePayment(ctx, *purchaseOrder)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "update purchase fail", "purchase_order", purchaseOrder)
	}
	return err
}

func (uc *PurchaseUsecase) buildPEEventData(purchaseOrder *model.PurchaseOrder, statusCode v1.ReturnCode_Code) []byte {
	data, _ := json.Marshal(model.ExchangeStatusEvent{
		TransID:   purchaseOrder.Order.TransID,
		RequestID: purchaseOrder.PERequestID,
		RefSofID:  cast.ToString(purchaseOrder.ID),
		Status: model.ExchangeStatus{
			Code:    v1.ReturnCode_Code_name[int32(statusCode)],
			SubCode: purchaseOrder.ErrorCode,
			Message: purchaseOrder.ErrorMessage,
		},
	})
	return data
}

func (uc *PurchaseUsecase) pushToPEKafka(ctx context.Context, transID int64, peRequestID string,
	refSofID int64, statusCode string, errorSubCode string, errorMessage string) error {
	data, _ := json.Marshal(model.ExchangeStatusEvent{
		TransID:   transID,
		RequestID: peRequestID,
		RefSofID:  cast.ToString(refSofID),
		Status: model.ExchangeStatus{
			Code:    statusCode,
			SubCode: errorSubCode,
			Message: errorMessage,
		},
	})

	err := uc.peUpdateExchangeStatusPublisher.PublishRaw(ctx, cast.ToString(refSofID), data)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "push to PE-Kafka fail", "data", string(data), "error", err)
		return err
	}

	uc.log.WithContext(ctx).Infow("msg", "push to PE-Kafka success", "data", string(data))
	return nil
}

func (uc *PurchaseUsecase) pushToPurchaseAuthenticateKafka(ctx context.Context, partnerCode string,
	accountID int64, refSofID int64, zalopayID int64, zpTransID int64) error {
	data, _ := json.Marshal(model.PurchaseEvent{
		EventName:       "",
		PartnerCode:     partnerCode,
		AccountID:       accountID,
		PurchaseOrderID: refSofID,
		ZalopayID:       zalopayID,
	})

	err := uc.purchaseAuthenticatePublisher.PublishRaw(ctx, cast.ToString(zpTransID), data)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "push to Authenticate Event fail",
			"data", string(data), "error", err, "zp_trans_id", zpTransID)
		return err
	}

	uc.log.WithContext(ctx).Infow("msg", "push to Authenticate Event success",
		"data", string(data), "zp_trans_id", zpTransID)
	return nil
}

const (
	customerRemarkFormat      = "Merchant category %v, merchant ID %v for order %v"
	purchaseDescriptionFormat = "Thanh toán đơn hàng %d cho dịch vụ %d - %d"
	otherCategoryID           = 17
	defaultMerchantIDFactor   = 10_000_000
)

func buildCustomerRemark(appID int32, txnID int64) string {
	merchantID := otherCategoryID*defaultMerchantIDFactor + appID
	return fmt.Sprintf(customerRemarkFormat, otherCategoryID, merchantID, txnID)
}

func buildDescription(appID int32, txnID int64) string {
	merchantID := otherCategoryID*defaultMerchantIDFactor + appID
	return fmt.Sprintf(purchaseDescriptionFormat, txnID, merchantID, otherCategoryID)
}
