package purchase

import (
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"

	"github.com/go-kratos/kratos/v2/log"
)

// PurchaseUsecase is a Purchase usecase.
type PurchaseUsecase struct {
	repo                            types.PaymentRepo
	accountAdapter                  types.AccountAdapter
	partnerConnector                types.PartnerConnector
	zasAdapter                      types.ZasAdapter
	log                             *log.Helper
	purchaseInitiatedPublisher      types.PurchaseInitiatedPublisher
	purchaseAuthenticatePublisher   types.PurchaseAuthenticatedPublisher
	peUpdateExchangeStatusPublisher types.PEUpdateExchangeStatusPublisher
	workflowAdapter                 types.WorkflowAdapter
	paymentAuthAdapter              types.PaymentAuthAdapter
}

// NewPurchaseUsecase new a Purchase usecase.
func NewPurchaseUsecase(logger log.Logger,
	repo types.PaymentRepo,
	partnerConnector types.PartnerConnector,
	accountAdapter types.AccountAdapter,
	purchaseInitiatedPublisher types.PurchaseInitiatedPublisher,
	purchaseAuthenticatePublisher types.PurchaseAuthenticatedPublisher,
	peUpdateExchangeStatusPublisher types.PEUpdateExchangeStatusPublisher,
	workflowAdapter types.WorkflowAdapter,
	paymentAuthAdapter types.PaymentAuthAdapter,
) *PurchaseUsecase {
	return &PurchaseUsecase{
		repo:                            repo,
		accountAdapter:                  accountAdapter,
		partnerConnector:                partnerConnector,
		zasAdapter:                      nil,
		log:                             log.NewHelper(logger),
		purchaseInitiatedPublisher:      purchaseInitiatedPublisher,
		purchaseAuthenticatePublisher:   purchaseAuthenticatePublisher,
		peUpdateExchangeStatusPublisher: peUpdateExchangeStatusPublisher,
		workflowAdapter:                 workflowAdapter,
		paymentAuthAdapter:              paymentAuthAdapter,
	}
}

type CreatePurchaseRequest struct {
	ZalopayID   int64
	PartnerCode string
	Order       model.Order
}

type CreatePurchaseResponse struct {
	PaymentID                  int64
	BankReceivableAccountingID int64
}
