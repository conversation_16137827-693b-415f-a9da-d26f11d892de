package purchase

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/spf13/cast"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
)

func (uc *PurchaseUsecase) PollingPaymentStatus(ctx context.Context, req *model.PaymentStatusWorkflowRequest) (*model.PaymentStatusWorkflowResponse, error) {
	logger := uc.log.WithContext(ctx)
	var cimbTrans *model.CIMBTransaction

	purchaseOrder, err := uc.repo.GetPayment(ctx, req.PaymentID, req.ZalopayID)
	if err != nil {
		logger.Errorw("msg", "[PollingPaymentStatus] get purchase order fail", "req", req, "error", err)
		return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, get purchase order fail", "", err)
	}

	cimbTrans, err = uc.partnerConnector.InquiryTransaction(ctx, cast.ToString(purchaseOrder.ID))
	if err != nil {
		logger.Errorw("msg", "[PollingPaymentStatus] inquiry transaction fail", "req", req, "error", err)
		return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, inquiry transaction fail", "", err)
	}

	// Cancel when confirm disbursement fail
	if cimbTrans.PaymentStatus == model.CIMBPaymentStatusVerifiedPendingConfirmation {
		ret, err := uc.partnerConnector.ConfirmDisbursement(ctx,
			purchaseOrder.ID, purchaseOrder.PartnerData.InitPurchasingResult.CIMBTransID, req.ZalopayID, true)
		if err != nil {
			logger.Errorw("msg", "[PollingPaymentStatus] confirm disbursement with cancel fail", "req", req, "error", err)
			return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, confirm disbursement with cancel fail", "", err)
		}

		logger.Errorw("msg", "[PollingPaymentStatus] confirm disbursement with cancel success. Continue polling final status", "req", req, "ret", ret)
		return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, confirm disbursement with cancel success", "", fmt.Errorf("continue polling final status"))
	}

	if !cimbTrans.PaymentStatus.IsFinalStatus() {
		logger.Errorw("msg", "[PollingPaymentStatus] inquiry transaction, status is not completed", "req", req, "cimbTrans", cimbTrans)
		return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, inquiry transaction", "", fmt.Errorf("status is not completed"))
	}

	// Data to push PE Kafka
	ese := model.ExchangeStatusEvent{
		TransID:   purchaseOrder.Order.TransID,
		RequestID: purchaseOrder.PERequestID,
		RefSofID:  cast.ToString(purchaseOrder.ID),
	}

	// Because of partner trans id may be generate async in partner side
	// so we need to have a flag to check if we need to continue to poll this value in another activity
	awaitingPartnerTransID := false
	purchaseOrder.PartnerData.Status = cimbTrans.PaymentStatus
	purchaseOrder.PartnerData.BankTransactionSequenceID = cimbTrans.BankTransactionSequenceID

	if cimbTrans.PaymentStatus == model.CIMBPaymentStatusComplete {
		purchaseOrder.Status = model.PaymentStatusSucceeded
		awaitingPartnerTransID = cimbTrans.BankTransactionSequenceID == ""
		ese.Status.Code = v1.ReturnCode_Code_name[int32(v1.ReturnCode_SUCCEEDED)]
	} else if cimbTrans.PaymentStatus == model.CIMBPaymentStatusTransferFailed ||
		cimbTrans.PaymentStatus == model.CIMBPaymentStatusExpired ||
		cimbTrans.PaymentStatus == model.CIMBPaymentStatusCancelled {
		purchaseOrder.Status = model.PaymentStatusFailed
		ese.Status.Code = v1.ReturnCode_Code_name[int32(v1.ReturnCode_FAILED)]
		//ese.Status.Message = ret.ErrorCode
		//ese.Status.Message = ret.Description
	} else {
		purchaseOrder.Status = model.PaymentStatusPending
	}

	// TODO Need retry
	_, err = uc.repo.UpdatePayment(ctx, *purchaseOrder)
	if err != nil {
		logger.Errorw("msg", "PollingPaymentStatus, update purchase fail", "purchase_order", purchaseOrder, "error", err)
		return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, update purchase fail", "", err)
	}

	if purchaseOrder.Status == model.PaymentStatusPending {
		logger.Warnw("msg", "PollingPaymentStatus, not push result to PE-Kafka with pending status", "payment_id", purchaseOrder.ID)
		return &model.PaymentStatusWorkflowResponse{
			PartnerPaymentStatus: cimbTrans.PaymentStatus.String(),
			PaymentStatus:        purchaseOrder.Status.String(),
		}, nil
	}

	// Push to PE kafka
	data, _ := json.Marshal(ese)
	err = uc.peUpdateExchangeStatusPublisher.PublishRaw(ctx, cast.ToString(purchaseOrder.ID), data)
	if err != nil {
		logger.Errorw("msg", "PollingPaymentStatus, push to PE-Kafka fail", "data", string(data), "error", err, "req", req)
		return nil, temporal.NewApplicationErrorWithCause("PollingPaymentStatus, push to PE-Kafka fail", "", err)
	}
	logger.Infow("msg", "PollingPaymentStatus, push result to PE-Kafka success", "data", string(data), "req", req)

	if purchaseOrder.Status == model.PaymentStatusSucceeded {
		_ = uc.triggerCreateInstallmentWorkflow(ctx, purchaseOrder)
	}

	_ = uc.triggerSyncAccountBalance(ctx, purchaseOrder.ID, model.SyncAccountBalanceParams{
		ZalopayID: purchaseOrder.AccountInfo.ZalopayID,
		AccountID: purchaseOrder.AccountInfo.ID,
	})

	return &model.PaymentStatusWorkflowResponse{
		PaymentStatus:          purchaseOrder.Status.String(),
		PartnerPaymentStatus:   cimbTrans.PaymentStatus.String(),
		AwaitingPartnerTransID: awaitingPartnerTransID,
	}, nil
}

func (uc *PurchaseUsecase) PollingPartnerTransID(ctx context.Context, req *model.PaymentStatusWorkflowRequest) error {
	logger := uc.log.WithContext(ctx)

	purchase, err := uc.repo.GetPayment(ctx, req.PaymentID, req.ZalopayID)
	if err != nil {
		logger.Errorw("msg", "[PollingPartnerTransID] get purchase order fail", "req", req, "error", err)
		return temporal.NewApplicationErrorWithCause("PollingPartnerTransID, get purchase order fail", "", err)
	}

	if purchase.Status != model.PaymentStatusSucceeded {
		return temporal.NewNonRetryableApplicationError("PollingPartnerTransID, purchase order status is not success", "", nil)
	}
	if purchase.PartnerData.BankTransactionSequenceID != "" {
		logger.Infow("msg", "[PollingPartnerTransID] already have BankTransactionSequenceID", "BankTransactionSequenceID", purchase.PartnerData.BankTransactionSequenceID)
		return nil
	}

	partnerTrans, err := uc.partnerConnector.InquiryTransaction(ctx, cast.ToString(purchase.ID))
	if err != nil {
		logger.Errorw("msg", "[PollingPartnerTransID] inquiry transaction fail", "req", req, "error", err)
		return temporal.NewApplicationErrorWithCause("PollingPartnerTransID, inquiry transaction fail", "", err)
	}
	if partnerTrans.BankTransactionSequenceID == "" {
		logger.Errorw("msg", "[PollingPartnerTransID] inquiry transaction, BankTransactionSequenceID is empty", "req", req, "partnerTrans", partnerTrans)
		return temporal.NewApplicationErrorWithCause("PollingPartnerTransID, inquiry transaction", "", fmt.Errorf("BankTransactionSequenceID is empty"))
	}

	err = uc.repo.UpdatePartnerTransID(ctx, purchase.ID, partnerTrans.BankTransactionSequenceID)
	if err != nil {
		logger.Errorw("msg", "[PollingPartnerTransID] update purchase order fail", "req", req, "error", err)
		return temporal.NewApplicationErrorWithCause("PollingPartnerTransID, update purchase order fail", "", err)
	}

	logger.Infow("msg", "[PollingPartnerTransID] updated PartnerTransactionID successfully", "req", req, "BankTransactionSequenceID", purchase.PartnerData.BankTransactionSequenceID)

	return nil
}

func (uc *PurchaseUsecase) triggerCreateInstallmentWorkflow(ctx context.Context,
	purchaseOrder *model.PurchaseOrder) error {
	wfType := "CreateInstallmentLoanWorkflow" // TODO: Move to config
	wfOption := client.StartWorkflowOptions{
		ID:                    fmt.Sprintf("PAYMENT-CREATE-LOAN-%d-%d", purchaseOrder.AccountInfo.ZalopayID, purchaseOrder.ID),
		TaskQueue:             "installment.common", // TODO: Move to config
		WorkflowIDReusePolicy: enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING,
	}

	var fsci model.FSChargeInfo
	err := json.Unmarshal([]byte(purchaseOrder.FSChargeInfo), &fsci)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[Trigger]  triggerCreateInstallmentWorkflow, decode charge info fail", "error", err, "fsci", purchaseOrder.FSChargeInfo, "payment_id", purchaseOrder.ID)
		return err
	}

	wfRun, err := uc.workflowAdapter.ExecuteWorkflow(ctx, wfOption, wfType, &model.InstallmentCreateParams{
		ZalopayID:   purchaseOrder.AccountInfo.ZalopayID,
		AccountID:   purchaseOrder.AccountInfo.ID,
		PartnerCode: partner.PartnerCode(purchaseOrder.AccountInfo.PartnerCode),
		Installment: model.InstallmentPlanParams{
			Tenure:         fsci.PlanTenure,
			EmiAmount:      fsci.EmiAmount,
			InterestRate:   fsci.InterestRate,
			InterestAmount: fsci.InterestAmount,
			DisburseAmount: purchaseOrder.Order.Amount,
			TotalFeeAmount: fsci.TotalFeeAmount,
			TotalDueAmount: fsci.TotalDueAmount,
			FeeDetails:     nil, //TODO FeeDetails on FSChargeInfo
		},
		Transaction: model.InstallmentTransParams{
			TransID:   purchaseOrder.ID,
			TransDesc: purchaseOrder.Order.Description,
			ZpTransID: purchaseOrder.Order.TransID,
		},
	})
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "[Trigger] triggerCreateInstallmentWorkflow fail",
			"error", err, "zalopayID", purchaseOrder.AccountInfo.ZalopayID, "payment_id", purchaseOrder.ID)
		return err
	}

	uc.log.WithContext(ctx).Infow("msg", "[Trigger] triggerCreateInstallmentWorkflow success",
		"runID", wfRun.GetRunID(), "zalopayID", purchaseOrder.AccountInfo.ZalopayID, "payment_id", purchaseOrder.ID)
	return nil
}
