package purchase

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

func (uc *PurchaseUsecase) PaymentAuthProcess(ctx context.Context, event model.AuthSessionStatusEvent) error {
	payment, err := uc.repo.GetPaymentByPaymentNo(ctx, event.PaymentNo)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "cannot get payment by payment no", "error", err, "payment_no", event.PaymentNo)
		return nil
	}

	authSessionStatus := model.FromStringToAuthSessionStatus(event.Status)
	if !authSessionStatus.IsCompleted() {
		uc.log.WithContext(ctx).Warnw("msg", "skip processing payment auth session status: status is not completed")
		return nil
	}

	if payment.PartnerData.PaymentAuth.AuthSessionID != event.AuthSessionId {
		uc.log.WithContext(ctx).Infow("msg", "skip processing payment auth session status: auth_session_id is invalid",
			"event", event, "auth_session_id", payment.PartnerData.PaymentAuth.AuthSessionID)
		return nil
	}

	payment.PartnerData.PaymentAuth.AuthSessionStatus = authSessionStatus

	if authSessionStatus.IsFailed() {
		uc.log.WithContext(ctx).Warnw("msg", "payment auth session is failed", "event", event)
		_ = uc.handleFailResult(ctx, payment, "payment_auth_status_failed", fmt.Sprintf("payment auth session is failed: %s", authSessionStatus))
		return nil
	}

	_, err = uc.repo.UpdatePayment(ctx, *payment)
	if err != nil {
		uc.log.WithContext(ctx).Errorw("msg", "update purchase fail", "payment", payment, "error", err)
		_ = uc.handleFailResult(ctx, payment, "zlp_update_payment_fail", err.Error())
		return nil
	}

	_ = uc.pushToPurchaseAuthenticateKafka(ctx,
		payment.AccountInfo.PartnerCode,
		cast.ToInt64(payment.AccountInfo.ID),
		payment.ID,
		payment.AccountInfo.ZalopayID,
		payment.Order.TransID)

	return nil
}
