// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: RefundRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/refund.go --package=repository_mocks . RefundRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockRefundRepo is a mock of RefundRepo interface.
type MockRefundRepo struct {
	ctrl     *gomock.Controller
	recorder *MockRefundRepoMockRecorder
	isgomock struct{}
}

// MockRefundRepoMockRecorder is the mock recorder for MockRefundRepo.
type MockRefundRepoMockRecorder struct {
	mock *MockRefundRepo
}

// NewMockRefundRepo creates a new mock instance.
func NewMockRefundRepo(ctrl *gomock.Controller) *MockRefundRepo {
	mock := &MockRefundRepo{ctrl: ctrl}
	mock.recorder = &MockRefundRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRefundRepo) EXPECT() *MockRefundRepoMockRecorder {
	return m.recorder
}

// CompleteRefundLog mocks base method.
func (m *MockRefundRepo) CompleteRefundLog(ctx context.Context, refundOrder *model.RefundOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteRefundLog", ctx, refundOrder)
	ret0, _ := ret[0].(error)
	return ret0
}

// CompleteRefundLog indicates an expected call of CompleteRefundLog.
func (mr *MockRefundRepoMockRecorder) CompleteRefundLog(ctx, refundOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteRefundLog", reflect.TypeOf((*MockRefundRepo)(nil).CompleteRefundLog), ctx, refundOrder)
}

// CreateRefundLog mocks base method.
func (m *MockRefundRepo) CreateRefundLog(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundLog", ctx, refundOrder)
	ret0, _ := ret[0].(*model.RefundOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundLog indicates an expected call of CreateRefundLog.
func (mr *MockRefundRepoMockRecorder) CreateRefundLog(ctx, refundOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundLog", reflect.TypeOf((*MockRefundRepo)(nil).CreateRefundLog), ctx, refundOrder)
}

// CreateRefundSettle mocks base method.
func (m *MockRefundRepo) CreateRefundSettle(ctx context.Context, refundSettle *model.RefundSettle) (*model.RefundSettle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundSettle", ctx, refundSettle)
	ret0, _ := ret[0].(*model.RefundSettle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundSettle indicates an expected call of CreateRefundSettle.
func (mr *MockRefundRepoMockRecorder) CreateRefundSettle(ctx, refundSettle any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundSettle", reflect.TypeOf((*MockRefundRepo)(nil).CreateRefundSettle), ctx, refundSettle)
}

// GetListRefundSettleByStatus mocks base method.
func (m *MockRefundRepo) GetListRefundSettleByStatus(ctx context.Context, status model.RefundSettleStatus, paging *model.Pagination) ([]*model.RefundSettle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetListRefundSettleByStatus", ctx, status, paging)
	ret0, _ := ret[0].([]*model.RefundSettle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetListRefundSettleByStatus indicates an expected call of GetListRefundSettleByStatus.
func (mr *MockRefundRepoMockRecorder) GetListRefundSettleByStatus(ctx, status, paging any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListRefundSettleByStatus", reflect.TypeOf((*MockRefundRepo)(nil).GetListRefundSettleByStatus), ctx, status, paging)
}

// GetRefundLogByRefundID mocks base method.
func (m *MockRefundRepo) GetRefundLogByRefundID(ctx context.Context, refundID int64) (*model.RefundOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundLogByRefundID", ctx, refundID)
	ret0, _ := ret[0].(*model.RefundOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundLogByRefundID indicates an expected call of GetRefundLogByRefundID.
func (mr *MockRefundRepoMockRecorder) GetRefundLogByRefundID(ctx, refundID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundLogByRefundID", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundLogByRefundID), ctx, refundID)
}

// GetRefundLogForUpdate mocks base method.
func (m *MockRefundRepo) GetRefundLogForUpdate(ctx context.Context, id int64) (*model.RefundOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundLogForUpdate", ctx, id)
	ret0, _ := ret[0].(*model.RefundOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundLogForUpdate indicates an expected call of GetRefundLogForUpdate.
func (mr *MockRefundRepoMockRecorder) GetRefundLogForUpdate(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundLogForUpdate", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundLogForUpdate), ctx, id)
}

// GetRefundLogsByZPTransID mocks base method.
func (m *MockRefundRepo) GetRefundLogsByZPTransID(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundLogsByZPTransID", ctx, zpTransID)
	ret0, _ := ret[0].([]*model.RefundOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundLogsByZPTransID indicates an expected call of GetRefundLogsByZPTransID.
func (mr *MockRefundRepoMockRecorder) GetRefundLogsByZPTransID(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundLogsByZPTransID", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundLogsByZPTransID), ctx, zpTransID)
}

// GetRefundLogsExpiredByZPTransIDForUpdate mocks base method.
func (m *MockRefundRepo) GetRefundLogsExpiredByZPTransIDForUpdate(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundLogsExpiredByZPTransIDForUpdate", ctx, zpTransID)
	ret0, _ := ret[0].([]*model.RefundOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundLogsExpiredByZPTransIDForUpdate indicates an expected call of GetRefundLogsExpiredByZPTransIDForUpdate.
func (mr *MockRefundRepoMockRecorder) GetRefundLogsExpiredByZPTransIDForUpdate(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundLogsExpiredByZPTransIDForUpdate", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundLogsExpiredByZPTransIDForUpdate), ctx, zpTransID)
}

// GetRefundLogsForSettlement mocks base method.
func (m *MockRefundRepo) GetRefundLogsForSettlement(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundLogsForSettlement", ctx, zpTransID)
	ret0, _ := ret[0].([]*model.RefundOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundLogsForSettlement indicates an expected call of GetRefundLogsForSettlement.
func (mr *MockRefundRepoMockRecorder) GetRefundLogsForSettlement(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundLogsForSettlement", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundLogsForSettlement), ctx, zpTransID)
}

// GetRefundSettleByID mocks base method.
func (m *MockRefundRepo) GetRefundSettleByID(ctx context.Context, id int64) (*model.RefundSettle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleByID", ctx, id)
	ret0, _ := ret[0].(*model.RefundSettle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleByID indicates an expected call of GetRefundSettleByID.
func (mr *MockRefundRepoMockRecorder) GetRefundSettleByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleByID", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundSettleByID), ctx, id)
}

// GetRefundSettleByZPTransID mocks base method.
func (m *MockRefundRepo) GetRefundSettleByZPTransID(ctx context.Context, zpTransID int64) (*model.RefundSettle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleByZPTransID", ctx, zpTransID)
	ret0, _ := ret[0].(*model.RefundSettle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleByZPTransID indicates an expected call of GetRefundSettleByZPTransID.
func (mr *MockRefundRepoMockRecorder) GetRefundSettleByZPTransID(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleByZPTransID", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundSettleByZPTransID), ctx, zpTransID)
}

// GetRefundSettleForUpdate mocks base method.
func (m *MockRefundRepo) GetRefundSettleForUpdate(ctx context.Context, id int64) (*model.RefundSettle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleForUpdate", ctx, id)
	ret0, _ := ret[0].(*model.RefundSettle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleForUpdate indicates an expected call of GetRefundSettleForUpdate.
func (mr *MockRefundRepoMockRecorder) GetRefundSettleForUpdate(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleForUpdate", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundSettleForUpdate), ctx, id)
}

// GetRefundSettleIDsHasExpiredItem mocks base method.
func (m *MockRefundRepo) GetRefundSettleIDsHasExpiredItem(ctx context.Context, paging *model.Pagination) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleIDsHasExpiredItem", ctx, paging)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleIDsHasExpiredItem indicates an expected call of GetRefundSettleIDsHasExpiredItem.
func (mr *MockRefundRepoMockRecorder) GetRefundSettleIDsHasExpiredItem(ctx, paging any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleIDsHasExpiredItem", reflect.TypeOf((*MockRefundRepo)(nil).GetRefundSettleIDsHasExpiredItem), ctx, paging)
}

// MarkRefundLogsAsExpiredByIDs mocks base method.
func (m *MockRefundRepo) MarkRefundLogsAsExpiredByIDs(ctx context.Context, ids []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkRefundLogsAsExpiredByIDs", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkRefundLogsAsExpiredByIDs indicates an expected call of MarkRefundLogsAsExpiredByIDs.
func (mr *MockRefundRepoMockRecorder) MarkRefundLogsAsExpiredByIDs(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkRefundLogsAsExpiredByIDs", reflect.TypeOf((*MockRefundRepo)(nil).MarkRefundLogsAsExpiredByIDs), ctx, ids)
}

// UpdateRefundSettleEventData mocks base method.
func (m *MockRefundRepo) UpdateRefundSettleEventData(ctx context.Context, refundSettle *model.RefundSettle, originVersion int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRefundSettleEventData", ctx, refundSettle, originVersion)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRefundSettleEventData indicates an expected call of UpdateRefundSettleEventData.
func (mr *MockRefundRepoMockRecorder) UpdateRefundSettleEventData(ctx, refundSettle, originVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRefundSettleEventData", reflect.TypeOf((*MockRefundRepo)(nil).UpdateRefundSettleEventData), ctx, refundSettle, originVersion)
}

// UpdateRefundSettlePaybackInfo mocks base method.
func (m *MockRefundRepo) UpdateRefundSettlePaybackInfo(ctx context.Context, id, paybackAmount int64, status model.RefundSettleStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRefundSettlePaybackInfo", ctx, id, paybackAmount, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRefundSettlePaybackInfo indicates an expected call of UpdateRefundSettlePaybackInfo.
func (mr *MockRefundRepoMockRecorder) UpdateRefundSettlePaybackInfo(ctx, id, paybackAmount, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRefundSettlePaybackInfo", reflect.TypeOf((*MockRefundRepo)(nil).UpdateRefundSettlePaybackInfo), ctx, id, paybackAmount, status)
}

// UpdateRefundSettleReconAmts mocks base method.
func (m *MockRefundRepo) UpdateRefundSettleReconAmts(ctx context.Context, refundSettle *model.RefundSettle) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRefundSettleReconAmts", ctx, refundSettle)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRefundSettleReconAmts indicates an expected call of UpdateRefundSettleReconAmts.
func (mr *MockRefundRepoMockRecorder) UpdateRefundSettleReconAmts(ctx, refundSettle any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRefundSettleReconAmts", reflect.TypeOf((*MockRefundRepo)(nil).UpdateRefundSettleReconAmts), ctx, refundSettle)
}

// UpdateRefundSettleStatusByID mocks base method.
func (m *MockRefundRepo) UpdateRefundSettleStatusByID(ctx context.Context, id int64, status model.RefundSettleStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRefundSettleStatusByID", ctx, id, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRefundSettleStatusByID indicates an expected call of UpdateRefundSettleStatusByID.
func (mr *MockRefundRepoMockRecorder) UpdateRefundSettleStatusByID(ctx, id, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRefundSettleStatusByID", reflect.TypeOf((*MockRefundRepo)(nil).UpdateRefundSettleStatusByID), ctx, id, status)
}

// UpdateRefundSettlementAmount mocks base method.
func (m *MockRefundRepo) UpdateRefundSettlementAmount(ctx context.Context, id, amount int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRefundSettlementAmount", ctx, id, amount)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRefundSettlementAmount indicates an expected call of UpdateRefundSettlementAmount.
func (mr *MockRefundRepoMockRecorder) UpdateRefundSettlementAmount(ctx, id, amount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRefundSettlementAmount", reflect.TypeOf((*MockRefundRepo)(nil).UpdateRefundSettlementAmount), ctx, id, amount)
}

// UpdateRefundTopupsAmount mocks base method.
func (m *MockRefundRepo) UpdateRefundTopupsAmount(ctx context.Context, id, topupsAmount int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRefundTopupsAmount", ctx, id, topupsAmount)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRefundTopupsAmount indicates an expected call of UpdateRefundTopupsAmount.
func (mr *MockRefundRepoMockRecorder) UpdateRefundTopupsAmount(ctx, id, topupsAmount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRefundTopupsAmount", reflect.TypeOf((*MockRefundRepo)(nil).UpdateRefundTopupsAmount), ctx, id, topupsAmount)
}
