// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: OrderRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/order.go --package=repository_mocks . OrderRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockOrderRepo is a mock of OrderRepo interface.
type MockOrderRepo struct {
	ctrl     *gomock.Controller
	recorder *MockOrderRepoMockRecorder
	isgomock struct{}
}

// MockOrderRepoMockRecorder is the mock recorder for MockOrderRepo.
type MockOrderRepoMockRecorder struct {
	mock *MockOrderRepo
}

// NewMockOrderRepo creates a new mock instance.
func NewMockOrderRepo(ctrl *gomock.Controller) *MockOrderRepo {
	mock := &MockOrderRepo{ctrl: ctrl}
	mock.recorder = &MockOrderRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderRepo) EXPECT() *MockOrderRepoMockRecorder {
	return m.recorder
}

// CreateRefundFundbackOrder mocks base method.
func (m *MockOrderRepo) CreateRefundFundbackOrder(ctx context.Context, fundbackOrder *model.RefundFundbackOrder) (*model.RefundFundbackOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundFundbackOrder", ctx, fundbackOrder)
	ret0, _ := ret[0].(*model.RefundFundbackOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundFundbackOrder indicates an expected call of CreateRefundFundbackOrder.
func (mr *MockOrderRepoMockRecorder) CreateRefundFundbackOrder(ctx, fundbackOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundFundbackOrder", reflect.TypeOf((*MockOrderRepo)(nil).CreateRefundFundbackOrder), ctx, fundbackOrder)
}

// CreateRefundSettleOrder mocks base method.
func (m *MockOrderRepo) CreateRefundSettleOrder(ctx context.Context, settleOrder *model.RefundSettleOrder) (*model.RefundSettleOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundSettleOrder", ctx, settleOrder)
	ret0, _ := ret[0].(*model.RefundSettleOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundSettleOrder indicates an expected call of CreateRefundSettleOrder.
func (mr *MockOrderRepoMockRecorder) CreateRefundSettleOrder(ctx, settleOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundSettleOrder", reflect.TypeOf((*MockOrderRepo)(nil).CreateRefundSettleOrder), ctx, settleOrder)
}

// CreateRefundTopupOrder mocks base method.
func (m *MockOrderRepo) CreateRefundTopupOrder(ctx context.Context, topupOrder *model.RefundTopupOrder) (*model.RefundTopupOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundTopupOrder", ctx, topupOrder)
	ret0, _ := ret[0].(*model.RefundTopupOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundTopupOrder indicates an expected call of CreateRefundTopupOrder.
func (mr *MockOrderRepoMockRecorder) CreateRefundTopupOrder(ctx, topupOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundTopupOrder", reflect.TypeOf((*MockOrderRepo)(nil).CreateRefundTopupOrder), ctx, topupOrder)
}

// CreateRepayOrder mocks base method.
func (m *MockOrderRepo) CreateRepayOrder(ctx context.Context, repayOrder model.RepayOrder) (*model.RepayOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRepayOrder", ctx, repayOrder)
	ret0, _ := ret[0].(*model.RepayOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRepayOrder indicates an expected call of CreateRepayOrder.
func (mr *MockOrderRepoMockRecorder) CreateRepayOrder(ctx, repayOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRepayOrder", reflect.TypeOf((*MockOrderRepo)(nil).CreateRepayOrder), ctx, repayOrder)
}

// GetRefundFundbackOrder mocks base method.
func (m *MockOrderRepo) GetRefundFundbackOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundFundbackOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundFundbackOrder", ctx, appTransID, appID)
	ret0, _ := ret[0].(*model.RefundFundbackOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundFundbackOrder indicates an expected call of GetRefundFundbackOrder.
func (mr *MockOrderRepoMockRecorder) GetRefundFundbackOrder(ctx, appTransID, appID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundFundbackOrder", reflect.TypeOf((*MockOrderRepo)(nil).GetRefundFundbackOrder), ctx, appTransID, appID)
}

// GetRefundSettleOrder mocks base method.
func (m *MockOrderRepo) GetRefundSettleOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundSettleOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleOrder", ctx, appTransID, appID)
	ret0, _ := ret[0].(*model.RefundSettleOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleOrder indicates an expected call of GetRefundSettleOrder.
func (mr *MockOrderRepoMockRecorder) GetRefundSettleOrder(ctx, appTransID, appID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleOrder", reflect.TypeOf((*MockOrderRepo)(nil).GetRefundSettleOrder), ctx, appTransID, appID)
}

// GetRefundSettleOrderByID mocks base method.
func (m *MockOrderRepo) GetRefundSettleOrderByID(ctx context.Context, orderID int64) (*model.RefundSettleOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleOrderByID", ctx, orderID)
	ret0, _ := ret[0].(*model.RefundSettleOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleOrderByID indicates an expected call of GetRefundSettleOrderByID.
func (mr *MockOrderRepoMockRecorder) GetRefundSettleOrderByID(ctx, orderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleOrderByID", reflect.TypeOf((*MockOrderRepo)(nil).GetRefundSettleOrderByID), ctx, orderID)
}

// GetRefundSettleOrderByRefundID mocks base method.
func (m *MockOrderRepo) GetRefundSettleOrderByRefundID(ctx context.Context, refundID int64) (*model.RefundSettleOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundSettleOrderByRefundID", ctx, refundID)
	ret0, _ := ret[0].(*model.RefundSettleOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundSettleOrderByRefundID indicates an expected call of GetRefundSettleOrderByRefundID.
func (mr *MockOrderRepoMockRecorder) GetRefundSettleOrderByRefundID(ctx, refundID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundSettleOrderByRefundID", reflect.TypeOf((*MockOrderRepo)(nil).GetRefundSettleOrderByRefundID), ctx, refundID)
}

// GetRefundTopupOrder mocks base method.
func (m *MockOrderRepo) GetRefundTopupOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundTopupOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefundTopupOrder", ctx, appTransID, appID)
	ret0, _ := ret[0].(*model.RefundTopupOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefundTopupOrder indicates an expected call of GetRefundTopupOrder.
func (mr *MockOrderRepoMockRecorder) GetRefundTopupOrder(ctx, appTransID, appID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefundTopupOrder", reflect.TypeOf((*MockOrderRepo)(nil).GetRefundTopupOrder), ctx, appTransID, appID)
}

// GetRepayOrder mocks base method.
func (m *MockOrderRepo) GetRepayOrder(ctx context.Context, appTransID string, appID int32, orderType model.OrderType) (*model.RepayOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepayOrder", ctx, appTransID, appID, orderType)
	ret0, _ := ret[0].(*model.RepayOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepayOrder indicates an expected call of GetRepayOrder.
func (mr *MockOrderRepoMockRecorder) GetRepayOrder(ctx, appTransID, appID, orderType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepayOrder", reflect.TypeOf((*MockOrderRepo)(nil).GetRepayOrder), ctx, appTransID, appID, orderType)
}

// ListRefundFundbackOrder mocks base method.
func (m *MockOrderRepo) ListRefundFundbackOrder(ctx context.Context, settleID int64) ([]*model.RefundFundbackOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRefundFundbackOrder", ctx, settleID)
	ret0, _ := ret[0].([]*model.RefundFundbackOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRefundFundbackOrder indicates an expected call of ListRefundFundbackOrder.
func (mr *MockOrderRepoMockRecorder) ListRefundFundbackOrder(ctx, settleID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRefundFundbackOrder", reflect.TypeOf((*MockOrderRepo)(nil).ListRefundFundbackOrder), ctx, settleID)
}

// ListRefundSettleOrder mocks base method.
func (m *MockOrderRepo) ListRefundSettleOrder(ctx context.Context, settleID int64) ([]*model.RefundSettleOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRefundSettleOrder", ctx, settleID)
	ret0, _ := ret[0].([]*model.RefundSettleOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRefundSettleOrder indicates an expected call of ListRefundSettleOrder.
func (mr *MockOrderRepoMockRecorder) ListRefundSettleOrder(ctx, settleID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRefundSettleOrder", reflect.TypeOf((*MockOrderRepo)(nil).ListRefundSettleOrder), ctx, settleID)
}

// ListRefundTopupOrder mocks base method.
func (m *MockOrderRepo) ListRefundTopupOrder(ctx context.Context, settleID int64) ([]*model.RefundTopupOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRefundTopupOrder", ctx, settleID)
	ret0, _ := ret[0].([]*model.RefundTopupOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRefundTopupOrder indicates an expected call of ListRefundTopupOrder.
func (mr *MockOrderRepoMockRecorder) ListRefundTopupOrder(ctx, settleID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRefundTopupOrder", reflect.TypeOf((*MockOrderRepo)(nil).ListRefundTopupOrder), ctx, settleID)
}

// UpdateOrderProgress mocks base method.
func (m *MockOrderRepo) UpdateOrderProgress(ctx context.Context, params model.OrderProgressUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderProgress", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderProgress indicates an expected call of UpdateOrderProgress.
func (mr *MockOrderRepoMockRecorder) UpdateOrderProgress(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderProgress", reflect.TypeOf((*MockOrderRepo)(nil).UpdateOrderProgress), ctx, params)
}

// UpdateOrderStatus mocks base method.
func (m *MockOrderRepo) UpdateOrderStatus(ctx context.Context, orderID int64, status model.OrderStatus, zpTransID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderStatus", ctx, orderID, status, zpTransID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderStatus indicates an expected call of UpdateOrderStatus.
func (mr *MockOrderRepoMockRecorder) UpdateOrderStatus(ctx, orderID, status, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderStatus", reflect.TypeOf((*MockOrderRepo)(nil).UpdateOrderStatus), ctx, orderID, status, zpTransID)
}
