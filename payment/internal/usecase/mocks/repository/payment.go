// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: PaymentRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/payment.go --package=repository_mocks . PaymentRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockPaymentRepo is a mock of PaymentRepo interface.
type MockPaymentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentRepoMockRecorder
	isgomock struct{}
}

// MockPaymentRepoMockRecorder is the mock recorder for MockPaymentRepo.
type MockPaymentRepoMockRecorder struct {
	mock *MockPaymentRepo
}

// NewMockPaymentRepo creates a new mock instance.
func NewMockPaymentRepo(ctrl *gomock.Controller) *MockPaymentRepo {
	mock := &MockPaymentRepo{ctrl: ctrl}
	mock.recorder = &MockPaymentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentRepo) EXPECT() *MockPaymentRepoMockRecorder {
	return m.recorder
}

// CreateEarlyDischargeLog mocks base method.
func (m *MockPaymentRepo) CreateEarlyDischargeLog(ctx context.Context, dischargeLog model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEarlyDischargeLog", ctx, dischargeLog)
	ret0, _ := ret[0].(*model.EarlyDischargeLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEarlyDischargeLog indicates an expected call of CreateEarlyDischargeLog.
func (mr *MockPaymentRepoMockRecorder) CreateEarlyDischargeLog(ctx, dischargeLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEarlyDischargeLog", reflect.TypeOf((*MockPaymentRepo)(nil).CreateEarlyDischargeLog), ctx, dischargeLog)
}

// CreatePayment mocks base method.
func (m *MockPaymentRepo) CreatePayment(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.PurchaseOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePayment", ctx, purchaseOrder)
	ret0, _ := ret[0].(*model.PurchaseOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePayment indicates an expected call of CreatePayment.
func (mr *MockPaymentRepoMockRecorder) CreatePayment(ctx, purchaseOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePayment", reflect.TypeOf((*MockPaymentRepo)(nil).CreatePayment), ctx, purchaseOrder)
}

// CreateRepaymentLog mocks base method.
func (m *MockPaymentRepo) CreateRepaymentLog(ctx context.Context, repaymentLog model.RepaymentLog) (*model.RepaymentLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRepaymentLog", ctx, repaymentLog)
	ret0, _ := ret[0].(*model.RepaymentLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRepaymentLog indicates an expected call of CreateRepaymentLog.
func (mr *MockPaymentRepoMockRecorder) CreateRepaymentLog(ctx, repaymentLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRepaymentLog", reflect.TypeOf((*MockPaymentRepo)(nil).CreateRepaymentLog), ctx, repaymentLog)
}

// GetBankRoute mocks base method.
func (m *MockPaymentRepo) GetBankRoute(ctx context.Context, partnerCode string, transType model.TransType) (*model.BankRoute, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankRoute", ctx, partnerCode, transType)
	ret0, _ := ret[0].(*model.BankRoute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankRoute indicates an expected call of GetBankRoute.
func (mr *MockPaymentRepoMockRecorder) GetBankRoute(ctx, partnerCode, transType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankRoute", reflect.TypeOf((*MockPaymentRepo)(nil).GetBankRoute), ctx, partnerCode, transType)
}

// GetEarlyDischargeLog mocks base method.
func (m *MockPaymentRepo) GetEarlyDischargeLog(ctx context.Context, paymentID, zalopayID int64) (*model.EarlyDischargeLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarlyDischargeLog", ctx, paymentID, zalopayID)
	ret0, _ := ret[0].(*model.EarlyDischargeLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischargeLog indicates an expected call of GetEarlyDischargeLog.
func (mr *MockPaymentRepoMockRecorder) GetEarlyDischargeLog(ctx, paymentID, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischargeLog", reflect.TypeOf((*MockPaymentRepo)(nil).GetEarlyDischargeLog), ctx, paymentID, zalopayID)
}

// GetEarlyDischargeLogByID mocks base method.
func (m *MockPaymentRepo) GetEarlyDischargeLogByID(ctx context.Context, paymentID int64) (*model.EarlyDischargeLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarlyDischargeLogByID", ctx, paymentID)
	ret0, _ := ret[0].(*model.EarlyDischargeLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischargeLogByID indicates an expected call of GetEarlyDischargeLogByID.
func (mr *MockPaymentRepoMockRecorder) GetEarlyDischargeLogByID(ctx, paymentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischargeLogByID", reflect.TypeOf((*MockPaymentRepo)(nil).GetEarlyDischargeLogByID), ctx, paymentID)
}

// GetEarlyDischargeLogByOrderID mocks base method.
func (m *MockPaymentRepo) GetEarlyDischargeLogByOrderID(ctx context.Context, orderID int64) (*model.EarlyDischargeLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarlyDischargeLogByOrderID", ctx, orderID)
	ret0, _ := ret[0].(*model.EarlyDischargeLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischargeLogByOrderID indicates an expected call of GetEarlyDischargeLogByOrderID.
func (mr *MockPaymentRepoMockRecorder) GetEarlyDischargeLogByOrderID(ctx, orderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischargeLogByOrderID", reflect.TypeOf((*MockPaymentRepo)(nil).GetEarlyDischargeLogByOrderID), ctx, orderID)
}

// GetPayment mocks base method.
func (m *MockPaymentRepo) GetPayment(ctx context.Context, purchaseOrderID, zalopayID int64) (*model.PurchaseOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayment", ctx, purchaseOrderID, zalopayID)
	ret0, _ := ret[0].(*model.PurchaseOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayment indicates an expected call of GetPayment.
func (mr *MockPaymentRepoMockRecorder) GetPayment(ctx, purchaseOrderID, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayment", reflect.TypeOf((*MockPaymentRepo)(nil).GetPayment), ctx, purchaseOrderID, zalopayID)
}

// GetPaymentByPaymentNo mocks base method.
func (m *MockPaymentRepo) GetPaymentByPaymentNo(ctx context.Context, paymentNo string) (*model.PurchaseOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentByPaymentNo", ctx, paymentNo)
	ret0, _ := ret[0].(*model.PurchaseOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentByPaymentNo indicates an expected call of GetPaymentByPaymentNo.
func (mr *MockPaymentRepoMockRecorder) GetPaymentByPaymentNo(ctx, paymentNo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentByPaymentNo", reflect.TypeOf((*MockPaymentRepo)(nil).GetPaymentByPaymentNo), ctx, paymentNo)
}

// GetPaymentByTransID mocks base method.
func (m *MockPaymentRepo) GetPaymentByTransID(ctx context.Context, transID int64) (*model.PurchaseOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentByTransID", ctx, transID)
	ret0, _ := ret[0].(*model.PurchaseOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentByTransID indicates an expected call of GetPaymentByTransID.
func (mr *MockPaymentRepoMockRecorder) GetPaymentByTransID(ctx, transID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentByTransID", reflect.TypeOf((*MockPaymentRepo)(nil).GetPaymentByTransID), ctx, transID)
}

// GetRepaymentByID mocks base method.
func (m *MockPaymentRepo) GetRepaymentByID(ctx context.Context, paymentID int64) (*model.RepaymentLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepaymentByID", ctx, paymentID)
	ret0, _ := ret[0].(*model.RepaymentLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepaymentByID indicates an expected call of GetRepaymentByID.
func (mr *MockPaymentRepoMockRecorder) GetRepaymentByID(ctx, paymentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepaymentByID", reflect.TypeOf((*MockPaymentRepo)(nil).GetRepaymentByID), ctx, paymentID)
}

// GetRepaymentByOrderID mocks base method.
func (m *MockPaymentRepo) GetRepaymentByOrderID(ctx context.Context, orderID int64) (*model.RepaymentLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepaymentByOrderID", ctx, orderID)
	ret0, _ := ret[0].(*model.RepaymentLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepaymentByOrderID indicates an expected call of GetRepaymentByOrderID.
func (mr *MockPaymentRepoMockRecorder) GetRepaymentByOrderID(ctx, orderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepaymentByOrderID", reflect.TypeOf((*MockPaymentRepo)(nil).GetRepaymentByOrderID), ctx, orderID)
}

// GetTransactionByZPTransID mocks base method.
func (m *MockPaymentRepo) GetTransactionByZPTransID(ctx context.Context, zpTransID int64) (*model.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionByZPTransID", ctx, zpTransID)
	ret0, _ := ret[0].(*model.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionByZPTransID indicates an expected call of GetTransactionByZPTransID.
func (mr *MockPaymentRepoMockRecorder) GetTransactionByZPTransID(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByZPTransID", reflect.TypeOf((*MockPaymentRepo)(nil).GetTransactionByZPTransID), ctx, zpTransID)
}

// ListPaymentAdvance mocks base method.
func (m *MockPaymentRepo) ListPaymentAdvance(ctx context.Context, params *model.ListPaymentQuery) ([]*model.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPaymentAdvance", ctx, params)
	ret0, _ := ret[0].([]*model.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPaymentAdvance indicates an expected call of ListPaymentAdvance.
func (mr *MockPaymentRepoMockRecorder) ListPaymentAdvance(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPaymentAdvance", reflect.TypeOf((*MockPaymentRepo)(nil).ListPaymentAdvance), ctx, params)
}

// UpdateEarlyDischargeLog mocks base method.
func (m *MockPaymentRepo) UpdateEarlyDischargeLog(ctx context.Context, dischargeLog model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEarlyDischargeLog", ctx, dischargeLog)
	ret0, _ := ret[0].(*model.EarlyDischargeLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEarlyDischargeLog indicates an expected call of UpdateEarlyDischargeLog.
func (mr *MockPaymentRepoMockRecorder) UpdateEarlyDischargeLog(ctx, dischargeLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEarlyDischargeLog", reflect.TypeOf((*MockPaymentRepo)(nil).UpdateEarlyDischargeLog), ctx, dischargeLog)
}

// UpdateEarlyDischargeLogStatus mocks base method.
func (m *MockPaymentRepo) UpdateEarlyDischargeLogStatus(ctx context.Context, paymentID int64, dischargeLog model.EarlyDischargeLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEarlyDischargeLogStatus", ctx, paymentID, dischargeLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEarlyDischargeLogStatus indicates an expected call of UpdateEarlyDischargeLogStatus.
func (mr *MockPaymentRepoMockRecorder) UpdateEarlyDischargeLogStatus(ctx, paymentID, dischargeLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEarlyDischargeLogStatus", reflect.TypeOf((*MockPaymentRepo)(nil).UpdateEarlyDischargeLogStatus), ctx, paymentID, dischargeLog)
}

// UpdatePartnerTransID mocks base method.
func (m *MockPaymentRepo) UpdatePartnerTransID(ctx context.Context, paymentID int64, partnerTransID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartnerTransID", ctx, paymentID, partnerTransID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartnerTransID indicates an expected call of UpdatePartnerTransID.
func (mr *MockPaymentRepoMockRecorder) UpdatePartnerTransID(ctx, paymentID, partnerTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartnerTransID", reflect.TypeOf((*MockPaymentRepo)(nil).UpdatePartnerTransID), ctx, paymentID, partnerTransID)
}

// UpdatePayment mocks base method.
func (m *MockPaymentRepo) UpdatePayment(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.PurchaseOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePayment", ctx, purchaseOrder)
	ret0, _ := ret[0].(*model.PurchaseOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePayment indicates an expected call of UpdatePayment.
func (mr *MockPaymentRepoMockRecorder) UpdatePayment(ctx, purchaseOrder any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePayment", reflect.TypeOf((*MockPaymentRepo)(nil).UpdatePayment), ctx, purchaseOrder)
}

// UpdatePaymentCommission mocks base method.
func (m *MockPaymentRepo) UpdatePaymentCommission(ctx context.Context, zpTransID int64, isCommission bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentCommission", ctx, zpTransID, isCommission)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePaymentCommission indicates an expected call of UpdatePaymentCommission.
func (mr *MockPaymentRepoMockRecorder) UpdatePaymentCommission(ctx, zpTransID, isCommission any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentCommission", reflect.TypeOf((*MockPaymentRepo)(nil).UpdatePaymentCommission), ctx, zpTransID, isCommission)
}

// UpdatePaymentLogOrderIDs mocks base method.
func (m *MockPaymentRepo) UpdatePaymentLogOrderIDs(ctx context.Context, paymentID int64, orderInfo model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentLogOrderIDs", ctx, paymentID, orderInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePaymentLogOrderIDs indicates an expected call of UpdatePaymentLogOrderIDs.
func (mr *MockPaymentRepoMockRecorder) UpdatePaymentLogOrderIDs(ctx, paymentID, orderInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentLogOrderIDs", reflect.TypeOf((*MockPaymentRepo)(nil).UpdatePaymentLogOrderIDs), ctx, paymentID, orderInfo)
}

// UpdateRepaymentLogStatus mocks base method.
func (m *MockPaymentRepo) UpdateRepaymentLogStatus(ctx context.Context, repaymentLog model.RepaymentLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRepaymentLogStatus", ctx, repaymentLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRepaymentLogStatus indicates an expected call of UpdateRepaymentLogStatus.
func (mr *MockPaymentRepoMockRecorder) UpdateRepaymentLogStatus(ctx, repaymentLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRepaymentLogStatus", reflect.TypeOf((*MockPaymentRepo)(nil).UpdateRepaymentLogStatus), ctx, repaymentLog)
}
