// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: Transaction)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/transaction.go --package=repository_mocks . Transaction
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockTransaction is a mock of Transaction interface.
type MockTransaction struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionMockRecorder
	isgomock struct{}
}

// MockTransactionMockRecorder is the mock recorder for MockTransaction.
type MockTransactionMockRecorder struct {
	mock *MockTransaction
}

// NewMockTransaction creates a new mock instance.
func NewMockTransaction(ctrl *gomock.Controller) *MockTransaction {
	mock := &MockTransaction{ctrl: ctrl}
	mock.recorder = &MockTransactionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransaction) EXPECT() *MockTransactionMockRecorder {
	return m.recorder
}

// BeginOrReuseTx mocks base method.
func (m *MockTransaction) BeginOrReuseTx(ctx context.Context) (context.Context, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginOrReuseTx", ctx)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginOrReuseTx indicates an expected call of BeginOrReuseTx.
func (mr *MockTransactionMockRecorder) BeginOrReuseTx(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginOrReuseTx", reflect.TypeOf((*MockTransaction)(nil).BeginOrReuseTx), ctx)
}

// BeginTx mocks base method.
func (m *MockTransaction) BeginTx(ctx context.Context) (context.Context, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTx", ctx)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginTx indicates an expected call of BeginTx.
func (mr *MockTransactionMockRecorder) BeginTx(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTx", reflect.TypeOf((*MockTransaction)(nil).BeginTx), ctx)
}

// BeginTxReadCommited mocks base method.
func (m *MockTransaction) BeginTxReadCommited(ctx context.Context) (context.Context, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTxReadCommited", ctx)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginTxReadCommited indicates an expected call of BeginTxReadCommited.
func (mr *MockTransactionMockRecorder) BeginTxReadCommited(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTxReadCommited", reflect.TypeOf((*MockTransaction)(nil).BeginTxReadCommited), ctx)
}

// BeginTxRepeatableRead mocks base method.
func (m *MockTransaction) BeginTxRepeatableRead(ctx context.Context) (context.Context, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTxRepeatableRead", ctx)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BeginTxRepeatableRead indicates an expected call of BeginTxRepeatableRead.
func (mr *MockTransactionMockRecorder) BeginTxRepeatableRead(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTxRepeatableRead", reflect.TypeOf((*MockTransaction)(nil).BeginTxRepeatableRead), ctx)
}

// CommitTx mocks base method.
func (m *MockTransaction) CommitTx(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitTx", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// CommitTx indicates an expected call of CommitTx.
func (mr *MockTransactionMockRecorder) CommitTx(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitTx", reflect.TypeOf((*MockTransaction)(nil).CommitTx), ctx)
}

// IsInTx mocks base method.
func (m *MockTransaction) IsInTx(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsInTx", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsInTx indicates an expected call of IsInTx.
func (mr *MockTransactionMockRecorder) IsInTx(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsInTx", reflect.TypeOf((*MockTransaction)(nil).IsInTx), ctx)
}

// IsTxActive mocks base method.
func (m *MockTransaction) IsTxActive(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTxActive", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsTxActive indicates an expected call of IsTxActive.
func (mr *MockTransactionMockRecorder) IsTxActive(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTxActive", reflect.TypeOf((*MockTransaction)(nil).IsTxActive), ctx)
}

// RollbackTx mocks base method.
func (m *MockTransaction) RollbackTx(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RollbackTx", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// RollbackTx indicates an expected call of RollbackTx.
func (mr *MockTransactionMockRecorder) RollbackTx(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollbackTx", reflect.TypeOf((*MockTransaction)(nil).RollbackTx), ctx)
}

// WithTx mocks base method.
func (m *MockTransaction) WithTx(ctx context.Context, exec func(context.Context) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", ctx, exec)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockTransactionMockRecorder) WithTx(ctx, exec any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockTransaction)(nil).WithTx), ctx, exec)
}
