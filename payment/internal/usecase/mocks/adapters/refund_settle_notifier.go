// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: RefundSettleNotifier)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/refund_settle_notifier.go --package=adapter_mocks . RefundSettleNotifier
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockRefundSettleNotifier is a mock of RefundSettleNotifier interface.
type MockRefundSettleNotifier struct {
	ctrl     *gomock.Controller
	recorder *MockRefundSettleNotifierMockRecorder
	isgomock struct{}
}

// MockRefundSettleNotifierMockRecorder is the mock recorder for MockRefundSettleNotifier.
type MockRefundSettleNotifierMockRecorder struct {
	mock *MockRefundSettleNotifier
}

// NewMockRefundSettleNotifier creates a new mock instance.
func NewMockRefundSettleNotifier(ctrl *gomock.Controller) *MockRefundSettleNotifier {
	mock := &MockRefundSettleNotifier{ctrl: ctrl}
	mock.recorder = &MockRefundSettleNotifierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRefundSettleNotifier) EXPECT() *MockRefundSettleNotifierMockRecorder {
	return m.recorder
}

// PublishRefundExpiredEvents mocks base method.
func (m *MockRefundSettleNotifier) PublishRefundExpiredEvents(ctx context.Context, refZPTransID int64, refunds model.RefundOrders) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishRefundExpiredEvents", ctx, refZPTransID, refunds)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRefundExpiredEvents indicates an expected call of PublishRefundExpiredEvents.
func (mr *MockRefundSettleNotifierMockRecorder) PublishRefundExpiredEvents(ctx, refZPTransID, refunds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRefundExpiredEvents", reflect.TypeOf((*MockRefundSettleNotifier)(nil).PublishRefundExpiredEvents), ctx, refZPTransID, refunds)
}

// PublishRefundExpiredResult mocks base method.
func (m *MockRefundSettleNotifier) PublishRefundExpiredResult(ctx context.Context, settleOrder *model.RefundSettleOrder, repayLog *model.RepaymentLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishRefundExpiredResult", ctx, settleOrder, repayLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRefundExpiredResult indicates an expected call of PublishRefundExpiredResult.
func (mr *MockRefundSettleNotifierMockRecorder) PublishRefundExpiredResult(ctx, settleOrder, repayLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRefundExpiredResult", reflect.TypeOf((*MockRefundSettleNotifier)(nil).PublishRefundExpiredResult), ctx, settleOrder, repayLog)
}

// PublishRefundSettleEvent mocks base method.
func (m *MockRefundSettleNotifier) PublishRefundSettleEvent(ctx context.Context, data *model.RefundSettle) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishRefundSettleEvent", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRefundSettleEvent indicates an expected call of PublishRefundSettleEvent.
func (mr *MockRefundSettleNotifierMockRecorder) PublishRefundSettleEvent(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRefundSettleEvent", reflect.TypeOf((*MockRefundSettleNotifier)(nil).PublishRefundSettleEvent), ctx, data)
}

// PublishRefundSettleResult mocks base method.
func (m *MockRefundSettleNotifier) PublishRefundSettleResult(ctx context.Context, settle *model.RefundSettle, eadLog *model.EarlyDischargeLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishRefundSettleResult", ctx, settle, eadLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRefundSettleResult indicates an expected call of PublishRefundSettleResult.
func (mr *MockRefundSettleNotifierMockRecorder) PublishRefundSettleResult(ctx, settle, eadLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRefundSettleResult", reflect.TypeOf((*MockRefundSettleNotifier)(nil).PublishRefundSettleResult), ctx, settle, eadLog)
}
