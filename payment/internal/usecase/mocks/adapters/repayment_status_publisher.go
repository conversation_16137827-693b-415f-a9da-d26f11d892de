// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: PEUpdateExchangeStatusPublisher)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/repayment_status_publisher.go --package=adapter_mocks . PEUpdateExchangeStatusPublisher
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	reflect "reflect"

	proto "github.com/gogo/protobuf/proto"
	gomock "go.uber.org/mock/gomock"
	context "golang.org/x/net/context"
	kafka_client "zalopay.io/zgo/kafka-client"
)

// MockPEUpdateExchangeStatusPublisher is a mock of PEUpdateExchangeStatusPublisher interface.
type MockPEUpdateExchangeStatusPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockPEUpdateExchangeStatusPublisherMockRecorder
	isgomock struct{}
}

// MockPEUpdateExchangeStatusPublisherMockRecorder is the mock recorder for MockPEUpdateExchangeStatusPublisher.
type MockPEUpdateExchangeStatusPublisherMockRecorder struct {
	mock *MockPEUpdateExchangeStatusPublisher
}

// NewMockPEUpdateExchangeStatusPublisher creates a new mock instance.
func NewMockPEUpdateExchangeStatusPublisher(ctrl *gomock.Controller) *MockPEUpdateExchangeStatusPublisher {
	mock := &MockPEUpdateExchangeStatusPublisher{ctrl: ctrl}
	mock.recorder = &MockPEUpdateExchangeStatusPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPEUpdateExchangeStatusPublisher) EXPECT() *MockPEUpdateExchangeStatusPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockPEUpdateExchangeStatusPublisher) Publish(arg0 context.Context, arg1 string, arg2 proto.Message, arg3 ...kafka_client.Header) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Publish", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockPEUpdateExchangeStatusPublisherMockRecorder) Publish(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockPEUpdateExchangeStatusPublisher)(nil).Publish), varargs...)
}

// PublishRaw mocks base method.
func (m *MockPEUpdateExchangeStatusPublisher) PublishRaw(arg0 context.Context, arg1 string, arg2 []byte, arg3 ...kafka_client.Header) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishRaw", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRaw indicates an expected call of PublishRaw.
func (mr *MockPEUpdateExchangeStatusPublisherMockRecorder) PublishRaw(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRaw", reflect.TypeOf((*MockPEUpdateExchangeStatusPublisher)(nil).PublishRaw), varargs...)
}

// PublishRaws mocks base method.
func (m *MockPEUpdateExchangeStatusPublisher) PublishRaws(ctx context.Context, key string, data ...kafka_client.Source) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range data {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishRaws", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRaws indicates an expected call of PublishRaws.
func (mr *MockPEUpdateExchangeStatusPublisherMockRecorder) PublishRaws(ctx, key any, data ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, data...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRaws", reflect.TypeOf((*MockPEUpdateExchangeStatusPublisher)(nil).PublishRaws), varargs...)
}

// Stop mocks base method.
func (m *MockPEUpdateExchangeStatusPublisher) Stop() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop")
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockPEUpdateExchangeStatusPublisherMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockPEUpdateExchangeStatusPublisher)(nil).Stop))
}
