// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: PurchaseInitiatedPublisher)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/purchase_init_publisher.go --package=adapter_mocks . PurchaseInitiatedPublisher
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	reflect "reflect"

	proto "github.com/gogo/protobuf/proto"
	gomock "go.uber.org/mock/gomock"
	context "golang.org/x/net/context"
	kafka_client "zalopay.io/zgo/kafka-client"
)

// MockPurchaseInitiatedPublisher is a mock of PurchaseInitiatedPublisher interface.
type MockPurchaseInitiatedPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockPurchaseInitiatedPublisherMockRecorder
	isgomock struct{}
}

// MockPurchaseInitiatedPublisherMockRecorder is the mock recorder for MockPurchaseInitiatedPublisher.
type MockPurchaseInitiatedPublisherMockRecorder struct {
	mock *MockPurchaseInitiatedPublisher
}

// NewMockPurchaseInitiatedPublisher creates a new mock instance.
func NewMockPurchaseInitiatedPublisher(ctrl *gomock.Controller) *MockPurchaseInitiatedPublisher {
	mock := &MockPurchaseInitiatedPublisher{ctrl: ctrl}
	mock.recorder = &MockPurchaseInitiatedPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPurchaseInitiatedPublisher) EXPECT() *MockPurchaseInitiatedPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockPurchaseInitiatedPublisher) Publish(arg0 context.Context, arg1 string, arg2 proto.Message, arg3 ...kafka_client.Header) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Publish", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockPurchaseInitiatedPublisherMockRecorder) Publish(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockPurchaseInitiatedPublisher)(nil).Publish), varargs...)
}

// PublishRaw mocks base method.
func (m *MockPurchaseInitiatedPublisher) PublishRaw(arg0 context.Context, arg1 string, arg2 []byte, arg3 ...kafka_client.Header) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishRaw", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRaw indicates an expected call of PublishRaw.
func (mr *MockPurchaseInitiatedPublisherMockRecorder) PublishRaw(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRaw", reflect.TypeOf((*MockPurchaseInitiatedPublisher)(nil).PublishRaw), varargs...)
}

// PublishRaws mocks base method.
func (m *MockPurchaseInitiatedPublisher) PublishRaws(ctx context.Context, key string, data ...kafka_client.Source) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range data {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishRaws", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRaws indicates an expected call of PublishRaws.
func (mr *MockPurchaseInitiatedPublisherMockRecorder) PublishRaws(ctx, key any, data ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, data...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRaws", reflect.TypeOf((*MockPurchaseInitiatedPublisher)(nil).PublishRaws), varargs...)
}

// Stop mocks base method.
func (m *MockPurchaseInitiatedPublisher) Stop() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop")
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockPurchaseInitiatedPublisherMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockPurchaseInitiatedPublisher)(nil).Stop))
}
