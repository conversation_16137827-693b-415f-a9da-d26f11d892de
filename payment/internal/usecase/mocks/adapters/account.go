// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: AccountAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/account.go --package=adapter_mocks . AccountAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockAccountAdapter is a mock of AccountAdapter interface.
type MockAccountAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockAccountAdapterMockRecorder
	isgomock struct{}
}

// MockAccountAdapterMockRecorder is the mock recorder for MockAccountAdapter.
type MockAccountAdapterMockRecorder struct {
	mock *MockAccountAdapter
}

// NewMockAccountAdapter creates a new mock instance.
func NewMockAccountAdapter(ctrl *gomock.Controller) *MockAccountAdapter {
	mock := &MockAccountAdapter{ctrl: ctrl}
	mock.recorder = &MockAccountAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountAdapter) EXPECT() *MockAccountAdapterMockRecorder {
	return m.recorder
}

// GetAccount mocks base method.
func (m *MockAccountAdapter) GetAccount(ctx context.Context, zalopayID int64, partnerCode string) (model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccount", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockAccountAdapterMockRecorder) GetAccount(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockAccountAdapter)(nil).GetAccount), ctx, zalopayID, partnerCode)
}
