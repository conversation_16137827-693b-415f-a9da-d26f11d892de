// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: ZasAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/zas.go --package=adapter_mocks . ZasAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockZasAdapter is a mock of ZasAdapter interface.
type MockZasAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockZasAdapterMockRecorder
	isgomock struct{}
}

// MockZasAdapterMockRecorder is the mock recorder for MockZasAdapter.
type MockZasAdapterMockRecorder struct {
	mock *MockZasAdapter
}

// NewMockZasAdapter creates a new mock instance.
func NewMockZasAdapter(ctrl *gomock.Controller) *MockZasAdapter {
	mock := &MockZasAdapter{ctrl: ctrl}
	mock.recorder = &MockZasAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZasAdapter) EXPECT() *MockZasAdapterMockRecorder {
	return m.recorder
}

// GetBankReceivable mocks base method.
func (m *MockZasAdapter) GetBankReceivable(ctx context.Context, bankAccountNumber string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankReceivable", ctx, bankAccountNumber)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankReceivable indicates an expected call of GetBankReceivable.
func (mr *MockZasAdapterMockRecorder) GetBankReceivable(ctx, bankAccountNumber any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankReceivable", reflect.TypeOf((*MockZasAdapter)(nil).GetBankReceivable), ctx, bankAccountNumber)
}
