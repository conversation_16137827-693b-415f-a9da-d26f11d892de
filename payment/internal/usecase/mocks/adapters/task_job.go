// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: TaskJobAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/task_job.go --package=adapter_mocks . TaskJobAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockTaskJobAdapter is a mock of TaskJobAdapter interface.
type MockTaskJobAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockTaskJobAdapterMockRecorder
	isgomock struct{}
}

// MockTaskJobAdapterMockRecorder is the mock recorder for MockTaskJobAdapter.
type MockTaskJobAdapterMockRecorder struct {
	mock *MockTaskJobAdapter
}

// NewMockTaskJobAdapter creates a new mock instance.
func NewMockTaskJobAdapter(ctrl *gomock.Controller) *MockTaskJobAdapter {
	mock := &MockTaskJobAdapter{ctrl: ctrl}
	mock.recorder = &MockTaskJobAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskJobAdapter) EXPECT() *MockTaskJobAdapterMockRecorder {
	return m.recorder
}

// ExecuteEarlyDischargePollingJob mocks base method.
func (m *MockTaskJobAdapter) ExecuteEarlyDischargePollingJob(ctx context.Context, params *model.EarlyDischargeStatusWorkflowRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteEarlyDischargePollingJob", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteEarlyDischargePollingJob indicates an expected call of ExecuteEarlyDischargePollingJob.
func (mr *MockTaskJobAdapterMockRecorder) ExecuteEarlyDischargePollingJob(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteEarlyDischargePollingJob", reflect.TypeOf((*MockTaskJobAdapter)(nil).ExecuteEarlyDischargePollingJob), ctx, params)
}

// ExecuteExpiredRefundRepaymentPollingJob mocks base method.
func (m *MockTaskJobAdapter) ExecuteExpiredRefundRepaymentPollingJob(ctx context.Context, params *model.RepaymentStatusPollingRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteExpiredRefundRepaymentPollingJob", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteExpiredRefundRepaymentPollingJob indicates an expected call of ExecuteExpiredRefundRepaymentPollingJob.
func (mr *MockTaskJobAdapterMockRecorder) ExecuteExpiredRefundRepaymentPollingJob(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteExpiredRefundRepaymentPollingJob", reflect.TypeOf((*MockTaskJobAdapter)(nil).ExecuteExpiredRefundRepaymentPollingJob), ctx, params)
}

// ExecuteReconcileRefundSettleJob mocks base method.
func (m *MockTaskJobAdapter) ExecuteReconcileRefundSettleJob(ctx context.Context, params *model.RefundSettleReconParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteReconcileRefundSettleJob", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteReconcileRefundSettleJob indicates an expected call of ExecuteReconcileRefundSettleJob.
func (mr *MockTaskJobAdapterMockRecorder) ExecuteReconcileRefundSettleJob(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteReconcileRefundSettleJob", reflect.TypeOf((*MockTaskJobAdapter)(nil).ExecuteReconcileRefundSettleJob), ctx, params)
}
