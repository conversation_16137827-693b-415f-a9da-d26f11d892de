// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: AcquiringCoreAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/acquiring_core.go --package=adapter_mocks . AcquiringCoreAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockAcquiringCoreAdapter is a mock of AcquiringCoreAdapter interface.
type MockAcquiringCoreAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockAcquiringCoreAdapterMockRecorder
	isgomock struct{}
}

// MockAcquiringCoreAdapterMockRecorder is the mock recorder for MockAcquiringCoreAdapter.
type MockAcquiringCoreAdapterMockRecorder struct {
	mock *MockAcquiringCoreAdapter
}

// NewMockAcquiringCoreAdapter creates a new mock instance.
func NewMockAcquiringCoreAdapter(ctrl *gomock.Controller) *MockAcquiringCoreAdapter {
	mock := &MockAcquiringCoreAdapter{ctrl: ctrl}
	mock.recorder = &MockAcquiringCoreAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquiringCoreAdapter) EXPECT() *MockAcquiringCoreAdapterMockRecorder {
	return m.recorder
}

// CreateOrder mocks base method.
func (m *MockAcquiringCoreAdapter) CreateOrder(ctx context.Context, rs model.RepayOrderRequest) (*model.RepayOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", ctx, rs)
	ret0, _ := ret[0].(*model.RepayOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockAcquiringCoreAdapterMockRecorder) CreateOrder(ctx, rs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockAcquiringCoreAdapter)(nil).CreateOrder), ctx, rs)
}

// CreateRefundFundbackOrder mocks base method.
func (m *MockAcquiringCoreAdapter) CreateRefundFundbackOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundFundbackOrder", ctx, params)
	ret0, _ := ret[0].(*model.CreateRefundOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundFundbackOrder indicates an expected call of CreateRefundFundbackOrder.
func (mr *MockAcquiringCoreAdapterMockRecorder) CreateRefundFundbackOrder(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundFundbackOrder", reflect.TypeOf((*MockAcquiringCoreAdapter)(nil).CreateRefundFundbackOrder), ctx, params)
}

// CreateRefundSettleOrder mocks base method.
func (m *MockAcquiringCoreAdapter) CreateRefundSettleOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundSettleOrder", ctx, params)
	ret0, _ := ret[0].(*model.CreateRefundOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundSettleOrder indicates an expected call of CreateRefundSettleOrder.
func (mr *MockAcquiringCoreAdapterMockRecorder) CreateRefundSettleOrder(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundSettleOrder", reflect.TypeOf((*MockAcquiringCoreAdapter)(nil).CreateRefundSettleOrder), ctx, params)
}

// CreateRefundTopupOrder mocks base method.
func (m *MockAcquiringCoreAdapter) CreateRefundTopupOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundTopupOrder", ctx, params)
	ret0, _ := ret[0].(*model.CreateRefundOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundTopupOrder indicates an expected call of CreateRefundTopupOrder.
func (mr *MockAcquiringCoreAdapterMockRecorder) CreateRefundTopupOrder(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundTopupOrder", reflect.TypeOf((*MockAcquiringCoreAdapter)(nil).CreateRefundTopupOrder), ctx, params)
}

// SubmitRefundFundbackOrder mocks base method.
func (m *MockAcquiringCoreAdapter) SubmitRefundFundbackOrder(ctx context.Context, order *model.RefundFundbackOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitRefundFundbackOrder", ctx, order)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitRefundFundbackOrder indicates an expected call of SubmitRefundFundbackOrder.
func (mr *MockAcquiringCoreAdapterMockRecorder) SubmitRefundFundbackOrder(ctx, order any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitRefundFundbackOrder", reflect.TypeOf((*MockAcquiringCoreAdapter)(nil).SubmitRefundFundbackOrder), ctx, order)
}

// SubmitRefundSettleOrder mocks base method.
func (m *MockAcquiringCoreAdapter) SubmitRefundSettleOrder(ctx context.Context, order *model.RefundSettleOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitRefundSettleOrder", ctx, order)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitRefundSettleOrder indicates an expected call of SubmitRefundSettleOrder.
func (mr *MockAcquiringCoreAdapterMockRecorder) SubmitRefundSettleOrder(ctx, order any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitRefundSettleOrder", reflect.TypeOf((*MockAcquiringCoreAdapter)(nil).SubmitRefundSettleOrder), ctx, order)
}
