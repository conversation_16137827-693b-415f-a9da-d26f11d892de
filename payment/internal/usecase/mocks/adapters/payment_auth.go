// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: PaymentAuthAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/payment_auth.go --package=adapter_mocks . PaymentAuthAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	payment_auth "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth"
	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockPaymentAuthAdapter is a mock of PaymentAuthAdapter interface.
type MockPaymentAuthAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentAuthAdapterMockRecorder
	isgomock struct{}
}

// MockPaymentAuthAdapterMockRecorder is the mock recorder for MockPaymentAuthAdapter.
type MockPaymentAuthAdapterMockRecorder struct {
	mock *MockPaymentAuthAdapter
}

// NewMockPaymentAuthAdapter creates a new mock instance.
func NewMockPaymentAuthAdapter(ctrl *gomock.Controller) *MockPaymentAuthAdapter {
	mock := &MockPaymentAuthAdapter{ctrl: ctrl}
	mock.recorder = &MockPaymentAuthAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentAuthAdapter) EXPECT() *MockPaymentAuthAdapterMockRecorder {
	return m.recorder
}

// Authenticate mocks base method.
func (m *MockPaymentAuthAdapter) Authenticate(ctx context.Context, po model.PurchaseOrder, authType payment_auth.AuthType) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authenticate", ctx, po, authType)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authenticate indicates an expected call of Authenticate.
func (mr *MockPaymentAuthAdapterMockRecorder) Authenticate(ctx, po, authType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authenticate", reflect.TypeOf((*MockPaymentAuthAdapter)(nil).Authenticate), ctx, po, authType)
}
