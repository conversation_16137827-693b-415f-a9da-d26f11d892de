// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: DistributedLock)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/distributed_lock.go --package=adapter_mocks . DistributedLock
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockDistributedLock is a mock of DistributedLock interface.
type MockDistributedLock struct {
	ctrl     *gomock.Controller
	recorder *MockDistributedLockMockRecorder
	isgomock struct{}
}

// MockDistributedLockMockRecorder is the mock recorder for MockDistributedLock.
type MockDistributedLockMockRecorder struct {
	mock *MockDistributedLock
}

// NewMockDistributedLock creates a new mock instance.
func NewMockDistributedLock(ctrl *gomock.Controller) *MockDistributedLock {
	mock := &MockDistributedLock{ctrl: ctrl}
	mock.recorder = &MockDistributedLockMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributedLock) EXPECT() *MockDistributedLockMockRecorder {
	return m.recorder
}

// Acquire mocks base method.
func (m *MockDistributedLock) Acquire(ctx context.Context, resource string, ttl time.Duration) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Acquire", ctx, resource, ttl)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Acquire indicates an expected call of Acquire.
func (mr *MockDistributedLockMockRecorder) Acquire(ctx, resource, ttl any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Acquire", reflect.TypeOf((*MockDistributedLock)(nil).Acquire), ctx, resource, ttl)
}

// AcquireProcessRefundSettle mocks base method.
func (m *MockDistributedLock) AcquireProcessRefundSettle(ctx context.Context, settleID int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireProcessRefundSettle", ctx, settleID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireProcessRefundSettle indicates an expected call of AcquireProcessRefundSettle.
func (mr *MockDistributedLockMockRecorder) AcquireProcessRefundSettle(ctx, settleID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireProcessRefundSettle", reflect.TypeOf((*MockDistributedLock)(nil).AcquireProcessRefundSettle), ctx, settleID)
}

// AcquireReconcileRefundSettle mocks base method.
func (m *MockDistributedLock) AcquireReconcileRefundSettle(ctx context.Context, zpTransID int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireReconcileRefundSettle", ctx, zpTransID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireReconcileRefundSettle indicates an expected call of AcquireReconcileRefundSettle.
func (mr *MockDistributedLockMockRecorder) AcquireReconcileRefundSettle(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireReconcileRefundSettle", reflect.TypeOf((*MockDistributedLock)(nil).AcquireReconcileRefundSettle), ctx, zpTransID)
}

// AcquireRefundSettleOrderProcess mocks base method.
func (m *MockDistributedLock) AcquireRefundSettleOrderProcess(ctx context.Context, paymentNo int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireRefundSettleOrderProcess", ctx, paymentNo)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireRefundSettleOrderProcess indicates an expected call of AcquireRefundSettleOrderProcess.
func (mr *MockDistributedLockMockRecorder) AcquireRefundSettleOrderProcess(ctx, paymentNo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireRefundSettleOrderProcess", reflect.TypeOf((*MockDistributedLock)(nil).AcquireRefundSettleOrderProcess), ctx, paymentNo)
}

// AcquireRefundTopupOrderProcess mocks base method.
func (m *MockDistributedLock) AcquireRefundTopupOrderProcess(ctx context.Context, paymentNo int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireRefundTopupOrderProcess", ctx, paymentNo)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireRefundTopupOrderProcess indicates an expected call of AcquireRefundTopupOrderProcess.
func (mr *MockDistributedLockMockRecorder) AcquireRefundTopupOrderProcess(ctx, paymentNo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireRefundTopupOrderProcess", reflect.TypeOf((*MockDistributedLock)(nil).AcquireRefundTopupOrderProcess), ctx, paymentNo)
}

// IsLocked mocks base method.
func (m *MockDistributedLock) IsLocked(ctx context.Context, lockKey string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsLocked", ctx, lockKey)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsLocked indicates an expected call of IsLocked.
func (mr *MockDistributedLockMockRecorder) IsLocked(ctx, lockKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsLocked", reflect.TypeOf((*MockDistributedLock)(nil).IsLocked), ctx, lockKey)
}

// Release mocks base method.
func (m *MockDistributedLock) Release(ctx context.Context, lockKey string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", ctx, lockKey)
	ret0, _ := ret[0].(error)
	return ret0
}

// Release indicates an expected call of Release.
func (mr *MockDistributedLockMockRecorder) Release(ctx, lockKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockDistributedLock)(nil).Release), ctx, lockKey)
}
