// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: WorkflowAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/workflow.go --package=adapter_mocks . WorkflowAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	enums "go.temporal.io/api/enums/v1"
	operatorservice "go.temporal.io/api/operatorservice/v1"
	workflowservice "go.temporal.io/api/workflowservice/v1"
	client "go.temporal.io/sdk/client"
	converter "go.temporal.io/sdk/converter"
	gomock "go.uber.org/mock/gomock"
)

// MockWorkflowAdapter is a mock of WorkflowAdapter interface.
type MockWorkflowAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockWorkflowAdapterMockRecorder
	isgomock struct{}
}

// MockWorkflowAdapterMockRecorder is the mock recorder for MockWorkflowAdapter.
type MockWorkflowAdapterMockRecorder struct {
	mock *MockWorkflowAdapter
}

// NewMockWorkflowAdapter creates a new mock instance.
func NewMockWorkflowAdapter(ctrl *gomock.Controller) *MockWorkflowAdapter {
	mock := &MockWorkflowAdapter{ctrl: ctrl}
	mock.recorder = &MockWorkflowAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkflowAdapter) EXPECT() *MockWorkflowAdapterMockRecorder {
	return m.recorder
}

// CancelWorkflow mocks base method.
func (m *MockWorkflowAdapter) CancelWorkflow(ctx context.Context, workflowID, runID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelWorkflow", ctx, workflowID, runID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelWorkflow indicates an expected call of CancelWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) CancelWorkflow(ctx, workflowID, runID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).CancelWorkflow), ctx, workflowID, runID)
}

// CheckHealth mocks base method.
func (m *MockWorkflowAdapter) CheckHealth(ctx context.Context, request *client.CheckHealthRequest) (*client.CheckHealthResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHealth", ctx, request)
	ret0, _ := ret[0].(*client.CheckHealthResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHealth indicates an expected call of CheckHealth.
func (mr *MockWorkflowAdapterMockRecorder) CheckHealth(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHealth", reflect.TypeOf((*MockWorkflowAdapter)(nil).CheckHealth), ctx, request)
}

// Close mocks base method.
func (m *MockWorkflowAdapter) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockWorkflowAdapterMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockWorkflowAdapter)(nil).Close))
}

// CompleteActivity mocks base method.
func (m *MockWorkflowAdapter) CompleteActivity(ctx context.Context, taskToken []byte, result any, err error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteActivity", ctx, taskToken, result, err)
	ret0, _ := ret[0].(error)
	return ret0
}

// CompleteActivity indicates an expected call of CompleteActivity.
func (mr *MockWorkflowAdapterMockRecorder) CompleteActivity(ctx, taskToken, result, err any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteActivity", reflect.TypeOf((*MockWorkflowAdapter)(nil).CompleteActivity), ctx, taskToken, result, err)
}

// CompleteActivityByID mocks base method.
func (m *MockWorkflowAdapter) CompleteActivityByID(ctx context.Context, namespace, workflowID, runID, activityID string, result any, err error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteActivityByID", ctx, namespace, workflowID, runID, activityID, result, err)
	ret0, _ := ret[0].(error)
	return ret0
}

// CompleteActivityByID indicates an expected call of CompleteActivityByID.
func (mr *MockWorkflowAdapterMockRecorder) CompleteActivityByID(ctx, namespace, workflowID, runID, activityID, result, err any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteActivityByID", reflect.TypeOf((*MockWorkflowAdapter)(nil).CompleteActivityByID), ctx, namespace, workflowID, runID, activityID, result, err)
}

// CountWorkflow mocks base method.
func (m *MockWorkflowAdapter) CountWorkflow(ctx context.Context, request *workflowservice.CountWorkflowExecutionsRequest) (*workflowservice.CountWorkflowExecutionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountWorkflow", ctx, request)
	ret0, _ := ret[0].(*workflowservice.CountWorkflowExecutionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountWorkflow indicates an expected call of CountWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) CountWorkflow(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).CountWorkflow), ctx, request)
}

// DescribeTaskQueue mocks base method.
func (m *MockWorkflowAdapter) DescribeTaskQueue(ctx context.Context, taskqueue string, taskqueueType enums.TaskQueueType) (*workflowservice.DescribeTaskQueueResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTaskQueue", ctx, taskqueue, taskqueueType)
	ret0, _ := ret[0].(*workflowservice.DescribeTaskQueueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTaskQueue indicates an expected call of DescribeTaskQueue.
func (mr *MockWorkflowAdapterMockRecorder) DescribeTaskQueue(ctx, taskqueue, taskqueueType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTaskQueue", reflect.TypeOf((*MockWorkflowAdapter)(nil).DescribeTaskQueue), ctx, taskqueue, taskqueueType)
}

// DescribeTaskQueueEnhanced mocks base method.
func (m *MockWorkflowAdapter) DescribeTaskQueueEnhanced(ctx context.Context, options client.DescribeTaskQueueEnhancedOptions) (client.TaskQueueDescription, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTaskQueueEnhanced", ctx, options)
	ret0, _ := ret[0].(client.TaskQueueDescription)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTaskQueueEnhanced indicates an expected call of DescribeTaskQueueEnhanced.
func (mr *MockWorkflowAdapterMockRecorder) DescribeTaskQueueEnhanced(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTaskQueueEnhanced", reflect.TypeOf((*MockWorkflowAdapter)(nil).DescribeTaskQueueEnhanced), ctx, options)
}

// DescribeWorkflowExecution mocks base method.
func (m *MockWorkflowAdapter) DescribeWorkflowExecution(ctx context.Context, workflowID, runID string) (*workflowservice.DescribeWorkflowExecutionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeWorkflowExecution", ctx, workflowID, runID)
	ret0, _ := ret[0].(*workflowservice.DescribeWorkflowExecutionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeWorkflowExecution indicates an expected call of DescribeWorkflowExecution.
func (mr *MockWorkflowAdapterMockRecorder) DescribeWorkflowExecution(ctx, workflowID, runID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeWorkflowExecution", reflect.TypeOf((*MockWorkflowAdapter)(nil).DescribeWorkflowExecution), ctx, workflowID, runID)
}

// ExecuteWorkflow mocks base method.
func (m *MockWorkflowAdapter) ExecuteWorkflow(ctx context.Context, options client.StartWorkflowOptions, workflow any, args ...any) (client.WorkflowRun, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, options, workflow}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteWorkflow", varargs...)
	ret0, _ := ret[0].(client.WorkflowRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteWorkflow indicates an expected call of ExecuteWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) ExecuteWorkflow(ctx, options, workflow any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, options, workflow}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).ExecuteWorkflow), varargs...)
}

// GetSearchAttributes mocks base method.
func (m *MockWorkflowAdapter) GetSearchAttributes(ctx context.Context) (*workflowservice.GetSearchAttributesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchAttributes", ctx)
	ret0, _ := ret[0].(*workflowservice.GetSearchAttributesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchAttributes indicates an expected call of GetSearchAttributes.
func (mr *MockWorkflowAdapterMockRecorder) GetSearchAttributes(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchAttributes", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetSearchAttributes), ctx)
}

// GetWorkerBuildIdCompatibility mocks base method.
func (m *MockWorkflowAdapter) GetWorkerBuildIdCompatibility(ctx context.Context, options *client.GetWorkerBuildIdCompatibilityOptions) (*client.WorkerBuildIDVersionSets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkerBuildIdCompatibility", ctx, options)
	ret0, _ := ret[0].(*client.WorkerBuildIDVersionSets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkerBuildIdCompatibility indicates an expected call of GetWorkerBuildIdCompatibility.
func (mr *MockWorkflowAdapterMockRecorder) GetWorkerBuildIdCompatibility(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkerBuildIdCompatibility", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetWorkerBuildIdCompatibility), ctx, options)
}

// GetWorkerTaskReachability mocks base method.
func (m *MockWorkflowAdapter) GetWorkerTaskReachability(ctx context.Context, options *client.GetWorkerTaskReachabilityOptions) (*client.WorkerTaskReachability, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkerTaskReachability", ctx, options)
	ret0, _ := ret[0].(*client.WorkerTaskReachability)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkerTaskReachability indicates an expected call of GetWorkerTaskReachability.
func (mr *MockWorkflowAdapterMockRecorder) GetWorkerTaskReachability(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkerTaskReachability", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetWorkerTaskReachability), ctx, options)
}

// GetWorkerVersioningRules mocks base method.
func (m *MockWorkflowAdapter) GetWorkerVersioningRules(ctx context.Context, options client.GetWorkerVersioningOptions) (*client.WorkerVersioningRules, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkerVersioningRules", ctx, options)
	ret0, _ := ret[0].(*client.WorkerVersioningRules)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkerVersioningRules indicates an expected call of GetWorkerVersioningRules.
func (mr *MockWorkflowAdapterMockRecorder) GetWorkerVersioningRules(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkerVersioningRules", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetWorkerVersioningRules), ctx, options)
}

// GetWorkflow mocks base method.
func (m *MockWorkflowAdapter) GetWorkflow(ctx context.Context, workflowID, runID string) client.WorkflowRun {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", ctx, workflowID, runID)
	ret0, _ := ret[0].(client.WorkflowRun)
	return ret0
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) GetWorkflow(ctx, workflowID, runID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetWorkflow), ctx, workflowID, runID)
}

// GetWorkflowHistory mocks base method.
func (m *MockWorkflowAdapter) GetWorkflowHistory(ctx context.Context, workflowID, runID string, isLongPoll bool, filterType enums.HistoryEventFilterType) client.HistoryEventIterator {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowHistory", ctx, workflowID, runID, isLongPoll, filterType)
	ret0, _ := ret[0].(client.HistoryEventIterator)
	return ret0
}

// GetWorkflowHistory indicates an expected call of GetWorkflowHistory.
func (mr *MockWorkflowAdapterMockRecorder) GetWorkflowHistory(ctx, workflowID, runID, isLongPoll, filterType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowHistory", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetWorkflowHistory), ctx, workflowID, runID, isLongPoll, filterType)
}

// GetWorkflowUpdateHandle mocks base method.
func (m *MockWorkflowAdapter) GetWorkflowUpdateHandle(ref client.GetWorkflowUpdateHandleOptions) client.WorkflowUpdateHandle {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowUpdateHandle", ref)
	ret0, _ := ret[0].(client.WorkflowUpdateHandle)
	return ret0
}

// GetWorkflowUpdateHandle indicates an expected call of GetWorkflowUpdateHandle.
func (mr *MockWorkflowAdapterMockRecorder) GetWorkflowUpdateHandle(ref any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowUpdateHandle", reflect.TypeOf((*MockWorkflowAdapter)(nil).GetWorkflowUpdateHandle), ref)
}

// ListArchivedWorkflow mocks base method.
func (m *MockWorkflowAdapter) ListArchivedWorkflow(ctx context.Context, request *workflowservice.ListArchivedWorkflowExecutionsRequest) (*workflowservice.ListArchivedWorkflowExecutionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListArchivedWorkflow", ctx, request)
	ret0, _ := ret[0].(*workflowservice.ListArchivedWorkflowExecutionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListArchivedWorkflow indicates an expected call of ListArchivedWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) ListArchivedWorkflow(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListArchivedWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).ListArchivedWorkflow), ctx, request)
}

// ListClosedWorkflow mocks base method.
func (m *MockWorkflowAdapter) ListClosedWorkflow(ctx context.Context, request *workflowservice.ListClosedWorkflowExecutionsRequest) (*workflowservice.ListClosedWorkflowExecutionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClosedWorkflow", ctx, request)
	ret0, _ := ret[0].(*workflowservice.ListClosedWorkflowExecutionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClosedWorkflow indicates an expected call of ListClosedWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) ListClosedWorkflow(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClosedWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).ListClosedWorkflow), ctx, request)
}

// ListOpenWorkflow mocks base method.
func (m *MockWorkflowAdapter) ListOpenWorkflow(ctx context.Context, request *workflowservice.ListOpenWorkflowExecutionsRequest) (*workflowservice.ListOpenWorkflowExecutionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOpenWorkflow", ctx, request)
	ret0, _ := ret[0].(*workflowservice.ListOpenWorkflowExecutionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOpenWorkflow indicates an expected call of ListOpenWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) ListOpenWorkflow(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOpenWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).ListOpenWorkflow), ctx, request)
}

// ListWorkflow mocks base method.
func (m *MockWorkflowAdapter) ListWorkflow(ctx context.Context, request *workflowservice.ListWorkflowExecutionsRequest) (*workflowservice.ListWorkflowExecutionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorkflow", ctx, request)
	ret0, _ := ret[0].(*workflowservice.ListWorkflowExecutionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorkflow indicates an expected call of ListWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) ListWorkflow(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).ListWorkflow), ctx, request)
}

// OperatorService mocks base method.
func (m *MockWorkflowAdapter) OperatorService() operatorservice.OperatorServiceClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OperatorService")
	ret0, _ := ret[0].(operatorservice.OperatorServiceClient)
	return ret0
}

// OperatorService indicates an expected call of OperatorService.
func (mr *MockWorkflowAdapterMockRecorder) OperatorService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OperatorService", reflect.TypeOf((*MockWorkflowAdapter)(nil).OperatorService))
}

// QueryWorkflow mocks base method.
func (m *MockWorkflowAdapter) QueryWorkflow(ctx context.Context, workflowID, runID, queryType string, args ...any) (converter.EncodedValue, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, workflowID, runID, queryType}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryWorkflow", varargs...)
	ret0, _ := ret[0].(converter.EncodedValue)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryWorkflow indicates an expected call of QueryWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) QueryWorkflow(ctx, workflowID, runID, queryType any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, workflowID, runID, queryType}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).QueryWorkflow), varargs...)
}

// QueryWorkflowWithOptions mocks base method.
func (m *MockWorkflowAdapter) QueryWorkflowWithOptions(ctx context.Context, request *client.QueryWorkflowWithOptionsRequest) (*client.QueryWorkflowWithOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryWorkflowWithOptions", ctx, request)
	ret0, _ := ret[0].(*client.QueryWorkflowWithOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryWorkflowWithOptions indicates an expected call of QueryWorkflowWithOptions.
func (mr *MockWorkflowAdapterMockRecorder) QueryWorkflowWithOptions(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryWorkflowWithOptions", reflect.TypeOf((*MockWorkflowAdapter)(nil).QueryWorkflowWithOptions), ctx, request)
}

// RecordActivityHeartbeat mocks base method.
func (m *MockWorkflowAdapter) RecordActivityHeartbeat(ctx context.Context, taskToken []byte, details ...any) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, taskToken}
	for _, a := range details {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordActivityHeartbeat", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordActivityHeartbeat indicates an expected call of RecordActivityHeartbeat.
func (mr *MockWorkflowAdapterMockRecorder) RecordActivityHeartbeat(ctx, taskToken any, details ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, taskToken}, details...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordActivityHeartbeat", reflect.TypeOf((*MockWorkflowAdapter)(nil).RecordActivityHeartbeat), varargs...)
}

// RecordActivityHeartbeatByID mocks base method.
func (m *MockWorkflowAdapter) RecordActivityHeartbeatByID(ctx context.Context, namespace, workflowID, runID, activityID string, details ...any) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, namespace, workflowID, runID, activityID}
	for _, a := range details {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordActivityHeartbeatByID", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordActivityHeartbeatByID indicates an expected call of RecordActivityHeartbeatByID.
func (mr *MockWorkflowAdapterMockRecorder) RecordActivityHeartbeatByID(ctx, namespace, workflowID, runID, activityID any, details ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, namespace, workflowID, runID, activityID}, details...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordActivityHeartbeatByID", reflect.TypeOf((*MockWorkflowAdapter)(nil).RecordActivityHeartbeatByID), varargs...)
}

// ResetWorkflowExecution mocks base method.
func (m *MockWorkflowAdapter) ResetWorkflowExecution(ctx context.Context, request *workflowservice.ResetWorkflowExecutionRequest) (*workflowservice.ResetWorkflowExecutionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetWorkflowExecution", ctx, request)
	ret0, _ := ret[0].(*workflowservice.ResetWorkflowExecutionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetWorkflowExecution indicates an expected call of ResetWorkflowExecution.
func (mr *MockWorkflowAdapterMockRecorder) ResetWorkflowExecution(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetWorkflowExecution", reflect.TypeOf((*MockWorkflowAdapter)(nil).ResetWorkflowExecution), ctx, request)
}

// ScanWorkflow mocks base method.
func (m *MockWorkflowAdapter) ScanWorkflow(ctx context.Context, request *workflowservice.ScanWorkflowExecutionsRequest) (*workflowservice.ScanWorkflowExecutionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanWorkflow", ctx, request)
	ret0, _ := ret[0].(*workflowservice.ScanWorkflowExecutionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanWorkflow indicates an expected call of ScanWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) ScanWorkflow(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).ScanWorkflow), ctx, request)
}

// ScheduleClient mocks base method.
func (m *MockWorkflowAdapter) ScheduleClient() client.ScheduleClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScheduleClient")
	ret0, _ := ret[0].(client.ScheduleClient)
	return ret0
}

// ScheduleClient indicates an expected call of ScheduleClient.
func (mr *MockWorkflowAdapterMockRecorder) ScheduleClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScheduleClient", reflect.TypeOf((*MockWorkflowAdapter)(nil).ScheduleClient))
}

// SignalWithStartWorkflow mocks base method.
func (m *MockWorkflowAdapter) SignalWithStartWorkflow(ctx context.Context, workflowID, signalName string, signalArg any, options client.StartWorkflowOptions, workflow any, workflowArgs ...any) (client.WorkflowRun, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, workflowID, signalName, signalArg, options, workflow}
	for _, a := range workflowArgs {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SignalWithStartWorkflow", varargs...)
	ret0, _ := ret[0].(client.WorkflowRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignalWithStartWorkflow indicates an expected call of SignalWithStartWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) SignalWithStartWorkflow(ctx, workflowID, signalName, signalArg, options, workflow any, workflowArgs ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, workflowID, signalName, signalArg, options, workflow}, workflowArgs...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignalWithStartWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).SignalWithStartWorkflow), varargs...)
}

// SignalWorkflow mocks base method.
func (m *MockWorkflowAdapter) SignalWorkflow(ctx context.Context, workflowID, runID, signalName string, arg any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignalWorkflow", ctx, workflowID, runID, signalName, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// SignalWorkflow indicates an expected call of SignalWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) SignalWorkflow(ctx, workflowID, runID, signalName, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignalWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).SignalWorkflow), ctx, workflowID, runID, signalName, arg)
}

// TerminateWorkflow mocks base method.
func (m *MockWorkflowAdapter) TerminateWorkflow(ctx context.Context, workflowID, runID, reason string, details ...any) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, workflowID, runID, reason}
	for _, a := range details {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TerminateWorkflow", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// TerminateWorkflow indicates an expected call of TerminateWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) TerminateWorkflow(ctx, workflowID, runID, reason any, details ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, workflowID, runID, reason}, details...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TerminateWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).TerminateWorkflow), varargs...)
}

// UpdateWorkerBuildIdCompatibility mocks base method.
func (m *MockWorkflowAdapter) UpdateWorkerBuildIdCompatibility(ctx context.Context, options *client.UpdateWorkerBuildIdCompatibilityOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkerBuildIdCompatibility", ctx, options)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWorkerBuildIdCompatibility indicates an expected call of UpdateWorkerBuildIdCompatibility.
func (mr *MockWorkflowAdapterMockRecorder) UpdateWorkerBuildIdCompatibility(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkerBuildIdCompatibility", reflect.TypeOf((*MockWorkflowAdapter)(nil).UpdateWorkerBuildIdCompatibility), ctx, options)
}

// UpdateWorkerVersioningRules mocks base method.
func (m *MockWorkflowAdapter) UpdateWorkerVersioningRules(ctx context.Context, options client.UpdateWorkerVersioningRulesOptions) (*client.WorkerVersioningRules, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkerVersioningRules", ctx, options)
	ret0, _ := ret[0].(*client.WorkerVersioningRules)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWorkerVersioningRules indicates an expected call of UpdateWorkerVersioningRules.
func (mr *MockWorkflowAdapterMockRecorder) UpdateWorkerVersioningRules(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkerVersioningRules", reflect.TypeOf((*MockWorkflowAdapter)(nil).UpdateWorkerVersioningRules), ctx, options)
}

// UpdateWorkflow mocks base method.
func (m *MockWorkflowAdapter) UpdateWorkflow(ctx context.Context, options client.UpdateWorkflowOptions) (client.WorkflowUpdateHandle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflow", ctx, options)
	ret0, _ := ret[0].(client.WorkflowUpdateHandle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWorkflow indicates an expected call of UpdateWorkflow.
func (mr *MockWorkflowAdapterMockRecorder) UpdateWorkflow(ctx, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflow", reflect.TypeOf((*MockWorkflowAdapter)(nil).UpdateWorkflow), ctx, options)
}

// WorkflowService mocks base method.
func (m *MockWorkflowAdapter) WorkflowService() workflowservice.WorkflowServiceClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WorkflowService")
	ret0, _ := ret[0].(workflowservice.WorkflowServiceClient)
	return ret0
}

// WorkflowService indicates an expected call of WorkflowService.
func (mr *MockWorkflowAdapterMockRecorder) WorkflowService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WorkflowService", reflect.TypeOf((*MockWorkflowAdapter)(nil).WorkflowService))
}
