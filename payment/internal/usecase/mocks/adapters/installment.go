// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: InstallmentAdapter)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/installment.go --package=adapter_mocks . InstallmentAdapter
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockInstallmentAdapter is a mock of InstallmentAdapter interface.
type MockInstallmentAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockInstallmentAdapterMockRecorder
	isgomock struct{}
}

// MockInstallmentAdapterMockRecorder is the mock recorder for MockInstallmentAdapter.
type MockInstallmentAdapterMockRecorder struct {
	mock *MockInstallmentAdapter
}

// NewMockInstallmentAdapter creates a new mock instance.
func NewMockInstallmentAdapter(ctrl *gomock.Controller) *MockInstallmentAdapter {
	mock := &MockInstallmentAdapter{ctrl: ctrl}
	mock.recorder = &MockInstallmentAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInstallmentAdapter) EXPECT() *MockInstallmentAdapterMockRecorder {
	return m.recorder
}

// GetEarlyDischarge mocks base method.
func (m *MockInstallmentAdapter) GetEarlyDischarge(ctx context.Context, zpTransID int64, forceLatest bool) (model.EarlyDischarge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarlyDischarge", ctx, zpTransID, forceLatest)
	ret0, _ := ret[0].(model.EarlyDischarge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischarge indicates an expected call of GetEarlyDischarge.
func (mr *MockInstallmentAdapterMockRecorder) GetEarlyDischarge(ctx, zpTransID, forceLatest any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischarge", reflect.TypeOf((*MockInstallmentAdapter)(nil).GetEarlyDischarge), ctx, zpTransID, forceLatest)
}

// GetInstallmentStatus mocks base method.
func (m *MockInstallmentAdapter) GetInstallmentStatus(ctx context.Context, zpTransID int64) (model.GetInstallmentStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentStatus", ctx, zpTransID)
	ret0, _ := ret[0].(model.GetInstallmentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentStatus indicates an expected call of GetInstallmentStatus.
func (mr *MockInstallmentAdapterMockRecorder) GetInstallmentStatus(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentStatus", reflect.TypeOf((*MockInstallmentAdapter)(nil).GetInstallmentStatus), ctx, zpTransID)
}

// NotifyInstallmentRefund mocks base method.
func (m *MockInstallmentAdapter) NotifyInstallmentRefund(ctx context.Context, refundSettle *model.RefundSettle) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyInstallmentRefund", ctx, refundSettle)
	ret0, _ := ret[0].(error)
	return ret0
}

// NotifyInstallmentRefund indicates an expected call of NotifyInstallmentRefund.
func (mr *MockInstallmentAdapterMockRecorder) NotifyInstallmentRefund(ctx, refundSettle any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyInstallmentRefund", reflect.TypeOf((*MockInstallmentAdapter)(nil).NotifyInstallmentRefund), ctx, refundSettle)
}
