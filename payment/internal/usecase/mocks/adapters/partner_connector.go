// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types (interfaces: PartnerConnector)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapters/partner_connector.go --package=adapter_mocks . PartnerConnector
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockPartnerConnector is a mock of PartnerConnector interface.
type MockPartnerConnector struct {
	ctrl     *gomock.Controller
	recorder *MockPartnerConnectorMockRecorder
	isgomock struct{}
}

// MockPartnerConnectorMockRecorder is the mock recorder for MockPartnerConnector.
type MockPartnerConnectorMockRecorder struct {
	mock *MockPartnerConnector
}

// NewMockPartnerConnector creates a new mock instance.
func NewMockPartnerConnector(ctrl *gomock.Controller) *MockPartnerConnector {
	mock := &MockPartnerConnector{ctrl: ctrl}
	mock.recorder = &MockPartnerConnectorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPartnerConnector) EXPECT() *MockPartnerConnectorMockRecorder {
	return m.recorder
}

// ConfirmDisbursement mocks base method.
func (m *MockPartnerConnector) ConfirmDisbursement(ctx context.Context, purchaseOrderID int64, partnerTransactionID string, zalopayID int64, isCancel bool) (*model.ConfirmDisbursementResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmDisbursement", ctx, purchaseOrderID, partnerTransactionID, zalopayID, isCancel)
	ret0, _ := ret[0].(*model.ConfirmDisbursementResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmDisbursement indicates an expected call of ConfirmDisbursement.
func (mr *MockPartnerConnectorMockRecorder) ConfirmDisbursement(ctx, purchaseOrderID, partnerTransactionID, zalopayID, isCancel any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmDisbursement", reflect.TypeOf((*MockPartnerConnector)(nil).ConfirmDisbursement), ctx, purchaseOrderID, partnerTransactionID, zalopayID, isCancel)
}

// ConfirmPurchasing mocks base method.
func (m *MockPartnerConnector) ConfirmPurchasing(ctx context.Context, po model.PurchaseOrder) (*model.ConfirmPurchaseResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmPurchasing", ctx, po)
	ret0, _ := ret[0].(*model.ConfirmPurchaseResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmPurchasing indicates an expected call of ConfirmPurchasing.
func (mr *MockPartnerConnectorMockRecorder) ConfirmPurchasing(ctx, po any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmPurchasing", reflect.TypeOf((*MockPartnerConnector)(nil).ConfirmPurchasing), ctx, po)
}

// GetAvailableBalance mocks base method.
func (m *MockPartnerConnector) GetAvailableBalance(ctx context.Context, zalopayID int64, partnerCode string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableBalance", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableBalance indicates an expected call of GetAvailableBalance.
func (mr *MockPartnerConnectorMockRecorder) GetAvailableBalance(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableBalance", reflect.TypeOf((*MockPartnerConnector)(nil).GetAvailableBalance), ctx, zalopayID, partnerCode)
}

// GetEarlyDischargeStatus mocks base method.
func (m *MockPartnerConnector) GetEarlyDischargeStatus(ctx context.Context, paymentID int64, refTransID string) (*model.CIMBTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarlyDischargeStatus", ctx, paymentID, refTransID)
	ret0, _ := ret[0].(*model.CIMBTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischargeStatus indicates an expected call of GetEarlyDischargeStatus.
func (mr *MockPartnerConnectorMockRecorder) GetEarlyDischargeStatus(ctx, paymentID, refTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischargeStatus", reflect.TypeOf((*MockPartnerConnector)(nil).GetEarlyDischargeStatus), ctx, paymentID, refTransID)
}

// InitPurchasing mocks base method.
func (m *MockPartnerConnector) InitPurchasing(ctx context.Context, po model.PurchaseOrder) (*model.InitPurchasingResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitPurchasing", ctx, po)
	ret0, _ := ret[0].(*model.InitPurchasingResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitPurchasing indicates an expected call of InitPurchasing.
func (mr *MockPartnerConnectorMockRecorder) InitPurchasing(ctx, po any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitPurchasing", reflect.TypeOf((*MockPartnerConnector)(nil).InitPurchasing), ctx, po)
}

// InquiryMultiTransaction mocks base method.
func (m *MockPartnerConnector) InquiryMultiTransaction(ctx context.Context, refTransIDs []string) ([]*model.CIMBTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InquiryMultiTransaction", ctx, refTransIDs)
	ret0, _ := ret[0].([]*model.CIMBTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InquiryMultiTransaction indicates an expected call of InquiryMultiTransaction.
func (mr *MockPartnerConnectorMockRecorder) InquiryMultiTransaction(ctx, refTransIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InquiryMultiTransaction", reflect.TypeOf((*MockPartnerConnector)(nil).InquiryMultiTransaction), ctx, refTransIDs)
}

// InquiryTransaction mocks base method.
func (m *MockPartnerConnector) InquiryTransaction(ctx context.Context, refTransID string) (*model.CIMBTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InquiryTransaction", ctx, refTransID)
	ret0, _ := ret[0].(*model.CIMBTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InquiryTransaction indicates an expected call of InquiryTransaction.
func (mr *MockPartnerConnectorMockRecorder) InquiryTransaction(ctx, refTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InquiryTransaction", reflect.TypeOf((*MockPartnerConnector)(nil).InquiryTransaction), ctx, refTransID)
}

// ODRepayment mocks base method.
func (m *MockPartnerConnector) ODRepayment(ctx context.Context, req model.ODRepaymentRequest) (*model.RepaymentResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ODRepayment", ctx, req)
	ret0, _ := ret[0].(*model.RepaymentResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ODRepayment indicates an expected call of ODRepayment.
func (mr *MockPartnerConnectorMockRecorder) ODRepayment(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ODRepayment", reflect.TypeOf((*MockPartnerConnector)(nil).ODRepayment), ctx, req)
}

// SubmitEarlyDischarge mocks base method.
func (m *MockPartnerConnector) SubmitEarlyDischarge(ctx context.Context, req model.EarlyDischargeRequest) (*model.EarlyDischargeResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitEarlyDischarge", ctx, req)
	ret0, _ := ret[0].(*model.EarlyDischargeResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitEarlyDischarge indicates an expected call of SubmitEarlyDischarge.
func (mr *MockPartnerConnectorMockRecorder) SubmitEarlyDischarge(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitEarlyDischarge", reflect.TypeOf((*MockPartnerConnector)(nil).SubmitEarlyDischarge), ctx, req)
}
