package types

import (
	"context"
	"time"

	pa "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	tempCli "go.temporal.io/sdk/client"
	kafka_client "zalopay.io/zgo/kafka-client"
)

//go:generate mockgen --destination=../mocks/repository/transaction.go --package=repository_mocks . Transaction
type Transaction interface {
	WithTx(ctx context.Context, exec func(ctx context.Context) error) error
	BeginTx(ctx context.Context) (context.Context, error)
	BeginOrReuseTx(ctx context.Context) (context.Context, error)
	BeginTxReadCommited(ctx context.Context) (context.Context, error)
	BeginTxRepeatableRead(ctx context.Context) (context.Context, error)
	CommitTx(ctx context.Context) error
	RollbackTx(ctx context.Context) error
	IsInTx(ctx context.Context) bool
	IsTxActive(ctx context.Context) bool
}

//go:generate mockgen --destination=../mocks/repository/payment.go --package=repository_mocks . PaymentRepo
type PaymentRepo interface {
	//CreatePayment(ctx context.Context, account model.Account, bankRoute model.BankRoute, order model.Order, bankReceivableAccountingID int64) (int64, error)
	CreatePayment(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.PurchaseOrder, error)
	CreateRepaymentLog(ctx context.Context, repaymentLog model.RepaymentLog) (*model.RepaymentLog, error)
	UpdateRepaymentLogStatus(ctx context.Context, repaymentLog model.RepaymentLog) error
	CreateEarlyDischargeLog(ctx context.Context, dischargeLog model.EarlyDischargeLog) (*model.EarlyDischargeLog, error)
	GetEarlyDischargeLog(ctx context.Context, paymentID int64, zalopayID int64) (*model.EarlyDischargeLog, error)
	GetEarlyDischargeLogByID(ctx context.Context, paymentID int64) (*model.EarlyDischargeLog, error)
	GetEarlyDischargeLogByOrderID(ctx context.Context, orderID int64) (*model.EarlyDischargeLog, error)
	UpdateEarlyDischargeLog(ctx context.Context, dischargeLog model.EarlyDischargeLog) (*model.EarlyDischargeLog, error)
	UpdateEarlyDischargeLogStatus(ctx context.Context, paymentID int64, dischargeLog model.EarlyDischargeLog) error
	UpdatePaymentLogOrderIDs(ctx context.Context, paymentID int64, orderInfo model.Order) error
	GetPayment(ctx context.Context, purchaseOrderID int64, zalopayID int64) (*model.PurchaseOrder, error)
	GetPaymentByTransID(ctx context.Context, transID int64) (*model.PurchaseOrder, error)
	GetPaymentByPaymentNo(ctx context.Context, paymentNo string) (*model.PurchaseOrder, error)
	GetRepaymentByID(ctx context.Context, paymentID int64) (*model.RepaymentLog, error)
	GetRepaymentByOrderID(ctx context.Context, orderID int64) (*model.RepaymentLog, error)
	UpdatePayment(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.PurchaseOrder, error)
	UpdatePartnerTransID(ctx context.Context, paymentID int64, partnerTransID string) error
	GetBankRoute(ctx context.Context, partnerCode string, transType model.TransType) (*model.BankRoute, error)
	UpdatePaymentCommission(ctx context.Context, zpTransID int64, isCommission bool) error
	ListPaymentAdvance(ctx context.Context, params *model.ListPaymentQuery) ([]*model.Transaction, error)
	GetTransactionByZPTransID(ctx context.Context, zpTransID int64) (*model.Transaction, error)
}

//go:generate mockgen --destination=../mocks/repository/order.go --package=repository_mocks . OrderRepo
type OrderRepo interface {
	CreateRepayOrder(ctx context.Context, repayOrder model.RepayOrder) (*model.RepayOrder, error)
	CreateRefundTopupOrder(ctx context.Context, topupOrder *model.RefundTopupOrder) (*model.RefundTopupOrder, error)
	CreateRefundSettleOrder(ctx context.Context, settleOrder *model.RefundSettleOrder) (*model.RefundSettleOrder, error)
	CreateRefundFundbackOrder(ctx context.Context, fundbackOrder *model.RefundFundbackOrder) (*model.RefundFundbackOrder, error)
	GetRepayOrder(ctx context.Context, appTransID string, appID int32, orderType model.OrderType) (*model.RepayOrder, error)
	GetRefundTopupOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundTopupOrder, error)
	GetRefundSettleOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundSettleOrder, error)
	GetRefundSettleOrderByID(ctx context.Context, orderID int64) (*model.RefundSettleOrder, error)
	GetRefundSettleOrderByRefundID(ctx context.Context, refundID int64) (*model.RefundSettleOrder, error)
	GetRefundFundbackOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundFundbackOrder, error)
	UpdateOrderStatus(ctx context.Context, orderID int64, status model.OrderStatus, zpTransID int64) error
	UpdateOrderProgress(ctx context.Context, params model.OrderProgressUpdate) error
	ListRefundTopupOrder(ctx context.Context, settleID int64) ([]*model.RefundTopupOrder, error)
	ListRefundFundbackOrder(ctx context.Context, settleID int64) ([]*model.RefundFundbackOrder, error)
	ListRefundSettleOrder(ctx context.Context, settleID int64) ([]*model.RefundSettleOrder, error)
}

//go:generate mockgen --destination=../mocks/repository/refund.go --package=repository_mocks . RefundRepo
type RefundRepo interface {
	CreateRefundLog(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error)
	CompleteRefundLog(ctx context.Context, refundOrder *model.RefundOrder) error
	GetRefundLogForUpdate(ctx context.Context, id int64) (*model.RefundOrder, error)
	GetRefundLogByRefundID(ctx context.Context, refundID int64) (*model.RefundOrder, error)
	GetRefundLogsByZPTransID(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error)
	GetRefundLogsForSettlement(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error)
	MarkRefundLogsAsExpiredByIDs(ctx context.Context, ids []int64) error
	GetRefundLogsExpiredByZPTransIDForUpdate(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error)

	CreateRefundSettle(ctx context.Context, refundSettle *model.RefundSettle) (*model.RefundSettle, error)
	GetRefundSettleByID(ctx context.Context, id int64) (*model.RefundSettle, error)
	GetRefundSettleForUpdate(ctx context.Context, id int64) (*model.RefundSettle, error)
	GetRefundSettleByZPTransID(ctx context.Context, zpTransID int64) (*model.RefundSettle, error)
	GetListRefundSettleByStatus(ctx context.Context, status model.RefundSettleStatus, paging *model.Pagination) ([]*model.RefundSettle, error)
	UpdateRefundSettleReconAmts(ctx context.Context, refundSettle *model.RefundSettle) error
	UpdateRefundSettleEventData(ctx context.Context, refundSettle *model.RefundSettle, originVersion int32) error
	UpdateRefundSettleStatusByID(ctx context.Context, id int64, status model.RefundSettleStatus) error
	UpdateRefundSettlePaybackInfo(ctx context.Context, id int64, paybackAmount int64, status model.RefundSettleStatus) error
	UpdateRefundTopupsAmount(ctx context.Context, id int64, topupsAmount int64) error
	UpdateRefundSettlementAmount(ctx context.Context, id int64, amount int64) error
	GetRefundSettleIDsHasExpiredItem(ctx context.Context, paging *model.Pagination) ([]int64, error)
}

//go:generate mockgen --destination=../mocks/adapters/account.go --package=adapter_mocks . AccountAdapter
type AccountAdapter interface {
	GetAccount(ctx context.Context, zalopayID int64, partnerCode string) (model.Account, error)
}

//go:generate mockgen --destination=../mocks/adapters/installment.go --package=adapter_mocks . InstallmentAdapter
type InstallmentAdapter interface {
	GetEarlyDischarge(ctx context.Context, zpTransID int64, forceLatest bool) (model.EarlyDischarge, error)
	GetInstallmentStatus(ctx context.Context, zpTransID int64) (model.GetInstallmentStatusResponse, error)
	NotifyInstallmentRefund(ctx context.Context, refundSettle *model.RefundSettle) error
}

//go:generate mockgen --destination=../mocks/adapters/partner_connector.go --package=adapter_mocks . PartnerConnector
type PartnerConnector interface {
	GetAvailableBalance(ctx context.Context, zalopayID int64, partnerCode string) (int64, error)
	InitPurchasing(ctx context.Context, po model.PurchaseOrder) (*model.InitPurchasingResult, error)
	ConfirmPurchasing(ctx context.Context, po model.PurchaseOrder) (*model.ConfirmPurchaseResult, error)
	ConfirmDisbursement(ctx context.Context, purchaseOrderID int64, partnerTransactionID string, zalopayID int64, isCancel bool) (*model.ConfirmDisbursementResult, error)
	InquiryMultiTransaction(ctx context.Context, refTransIDs []string) ([]*model.CIMBTransaction, error)
	InquiryTransaction(ctx context.Context, refTransID string) (*model.CIMBTransaction, error)
	ODRepayment(ctx context.Context, req model.ODRepaymentRequest) (*model.RepaymentResult, error)
	SubmitEarlyDischarge(ctx context.Context, req model.EarlyDischargeRequest) (*model.EarlyDischargeResult, error)
	GetEarlyDischargeStatus(ctx context.Context, paymentID int64, refTransID string) (*model.CIMBTransaction, error)
}

//go:generate mockgen --destination=../mocks/adapters/zas.go --package=adapter_mocks . ZasAdapter
type ZasAdapter interface {
	GetBankReceivable(ctx context.Context, bankAccountNumber string) (int64, error)
}

//go:generate mockgen --destination=../mocks/adapters/refund_settle_notifier.go --package=adapter_mocks . RefundSettleNotifier
type RefundSettleNotifier interface {
	PublishRefundSettleEvent(ctx context.Context, data *model.RefundSettle) error
	PublishRefundSettleResult(ctx context.Context, settle *model.RefundSettle, eadLog *model.EarlyDischargeLog) error
	PublishRefundExpiredEvents(ctx context.Context, refZPTransID int64, refunds model.RefundOrders) error
	PublishRefundExpiredResult(ctx context.Context, settleOrder *model.RefundSettleOrder, repayLog *model.RepaymentLog) error
}

//go:generate mockgen --destination=../mocks/adapters/purchase_init_publisher.go --package=adapter_mocks . PurchaseInitiatedPublisher
type PurchaseInitiatedPublisher interface {
	kafka_client.Publisher
}

//go:generate mockgen --destination=../mocks/adapters/purchase_authenticated_publisher.go --package=adapter_mocks . PurchaseAuthenticatedPublisher
type PurchaseAuthenticatedPublisher interface {
	kafka_client.Publisher
}

//go:generate mockgen --destination=../mocks/adapters/repayment_status_publisher.go --package=adapter_mocks . PEUpdateExchangeStatusPublisher
type PEUpdateExchangeStatusPublisher interface {
	kafka_client.Publisher
}

//go:generate mockgen --destination=../mocks/adapters/acquiring_core.go --package=adapter_mocks . AcquiringCoreAdapter
type AcquiringCoreAdapter interface {
	CreateOrder(ctx context.Context, rs model.RepayOrderRequest) (*model.RepayOrderResponse, error)
	CreateRefundTopupOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error)
	CreateRefundSettleOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error)
	SubmitRefundSettleOrder(ctx context.Context, order *model.RefundSettleOrder) error
	CreateRefundFundbackOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error)
	SubmitRefundFundbackOrder(ctx context.Context, order *model.RefundFundbackOrder) error
}

//go:generate mockgen --destination=../mocks/adapters/workflow.go --package=adapter_mocks . WorkflowAdapter
type WorkflowAdapter interface {
	tempCli.Client
}

//go:generate mockgen --destination=../mocks/adapters/payment_auth.go --package=adapter_mocks . PaymentAuthAdapter
type PaymentAuthAdapter interface {
	Authenticate(ctx context.Context, po model.PurchaseOrder, authType pa.AuthType) (string, error)
}

//go:generate mockgen --destination=../mocks/adapters/task_job.go --package=adapter_mocks . TaskJobAdapter
type TaskJobAdapter interface {
	ExecuteReconcileRefundSettleJob(ctx context.Context, params *model.RefundSettleReconParams) error
	ExecuteEarlyDischargePollingJob(ctx context.Context, params *model.EarlyDischargeStatusWorkflowRequest) error
	ExecuteExpiredRefundRepaymentPollingJob(ctx context.Context, params *model.RepaymentStatusPollingRequest) error
}

//go:generate mockgen --destination=../mocks/adapters/distributed_lock.go --package=adapter_mocks . DistributedLock
type DistributedLock interface {
	// Acquire attempts to acquire the lock for the specified resource.
	// It returns an error if the lock could not be acquired.
	Acquire(ctx context.Context, resource string, ttl time.Duration) (lockKey string, err error)

	// Release releases the lock for the specified resource.
	// It returns an error if the lock could not be released.
	Release(ctx context.Context, lockKey string) error

	// IsLocked checks if the specified resource is currently locked.
	// It returns a boolean indicating the lock status and an error if the status could not be determined.
	IsLocked(ctx context.Context, lockKey string) (bool, error)

	// AcquireReconcileRefundSettle
	AcquireReconcileRefundSettle(ctx context.Context, zpTransID int64) (lockKey string, err error)

	// AcquireProcessRefundSettle acquires a lock for processing a refund settlement by settle ID
	AcquireProcessRefundSettle(ctx context.Context, settleID int64) (lockKey string, err error)

	// AcquireRefundTopupOrderProcess
	AcquireRefundTopupOrderProcess(ctx context.Context, paymentNo int64) (lockKey string, err error)

	// AcquireRefundSettleOrderProcess
	AcquireRefundSettleOrderProcess(ctx context.Context, paymentNo int64) (lockKey string, err error)
}
