package transaction

import (
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
)

type Usecase struct {
	logger      *log.Helper
	paymentRepo types.PaymentRepo
}

const (
	maxQueryLimit = 100
)

func NewTransactionUsecase(
	kLogger log.Logger,
	paymentRepo types.PaymentRepo) *Usecase {
	return &Usecase{
		logger:      log.NewHelper(kLogger),
		paymentRepo: paymentRepo,
	}
}
