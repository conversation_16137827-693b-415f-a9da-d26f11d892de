package transaction

import (
	"context"
	"encoding/base64"
	"fmt"

	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type ListTransactionRequest model.ListPaymentQuery

func (req *ListTransactionRequest) SetLimit(limit int) *ListTransactionRequest {
	req.Pagination.Limit = limit
	return req
}

type ListTransactionResponse struct {
	Transactions  []*model.Transaction
	PaginationRes model.PaginationResult
}

// ListTransaction currently serve for installment FE to get list of transaction,
// so consider to use this  for internal and external service
func (uc *Usecase) ListTransaction(ctx context.Context, req *ListTransactionRequest) (*ListTransactionResponse, error) {
	var err error
	params := (*model.ListPaymentQuery)(req)
	if err = params.Validate(maxQueryLimit); err != nil {
		return nil, err
	}

	// We need to query 1 more record to check if there is next page
	params = params.InitPagination()
	originLimit := params.Pagination.Limit
	queryLimit := params.Pagination.Limit + 1
	params.Pagination, err = buildTransPaginationParams(params.Pagination, queryLimit)
	if err != nil {
		return nil, err
	}

	transactions, err := uc.paymentRepo.ListPaymentAdvance(ctx, params)
	if err != nil {
		return nil, err
	}

	// Build result
	isOverLimit := len(transactions) > originLimit
	transactions = processTransListData(params.Pagination, transactions, isOverLimit)
	paginationData := buildTransPaginationData(params.Pagination, transactions, isOverLimit)

	return &ListTransactionResponse{
		Transactions:  transactions,
		PaginationRes: paginationData,
	}, nil
}

func buildTransPaginationParams(pagination *model.Pagination, limit int) (*model.Pagination, error) {
	result := pagination
	result.Limit = limit

	// Priority to use offset for querying
	if pagination.HasOffsetQuery() {
		return result, nil
	}
	if !pagination.HasCursorQuery() {
		return result, nil
	}

	cursor, err := deserializeTransCursor(pagination.Cursor)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor: %w", err)
	}
	if pagination.IsNext() && cursor == 0 {
		result.Cursor = nil
		return result, nil
	}

	result.Cursor = cursor
	return result, nil
}

func processTransListData(
	pagination *model.Pagination,
	transactions []*model.Transaction,
	isOverLimit bool) []*model.Transaction {
	if pagination.HasOffsetQuery() {
		return transactions
	}

	transLen := len(transactions)
	if pagination.IsNext() && isOverLimit {
		return transactions[:transLen-1]
	}
	if pagination.IsPrev() && isOverLimit {
		return transactions[1:transLen]
	}
	return transactions
}

func buildTransPaginationData(
	pagination *model.Pagination,
	transactions []*model.Transaction,
	isOverLimit bool) model.PaginationResult {
	result := model.PaginationResult{}
	transLen := len(transactions)

	// Priority to use offset for querying
	if pagination.HasOffsetQuery() {
		result.HasNext = isOverLimit
		result.HasPrev = *pagination.Offset > 0
		return result
	}

	if pagination.IsNext() {
		result.HasNext = isOverLimit
		result.HasPrev = pagination.Cursor != nil && transLen > 0
	}
	if pagination.IsPrev() {
		result.HasNext = pagination.Cursor != nil && transLen > 0
		result.HasPrev = isOverLimit
	}

	if transLen != 0 {
		result.NextCursor = serializeTransCursor(transactions[transLen-1].TransID)
		result.PrevCursor = serializeTransCursor(transactions[0].TransID)
	}

	return result
}

func serializeTransCursor(transID int64) string {
	return base64.StdEncoding.EncodeToString([]byte(cast.ToString(transID)))
}

func deserializeTransCursor(cursor any) (int64, error) {
	decoded, err := base64.StdEncoding.DecodeString(cast.ToString(cursor))
	if err != nil {
		return 0, err
	}
	return cast.ToInt64(string(decoded)), nil
}

/**
10
9
8
7
6
5
4
3
*/
