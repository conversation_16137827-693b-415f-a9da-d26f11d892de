package transaction

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type GetTransactionRequest struct {
	ZalopayID int64
	ZPTransID int64
}

func (uc *Usecase) GetTransaction(ctx context.Context,
	req *GetTransactionRequest) (*model.Transaction, error) {
	transaction, err := uc.paymentRepo.GetTransactionByZPTransID(ctx, req.ZPTransID)
	if err != nil {
		return nil, fmt.Errorf("query transaction by zpTransID failed, %w", err)
	}

	if transaction.AccountInfo.ZalopayID != req.ZalopayID {
		return nil, fmt.Errorf("transaction not found, zpTransID: %d, zalopayID: %d", req.ZPTransID, req.ZalopayID)
	}

	return transaction, nil
}
