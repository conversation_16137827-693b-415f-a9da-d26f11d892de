package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.uber.org/mock/gomock"
)

func (suite *RefundTestSuite) TestPreCheck_Success_SettlementProcess() {
	ctx := context.Background()
	zpTransID := int64(12345)
	refundID := int64(67890)
	amount := int64(100000)

	req := model.RefundOrder{
		ZPTransID:          zpTransID,
		RefundID:           refundID,
		Amount:             amount,
		AppTransID:         "test_app_trans_id",
		AppID:              123,
		PaymentDescription: "Test refund",
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusOpen,
		LoanID: "test_loan_id",
	}

	expectedRefundOrder := &model.RefundOrder{
		ID:                 1,
		ZPTransID:          zpTransID,
		RefundID:           refundID,
		Amount:             amount,
		AppTransID:         "test_app_trans_id",
		AppID:              123,
		PaymentDescription: "Test refund",
		Status:             model.RefundStatusInit,
		RefundType:         model.RefundTypeAuto,
		ProcessType:        model.RefundProcessTypeSettlement,
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
			assert.Equal(suite.T(), model.RefundStatusInit, refundOrder.Status)
			assert.Equal(suite.T(), model.RefundTypeAuto, refundOrder.RefundType)
			assert.Equal(suite.T(), model.RefundProcessTypeSettlement, refundOrder.ProcessType)
			return expectedRefundOrder, nil
		})

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
	assert.Equal(suite.T(), model.SettleStatusUnsettled, result.SettleStatus)
}

func (suite *RefundTestSuite) TestPreCheck_Success_FundbackProcess() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
		RefundID:  67890,
		Amount:    100000,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusClosed,
		LoanID: "test_loan_id",
	}

	expectedRefundOrder := &model.RefundOrder{
		ID:          1,
		ZPTransID:   zpTransID,
		Status:      model.RefundStatusInit,
		RefundType:  model.RefundTypeAuto,
		ProcessType: model.RefundProcessTypeFundback,
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
			assert.Equal(suite.T(), model.RefundProcessTypeFundback, refundOrder.ProcessType)
			return expectedRefundOrder, nil
		})

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
	assert.Equal(suite.T(), model.SettleStatusSettled, result.SettleStatus)
}

func (suite *RefundTestSuite) TestPreCheck_Success_InCreationProcess() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
		RefundID:  67890,
		Amount:    100000,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusInCreation,
		LoanID: "test_loan_id",
	}

	expectedRefundOrder := &model.RefundOrder{
		ID:          1,
		ZPTransID:   zpTransID,
		Status:      model.RefundStatusInit,
		RefundType:  model.RefundTypeAuto,
		ProcessType: model.RefundProcessTypeSettlement,
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
			assert.Equal(suite.T(), model.RefundProcessTypeSettlement, refundOrder.ProcessType)
			return expectedRefundOrder, nil
		})

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
	assert.Equal(suite.T(), model.SettleStatusUnsettled, result.SettleStatus)
}

func (suite *RefundTestSuite) TestPreCheck_PaymentRepoError() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
	}

	expectedError := errors.New("payment not found")

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(nil, expectedError)

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestPreCheck_PaymentStatusNotSucceeded() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
	}

	testCases := []struct {
		name          string
		paymentStatus model.PaymentStatus
	}{
		{"Failed Status", model.PaymentStatusFailed},
		{"Pending Status", model.PaymentStatusPending},
		{"Processing Status", model.PaymentStatusProcessing},
		{"Init Status", model.PaymentStatusInit},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			payment := &model.PurchaseOrder{
				Status: tc.paymentStatus,
			}

			suite.mockPaymentRepo.EXPECT().
				GetPaymentByTransID(ctx, zpTransID).
				Return(payment, nil)

			result, err := suite.usecase.PreCheck(ctx, req)

			assert.Error(suite.T(), err)
			assert.Nil(suite.T(), result)
			assert.Contains(suite.T(), err.Error(), "payment status is not success")
		})
	}
}

func (suite *RefundTestSuite) TestPreCheck_InstallmentAdapterError() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	expectedError := errors.New("installment service unavailable")

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(model.GetInstallmentStatusResponse{}, expectedError)

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestPreCheck_InvalidInstallmentStatus() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusUnknown,
		LoanID: "test_loan_id",
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "invalid status")
}

func (suite *RefundTestSuite) TestPreCheck_CreateRefundLogError() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusOpen,
		LoanID: "test_loan_id",
	}

	expectedError := errors.New("database error")

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		Return(nil, expectedError)

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestPreCheck_ProcessTypeMapping() {
	ctx := context.Background()
	zpTransID := int64(12345)

	testCases := []struct {
		name                 string
		installmentStatus    model.InstallmentStatus
		expectedProcessType  model.RefundProcessType
		expectedSettleStatus model.SettleStatus
	}{
		{
			name:                 "Open Status Maps to Settlement",
			installmentStatus:    model.InstallmentStatusOpen,
			expectedProcessType:  model.RefundProcessTypeSettlement,
			expectedSettleStatus: model.SettleStatusUnsettled,
		},
		{
			name:                 "InCreation Status Maps to Settlement",
			installmentStatus:    model.InstallmentStatusInCreation,
			expectedProcessType:  model.RefundProcessTypeSettlement,
			expectedSettleStatus: model.SettleStatusUnsettled,
		},
		{
			name:                 "Closed Status Maps to Fundback",
			installmentStatus:    model.InstallmentStatusClosed,
			expectedProcessType:  model.RefundProcessTypeFundback,
			expectedSettleStatus: model.SettleStatusSettled,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			req := model.RefundOrder{
				ZPTransID: zpTransID,
				RefundID:  67890,
				Amount:    100000,
			}

			payment := &model.PurchaseOrder{
				Status: model.PaymentStatusSucceeded,
			}

			instStatus := model.GetInstallmentStatusResponse{
				Status: tc.installmentStatus,
				LoanID: "test_loan_id",
			}

			expectedRefundOrder := &model.RefundOrder{
				ID:          1,
				ZPTransID:   zpTransID,
				Status:      model.RefundStatusInit,
				RefundType:  model.RefundTypeAuto,
				ProcessType: tc.expectedProcessType,
			}

			suite.mockPaymentRepo.EXPECT().
				GetPaymentByTransID(ctx, zpTransID).
				Return(payment, nil)

			suite.mockInstallmentAdapter.EXPECT().
				GetInstallmentStatus(ctx, zpTransID).
				Return(instStatus, nil)

			suite.mockRefundRepo.EXPECT().
				CreateRefundLog(ctx, gomock.Any()).
				DoAndReturn(func(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
					assert.Equal(suite.T(), tc.expectedProcessType, refundOrder.ProcessType)
					assert.Equal(suite.T(), model.RefundStatusInit, refundOrder.Status)
					assert.Equal(suite.T(), model.RefundTypeAuto, refundOrder.RefundType)
					return expectedRefundOrder, nil
				})

			result, err := suite.usecase.PreCheck(ctx, req)

			assert.NoError(suite.T(), err)
			assert.NotNil(suite.T(), result)
			assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
			assert.Equal(suite.T(), tc.expectedSettleStatus, result.SettleStatus)
		})
	}
}

func (suite *RefundTestSuite) TestPreCheck_RequestModification() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID:          zpTransID,
		RefundID:           67890,
		Amount:             100000,
		AppTransID:         "test_app_trans_id",
		AppID:              123,
		PaymentDescription: "Test refund",
		Status:             model.RefundStatusUnknown,
		RefundType:         model.RefundTypeManual,
		ProcessType:        model.RefundProcessTypeManual,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusOpen,
		LoanID: "test_loan_id",
	}

	expectedRefundOrder := &model.RefundOrder{
		ID:                 1,
		ZPTransID:          zpTransID,
		RefundID:           67890,
		Amount:             100000,
		AppTransID:         "test_app_trans_id",
		AppID:              123,
		PaymentDescription: "Test refund",
		Status:             model.RefundStatusInit,
		RefundType:         model.RefundTypeAuto,
		ProcessType:        model.RefundProcessTypeSettlement,
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
			assert.Equal(suite.T(), model.RefundStatusInit, refundOrder.Status)
			assert.Equal(suite.T(), model.RefundTypeAuto, refundOrder.RefundType)
			assert.Equal(suite.T(), model.RefundProcessTypeSettlement, refundOrder.ProcessType)
			assert.Equal(suite.T(), zpTransID, refundOrder.ZPTransID)
			assert.Equal(suite.T(), int64(67890), refundOrder.RefundID)
			assert.Equal(suite.T(), int64(100000), refundOrder.Amount)
			assert.Equal(suite.T(), "test_app_trans_id", refundOrder.AppTransID)
			assert.Equal(suite.T(), int32(123), refundOrder.AppID)
			assert.Equal(suite.T(), "Test refund", refundOrder.PaymentDescription)
			return expectedRefundOrder, nil
		})

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
	assert.Equal(suite.T(), model.SettleStatusUnsettled, result.SettleStatus)
}

func (suite *RefundTestSuite) TestPreCheck_EmptyRequest() {
	ctx := context.Background()

	req := model.RefundOrder{}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusOpen,
		LoanID: "test_loan_id",
	}

	expectedRefundOrder := &model.RefundOrder{
		ID:          1,
		Status:      model.RefundStatusInit,
		RefundType:  model.RefundTypeAuto,
		ProcessType: model.RefundProcessTypeSettlement,
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, int64(0)).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, int64(0)).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		Return(expectedRefundOrder, nil)

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
	assert.Equal(suite.T(), model.SettleStatusUnsettled, result.SettleStatus)
}

func (suite *RefundTestSuite) TestPreCheck_ZeroAmountRequest() {
	ctx := context.Background()
	zpTransID := int64(12345)

	req := model.RefundOrder{
		ZPTransID: zpTransID,
		Amount:    0,
	}

	payment := &model.PurchaseOrder{
		Status: model.PaymentStatusSucceeded,
	}

	instStatus := model.GetInstallmentStatusResponse{
		Status: model.InstallmentStatusOpen,
		LoanID: "test_loan_id",
	}

	expectedRefundOrder := &model.RefundOrder{
		ID:          1,
		ZPTransID:   zpTransID,
		Amount:      0,
		Status:      model.RefundStatusInit,
		RefundType:  model.RefundTypeAuto,
		ProcessType: model.RefundProcessTypeSettlement,
	}

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(payment, nil)

	suite.mockInstallmentAdapter.EXPECT().
		GetInstallmentStatus(ctx, zpTransID).
		Return(instStatus, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundLog(ctx, gomock.Any()).
		Return(expectedRefundOrder, nil)

	result, err := suite.usecase.PreCheck(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedRefundOrder, result.RefundOrder)
	assert.Equal(suite.T(), model.SettleStatusUnsettled, result.SettleStatus)
}
