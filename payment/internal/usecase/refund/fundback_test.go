package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.uber.org/mock/gomock"
)

func (suite *RefundTestSuite) TestExecuteFundback_SettleStatusNotSettled() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:     1,
		Status: model.RefundSettleStatusPending,
	}

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "settle status is not settled")
}

func (suite *RefundTestSuite) TestExecuteFundback_ZeroPaybackAmount() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		NetRefundAmount:   1000,
		UserTopupAmount:   500,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleStatusByID(ctx, settle.ID, model.RefundSettleStatusCompleted).
		Return(nil)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestExecuteFundback_ZeroPaybackAmount_UpdateError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		NetRefundAmount:   1000,
		UserTopupAmount:   500,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	expectedErr := errors.New("update error")

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleStatusByID(ctx, settle.ID, model.RefundSettleStatusCompleted).
		Return(expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_ListOrdersError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	expectedErr := errors.New("list orders error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(nil, expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_HasProcessingOrder() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusProcessing,
		},
	}

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestExecuteFundback_BeginTxError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	expectedErr := errors.New("begin tx error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(nil, expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_GetPaymentError() {
	ctx := context.Background()
	tCtx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		ZPTransID:         12345,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	expectedErr := errors.New("get payment error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(tCtx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(tCtx)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(tCtx, settle.ZPTransID).
		Return(nil, expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_CreateOrderAdapterError() {
	ctx := context.Background()
	tCtx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		ZPTransID:         12345,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	purchase := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   67890,
			PartnerCode: "PARTNER_A",
		},
	}
	expectedErr := errors.New("create order adapter error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(tCtx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(tCtx)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(tCtx, settle.ZPTransID).
		Return(purchase, nil)

	suite.mockAcquiringCore.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(nil, expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_CreateFundbackOrderError() {
	ctx := context.Background()
	tCtx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		ZPTransID:         12345,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	purchase := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   67890,
			PartnerCode: "PARTNER_A",
		},
	}
	acOrder := &model.CreateRefundOrderResponse{
		AppID:        123,
		AppTransID:   "test_app_trans_id",
		Amount:       500,
		ZpTransToken: "test_token",
		Description:  "Test description",
		OrderNo:      12345,
		DataChecksum: "test_checksum",
	}
	expectedErr := errors.New("create fundback order error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(tCtx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(tCtx)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(tCtx, settle.ZPTransID).
		Return(purchase, nil)

	suite.mockAcquiringCore.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(acOrder, nil)

	suite.mockOrderRepo.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(nil, expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_SubmitOrderError() {
	ctx := context.Background()
	tCtx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		ZPTransID:         12345,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	purchase := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   67890,
			PartnerCode: "PARTNER_A",
		},
	}
	acOrder := &model.CreateRefundOrderResponse{
		AppID:        123,
		AppTransID:   "test_app_trans_id",
		Amount:       500,
		ZpTransToken: "test_token",
		Description:  "Test description",
		OrderNo:      12345,
		DataChecksum: "test_checksum",
	}
	fbOrder := &model.RefundFundbackOrder{
		ID:             1,
		ZalopayID:      67890,
		AppTransID:     "test_app_trans_id",
		AppID:          123,
		Amount:         500,
		ZPTransToken:   "test_token",
		Type:           model.OrderTypeRefund,
		Status:         model.OrderStatusInit,
		Stage:          model.OrderStageFundback,
		Description:    "Test description",
		RefundSettleID: 1,
		ZPTransID:      12345,
		PartnerCode:    "PARTNER_A",
	}
	expectedErr := errors.New("submit order error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(tCtx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(tCtx)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(tCtx, settle.ZPTransID).
		Return(purchase, nil)

	suite.mockAcquiringCore.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(acOrder, nil)

	suite.mockOrderRepo.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(fbOrder, nil)

	suite.mockAcquiringCore.EXPECT().
		SubmitRefundFundbackOrder(tCtx, fbOrder).
		Return(expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_CommitTxError() {
	ctx := context.Background()
	tCtx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		ZPTransID:         12345,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	purchase := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   67890,
			PartnerCode: "PARTNER_A",
		},
	}
	acOrder := &model.CreateRefundOrderResponse{
		AppID:        123,
		AppTransID:   "test_app_trans_id",
		Amount:       500,
		ZpTransToken: "test_token",
		Description:  "Test description",
		OrderNo:      12345,
		DataChecksum: "test_checksum",
	}
	fbOrder := &model.RefundFundbackOrder{
		ID:             1,
		ZalopayID:      67890,
		AppTransID:     "test_app_trans_id",
		AppID:          123,
		Amount:         500,
		ZPTransToken:   "test_token",
		Type:           model.OrderTypeRefund,
		Status:         model.OrderStatusInit,
		Stage:          model.OrderStageFundback,
		Description:    "Test description",
		RefundSettleID: 1,
		ZPTransID:      12345,
		PartnerCode:    "PARTNER_A",
	}
	expectedErr := errors.New("commit tx error")

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(tCtx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(tCtx)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(tCtx, settle.ZPTransID).
		Return(purchase, nil)

	suite.mockAcquiringCore.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(acOrder, nil)

	suite.mockOrderRepo.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(fbOrder, nil)

	suite.mockAcquiringCore.EXPECT().
		SubmitRefundFundbackOrder(tCtx, fbOrder).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(tCtx).
		Return(expectedErr)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

func (suite *RefundTestSuite) TestExecuteFundback_Success() {
	ctx := context.Background()
	tCtx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		Status:            model.RefundSettleStatusSettled,
		ZPTransID:         12345,
		NetRefundAmount:   1000,
		UserTopupAmount:   1000,
		SettlementAmount:  1500,
		UserPaybackAmount: 0,
	}
	fbOrders := []*model.RefundFundbackOrder{
		{
			ID:     1,
			Status: model.OrderStatusSucceeded,
		},
	}
	purchase := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   67890,
			PartnerCode: "PARTNER_A",
		},
	}
	acOrder := &model.CreateRefundOrderResponse{
		AppID:        123,
		AppTransID:   "test_app_trans_id",
		Amount:       500,
		ZpTransToken: "test_token",
		Description:  "Test description",
		OrderNo:      12345,
		DataChecksum: "test_checksum",
	}
	fbOrder := &model.RefundFundbackOrder{
		ID:             1,
		ZalopayID:      67890,
		AppTransID:     "test_app_trans_id",
		AppID:          123,
		Amount:         500,
		ZPTransToken:   "test_token",
		Type:           model.OrderTypeRefund,
		Status:         model.OrderStatusInit,
		Stage:          model.OrderStageFundback,
		Description:    "Test description",
		RefundSettleID: 1,
		ZPTransID:      12345,
		PartnerCode:    "PARTNER_A",
	}

	suite.mockOrderRepo.EXPECT().
		ListRefundFundbackOrder(ctx, settle.ID).
		Return(fbOrders, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(tCtx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(tCtx)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(tCtx, settle.ZPTransID).
		Return(purchase, nil)

	suite.mockAcquiringCore.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(acOrder, nil)

	suite.mockOrderRepo.EXPECT().
		CreateRefundFundbackOrder(tCtx, gomock.Any()).
		Return(fbOrder, nil)

	suite.mockAcquiringCore.EXPECT().
		SubmitRefundFundbackOrder(tCtx, fbOrder).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(tCtx).
		Return(nil)

	err := suite.usecase.ExecuteFundback(ctx, settle)

	assert.NoError(suite.T(), err)
}
