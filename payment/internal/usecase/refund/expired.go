package refund

import (
	"context"
	"sync"
	"time"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
	workerExpired = 5
)

type ExpiredRefundSettleRequest struct {
	RefundID     int64
	SettleAmount int64
	RefZPTransID int64
}

func (uc *Usecase) ExcuteExpiredRefundSettlement(ctx context.Context, params *ExpiredRefundSettleRequest) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "excute expired refund settlement")

	// Precheck if expired refund settlement is needed
	eligible, err := uc.isEligibleForExpiredRefundSettle(ctx, params.RefundID)
	if err != nil {
		logger.Errorw("msg", "check eligible for expired refund settlement fail", "error", err)
		return err
	}
	if !eligible {
		logger.Infow("msg", "refund log is not eligible for expired refund settlement")
		return nil
	}

	account, purchase, bankRoute, err := uc.getResourceForPreparingExpiredSettle(ctx, params.RefZPTransID)
	if err != nil {
		logger.Errorw("msg", "get resource for preparing expired settle fail", "error", err)
		return err
	}

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin transaction fail", "error", err)
		return err
	}
	defer uc.txn.RollbackTx(tCtx)

	settleOrder, err := uc.buildExpiredRefundSettleOrder(params, purchase)
	if err != nil {
		logger.Errorw("msg", "build expired refund settle order fail", "error", err)
		return err
	}
	settleOrder, err = uc.orderRepo.CreateRefundSettleOrder(tCtx, settleOrder)
	if err != nil {
		logger.Errorw("msg", "create refund settle order fail", "error", err)
		return err
	}

	repayLog := model.NewRepaymentLogForSettlement(settleOrder, account, bankRoute)
	repayLog, err = uc.paymentRepo.CreateRepaymentLog(tCtx, *repayLog)
	if err != nil {
		logger.Errorw("msg", "create repayment log fail", "error", err)
		return err
	}

	if err = uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit transaction fail", "error", err)
		return err
	}

	if err := uc.ProcessRepayment(ctx, repayLog); err != nil {
		logger.Errorw("msg", "process repayment fail", "error", err)
		return err
	}

	logger.Infow("msg", "excute expired refund settlement success", "settle_id", settleOrder.ID)

	return nil
}

func (uc *Usecase) ProcessRepayment(ctx context.Context, repayLog *model.RepaymentLog) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "process repayment")

	repayReq := model.ODRepaymentRequest{
		RefOrderID:         repayLog.Order.ID,
		PartnerReqID:       idgen.GenPaymentTransID(),
		Amount:             repayLog.Order.Amount,
		PaymentDescription: repayLog.Order.Description,
		ZalopayID:          repayLog.AccountInfo.ZalopayID,
		CorpAccountNumber:  repayLog.BankRoute.BankAccountNumber,
		CorpAccountName:    repayLog.BankRoute.BankAccountName,
		BankAccountNumber:  repayLog.AccountInfo.PartnerAccountId,
		BankAccountName:    repayLog.AccountInfo.PartnerAccountName,
	}
	repayRes, err := uc.connector.ODRepayment(ctx, repayReq)
	if err != nil || repayRes.IsError() {
		if err == nil {
			err = repayRes.CIMBError
		}
		logger.Errorw("msg", "OD repayment fail", "error", err)
		return uc.handleSubmitRepaymentFailed(ctx, repayLog, err)
	}

	repayLog = repayLog.HandlePartnerRepayResult(repayRes)
	if err = uc.paymentRepo.UpdateRepaymentLogStatus(ctx, *repayLog); err != nil {
		logger.Errorw("msg", "update repayment log fail", "error", err)
		return err
	}

	switch repayLog.Status {
	case model.PaymentStatusSucceeded, model.PaymentStatusFailed:
		if err = uc.PublishRefundExpiredResult(ctx, repayLog); err != nil {
			logger.Errorw("msg", "publish refund expired result fail", "error", err)
			return err
		}
		logger.Infow("msg", "publish expired refund result success", "repay_log_id", repayLog.ID)
		return nil
	case model.PaymentStatusPending, model.PaymentStatusProcessing:
		if err = uc.TriggerPollingRepayStatus(ctx, repayLog); err != nil {
			logger.Errorw("msg", "trigger polling repay status fail", "error", err)
			return err
		}
		logger.Infow("msg", "trigger polling repay status success", "repay_log_id", repayLog.ID)
		return nil
	default:
		logger.Errorw("msg", "invalid repayment log status", "status", repayLog.Status)
		return errors.New("invalid repayment log status")
	}
}

func (uc *Usecase) ProcessExpiredRefund(ctx context.Context) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "start process expired refund")

	settleIDsCh, err := uc.buildListRefundLogExpiredCh(ctx)
	if err != nil {
		logger.Errorw("msg", "build list refund log expired channel fail", "error", err)
		return err
	}
	if settleIDsCh == nil {
		logger.Infow("msg", "no expired refund logs to process")
		return nil
	}

	waitGroup := new(sync.WaitGroup)
	waitGroup.Add(workerExpired)

	for i := range workerExpired {
		go func(id int) {
			defer waitGroup.Done()
			logger.Infow("msg", "worker process expired refund logs", "worker_id", id)
			uc.workerProcessExpired(ctx, settleIDsCh)
		}(i)
	}

	waitGroup.Wait()

	return nil
}

func (uc *Usecase) TriggerPollingRepayStatus(ctx context.Context, repayLog *model.RepaymentLog) error {
	logger := uc.logger.WithContext(ctx)
	retryOpts := []retry.Option{
		retry.Attempts(3),
		retry.Delay(3 * time.Second),
	}

	logger.Infow("msg", "trigger polling repay status")

	err := retry.Do(func() error {
		err := uc.taskJob.ExecuteExpiredRefundRepaymentPollingJob(ctx, &model.RepaymentStatusPollingRequest{
			OrderID:   repayLog.Order.ID,
			PaymentID: repayLog.ID,
		})
		if err != nil {
			return err
		}
		return nil
	}, retryOpts...)
	if err != nil {
		logger.Errorw("msg", "trigger polling repay status fail", "error", err)
		return err
	}

	logger.Infow("msg", "trigger polling repay status success", "repay_log_id", repayLog.ID)

	return nil
}

func (uc *Usecase) PublishRefundExpiredResult(ctx context.Context, repayLog *model.RepaymentLog) error {
	logger := uc.logger.WithContext(ctx)
	retryOpts := []retry.Option{
		retry.Attempts(3),
		retry.Delay(3 * time.Second),
	}

	err := retry.Do(func() error {
		order, err := uc.orderRepo.GetRefundSettleOrder(ctx,
			repayLog.Order.AppTransID,
			repayLog.Order.AppID,
		)
		if err != nil {
			logger.Errorw("msg", "get refund settle order fail", "error", err)
			return err
		}

		err = uc.settleNotifier.PublishRefundExpiredResult(ctx, order, repayLog)
		if err != nil {
			return err
		}
		return nil
	}, retryOpts...)
	if err != nil {
		logger.Errorw("msg", "publish refund expired result fail", "error", err)
		return err
	}

	logger.Infow("msg", "publish expired refund result")

	return nil
}

func (uc *Usecase) workerProcessExpired(ctx context.Context, settleIDsCh <-chan int64) {
	logger := uc.logger.WithContext(ctx)

	for settleID := range settleIDsCh {
		logger.Infow("msg", "process expired refund", "settle_id", settleID)

		// Get refund logs by settle ID
		refundSettle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
		if err != nil {
			logger.Errorw("msg", "get refund logs by settle ID fail", "error", err)
			continue
		}

		err = uc.processExpiredRefundLogs(ctx, refundSettle)
		if err != nil {
			logger.Errorw("msg", "process expired refund fail", "error", err)
			continue
		}

		logger.Infow("msg", "process expired refund success", "settle_id", settleID)

		newCtx := context.WithoutCancel(ctx)
		go uc.TriggerJobReconcileRefundSettlements(newCtx, refundSettle.ZPTransID)
	}
}

func (uc *Usecase) processExpiredRefundLogs(ctx context.Context, settle *model.RefundSettle) error {
	logger := uc.logger.WithContext(ctx)

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin transaction fail", "error", err)
		return err
	}
	defer uc.txn.RollbackTx(tCtx)

	refundLogs, err := uc.repo.GetRefundLogsExpiredByZPTransIDForUpdate(tCtx, settle.ZPTransID)
	if err != nil {
		logger.Errorw("msg", "get refund logs by ZPTransID fail", "error", err)
		return err
	}
	if len(refundLogs) == 0 {
		logger.Infow("msg", "no refund logs to process")
		return nil
	}

	refundLogsCast := model.RefundOrders(refundLogs)

	if err = uc.repo.MarkRefundLogsAsExpiredByIDs(tCtx, refundLogsCast.GetIDs()); err != nil {
		logger.Errorw("msg", "mark refund logs as expired fail", "error", err)
		return err
	}

	if err = uc.settleNotifier.PublishRefundExpiredEvents(tCtx, settle.ZPTransID, refundLogsCast); err != nil {
		logger.Errorw("msg", "publish refund settle result fail", "error", err)
		return err
	}

	if err = uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit transaction fail", "error", err)
		return err
	}

	logger.Infow("msg", "process expired refund success", "settle_id", settle.ID)

	return nil
}

func (uc *Usecase) buildListRefundLogExpiredCh(ctx context.Context) (<-chan int64, error) {
	lastID := int64(0)
	logger := uc.logger.WithContext(ctx)
	result := make(chan int64, channelSize)

	go func() {
		defer close(result)
		for {
			cursor := cast.ToString(lastID)
			pagination := &model.Pagination{
				Limit:  inquirySize,
				Cursor: &cursor,
			}
			listRfLogs, err := uc.repo.GetRefundSettleIDsHasExpiredItem(ctx, pagination)
			if err != nil {
				logger.Errorw("msg", "get list refund log has expired fail", "error", err)
				return
			}
			if len(listRfLogs) == 0 {
				// No more refund log to process
				logger.Infow("msg", "no more refund log to process")
				break
			}
			for _, item := range listRfLogs {
				result <- item
			}
			lastID = listRfLogs[len(listRfLogs)-1]
		}
	}()

	return result, nil
}

func (uc *Usecase) isEligibleForExpiredRefundSettle(ctx context.Context, refundID int64) (bool, error) {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "precheck expired refund settle")

	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin transaction fail", "error", err)
		return false, err
	}
	defer uc.txn.RollbackTx(ctx)

	// Check if refund log is expired
	refundLog, err := uc.repo.GetRefundLogForUpdate(ctx, refundID)
	if err != nil {
		logger.Errorw("msg", "get refund log fail", "error", err)
		return false, err
	}
	if refundLog.ProcessType != model.RefundProcessTypeRepayment {
		logger.Infow("msg", "refund log is not repayment type")
		return false, nil
	}

	settleOrder, err := uc.orderRepo.GetRefundSettleOrderByRefundID(ctx, refundID)
	if errors.Is(err, model.ErrOrderNotFound) {
		logger.Infow("msg", "refund settle order not found")
		return true, nil
	}
	if err != nil {
		logger.Errorw("msg", "get refund settle order fail", "error", err)
		return false, err
	}

	repayLog, err := uc.paymentRepo.GetRepaymentByOrderID(ctx, settleOrder.ID)
	if err != nil {
		logger.Errorw("msg", "get repayment log fail", "error", err)
		return false, err
	}

	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorw("msg", "commit transaction fail", "error", err)
		return false, err
	}

	return settleOrder.IsFailedOrCancelled() && repayLog.Status.IsFailedStatus(), nil
}

func (uc *Usecase) buildExpiredRefundSettleOrder(params *ExpiredRefundSettleRequest, payment *model.PurchaseOrder) (*model.RefundSettleOrder, error) {
	config, ok := uc.orderCfgs.GetConfigRefundSettle("")
	if !ok {
		return nil, errors.New("refund settle order config not found")
	}

	acctInfo := payment.AccountInfo
	result := model.NewRefundSettleOrder(
		acctInfo.ZalopayID,
		acctInfo.PartnerCode,
		params.RefZPTransID,
		params.SettleAmount,
	)
	result.AppID = config.GetAppId()
	result.RefundID = params.RefundID
	result.AppTransID = idgen.GenAppTransID()
	result.Description = SettleOrderDescription

	return result, nil
}

func (uc *Usecase) getResourceForPreparingExpiredSettle(ctx context.Context, refZPTransID int64) (*model.Account, *model.PurchaseOrder, *model.BankRoute, error) {
	logger := uc.logger.WithContext(ctx)

	purchase, err := uc.paymentRepo.GetPaymentByTransID(ctx, refZPTransID)
	if err != nil {
		logger.Errorw("msg", "get purchase fail", "error", err)
		return nil, nil, nil, err
	}

	account, err := uc.accountAdapter.GetAccount(ctx,
		purchase.AccountInfo.ZalopayID,
		purchase.AccountInfo.PartnerCode,
	)
	if err != nil {
		logger.Errorw("msg", "get account fail", "error", err)
		return nil, nil, nil, err
	}

	bankRoute, err := uc.paymentRepo.GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment)
	if err != nil {
		logger.Errorw("msg", "get bank route fail", "error", err)
		return nil, nil, nil, err
	}

	return &account, purchase, bankRoute, nil
}

func (uc *Usecase) handleSubmitRepaymentFailed(ctx context.Context, repayLog *model.RepaymentLog, submitErr error) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "handle failed submit repayment")

	repayLog = repayLog.HandlePartnerRepayError(submitErr)
	if err := uc.paymentRepo.UpdateRepaymentLogStatus(ctx, *repayLog); err != nil {
		logger.Errorw("msg", "update repayment log fail", "error", err)
		return err
	}

	if err := uc.TriggerPollingRepayStatus(ctx, repayLog); err != nil {
		logger.Errorw("msg", "trigger polling repay status fail", "error", err)
		return err
	}

	logger.Infow("msg", "trigger polling repay status success", "repay_log_id", repayLog.ID)

	return nil
}
