package refund

import (
	"context"
	"time"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
	SettleOrderDescription = "Tất toán khoản vay"
)

func (uc *Usecase) ProcessRefundSettleInfo(ctx context.Context, settleID int64) error {
	logger := uc.logger.WithContext(ctx)

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin or reuse tx fail", "error", err)
		return err
	}
	defer uc.txn.RollbackTx(tCtx)

	refundSettle, err := uc.repo.GetRefundSettleForUpdate(tCtx, settleID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return err
	}
	if !refundSettle.ShouldTriggerSettlement() {
		logger.Infow("msg", "not have condition to trigger settlement", "refundSettle", refundSettle)
		return nil
	}

	err = uc.repo.UpdateRefundSettleStatusByID(tCtx, settleID, model.RefundSettleStatusPending)
	if err != nil {
		logger.Errorw("msg", "update refund settle status fail", "error", err)
		return err
	}

	if err = uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit tx fail", "error", err)
		return err
	}

	err = uc.PublishRefundSettleRequest(ctx, refundSettle)
	if err != nil {
		logger.Errorw("msg", "publish refund settle request fail", "error", err)
		return err
	}

	return nil
}

func (uc *Usecase) DispatchRefundSettleChanged(ctx context.Context, settleID int64) error {
	logger := uc.logger.WithContext(ctx)
	retryOpts := []retry.Option{
		retry.Attempts(3),
		retry.Delay(500 * time.Millisecond),
	}
	err := retry.Do(func() error {
		tCtx, err := uc.txn.BeginTx(ctx)
		if err != nil {
			logger.Errorw("msg", "begin tx fail", "error", err)
			return err
		}
		defer uc.txn.RollbackTx(tCtx)

		refundSettle, err := uc.repo.GetRefundSettleByID(tCtx, settleID)
		if err != nil {
			logger.Errorw("msg", "get refund settle fail", "error", err)
			return err
		}
		if !refundSettle.HasSnapshotChanged() {
			logger.Infow("msg", "refund settle snapshot not changed")
			if err = uc.txn.CommitTx(tCtx); err != nil {
				logger.Errorw("msg", "commit tx fail", "error", err)
				return err
			}
			return nil
		}

		originVersion := refundSettle.RefreshSnapshotData()
		if err = uc.repo.UpdateRefundSettleEventData(tCtx, refundSettle, originVersion); err != nil {
			logger.Errorw("msg", "update refund settle fail", "error", err)
			return err
		}
		if err = uc.installmentAdapter.NotifyInstallmentRefund(tCtx, refundSettle); err != nil {
			logger.Errorw("msg", "notify installment refund fail", "error", err)
			return err
		}

		if err = uc.txn.CommitTx(tCtx); err != nil {
			logger.Errorw("msg", "commit tx fail", "error", err)
			return err
		}
		return nil
	}, retryOpts...)

	if err != nil {
		logger.Errorw("msg", "dispatch refund settle changed fail", "error", err)
		return err
	}
	return nil
}

// syncSettlementAmountIfChanged is a helper method that checks if settlement amount has changed
// and updates it in the database if needed
func (uc *Usecase) syncSettlementAmountIfChanged(ctx context.Context, settle *model.RefundSettle, earlyDischarge model.EarlyDischarge) (*model.RefundSettle, error) {
	logger := uc.logger.WithContext(ctx)

	if settle.SettlementAmount == earlyDischarge.TotalAmount {
		logger.Infow("msg", "settlement amount not changed")
		return settle, nil
	}

	settle, err := uc.repo.GetRefundSettleByID(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return nil, err
	}

	err = uc.repo.UpdateRefundSettlementAmount(ctx, settle.ID, earlyDischarge.TotalAmount)
	if err != nil {
		logger.Errorw("msg", "update refund settle fail", "error", err)
		return nil, err
	}

	return settle.SetSettlementAmount(earlyDischarge.TotalAmount), nil
}

func (uc *Usecase) SyncLatestSettlementAmount(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) {
	logger := uc.logger.WithContext(ctx)

	earlyDischarge, err := uc.installmentAdapter.GetEarlyDischarge(ctx, settle.ZPTransID, true)
	if err != nil {
		logger.Errorw("msg", "get early discharge fail", "error", err)
		return nil, err
	}
	if err = earlyDischarge.Validate(); err != nil {
		logger.Errorw("msg", "validate early discharge fail", "error", err)
		return nil, err
	}

	return uc.syncSettlementAmountIfChanged(ctx, settle, earlyDischarge)
}

func (uc *Usecase) PublishRefundSettleRequest(ctx context.Context, settle *model.RefundSettle) error {
	logger := uc.logger.WithContext(ctx)

	if err := uc.settleNotifier.PublishRefundSettleEvent(ctx, settle); err != nil {
		logger.Errorw("msg", "publish refund settle event fail", "error", err)
		return err
	}

	logger.Infow("msg", "refund settle event published successfully", "settleInfo", settle)

	return nil
}

func (uc *Usecase) PublishRefundSettleResult(ctx context.Context, eadLog *model.EarlyDischargeLog) error {
	logger := uc.logger.WithContext(ctx)

	order, err := uc.orderRepo.GetRefundSettleOrder(ctx, eadLog.Order.AppTransID, eadLog.Order.AppID)
	if err != nil {
		logger.Errorw("msg", "get refund settle order fail", "error", err)
		return err
	}

	settle, err := uc.repo.GetRefundSettleByID(ctx, order.RefundSettleID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return err
	}

	if err := uc.settleNotifier.PublishRefundSettleResult(ctx, settle, eadLog); err != nil {
		logger.Errorw("msg", "publish refund settle event fail", "error", err)
		return err
	}

	logger.Infow("msg", "refund settle event published successfully", "settleInfo", settle)

	return nil
}

func (uc *Usecase) GetRefundSettleInfo(ctx context.Context, settleID int64) (*model.RefundSettle, error) {
	logger := uc.logger.WithContext(ctx)

	settle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return nil, err
	}

	return settle, nil
}

func (uc *Usecase) GetRefundSettleByZPTransID(ctx context.Context, zpTransID int64) (*model.RefundSettle, error) {
	logger := uc.logger.WithContext(ctx)

	settle, err := uc.repo.GetRefundSettleByZPTransID(ctx, zpTransID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return nil, err
	}

	return settle, nil
}

func (uc *Usecase) ExecuteRefundSettlement(ctx context.Context, settleID int64) error {
	logger := uc.logger.WithContext(ctx)

	settle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return err
	}
	if !settle.CanStartSettlement() {
		logger.Infow("msg", "refund settlement cannot start", "settleID", settle.ID, "status", settle.Status)
		return nil
	}

	settle, err = uc.SyncLatestSettlementAmount(ctx, settle)
	if err != nil {
		logger.Warnw("msg", "sync latest settlement amount fail", "error", err)
		return err
	}
	if !settle.HasSufficientFunds() {
		logger.Infow("msg", "not have sufficient funds to trigger settlement", "settle", settle)
		uc.TriggerJobReconcileRefundSettlements(ctx, settle.ZPTransID)
		return model.ErrInsufficientSettlementFunds
	}

	account, purchase, bankRoute, err := uc.getResourceForPreparingStandardSettle(ctx, settle.ZPTransID)
	if err != nil {
		logger.Errorw("msg", "get resource for preparing standard settle fail", "error", err)
		return err
	}

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin tx fail", "error", err)
		return err
	}
	defer uc.txn.RollbackTx(tCtx)

	if err = uc.repo.UpdateRefundSettleStatusByID(tCtx, settle.ID, model.RefundSettleStatusProcessing); err != nil {
		logger.Errorw("msg", "update refund settle status fail", "error", err)
		return err
	}

	order, err := uc.buildRefundSettleOrder(settle, purchase)
	if err != nil {
		logger.Errorw("msg", "build refund settle order fail", "error", err)
		return err
	}

	order, err = uc.orderRepo.CreateRefundSettleOrder(tCtx, order)
	if err != nil {
		logger.Errorw("msg", "create refund settle order fail", "error", err)
		return err
	}

	dischargeLog := model.NewEarlyDischargeLog(order, bankRoute, account)
	dischargeLog, err = uc.paymentRepo.CreateEarlyDischargeLog(tCtx, *dischargeLog)
	if err != nil {
		logger.Errorw("msg", "create early discharge log fail", "error", err, "discharge_log", dischargeLog)
		return err
	}

	if err = uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit tx fail", "error", err)
		return err
	}

	installmentStatus, err := uc.installmentAdapter.GetInstallmentStatus(ctx, cast.ToInt64(purchase.ZpTransID))
	if err != nil {
		logger.Errorw("msg", "get installment status fail", "error", err)
		return err
	}

	earlyDischargeReq := model.EarlyDischargeRequest{
		PaymentID:          dischargeLog.ID,
		RequestID:          dischargeLog.PartnerReqID,
		LoanID:             installmentStatus.LoanID,
		Amount:             dischargeLog.Order.Amount,
		PaymentDescription: SettleOrderDescription,
		CorpAccountNumber:  dischargeLog.BankRoute.BankAccountNumber,
		CorpAccountName:    dischargeLog.BankRoute.BankAccountName,
	}

	result, err := uc.connector.SubmitEarlyDischarge(ctx, earlyDischargeReq)
	if err != nil || result.IsError() {
		if err == nil {
			err = result.CIMBError
		}
		logger.Errorw("msg", "submit early discharge fail", "error", err, "req", earlyDischargeReq)
		return uc.handleSubmitEarlyDischargeError(ctx, err, dischargeLog)
	}

	err = uc.handleSubmitEarlyDischargeResult(ctx, settle, result, dischargeLog)
	if err != nil {
		logger.Errorw("msg", "handle early discharge result fail", "error", err)
		return err
	}

	return nil
}

// handleSubmitEarlyDischargeError sets error information in the discharge log
// This is a simple helper method that doesn't handle transactions or trigger polling
func (uc *Usecase) handleSubmitEarlyDischargeError(ctx context.Context, submitErr error, dischargeLog *model.EarlyDischargeLog) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "handle early discharge submission error info", "discharge_id", dischargeLog.ID, "error", submitErr)

	// Set error info in the discharge log
	dischargeLog.Status = model.PaymentStatusPending
	dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode = "submit_early_discharge_error"
	dischargeLog.PartnerData.RepaymentResult.CIMBError.Description = submitErr.Error()

	dischargeLog, err := uc.paymentRepo.UpdateEarlyDischargeLog(ctx, *dischargeLog)
	if err != nil {
		logger.Errorw("msg", "update early discharge log fail", "error", err)
		return errors.Wrap(err, "failed to update early discharge log")
	}

	// Trigger polling regardless of submission result
	err = uc.TriggerPollingEarlyDischargeStatus(ctx, dischargeLog.ID, dischargeLog.AccountInfo.ZalopayID)
	if err != nil {
		logger.Errorw("msg", "trigger polling early discharge status fail", "error", err)
		return errors.Wrap(err, "failed to trigger polling early discharge status")
	}

	logger.Errorw("msg", "Handled early discharge submission error", "discharge_id", dischargeLog.ID)

	return nil
}

func (uc *Usecase) handleSubmitEarlyDischargeResult(ctx context.Context, settle *model.RefundSettle, result *model.EarlyDischargeResult, dischargeLog *model.EarlyDischargeLog) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "handle early discharge submission result", "result", *result)

	needReconcilation := result.NeedReconcilation
	dischargeLog.Status = model.FromCIMBPaymentStatusToPaymentStatus(result.TransactionStatus)
	switch dischargeLog.Status {
	case model.PaymentStatusSucceeded:
		dischargeLog.PartnerData.RepaymentResult.CIMBError = model.CIMBError{}
	case model.PaymentStatusFailed:
		dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode = result.ErrorCode
		dischargeLog.PartnerData.RepaymentResult.CIMBError.Description = result.ErrorCode
	}

	err := uc.paymentRepo.UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, *dischargeLog)
	if err != nil {
		logger.Errorw("msg", "update early discharge log fail", "error", err)
		return errors.Wrap(err, "failed to update early discharge log status")
	}

	switch dischargeLog.Status {
	case model.PaymentStatusSucceeded, model.PaymentStatusFailed:
		if err = uc.PublishRefundSettleResult(ctx, dischargeLog); err != nil {
			logger.Errorw("msg", "publish refund settle result fail", "error", err)
			return err
		}
		logger.Infow("msg", "publish refund settle result success", "discharge_id", dischargeLog.ID)

		if !needReconcilation {
			return nil
		}
		if err := uc.TriggerJobReconcileRefundSettlements(ctx, settle.ZPTransID); err != nil {
			logger.Errorw("msg", "trigger job reconcile refund settlements fail", "error", err)
			return err
		}
		logger.Infow("msg", "trigger job reconcile refund settlements success", "discharge_id", dischargeLog.ID)

		return nil
	case model.PaymentStatusPending, model.PaymentStatusProcessing:
		paymentID := dischargeLog.ID
		zalopayID := dischargeLog.AccountInfo.ZalopayID
		if err = uc.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID); err != nil {
			logger.Errorw("msg", "trigger polling early discharge status fail", "error", err)
			return err
		}
		logger.Infow("msg", "trigger polling early discharge status success", "discharge_id", dischargeLog.ID)
		return nil
	default:
		logger.Errorw("msg", "invalid discharge log status", "status", dischargeLog.Status)
		return errors.New("invalid discharge log status")
	}
}

type RefundSettleResult struct {
	AppID               int32
	OrderID             int64
	AppTransID          string
	RefTransID          int64
	SettlePaymentID     int64
	SettlePaymentStatus model.PaymentStatus
}

func (uc *Usecase) ProcessRefundSettleResult(ctx context.Context, event *RefundSettleResult) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "Processing refund settle response", "event", event)

	if !event.SettlePaymentStatus.IsFinalStatus() {
		logger.Infow("msg", "Ignoring non-final status", "status", event.SettlePaymentStatus)
		return nil
	}

	order, err := uc.orderRepo.GetRefundSettleOrder(ctx, event.AppTransID, event.AppID)
	if err != nil {
		logger.Errorw("msg", "Failed to get refund settle order", "error", err)
		return errors.Wrap(err, "failed to get refund settle order")
	}
	if order == nil {
		logger.Warnw("msg", "Refund settle order not found", "app_id", event.AppID, "app_trans_id", event.AppTransID)
		return nil
	}

	if event.SettlePaymentStatus.IsFailedStatus() {
		logger.Infow("msg", "Refund settle order failed", "order_id", order.ID, "app_trans_id", event.AppTransID)
		return uc.handleRefundSettleFailed(ctx, order, event)
	}

	err = uc.txn.WithTx(ctx, func(ctx context.Context) error {
		// Create refund settle order
		acOrder, err := uc.orderAdapter.CreateRefundSettleOrder(ctx, &model.CreateRefundOrderRequest{
			Amount:       order.Amount,
			AppTransID:   order.AppTransID,
			ZaloPayID:    order.ZalopayID,
			Description:  order.Description,
			RefZPTransID: order.ExtraData.RefZPTransID,
		})
		if err != nil {
			logger.Errorw("msg", "Failed to create refund settle order", "error", err)
			return errors.Wrap(err, "failed to create refund settle order")
		}

		order = order.
			WithStatus(model.OrderStatusPending).
			FulfillSettleOrder(acOrder)

		// Update order status to pending
		err = uc.orderRepo.UpdateOrderProgress(ctx, model.OrderProgressUpdate{
			OrderID:   order.ID,
			Status:    order.Status,
			ExtraData: order.ExtraData,
		})
		if err != nil {
			logger.Errorw("msg", "Failed to update order status", "error", err, "order_id", order.ID)
			return errors.Wrap(err, "failed to update order status")
		}

		err = uc.orderAdapter.SubmitRefundSettleOrder(ctx, order)
		if err != nil {
			logger.Errorw("msg", "Failed to submit refund settle order", "error", err, "order_id", order.ID)
			return errors.Wrap(err, "failed to submit refund settle order")
		}

		logger.Infow("msg", "Successfully submitted refund settle order", "order_id", order.ID)

		return nil
	})

	if err != nil {
		logger.Errorw("msg", "Transaction failed", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed refund settle order",
		"order_id", order.ID,
		"app_trans_id", order.AppTransID,
		"zp_trans_id", event.SettlePaymentID)

	return nil
}

func (uc *Usecase) handleRefundSettleFailed(ctx context.Context, order *model.RefundSettleOrder, event *RefundSettleResult) error {
	logger := uc.logger.WithContext(ctx)

	err := uc.txn.WithTx(ctx, func(tCtx context.Context) error {
		// Update order status to cancelled
		order.CancelOrder()

		err := uc.orderRepo.UpdateOrderProgress(tCtx, model.OrderProgressUpdate{
			OrderID:   order.ID,
			Status:    order.Status,
			ZpTransID: order.ZPTransID,
			ExtraData: order.ExtraData,
		})
		if err != nil {
			logger.Errorw("msg", "Failed to update order status", "error", err)
			return errors.Wrap(err, "failed to update order status")
		}

		err = uc.repo.UpdateRefundSettleStatusByID(tCtx, order.RefundSettleID, model.RefundSettleStatusPending)
		if err != nil {
			logger.Errorw("msg", "Failed to update refund settle status", "error", err)
			return errors.Wrap(err, "failed to update refund settle status")
		}
		return nil
	})

	if err != nil {
		logger.Errorw("msg", "Transaction failed", "error", err)
		return err
	}

	return nil
}

type SettleOrderUpdateRequest struct {
	AppID        int32
	AppTransID   string
	ZPTransID    int64
	OrderStatus  model.OrderStatus
	OriginStatus int
	ReasonStatus string
}

func (uc *Usecase) ProcessSettleOrderUpdate(ctx context.Context, params *SettleOrderUpdateRequest) error {
	logger := uc.logger.WithContext(ctx)

	order, err := uc.orderRepo.GetRefundSettleOrder(ctx, params.AppTransID, params.AppID)
	if errors.Is(err, model.ErrOrderNotFound) {
		logger.Warnw("msg", "Settle order not found", "error", err)
		return nil
	}
	if err != nil {
		logger.Errorw("msg", "Failed to get settle order", "error", err)
		return err
	}
	if order.IsFinal() {
		logger.Infow("msg", "Settle order is final", "order", order)
		return nil
	}

	order.HandleOrderUpdate(
		params.ZPTransID,
		params.OrderStatus,
		params.ReasonStatus,
	)

	err = uc.txn.WithTx(ctx, func(tCtx context.Context) error {
		if err = uc.orderRepo.UpdateOrderProgress(tCtx, model.OrderProgressUpdate{
			OrderID:   order.ID,
			Status:    order.Status,
			ZpTransID: order.ZPTransID,
			ExtraData: order.ExtraData,
		}); err != nil {
			logger.Errorw("msg", "Failed to update settle order", "error", err)
			return err
		}

		if !order.IsSuccess() {
			logger.Infow("msg", "Settle order is not success", "order", order)
			return nil
		}

		if err := uc.updatePaymentLogWithOrderInfo(tCtx, order.ID, order); err != nil {
			logger.Errorw("msg", "Failed to update payment log with order", "error", err, "order_id", order.ID)
			return err
		}

		if order.GetRefundSettleMode() != model.RefundSettleModeStandard {
			logger.Infow("msg", "Settle order is not standard mode, not need update refund settle info", "order", order)
			return nil
		}

		settle, err := uc.repo.GetRefundSettleForUpdate(tCtx, order.RefundSettleID)
		if errors.Is(err, model.ErrRefundSettleNotFound) || settle == nil {
			logger.Errorw("msg", "Refund settle not found")
			return errors.New("refund settle not found")
		}
		if err != nil {
			logger.Errorw("msg", "Failed to get refund settle", "error", err)
			return err
		}

		if err := uc.repo.UpdateRefundSettleStatusByID(tCtx, settle.ID, model.RefundSettleStatusSettled); err != nil {
			logger.Errorw("msg", "Failed to update refund settle status", "error", err)
			return err
		}

		logger.Infow("msg", "Successfully updated refund settle status", "settle_id", settle.ID, "new_status", model.RefundSettleStatusSettled)

		return nil
	})
	if err != nil {
		logger.Errorw("msg", "Transaction process settle order update has failed", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed settle order update", "order_id", order.ID, "new_status", order.Status)

	return nil
}

// updatePaymentLogWithOrder will update the early discharge log or repayment log with the order information.
// both 2 logs have serve for settlement process, so we can update them with the order information
func (uc *Usecase) updatePaymentLogWithOrderInfo(ctx context.Context, orderID int64, order *model.RefundSettleOrder) error {
	logger := uc.logger.WithContext(ctx)

	var err error
	var paymentID int64
	var orderInfo model.Order

	switch order.GetRefundSettleMode() {
	case model.RefundSettleModeStandard:
		log, err := uc.paymentRepo.GetEarlyDischargeLogByOrderID(ctx, orderID)
		if err != nil {
			logger.Errorw("msg", "get early discharge log by order id fail", "error", err, "order_id", orderID)
			return err
		}
		paymentID = log.ID
		orderInfo = log.WithRefundSettleOrder(order).Order
	case model.RefundSettleModeExpired:
		log, err := uc.paymentRepo.GetRepaymentByOrderID(ctx, orderID)
		if err != nil {
			logger.Errorw("msg", "get repayment log by order id fail", "error", err, "order_id", orderID)
			return err
		}
		paymentID = log.ID
		orderInfo = log.WithRefundSettleOrder(order).Order
	default:
		logger.Errorw("msg", "unknown refund settle mode", "mode", order.GetRefundSettleMode())
		return errors.Errorf("unknown refund settle mode: %d", order.GetRefundSettleMode())
	}

	if orderInfo.IsEmpty() || paymentID == 0 {
		logger.Errorw("msg", "order info is nil or payment id is zero", "payment_id", paymentID, "order_id", orderID)
		return errors.New("order info is nil or payment id is zero")
	}

	if err = uc.paymentRepo.UpdatePaymentLogOrderIDs(ctx, paymentID, orderInfo); err != nil {
		logger.Errorw("msg", "update payment log with order info fail", "error", err, "payment_id", paymentID, "order_info", orderInfo)
		return errors.Wrap(err, "failed to update payment log with order info")
	}

	return nil
}

func (uc *Usecase) buildRefundSettleOrder(settle *model.RefundSettle, payment *model.PurchaseOrder) (*model.RefundSettleOrder, error) {
	config, ok := uc.orderCfgs.GetConfigRefundSettle("")
	if !ok {
		return nil, errors.New("refund settle order config not found")
	}

	zalopayID := payment.AccountInfo.ZalopayID
	partnerCode := payment.AccountInfo.PartnerCode

	result := model.NewRefundSettleOrder(
		zalopayID, partnerCode,
		settle.ZPTransID, settle.SettlementAmount,
	)
	result.AppID = config.GetAppId()
	result.AppTransID = idgen.GenAppTransID()
	result.Description = SettleOrderDescription
	result.RefundSettleID = settle.ID

	return result, nil
}

func (uc *Usecase) getResourceForPreparingStandardSettle(ctx context.Context, refZPTransID int64) (*model.Account, *model.PurchaseOrder, *model.BankRoute, error) {
	logger := uc.logger.WithContext(ctx)

	purchase, err := uc.paymentRepo.GetPaymentByTransID(ctx, refZPTransID)
	if err != nil {
		logger.Errorw("msg", "get purchase fail", "error", err)
		return nil, nil, nil, err
	}

	account, err := uc.accountAdapter.GetAccount(ctx,
		purchase.AccountInfo.ZalopayID,
		purchase.AccountInfo.PartnerCode,
	)
	if err != nil {
		logger.Errorw("msg", "get account fail", "error", err)
		return nil, nil, nil, err
	}

	bankRoute, err := uc.paymentRepo.GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment)
	if err != nil {
		logger.Errorw("msg", "get bank route fail", "error", err)
		return nil, nil, nil, err
	}

	return &account, purchase, bankRoute, nil
}
