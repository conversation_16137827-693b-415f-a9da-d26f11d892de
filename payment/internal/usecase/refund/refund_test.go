package refund

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/adapters"
	repository_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/repository"
	"go.uber.org/mock/gomock"
)

type RefundTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	usecase *Usecase

	// Repository mocks
	mockRefundRepo  *repository_mocks.MockRefundRepo
	mockPaymentRepo *repository_mocks.MockPaymentRepo
	mockOrderRepo   *repository_mocks.MockOrderRepo
	mockTransaction *repository_mocks.MockTransaction

	// Adapter mocks
	mockAccountAdapter       *adapter_mocks.MockAccountAdapter
	mockPartnerConnector     *adapter_mocks.MockPartnerConnector
	mockAcquiringCore        *adapter_mocks.MockAcquiringCoreAdapter
	mockInstallmentAdapter   *adapter_mocks.MockInstallmentAdapter
	mockTaskJobAdapter       *adapter_mocks.MockTaskJobAdapter
	mockDistributedLock      *adapter_mocks.MockDistributedLock
	mockRefundSettleNotifier *adapter_mocks.MockRefundSettleNotifier

	// Test configs
	paymentConfig      *configs.Payment
	orderConfigsHelper *configs.OrderConfigsHelper
	logger             log.Logger
}

func (suite *RefundTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())

	// Initialize repository mocks
	suite.mockRefundRepo = repository_mocks.NewMockRefundRepo(suite.ctrl)
	suite.mockPaymentRepo = repository_mocks.NewMockPaymentRepo(suite.ctrl)
	suite.mockOrderRepo = repository_mocks.NewMockOrderRepo(suite.ctrl)
	suite.mockTransaction = repository_mocks.NewMockTransaction(suite.ctrl)

	// Initialize adapter mocks
	suite.mockAccountAdapter = adapter_mocks.NewMockAccountAdapter(suite.ctrl)
	suite.mockPartnerConnector = adapter_mocks.NewMockPartnerConnector(suite.ctrl)
	suite.mockAcquiringCore = adapter_mocks.NewMockAcquiringCoreAdapter(suite.ctrl)
	suite.mockInstallmentAdapter = adapter_mocks.NewMockInstallmentAdapter(suite.ctrl)
	suite.mockTaskJobAdapter = adapter_mocks.NewMockTaskJobAdapter(suite.ctrl)
	suite.mockDistributedLock = adapter_mocks.NewMockDistributedLock(suite.ctrl)
	suite.mockRefundSettleNotifier = adapter_mocks.NewMockRefundSettleNotifier(suite.ctrl)

	// Initialize configs
	suite.logger = log.DefaultLogger
	suite.paymentConfig = &configs.Payment{
		Refund: &configs.Refund{},
	}
	suite.orderConfigsHelper = &configs.OrderConfigsHelper{}

	// Initialize usecase with all dependencies
	suite.usecase = NewRefundUsecase(
		suite.logger,
		suite.mockRefundRepo,
		suite.mockTransaction,
		suite.mockTaskJobAdapter,
		suite.mockDistributedLock,
		suite.paymentConfig,
		suite.orderConfigsHelper,
		suite.mockOrderRepo,
		suite.mockPaymentRepo,
		suite.mockPartnerConnector,
		suite.mockAcquiringCore,
		suite.mockAccountAdapter,
		suite.mockRefundSettleNotifier,
		suite.mockInstallmentAdapter,
	)
}

func (suite *RefundTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

// Test basic usecase initialization
func (suite *RefundTestSuite) TestUsecaseInitialization() {
	// Test that the usecase is properly initialized with all dependencies
	suite.NotNil(suite.usecase)
	suite.NotNil(suite.mockRefundRepo)
	suite.NotNil(suite.mockPaymentRepo)
	suite.NotNil(suite.mockOrderRepo)
	suite.NotNil(suite.mockTransaction)
	suite.NotNil(suite.mockAccountAdapter)
	suite.NotNil(suite.mockPartnerConnector)
	suite.NotNil(suite.mockAcquiringCore)
	suite.NotNil(suite.mockInstallmentAdapter)
	suite.NotNil(suite.mockTaskJobAdapter)
	suite.NotNil(suite.mockDistributedLock)
	suite.NotNil(suite.mockRefundSettleNotifier)
}

func TestRefundTestSuite(t *testing.T) {
	suite.Run(t, new(RefundTestSuite))
}

func (suite *RefundTestSuite) TestRetrySubmitSettleOrder() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name           string
		setupMocks     func()
		expectedError  string
		expectedResult bool
	}{
		{
			name: "success_retry_submit",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				failedOrder := &model.RefundSettleOrder{
					ID:          1,
					ZalopayID:   456,
					AppTransID:  "old_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					Status:      model.OrderStatusFailed,
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				orders := []*model.RefundSettleOrder{failedOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)

				acOrderResp := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "new_app_trans_id",
					ZpTransToken: "token123",
					OrderNo:      12345,
					Description:  "test order",
					DataChecksum: "checksum123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(acOrderResp, nil)

				newOrder := &model.RefundSettleOrder{
					ID:          2,
					ZalopayID:   456,
					AppTransID:  "new_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Status:      model.OrderStatusPending,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(newOrder, nil)

				eadLog := &model.EarlyDischargeLog{
					ID: 100,
					Order: model.Order{
						ID:         1,
						AppTransID: "old_app_trans_id",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(gomock.Any(), int64(1)).
					Return(eadLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(gomock.Any(), int64(100), gomock.Any()).
					Return(nil)

				suite.mockAcquiringCore.EXPECT().
					SubmitRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(nil)

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError:  "",
			expectedResult: true,
		},
		{
			name: "error_get_settle_info",
			setupMocks: func() {
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(nil, errors.New("database error"))
			},
			expectedError:  "database error",
			expectedResult: false,
		},
		{
			name: "settle_not_processing_status",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusCompleted,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)
			},
			expectedError:  "",
			expectedResult: true,
		},
		{
			name: "error_list_settle_orders",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(nil, errors.New("list orders error"))
			},
			expectedError:  "list orders error",
			expectedResult: false,
		},
		{
			name: "no_failed_order_to_retry",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				successOrder := &model.RefundSettleOrder{
					ID:     1,
					Status: model.OrderStatusSucceeded,
				}
				orders := []*model.RefundSettleOrder{successOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)
			},
			expectedError:  "",
			expectedResult: true,
		},
		{
			name: "not_eligible_to_retry",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				orders := []*model.RefundSettleOrder{
					{ID: 1, Status: model.OrderStatusFailed},
					{ID: 2, Status: model.OrderStatusFailed},
					{ID: 3, Status: model.OrderStatusFailed},
					{ID: 4, Status: model.OrderStatusFailed},
				}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)
			},
			expectedError:  "",
			expectedResult: true,
		},
		{
			name: "error_create_refund_settle_order",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				failedOrder := &model.RefundSettleOrder{
					ID:          1,
					ZalopayID:   456,
					AppTransID:  "old_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					Status:      model.OrderStatusFailed,
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				orders := []*model.RefundSettleOrder{failedOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)

				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("create order error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError:  "create order error",
			expectedResult: false,
		},
		{
			name: "error_create_refund_settle_order_in_repo",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				failedOrder := &model.RefundSettleOrder{
					ID:          1,
					ZalopayID:   456,
					AppTransID:  "old_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					Status:      model.OrderStatusFailed,
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				orders := []*model.RefundSettleOrder{failedOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)

				acOrderResp := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "new_app_trans_id",
					ZpTransToken: "token123",
					OrderNo:      12345,
					Description:  "test order",
					DataChecksum: "checksum123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(acOrderResp, nil)

				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("repo create error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError:  "repo create error",
			expectedResult: false,
		},
		{
			name: "error_get_early_discharge_log",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				failedOrder := &model.RefundSettleOrder{
					ID:          1,
					ZalopayID:   456,
					AppTransID:  "old_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					Status:      model.OrderStatusFailed,
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				orders := []*model.RefundSettleOrder{failedOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)

				acOrderResp := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "new_app_trans_id",
					ZpTransToken: "token123",
					OrderNo:      12345,
					Description:  "test order",
					DataChecksum: "checksum123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(acOrderResp, nil)

				newOrder := &model.RefundSettleOrder{
					ID:          2,
					ZalopayID:   456,
					AppTransID:  "new_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Status:      model.OrderStatusPending,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(newOrder, nil)

				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(gomock.Any(), int64(1)).
					Return(nil, errors.New("get discharge log error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError:  "get discharge log error",
			expectedResult: false,
		},
		{
			name: "error_update_payment_log_order_ids",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				failedOrder := &model.RefundSettleOrder{
					ID:          1,
					ZalopayID:   456,
					AppTransID:  "old_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					Status:      model.OrderStatusFailed,
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				orders := []*model.RefundSettleOrder{failedOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)

				acOrderResp := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "new_app_trans_id",
					ZpTransToken: "token123",
					OrderNo:      12345,
					Description:  "test order",
					DataChecksum: "checksum123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(acOrderResp, nil)

				newOrder := &model.RefundSettleOrder{
					ID:          2,
					ZalopayID:   456,
					AppTransID:  "new_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Status:      model.OrderStatusPending,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(newOrder, nil)

				eadLog := &model.EarlyDischargeLog{
					ID: 100,
					Order: model.Order{
						ID:         1,
						AppTransID: "old_app_trans_id",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(gomock.Any(), int64(1)).
					Return(eadLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(gomock.Any(), int64(100), gomock.Any()).
					Return(errors.New("update payment log error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError:  "update payment log error",
			expectedResult: false,
		},
		{
			name: "error_submit_refund_settle_order",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				failedOrder := &model.RefundSettleOrder{
					ID:          1,
					ZalopayID:   456,
					AppTransID:  "old_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					Status:      model.OrderStatusFailed,
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				orders := []*model.RefundSettleOrder{failedOrder}
				suite.mockOrderRepo.EXPECT().
					ListRefundSettleOrder(ctx, settleID).
					Return(orders, nil)

				acOrderResp := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "new_app_trans_id",
					ZpTransToken: "token123",
					OrderNo:      12345,
					Description:  "test order",
					DataChecksum: "checksum123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(acOrderResp, nil)

				newOrder := &model.RefundSettleOrder{
					ID:          2,
					ZalopayID:   456,
					AppTransID:  "new_app_trans_id",
					AppID:       789,
					Amount:      1000,
					Status:      model.OrderStatusPending,
					Description: "test order",
					PartnerCode: "PARTNER_A",
					ExtraData:   model.RefundOrderExtra{RefZPTransID: 999},
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(newOrder, nil)

				eadLog := &model.EarlyDischargeLog{
					ID: 100,
					Order: model.Order{
						ID:         1,
						AppTransID: "old_app_trans_id",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(gomock.Any(), int64(1)).
					Return(eadLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(gomock.Any(), int64(100), gomock.Any()).
					Return(nil)

				suite.mockAcquiringCore.EXPECT().
					SubmitRefundSettleOrder(gomock.Any(), gomock.Any()).
					Return(errors.New("submit order error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError:  "submit order error",
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.RetrySubmitSettleOrder(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}
