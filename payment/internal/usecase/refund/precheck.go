package refund

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type PreCheckResponse struct {
	RefundOrder  *model.RefundOrder
	SettleStatus model.SettleStatus
}

func (uc *Usecase) PreCheck(ctx context.Context, req model.RefundOrder) (*PreCheckResponse, error) {
	uc.logger.WithContext(ctx).Infow("msg", "PreCheck", "req", req)

	payment, err := uc.paymentRepo.GetPaymentByTransID(ctx, req.ZPTransID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
		return nil, err
	}
	if payment.Status != model.PaymentStatusSucceeded {
		uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
		return nil, fmt.Errorf("payment status is not success")
	}

	// Check Refund Condition from Partner
	instStatus, err := uc.installmentAdapter.GetInstallmentStatus(ctx, req.ZPTransID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "get installment status fail", "error", err)
		return nil, err
	}
	if !instStatus.Status.IsValid() {
		uc.logger.WithContext(ctx).Errorw("msg", "get installment status fail", "error", "invalid status", "raw_status", instStatus.Status)
		return nil, fmt.Errorf("invalid status: %v", instStatus.Status)
	}

	req.Status = model.RefundStatusInit
	req.RefundType = model.RefundTypeAuto

	switch instStatus.Status {
	case model.InstallmentStatusOpen, model.InstallmentStatusInCreation:
		req.ProcessType = model.RefundProcessTypeSettlement
	case model.InstallmentStatusClosed:
		req.ProcessType = model.RefundProcessTypeFundback
	}

	// Insert RefundLogs
	refundOrder, err := uc.repo.CreateRefundLog(ctx, req)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "insert refund logs to DB fail", "error", err)
		return nil, err
	}

	settleStatus := model.FromInstallmentToSettleStatus(instStatus.Status)

	return &PreCheckResponse{
		RefundOrder:  refundOrder,
		SettleStatus: settleStatus,
	}, nil
}
