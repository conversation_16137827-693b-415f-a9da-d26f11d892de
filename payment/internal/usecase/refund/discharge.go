package refund

import (
	"context"
	"time"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

func (uc *Usecase) GetEarlyDischargeLogByID(ctx context.Context, paymentID int64) (*model.EarlyDischargeLog, error) {
	logger := uc.logger.WithContext(ctx)

	earlyDischargeLog, err := uc.paymentRepo.GetEarlyDischargeLogByID(ctx, paymentID)
	if err != nil {
		logger.Errorw("msg", "get early discharge log fail", "error", err)
		return nil, err
	}
	if earlyDischargeLog == nil {
		logger.Errorw("msg", "early discharge log not found")
		return nil, errors.New("early discharge log not found")
	}

	return earlyDischargeLog, nil
}

func (uc *Usecase) TriggerPollingEarlyDischargeStatus(ctx context.Context, paymentID int64, zalopayID int64) error {
	logger := uc.logger.WithContext(ctx)
	retryOtps := []retry.Option{
		retry.Attempts(3),
		retry.Delay(3 * time.Second),
	}

	err := retry.Do(func() error {
		err := uc.taskJob.ExecuteEarlyDischargePollingJob(ctx, &model.EarlyDischargeStatusWorkflowRequest{
			PaymentID: paymentID,
			ZalopayID: zalopayID,
		})
		if err != nil {
			return err
		}
		return nil
	}, retryOtps...)
	if err != nil {
		logger.Errorw("msg", "trigger polling early discharge status fail", "error", err)
		return err
	}

	logger.Infow("msg", "[Trigger] polling early discharge status success", "zalopayID", zalopayID, "paymentID", paymentID)

	return nil
}
