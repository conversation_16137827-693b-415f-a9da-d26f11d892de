package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

func (suite *RefundTestSuite) TestGetEarlyDischargeLogByID_Success() {
	ctx := context.Background()
	paymentID := int64(12345)

	expectedLog := &model.EarlyDischargeLog{
		ID:           1,
		ZpTransID:    paymentID,
		Status:       model.PaymentStatusInit,
		TransType:    model.TransTypeEarlyDischarge,
		PartnerReqID: "test_partner_req_id",
		AccountInfo: model.Account{
			ID:        1,
			ZalopayID: 67890,
		},
		Order: model.Order{
			ID:     1,
			Amount: 1000,
		},
	}

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLogByID(ctx, paymentID).
		Return(expectedLog, nil)

	result, err := suite.usecase.GetEarlyDischargeLogByID(ctx, paymentID)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedLog, result)
}

func (suite *RefundTestSuite) TestGetEarlyDischargeLogByID_RepositoryError() {
	ctx := context.Background()
	paymentID := int64(12345)
	expectedError := errors.New("repository error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLogByID(ctx, paymentID).
		Return(nil, expectedError)

	result, err := suite.usecase.GetEarlyDischargeLogByID(ctx, paymentID)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestGetEarlyDischargeLogByID_NotFound() {
	ctx := context.Background()
	paymentID := int64(12345)

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLogByID(ctx, paymentID).
		Return(nil, nil)

	result, err := suite.usecase.GetEarlyDischargeLogByID(ctx, paymentID)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "early discharge log not found")
}

func (suite *RefundTestSuite) TestTriggerPollingEarlyDischargeStatus_Success() {
	ctx := context.Background()
	paymentID := int64(12345)
	zalopayID := int64(67890)

	expectedRequest := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: paymentID,
		ZalopayID: zalopayID,
	}

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteEarlyDischargePollingJob(ctx, expectedRequest).
		Return(nil)

	err := suite.usecase.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestTriggerPollingEarlyDischargeStatus_TaskJobError() {
	ctx := context.Background()
	paymentID := int64(12345)
	zalopayID := int64(67890)
	expectedError := errors.New("task job error")

	expectedRequest := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: paymentID,
		ZalopayID: zalopayID,
	}

	// Mock the call to return error on all retry attempts
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteEarlyDischargePollingJob(ctx, expectedRequest).
		Return(expectedError).
		Times(3) // Should retry 3 times

	err := suite.usecase.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), expectedError.Error())
}

func (suite *RefundTestSuite) TestTriggerPollingEarlyDischargeStatus_RetrySuccessOnSecondAttempt() {
	ctx := context.Background()
	paymentID := int64(12345)
	zalopayID := int64(67890)
	temporaryError := errors.New("temporary error")

	expectedRequest := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: paymentID,
		ZalopayID: zalopayID,
	}

	// Mock first call to fail, second call to succeed
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteEarlyDischargePollingJob(ctx, expectedRequest).
		Return(temporaryError).
		Times(1)

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteEarlyDischargePollingJob(ctx, expectedRequest).
		Return(nil).
		Times(1)

	err := suite.usecase.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestTriggerPollingEarlyDischargeStatus_WithZeroIDs() {
	ctx := context.Background()
	paymentID := int64(0)
	zalopayID := int64(0)

	expectedRequest := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: paymentID,
		ZalopayID: zalopayID,
	}

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteEarlyDischargePollingJob(ctx, expectedRequest).
		Return(nil)

	err := suite.usecase.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestTriggerPollingEarlyDischargeStatus_WithNegativeIDs() {
	ctx := context.Background()
	paymentID := int64(-1)
	zalopayID := int64(-1)

	expectedRequest := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: paymentID,
		ZalopayID: zalopayID,
	}

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteEarlyDischargePollingJob(ctx, expectedRequest).
		Return(nil)

	err := suite.usecase.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID)

	assert.NoError(suite.T(), err)
}
