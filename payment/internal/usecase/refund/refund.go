package refund

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
)

type Usecase struct {
	logger             *log.Helper
	repo               types.RefundRepo
	txn                types.Transaction
	orderCfgs          *configs.OrderConfigsHelper
	refundCfg          *configs.Refund
	orderRepo          types.OrderRepo
	paymentRepo        types.PaymentRepo
	taskJob            types.TaskJobAdapter
	distLock           types.DistributedLock
	connector          types.PartnerConnector
	settleNotifier     types.RefundSettleNotifier
	orderAdapter       types.AcquiringCoreAdapter
	accountAdapter     types.AccountAdapter
	installmentAdapter types.InstallmentAdapter
}

func NewRefundUsecase(logger log.Logger,
	repo types.RefundRepo,
	txn types.Transaction,
	taskJob types.TaskJobAdapter,
	distLock types.DistributedLock,
	rootCfg *configs.Payment,
	orderCfgs *configs.OrderConfigsHelper,
	orderRepo types.OrderRepo,
	paymentRepo types.PaymentRepo,
	connector types.PartnerConnector,
	orderAdapter types.AcquiringCoreAdapter,
	accountAdapter types.AccountAdapter,
	refundSettleNotifier types.RefundSettleNotifier,
	installmentAdapter types.InstallmentAdapter) *Usecase {
	return &Usecase{
		logger:             log.NewHelper(log.With(logger, "module", "RefundUsecase")),
		txn:                txn,
		repo:               repo,
		taskJob:            taskJob,
		distLock:           distLock,
		connector:          connector,
		orderCfgs:          orderCfgs,
		refundCfg:          rootCfg.Refund,
		orderRepo:          orderRepo,
		paymentRepo:        paymentRepo,
		accountAdapter:     accountAdapter,
		orderAdapter:       orderAdapter,
		installmentAdapter: installmentAdapter,
		settleNotifier:     refundSettleNotifier,
	}
}

func (uc *Usecase) Refund(ctx context.Context, req model.RefundOrder) (*model.RefundOrder, error) {
	uc.logger.WithContext(ctx).Infow("msg", "Refund", "req", req)
	payment, err := uc.paymentRepo.GetPaymentByTransID(ctx, req.ZPTransID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
		return nil, err
	}
	if payment.Status != model.PaymentStatusSucceeded {
		uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
		return nil, fmt.Errorf("payment status is not success")
	}

	req.Status = model.RefundStatusSuccess
	req.RefundType = model.RefundTypeManual
	req.ProcessType = model.RefundProcessTypeManual
	// Insert RefundLogs
	refundOrder, err := uc.repo.CreateRefundLog(ctx, req)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "insert refund logs to DB fail", "error", err)
		return nil, err
	}

	return refundOrder, nil
}

func (uc *Usecase) RefundQuery(ctx context.Context, refundID int64) (*model.RefundOrder, error) {
	refundLog, err := uc.repo.GetRefundLogByRefundID(ctx, refundID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorw("msg", "get refund log fail", "error", err)
		return nil, err
	}
	return refundLog, nil
}

func (uc *Usecase) ProcessCompletion(ctx context.Context, refundID int64, finalStatus model.RefundStatus) error {
	logger := uc.logger.WithContext(ctx)

	if !finalStatus.IsFinal() {
		logger.Errorw("msg", "invalid final status", "finalStatus", finalStatus)
		return fmt.Errorf("invalid final status")
	}

	refundLog, err := uc.repo.GetRefundLogByRefundID(ctx, refundID)
	if err != nil {
		logger.Errorw("msg", "get refund log fail", "error", err)
		return err
	}
	if refundLog.IsComplete() {
		logger.Infow("msg", "refund log is already completed", "refundLog", refundLog)
		return nil
	}

	refundLog.Status = finalStatus
	if refundLog.IsSuccess() {
		expiredDuration := uc.refundCfg.TransAliveIn.AsDuration()
		refundLog.DeadlineAt = time.Now().Add(expiredDuration)
	}

	if err = uc.repo.CompleteRefundLog(ctx, refundLog); err != nil {
		logger.Errorw("msg", "complete refund log fail", "error", err)
		return err
	}

	go func() {
		if !refundLog.IsSuccess() {
			logger.Warnw("msg", "refund log is not success", "refundLog", refundLog)
			return
		}
		if refundLog.ProcessType != model.RefundProcessTypeSettlement {
			logger.Infow("msg", "refund log is not settlement", "refundLog", refundLog)
			return
		}
		ctx = context.WithoutCancel(ctx)
		err := uc.TriggerJobReconcileRefundSettlements(ctx, refundLog.ZPTransID)
		if err != nil {
			logger.Errorw("msg", "trigger job reconcile refund settlements fail", "error", err)
		}
	}()

	return nil
}
