package refund

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
	TopupOrderDescription = "Nạp tiền vào tà<PERSON> k<PERSON>n <PERSON>aloPay"
)

type CreateTopupRequest struct {
	Amount       int64
	ZaloPayID    int64
	RefZPTransID int64
}

func (s *Usecase) CreateTopup(ctx context.Context, req *CreateTopupRequest) (*model.RefundTopupOrder, error) {
	logger := s.logger.WithContext(ctx)

	// Get and validate settle info
	topupOrder := model.NewRefundTopupOrder(req.ZaloPayID, req.RefZPTransID, req.Amount)
	settleInfo, err := s.repo.GetRefundSettleByZPTransID(ctx, req.RefZPTransID)
	if err != nil {
		logger.Errorw("msg", "Failed to get settle info", "error", err)
		return nil, err
	}
	if err = s.precheckTopupCondition(ctx, settleInfo); err != nil {
		logger.Errorw("msg", "Failed to precheck topup orders", "error", err)
		return nil, err
	}

	settleInfo, err = s.ValidateAndSyncSettlementForTopup(ctx, settleInfo)
	if err != nil {
		logger.Errorw("msg", "Failed to sync latest settlement amount", "error", err)
		return nil, err
	}
	if err = settleInfo.ValidateTopupOrderAmount(topupOrder.Amount); err != nil {
		logger.Errorf("invalid topup order amount: %v, settleID: %d", err, settleInfo.ID)
		return nil, errors.Errorf("invalid topup order amount: %v", err)
	}

	purchaseOrder, err := s.paymentRepo.GetPaymentByTransID(ctx, settleInfo.ZPTransID)
	if err != nil {
		logger.Errorw("msg", "Failed to get payment info", "error", err)
		return nil, err
	}

	// Create td-core order
	topupOrder = topupOrder.EnrichTopupOrder(settleInfo, purchaseOrder, TopupOrderDescription)
	tdTopupOrder, err := s.orderAdapter.CreateRefundTopupOrder(ctx, &model.CreateRefundOrderRequest{
		Amount:       topupOrder.Amount,
		ZaloPayID:    topupOrder.ZalopayID,
		AppTransID:   idgen.GenAppTransID(),
		Description:  topupOrder.Description,
		RefZPTransID: topupOrder.GetRefZPTransID(),
	})
	if err != nil {
		logger.Errorw("msg", "Failed to create topup order", "error", err)
		return nil, err
	}

	topupOrder = topupOrder.FulfillTopupOrder(tdTopupOrder)
	topupOrder, err = s.orderRepo.CreateRefundTopupOrder(ctx, topupOrder)
	if err != nil {
		logger.Errorw("msg", "Failed to save topup order", "error", err)
		return nil, err
	}
	return topupOrder, nil
}

func (s *Usecase) GetTopupOrder(ctx context.Context, appID int32, appTransID string) (*model.RefundTopupOrder, error) {
	logger := s.logger.WithContext(ctx)

	order, err := s.orderRepo.GetRefundTopupOrder(ctx, appTransID, appID)
	if err != nil {
		logger.Errorw("msg", "Failed to get topup orders", "error", err)
		return nil, err
	}
	return order, nil
}

type TopupOrderUpdateRequest struct {
	AppID        int32
	AppTransID   string
	ZPTransID    int64
	OrderStatus  model.OrderStatus
	OriginStatus int
	ReasonStatus string
}

func (s *Usecase) ProcessTopupOrderUpdate(ctx context.Context, req *TopupOrderUpdateRequest) error {
	logger := s.logger.WithContext(ctx)

	logger.Infow("msg", "Processing topup order update", "request", req)

	order, err := s.orderRepo.GetRefundTopupOrder(ctx, req.AppTransID, req.AppID)
	if errors.Is(err, model.ErrOrderNotFound) {
		logger.Warnw("msg", "Topup order not found", "error", err)
		return nil
	}
	if err != nil {
		logger.Errorw("msg", "Failed to get topup orders", "error", err)
		return err
	}
	if order.IsFinal() {
		logger.Infow("msg", "Topup order is final", "order", order)
		return nil
	}

	order.HandleOrderUpdate(
		req.ZPTransID,
		req.OrderStatus,
		req.ReasonStatus,
	)

	if err = s.orderRepo.UpdateOrderProgress(ctx, model.OrderProgressUpdate{
		OrderID:   order.ID,
		Status:    order.Status,
		ZpTransID: order.ZPTransID,
		ExtraData: order.ExtraData,
	}); err != nil {
		logger.Errorw("msg", "Failed to update topup order", "error", err)
		return err
	}

	if !order.IsSuccess() {
		logger.Infow("msg", "Topup order is not success", "order", order)
		return nil
	}

	ctx = context.WithoutCancel(ctx)
	go s.calcAndUpdateSettleTopups(ctx, order.RefundSettleID)

	return nil
}

func (s *Usecase) calcAndUpdateSettleTopups(ctx context.Context, settleID int64) error {
	logger := s.logger.WithContext(ctx)

	orders, err := s.orderRepo.ListRefundTopupOrder(ctx, settleID)
	if err != nil {
		logger.Errorw("msg", "Failed to get topup orders", "error", err)
		return err
	}

	ordersCast := model.RefundTopupOrders(orders)
	topupAmount := ordersCast.GetSuccessAmount()

	if err = s.repo.UpdateRefundTopupsAmount(ctx, settleID, topupAmount); err != nil {
		logger.Errorw("msg", "Failed to update topup amount", "error", err)
		return err
	}

	dsCtx := context.WithoutCancel(ctx)
	go s.DispatchRefundSettleChanged(dsCtx, settleID)

	if err = s.ProcessRefundSettleInfo(ctx, settleID); err != nil {
		logger.Errorw("msg", "Failed to process settle info", "error", err)
	}

	return nil
}

func (s *Usecase) precheckTopupCondition(ctx context.Context, settle *model.RefundSettle) error {
	logger := s.logger.WithContext(ctx)

	if !settle.IsUserTopupRequired() {
		return errors.Errorf("Settlement is not in user topup required state, settleID: %d", settle.ID)
	}

	topupOrders, err := s.orderRepo.ListRefundTopupOrder(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "Failed to get topup orders", "error", err)
		return errors.Errorf("Failed to get topup orders: %v", err)
	}
	if len(topupOrders) == 0 {
		return nil
	}

	topupOrdersCast := model.RefundTopupOrders(topupOrders)
	if topupOrdersCast.HasProcessingOrder() {
		return errors.Errorf("There is a processing topup order, settleID: %d", settle.ID)
	}

	return nil
}

func (s *Usecase) ValidateAndSyncSettlementForTopup(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) {
	logger := s.logger.WithContext(ctx)

	earlyDischarge, err := s.installmentAdapter.GetEarlyDischarge(ctx, settle.ZPTransID, true)
	if err != nil {
		logger.Errorw("msg", "get early discharge fail", "error", err)
		return nil, err
	}

	if err = earlyDischarge.Validate(); err != nil {
		logger.Errorw("msg", "validate early discharge fail", "error", err)
		return nil, err
	}

	// Check session availability for topups
	if !earlyDischarge.InSession {
		logger.Errorw("msg", "session is not available for topup", "zp_trans_id", settle.ZPTransID)
		return nil, errors.New("session not available for topup")
	}

	// Reuse common logic for settlement amount syncing
	return s.syncSettlementAmountIfChanged(ctx, settle, earlyDischarge)
}
