package refund

import (
	"context"
	"sync"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

const (
	channelSize              = 15
	inquirySize              = 50
	workerFundback           = 15
	FundbackOrderDescription = "Hoàn tiền lại cho khách hàng"
)

func (uc *Usecase) ExecuteFundback(ctx context.Context, settle *model.RefundSettle) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "execute fundback", "settle_id", settle.ID)

	if settle.Status != model.RefundSettleStatusSettled {
		return errors.New("settle status is not settled")
	}
	if settle.GetPaybackObligationAmount() <= 0 {
		stt := model.RefundSettleStatusCompleted
		err := uc.repo.UpdateRefundSettleStatusByID(ctx, settle.ID, stt)
		if err != nil {
			logger.Errorw("msg", "update refund settle status fail", "error", err)
			return err
		}
		return nil
	}

	fbOrders, err := uc.orderRepo.ListRefundFundbackOrder(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "list refund fundback order fail", "error", err)
		return err
	}
	fbOrdersCast := model.RefundFundbackOrders(fbOrders)
	if fbOrdersCast.HasProcessingOrder() {
		logger.Infow("msg", "fundback order is processing")
		return nil
	}

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin transaction fail", "error", err)
		return err
	}
	defer uc.txn.RollbackTx(tCtx)

	purchase, err := uc.paymentRepo.GetPaymentByTransID(tCtx, settle.ZPTransID)
	if err != nil {
		logger.Errorw("msg", "get payment by trans ID fail", "error", err)
		return err
	}

	zpTransID := settle.ZPTransID
	zalopayID := purchase.AccountInfo.ZalopayID
	fBackAmount := settle.GetPaybackObligationAmount()
	partnerCode := partner.CodeFromString(purchase.AccountInfo.PartnerCode)
	params := &model.CreateRefundOrderRequest{
		Amount:       fBackAmount,
		ZaloPayID:    zalopayID,
		RefZPTransID: zpTransID,
		PartnerCode:  partnerCode,
		AppTransID:   idgen.GenAppTransID(),
		Description:  FundbackOrderDescription,
	}
	acOrder, err := uc.orderAdapter.CreateRefundFundbackOrder(tCtx, params)
	if err != nil {
		logger.Errorw("msg", "create refund fundback order fail", "error", err)
		return err
	}

	fbOrder := model.NewRefundFundbackOrder(zalopayID, zpTransID, fBackAmount)
	fbOrder = fbOrder.EnrichFundbackOrder(settle, purchase).FulfillFundbackOrder(acOrder)
	fbOrder, err = uc.orderRepo.CreateRefundFundbackOrder(tCtx, fbOrder)
	if err != nil {
		logger.Errorw("msg", "create refund fundback order fail", "error", err)
		return err
	}

	err = uc.orderAdapter.SubmitRefundFundbackOrder(tCtx, fbOrder)
	if err != nil {
		logger.Errorw("msg", "submit refund fundback order failed", "error", err)
		return err
	}

	if err := uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit transaction fail", "error", err)
		return err
	}

	uc.logger.Infow("msg", "fundback order created", "order_id", fbOrder.ID)

	return nil
}

func (uc *Usecase) ProcessFundbackAfterSettlements(ctx context.Context) error {
	logger := uc.logger.WithContext(ctx)

	listSettleCh, err := uc.getRefundSettlesSuccess(ctx)
	if err != nil {
		logger.Errorf("failed to get refund settles: %v", err)
		return err
	}
	if listSettleCh == nil {
		logger.Warn("no success refund settles to process")
		return nil
	}

	waitGroup := new(sync.WaitGroup)
	waitGroup.Add(workerFundback)

	for i := range workerFundback {
		go func(id int) {
			defer waitGroup.Done()
			logger.Infow("msg", "worker process fundback", "worker_id", id)
			uc.workerProcessFundback(ctx, listSettleCh)
		}(i)
	}

	waitGroup.Wait()

	return nil
}

type FundbackOrderUpdateRequest struct {
	AppID        int32
	AppTransID   string
	ZPTransID    int64
	OrderStatus  model.OrderStatus
	OriginStatus int
	ReasonStatus string
}

func (uc *Usecase) HandleFundbackOrderUpdate(ctx context.Context, request *FundbackOrderUpdateRequest) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "handle fundback order update", "request", request)

	order, err := uc.orderRepo.GetRefundFundbackOrder(ctx, request.AppTransID, request.AppID)
	if errors.Is(err, model.ErrOrderNotFound) {
		logger.Warnw("msg", "fundback order not found", "error", err)
		return nil
	}
	if err != nil {
		logger.Errorw("msg", "get fundback order fail", "error", err)
		return err
	}
	if order.IsFinal() {
		logger.Infow("msg", "fundback order is final", "order", order)
		return nil
	}

	order.HandleOrderUpdate(
		request.ZPTransID,
		request.OrderStatus,
		request.ReasonStatus,
	)

	err = uc.txn.WithTx(ctx, func(tCtx context.Context) error {
		tErr := uc.orderRepo.UpdateOrderProgress(tCtx, model.OrderProgressUpdate{
			OrderID:   order.ID,
			Status:    order.Status,
			ZpTransID: order.ZPTransID,
			ExtraData: order.ExtraData,
		})
		if tErr != nil {
			logger.Errorw("msg", "update order progress fail", "error", tErr)
			return tErr
		}
		if !order.IsSuccess() {
			logger.Infow("msg", "fundback order is not success", "order", order)
			return nil
		}

		settleID := order.RefundSettleID
		settle, err := uc.repo.GetRefundSettleForUpdate(tCtx, settleID)
		if err != nil {
			logger.Errorw("msg", "get refund settle for update fail", "error", err)
			return err
		}

		fdOrders, err := uc.orderRepo.ListRefundFundbackOrder(tCtx, settleID)
		if err != nil {
			logger.Errorw("msg", "get fundback orders fail", "error", err)
			return err
		}
		fdOrdersCast := model.RefundFundbackOrders(fdOrders)
		totalPayback := fdOrdersCast.GetTotalSuccessAmount()
		settleStatus := settle.EvaluateStatusByPayback(totalPayback)

		tErr = uc.repo.UpdateRefundSettlePaybackInfo(
			tCtx, settle.ID,
			totalPayback, settleStatus,
		)
		if tErr != nil {
			logger.Errorw("msg", "update refund settle payback info fail", "error", tErr)
			return tErr
		}

		logger.Infow("msg", "update refund settle payback info success", "settle_id", settle.ID)

		return nil
	})
	if err != nil {
		logger.Errorw("msg", "handle fundback order update fail", "error", err)
		return err
	}

	logger.Infow("msg", "handle fundback order update success", "order_id", order.ID)

	return nil
}

func (uc *Usecase) getRefundSettlesSuccess(ctx context.Context) (<-chan *model.RefundSettle, error) {
	lastID := int64(0)
	logger := uc.logger.WithContext(ctx)
	result := make(chan *model.RefundSettle, channelSize)

	go func() {
		defer close(result)
		for {
			cursor := cast.ToString(lastID)
			pagination := &model.Pagination{
				Limit:  inquirySize,
				Cursor: &cursor,
			}
			listSettle, err := uc.repo.GetListRefundSettleByStatus(ctx, model.RefundSettleStatusSettled, pagination)
			if err != nil {
				logger.Errorw("msg", "get list refund settle fail", "error", err)
				return
			}
			if len(listSettle) == 0 {
				// No more refund settle to process
				logger.Infow("msg", "no more refund settle to process")
				break
			}
			for _, item := range listSettle {
				result <- item
			}
			lastID = listSettle[len(listSettle)-1].ID
		}
	}()

	return result, nil
}

func (uc *Usecase) workerProcessFundback(ctx context.Context, dataCh <-chan *model.RefundSettle) {
	logger := uc.logger.WithContext(ctx)

	for settle := range dataCh {
		logger.Infow("msg", "process fundback", "settle_id", settle.ID)
		if err := uc.ExecuteFundback(ctx, settle); err != nil {
			logger.Errorw("msg", "process fundback fail", "settle_id", settle.ID, "error", err)
			continue
		}
		logger.Infow("msg", "process fundback completed", "settle_id", settle.ID)
	}
}
