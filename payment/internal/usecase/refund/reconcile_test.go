package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.uber.org/mock/gomock"
)

func (suite *RefundTestSuite) TestTriggerJobReconcileRefundSettlements_Success() {
	ctx := context.Background()
	zpTransID := int64(12345)

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteReconcileRefundSettleJob(ctx, &model.RefundSettleReconParams{ZPTransID: zpTransID}).
		Return(nil).
		Times(1)

	err := suite.usecase.TriggerJobReconcileRefundSettlements(ctx, zpTransID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestTriggerJobReconcileRefundSettlements_TaskJobError() {
	ctx := context.Background()
	zpTransID := int64(12345)
	expectedError := errors.New("task job execution failed")

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteReconcileRefundSettleJob(ctx, &model.RefundSettleReconParams{ZPTransID: zpTransID}).
		Return(expectedError).
		Times(3)

	err := suite.usecase.TriggerJobReconcileRefundSettlements(ctx, zpTransID)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "task job execution failed")
}

func (suite *RefundTestSuite) TestTriggerJobReconcileRefundSettlements_RetrySuccess() {
	ctx := context.Background()
	zpTransID := int64(12345)
	temporaryError := errors.New("temporary error")

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteReconcileRefundSettleJob(ctx, &model.RefundSettleReconParams{ZPTransID: zpTransID}).
		Return(temporaryError).
		Times(2)

	suite.mockTaskJobAdapter.EXPECT().
		ExecuteReconcileRefundSettleJob(ctx, &model.RefundSettleReconParams{ZPTransID: zpTransID}).
		Return(nil).
		Times(1)

	err := suite.usecase.TriggerJobReconcileRefundSettlements(ctx, zpTransID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_GetEarlyDischargeError() {
	ctx := context.Background()
	zpTransID := int64(12345)
	expectedError := errors.New("installment service error")

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(model.EarlyDischarge{}, expectedError)

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_ValidateEarlyDischargeError() {
	ctx := context.Background()
	zpTransID := int64(12345)
	invalidEarlyDischarge := model.EarlyDischarge{
		IsSettled:   false,
		IsEligible:  false,
		TotalAmount: 100000,
	}

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(invalidEarlyDischarge, nil)

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid status")
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_GetRefundLogsError() {
	ctx := context.Background()
	zpTransID := int64(12345)
	earlyDischarge := model.EarlyDischarge{
		IsSettled:   true,
		IsEligible:  false,
		TotalAmount: 100000,
	}
	expectedError := errors.New("database error")

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(earlyDischarge, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundLogsByZPTransID(ctx, zpTransID).
		Return(nil, expectedError)

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_NoRefundLogsFound() {
	ctx := context.Background()
	zpTransID := int64(12345)
	earlyDischarge := model.EarlyDischarge{
		IsSettled:   true,
		IsEligible:  false,
		TotalAmount: 100000,
	}

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(earlyDischarge, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundLogsByZPTransID(ctx, zpTransID).
		Return([]*model.RefundOrder{}, nil)

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "no refund log settlements found")
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_CreateNewSettle_Success() {
	ctx := context.Background()
	zpTransID := int64(12345)
	earlyDischarge := model.EarlyDischarge{
		IsSettled:   true,
		IsEligible:  false,
		TotalAmount: 100000,
	}
	refundLogs := []*model.RefundOrder{
		{
			ID:          1,
			ZPTransID:   zpTransID,
			Amount:      50000,
			Status:      model.RefundStatusSuccess,
			ProcessType: model.RefundProcessTypeSettlement,
		},
		{
			ID:          2,
			ZPTransID:   zpTransID,
			Amount:      30000,
			Status:      model.RefundStatusSuccess,
			ProcessType: model.RefundProcessTypeSettlement,
		},
	}
	purchase := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ID:          123,
			ZalopayID:   456,
			PartnerCode: "PARTNER_A",
		},
	}
	expectedSettle := &model.RefundSettle{
		ID:                1,
		ZPTransID:         zpTransID,
		NetRefundAmount:   80000,
		TotalRefundAmount: 80000,
		SettlementAmount:  100000,
		Status:            model.RefundSettleStatusInit,
		EventVersion:      1,
		ExtraData: model.RefundSettleExtra{
			EventSnapshot: &model.RefundSettleSnapshot{
				Status:            model.RefundSettleStatusInit,
				EventVersion:      1,
				NetRefundAmount:   80000,
				TotalRefundAmount: 80000,
			},
		},
	}

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(earlyDischarge, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundLogsByZPTransID(ctx, zpTransID).
		Return(refundLogs, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByZPTransID(ctx, zpTransID).
		Return(nil, model.ErrRefundSettleNotFound)

	suite.mockPaymentRepo.EXPECT().
		GetPaymentByTransID(ctx, zpTransID).
		Return(purchase, nil)

	suite.mockRefundRepo.EXPECT().
		CreateRefundSettle(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) {
			assert.Equal(suite.T(), zpTransID, settle.ZPTransID)
			assert.Equal(suite.T(), int64(80000), settle.NetRefundAmount)
			assert.Equal(suite.T(), int64(80000), settle.TotalRefundAmount)
			assert.Equal(suite.T(), int64(100000), settle.SettlementAmount)
			return expectedSettle, nil
		})

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), expectedSettle.ID).
		Return(expectedSettle, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleEventData(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	suite.mockInstallmentAdapter.EXPECT().
		NotifyInstallmentRefund(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil).
		AnyTimes()

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any()).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(gomock.Any(), expectedSettle.ID).
		Return(expectedSettle, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleStatusByID(gomock.Any(), expectedSettle.ID, model.RefundSettleStatusPending).
		Return(nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleEvent(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_CreateNewSettle_ZeroNetRefund() {
	ctx := context.Background()
	zpTransID := int64(12345)
	earlyDischarge := model.EarlyDischarge{
		IsSettled:   true,
		IsEligible:  false,
		TotalAmount: 100000,
	}
	refundLogs := []*model.RefundOrder{
		{
			ID:          1,
			ZPTransID:   zpTransID,
			Amount:      50000,
			Status:      model.RefundStatusSuccess,
			ProcessType: model.RefundProcessTypeFundback,
		},
	}

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(earlyDischarge, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundLogsByZPTransID(ctx, zpTransID).
		Return(refundLogs, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByZPTransID(ctx, zpTransID).
		Return(nil, model.ErrRefundSettleNotFound)

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestReconcileRefundSettlements_UpdateExistingSettle_NoChanges() {
	ctx := context.Background()
	zpTransID := int64(12345)
	earlyDischarge := model.EarlyDischarge{
		IsSettled:   true,
		IsEligible:  false,
		TotalAmount: 100000,
	}
	refundLogs := []*model.RefundOrder{
		{
			ID:          1,
			ZPTransID:   zpTransID,
			Amount:      80000,
			Status:      model.RefundStatusSuccess,
			ProcessType: model.RefundProcessTypeSettlement,
		},
	}
	existingSettle := &model.RefundSettle{
		ID:                1,
		ZPTransID:         zpTransID,
		NetRefundAmount:   80000,
		TotalRefundAmount: 80000,
		SettlementAmount:  100000,
		ExtraData: model.RefundSettleExtra{
			EventSnapshot: &model.RefundSettleSnapshot{
				Status:            model.RefundSettleStatusInit,
				EventVersion:      1,
				UserTopupAmount:   0,
				NetRefundAmount:   80000,
				TotalRefundAmount: 80000,
			},
		},
		Status:       model.RefundSettleStatusInit,
		EventVersion: 1,
	}

	suite.mockInstallmentAdapter.EXPECT().
		GetEarlyDischarge(ctx, zpTransID, true).
		Return(earlyDischarge, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundLogsByZPTransID(ctx, zpTransID).
		Return(refundLogs, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByZPTransID(ctx, zpTransID).
		Return(existingSettle, nil)

	err := suite.usecase.ReconcileRefundSettlements(ctx, zpTransID)

	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestCreateRefundSettle_Success() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ZPTransID:         12345,
		NetRefundAmount:   100000,
		TotalRefundAmount: 100000,
		SettlementAmount:  100000,
	}
	expectedSettle := &model.RefundSettle{
		ID:                1,
		ZPTransID:         12345,
		NetRefundAmount:   100000,
		TotalRefundAmount: 100000,
		SettlementAmount:  100000,
		Status:            model.RefundSettleStatusInit,
	}

	suite.mockRefundRepo.EXPECT().
		CreateRefundSettle(ctx, settle).
		Return(expectedSettle, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).
		Times(2)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), expectedSettle.ID).
		Return(expectedSettle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleEventData(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockInstallmentAdapter.EXPECT().
		NotifyInstallmentRefund(gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil).
		Times(2)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any()).
		Times(2)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(gomock.Any(), expectedSettle.ID).
		Return(expectedSettle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleStatusByID(gomock.Any(), expectedSettle.ID, model.RefundSettleStatusPending).
		Return(nil)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleEvent(gomock.Any(), gomock.Any()).
		Return(nil)

	result, err := suite.usecase.createRefundSettle(ctx, settle)

	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), expectedSettle, result)
}

func (suite *RefundTestSuite) TestCreateRefundSettle_NilSettle() {
	ctx := context.Background()

	result, err := suite.usecase.createRefundSettle(ctx, nil)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "settle is nil")
}

func (suite *RefundTestSuite) TestCreateRefundSettle_CreateError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ZPTransID: 12345,
	}
	expectedError := errors.New("database error")

	suite.mockRefundRepo.EXPECT().
		CreateRefundSettle(ctx, settle).
		Return(nil, expectedError)

	result, err := suite.usecase.createRefundSettle(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestCreateRefundSettle_DispatchError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ZPTransID: 12345,
	}
	expectedError := errors.New("dispatch error")

	suite.mockRefundRepo.EXPECT().
		CreateRefundSettle(ctx, settle).
		Return(nil, expectedError)

	result, err := suite.usecase.createRefundSettle(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "dispatch error")
}

func (suite *RefundTestSuite) TestCreateRefundSettle_ProcessError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ZPTransID: 12345,
	}
	expectedSettle := &model.RefundSettle{
		ID:        1,
		ZPTransID: 12345,
		Status:    model.RefundSettleStatusInit,
	}
	expectedError := errors.New("process error")

	suite.mockRefundRepo.EXPECT().
		CreateRefundSettle(ctx, settle).
		Return(expectedSettle, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).
		Times(2)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), expectedSettle.ID).
		Return(expectedSettle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleEventData(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockInstallmentAdapter.EXPECT().
		NotifyInstallmentRefund(gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any()).
		Times(2)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(gomock.Any(), expectedSettle.ID).
		Return(expectedSettle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleStatusByID(gomock.Any(), expectedSettle.ID, model.RefundSettleStatusPending).
		Return(expectedError)

	result, err := suite.usecase.createRefundSettle(ctx, settle)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestUpdateRefundAfterRecon_Success() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:                1,
		ZPTransID:         12345,
		NetRefundAmount:   80000,
		TotalRefundAmount: 80000,
		SettlementAmount:  100000,
	}
	recon := &model.RefundSettleRecon{
		NetRefundAmount:   90000,
		TotalRefundAmount: 90000,
		SettlementAmount:  120000,
	}
	updatedSettle := &model.RefundSettle{
		ID:                1,
		ZPTransID:         12345,
		NetRefundAmount:   90000,
		TotalRefundAmount: 90000,
		SettlementAmount:  120000,
		Status:            model.RefundSettleStatusInit,
	}

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(ctx, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(ctx, settle.ID).
		Return(settle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleReconAmts(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, s *model.RefundSettle) error {
			assert.Equal(suite.T(), int64(90000), s.NetRefundAmount)
			assert.Equal(suite.T(), int64(90000), s.TotalRefundAmount)
			assert.Equal(suite.T(), int64(120000), s.SettlementAmount)
			return nil
		})

	suite.mockTransaction.EXPECT().
		CommitTx(ctx).
		Return(nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(ctx)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).
		MinTimes(1).MaxTimes(2)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), settle.ID).
		Return(updatedSettle, nil).
		MinTimes(1).MaxTimes(2)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleEventData(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		MinTimes(0).MaxTimes(1)

	suite.mockInstallmentAdapter.EXPECT().
		NotifyInstallmentRefund(gomock.Any(), gomock.Any()).
		Return(nil).
		MinTimes(0).MaxTimes(1)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil).
		MinTimes(1).MaxTimes(2)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any()).
		MinTimes(1).MaxTimes(2)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(gomock.Any(), settle.ID).
		Return(updatedSettle, nil).
		MinTimes(0).MaxTimes(1)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleStatusByID(gomock.Any(), settle.ID, model.RefundSettleStatusPending).
		Return(nil).
		MinTimes(0).MaxTimes(1)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleEvent(gomock.Any(), gomock.Any()).
		Return(nil).
		MinTimes(0).MaxTimes(1)

	result, err := suite.usecase.updateRefundAfterRecon(ctx, settle, recon)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
}

func (suite *RefundTestSuite) TestUpdateRefundAfterRecon_NilSettle() {
	ctx := context.Background()
	recon := &model.RefundSettleRecon{
		NetRefundAmount: 90000,
	}

	result, err := suite.usecase.updateRefundAfterRecon(ctx, nil, recon)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "settle is nil")
}

func (suite *RefundTestSuite) TestUpdateRefundAfterRecon_BeginTxError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID: 1,
	}
	recon := &model.RefundSettleRecon{
		NetRefundAmount: 90000,
	}
	expectedError := errors.New("transaction error")

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(nil, expectedError)

	result, err := suite.usecase.updateRefundAfterRecon(ctx, settle, recon)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestUpdateRefundAfterRecon_GetSettleError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID: 1,
	}
	recon := &model.RefundSettleRecon{
		NetRefundAmount: 90000,
	}
	expectedError := errors.New("get settle error")

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(ctx, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(ctx, settle.ID).
		Return(nil, expectedError)

	suite.mockTransaction.EXPECT().
		RollbackTx(ctx)

	result, err := suite.usecase.updateRefundAfterRecon(ctx, settle, recon)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestUpdateRefundAfterRecon_UpdateError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID: 1,
	}
	recon := &model.RefundSettleRecon{
		NetRefundAmount: 90000,
	}
	expectedError := errors.New("update error")

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(ctx, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(ctx, settle.ID).
		Return(settle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleReconAmts(ctx, gomock.Any()).
		Return(expectedError)

	suite.mockTransaction.EXPECT().
		RollbackTx(ctx)

	result, err := suite.usecase.updateRefundAfterRecon(ctx, settle, recon)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestUpdateRefundAfterRecon_CommitError() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID: 1,
	}
	recon := &model.RefundSettleRecon{
		NetRefundAmount: 90000,
	}
	expectedError := errors.New("commit error")

	suite.mockTransaction.EXPECT().
		BeginTx(ctx).
		Return(ctx, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleForUpdate(ctx, settle.ID).
		Return(settle, nil)

	suite.mockRefundRepo.EXPECT().
		UpdateRefundSettleReconAmts(ctx, gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(ctx).
		Return(expectedError)

	suite.mockTransaction.EXPECT().
		RollbackTx(ctx)

	result, err := suite.usecase.updateRefundAfterRecon(ctx, settle, recon)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}
