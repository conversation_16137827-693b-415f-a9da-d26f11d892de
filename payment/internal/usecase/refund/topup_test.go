package refund

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/adapters"
	repository_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/repository"
	"go.uber.org/mock/gomock"
)

type TopupTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	usecase *Usecase

	// Repository mocks
	mockRefundRepo  *repository_mocks.MockRefundRepo
	mockPaymentRepo *repository_mocks.MockPaymentRepo
	mockOrderRepo   *repository_mocks.MockOrderRepo
	mockTransaction *repository_mocks.MockTransaction

	// Adapter mocks
	mockAccountAdapter       *adapter_mocks.MockAccountAdapter
	mockPartnerConnector     *adapter_mocks.MockPartnerConnector
	mockAcquiringCore        *adapter_mocks.MockAcquiringCoreAdapter
	mockInstallmentAdapter   *adapter_mocks.MockInstallmentAdapter
	mockTaskJobAdapter       *adapter_mocks.MockTaskJobAdapter
	mockDistributedLock      *adapter_mocks.MockDistributedLock
	mockRefundSettleNotifier *adapter_mocks.MockRefundSettleNotifier

	// Test configs
	paymentConfig      *configs.Payment
	orderConfigsHelper *configs.OrderConfigsHelper
	logger             log.Logger
}

func (suite *TopupTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())

	// Initialize repository mocks
	suite.mockRefundRepo = repository_mocks.NewMockRefundRepo(suite.ctrl)
	suite.mockPaymentRepo = repository_mocks.NewMockPaymentRepo(suite.ctrl)
	suite.mockOrderRepo = repository_mocks.NewMockOrderRepo(suite.ctrl)
	suite.mockTransaction = repository_mocks.NewMockTransaction(suite.ctrl)

	// Initialize adapter mocks
	suite.mockAccountAdapter = adapter_mocks.NewMockAccountAdapter(suite.ctrl)
	suite.mockPartnerConnector = adapter_mocks.NewMockPartnerConnector(suite.ctrl)
	suite.mockAcquiringCore = adapter_mocks.NewMockAcquiringCoreAdapter(suite.ctrl)
	suite.mockInstallmentAdapter = adapter_mocks.NewMockInstallmentAdapter(suite.ctrl)
	suite.mockTaskJobAdapter = adapter_mocks.NewMockTaskJobAdapter(suite.ctrl)
	suite.mockDistributedLock = adapter_mocks.NewMockDistributedLock(suite.ctrl)
	suite.mockRefundSettleNotifier = adapter_mocks.NewMockRefundSettleNotifier(suite.ctrl)

	// Initialize configs
	suite.logger = log.DefaultLogger
	suite.paymentConfig = &configs.Payment{
		Refund: &configs.Refund{},
	}
	suite.orderConfigsHelper = &configs.OrderConfigsHelper{}

	// Initialize usecase with all dependencies
	suite.usecase = NewRefundUsecase(
		suite.logger,
		suite.mockRefundRepo,
		suite.mockTransaction,
		suite.mockTaskJobAdapter,
		suite.mockDistributedLock,
		suite.paymentConfig,
		suite.orderConfigsHelper,
		suite.mockOrderRepo,
		suite.mockPaymentRepo,
		suite.mockPartnerConnector,
		suite.mockAcquiringCore,
		suite.mockAccountAdapter,
		suite.mockRefundSettleNotifier,
		suite.mockInstallmentAdapter,
	)
}

func (suite *TopupTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *TopupTestSuite) TestCreateTopup() {
	ctx := context.Background()
	req := &CreateTopupRequest{
		Amount:       1000,
		ZaloPayID:    12345,
		RefZPTransID: 456,
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_create_topup",
			setupMocks: func() {
				// Mock GetRefundSettleByZPTransID
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 2000,
					NetRefundAmount:  1000,
					UserTopupAmount:  0,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)

				// Mock ListRefundTopupOrder for precheck
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settle.ID).
					Return([]*model.RefundTopupOrder{}, nil)

				// Mock ValidateAndSyncSettlementForTopup
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 2000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Mock GetPaymentByTransID
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				// Mock CreateRefundTopupOrder (td-core)
				tdOrder := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "app_trans_123",
					ZpTransToken: "token_123",
					OrderNo:      12345,
					Description:  TopupOrderDescription,
					DataChecksum: "checksum_123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundTopupOrder(ctx, gomock.Any()).
					Return(tdOrder, nil)

				// Mock CreateRefundTopupOrder (save to DB)
				expectedOrder := &model.RefundTopupOrder{
					ID:             1,
					ZalopayID:      12345,
					AppTransID:     "app_trans_123",
					AppID:          789,
					Amount:         1000,
					ZPTransToken:   "token_123",
					Type:           model.OrderTypeRefund,
					Status:         model.OrderStatusInit,
					Stage:          model.OrderStageTopup,
					Description:    TopupOrderDescription,
					RefundSettleID: 123,
					PartnerCode:    "PARTNER_A",
					ExtraData: model.RefundOrderExtra{
						RefZPTransID: 456,
						ExtOrderNo:   12345,
						ExtChecksum:  "checksum_123",
					},
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundTopupOrder(ctx, gomock.Any()).
					Return(expectedOrder, nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_settle_info",
			setupMocks: func() {
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(nil, errors.New("settle not found"))
			},
			expectedError: "settle not found",
		},
		{
			name: "error_precheck_topup_condition",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusCompleted, // Not in topup required state
					SettlementAmount: 2000,
					NetRefundAmount:  2000, // Already has sufficient funds
					UserTopupAmount:  0,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)
			},
			expectedError: "Settlement is not in user topup required state",
		},
		{
			name: "error_validate_and_sync_settlement",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 2000,
					NetRefundAmount:  1000,
					UserTopupAmount:  0,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)

				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settle.ID).
					Return([]*model.RefundTopupOrder{}, nil)

				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(model.EarlyDischarge{}, errors.New("get early discharge error"))
			},
			expectedError: "get early discharge error",
		},
		{
			name: "error_invalid_topup_amount",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 2000,
					NetRefundAmount:  1500, // GetTotalCollectAmount() = 1500, obligation = 2000-1500 = 500
					UserTopupAmount:  0,    // But request amount is 1000, which doesn't match
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)

				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settle.ID).
					Return([]*model.RefundTopupOrder{}, nil)

				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 2000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)
			},
			expectedError: "invalid topup order amount",
		},
		{
			name: "error_get_payment_info",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 2000,
					NetRefundAmount:  1000,
					UserTopupAmount:  0,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)

				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settle.ID).
					Return([]*model.RefundTopupOrder{}, nil)

				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 2000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(nil, errors.New("payment not found"))
			},
			expectedError: "payment not found",
		},
		{
			name: "error_create_td_topup_order",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 2000,
					NetRefundAmount:  1000,
					UserTopupAmount:  0,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)

				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settle.ID).
					Return([]*model.RefundTopupOrder{}, nil)

				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 2000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				suite.mockAcquiringCore.EXPECT().
					CreateRefundTopupOrder(ctx, gomock.Any()).
					Return(nil, errors.New("create td order error"))
			},
			expectedError: "create td order error",
		},
		{
			name: "error_save_topup_order",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 2000,
					NetRefundAmount:  1000,
					UserTopupAmount:  0,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, req.RefZPTransID).
					Return(settle, nil)

				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settle.ID).
					Return([]*model.RefundTopupOrder{}, nil)

				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 2000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				tdOrder := &model.CreateRefundOrderResponse{
					AppID:        789,
					Amount:       1000,
					AppTransID:   "app_trans_123",
					ZpTransToken: "token_123",
					OrderNo:      12345,
					Description:  TopupOrderDescription,
					DataChecksum: "checksum_123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundTopupOrder(ctx, gomock.Any()).
					Return(tdOrder, nil)

				suite.mockOrderRepo.EXPECT().
					CreateRefundTopupOrder(ctx, gomock.Any()).
					Return(nil, errors.New("save order error"))
			},
			expectedError: "save order error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.CreateTopup(ctx, req)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(result)
			} else {
				suite.NoError(err)
				suite.NotNil(result)
				suite.Equal(int64(1), result.ID)
				suite.Equal(req.Amount, result.Amount)
				suite.Equal(req.ZaloPayID, result.ZalopayID)
			}
		})
	}
}

func (suite *TopupTestSuite) TestGetTopupOrder() {
	ctx := context.Background()
	appID := int32(789)
	appTransID := "app_trans_123"

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_get_topup_order",
			setupMocks: func() {
				expectedOrder := &model.RefundTopupOrder{
					ID:             1,
					ZalopayID:      12345,
					AppTransID:     appTransID,
					AppID:          appID,
					Amount:         1000,
					Status:         model.OrderStatusPending,
					RefundSettleID: 123,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, appTransID, appID).
					Return(expectedOrder, nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_topup_order",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, appTransID, appID).
					Return(nil, errors.New("order not found"))
			},
			expectedError: "order not found",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.GetTopupOrder(ctx, appID, appTransID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(result)
			} else {
				suite.NoError(err)
				suite.NotNil(result)
				suite.Equal(appID, result.AppID)
				suite.Equal(appTransID, result.AppTransID)
			}
		})
	}
}

func (suite *TopupTestSuite) TestProcessTopupOrderUpdate() {
	ctx := context.Background()
	req := &TopupOrderUpdateRequest{
		AppID:        789,
		AppTransID:   "app_trans_123",
		ZPTransID:    456,
		OrderStatus:  model.OrderStatusSucceeded,
		OriginStatus: 1,
		ReasonStatus: "success",
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_process_order_update",
			setupMocks: func() {
				order := &model.RefundTopupOrder{
					ID:             1,
					ZalopayID:      12345,
					AppTransID:     req.AppTransID,
					AppID:          req.AppID,
					Amount:         1000,
					Status:         model.OrderStatusPending,
					RefundSettleID: 123,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, req.AppTransID, req.AppID).
					Return(order, nil)

				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(nil)

				// Mock for calcAndUpdateSettleTopups (async call)
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(gomock.Any(), order.RefundSettleID).
					Return([]*model.RefundTopupOrder{order}, nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					UpdateRefundTopupsAmount(gomock.Any(), order.RefundSettleID, gomock.Any()).
					Return(nil).
					AnyTimes()

				// Mock for ProcessRefundSettleInfo (async call)
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(gomock.Any(), order.RefundSettleID).
					Return(&model.RefundSettle{
						ID:     order.RefundSettleID,
						Status: model.RefundSettleStatusInit,
					}, nil).
					AnyTimes()

				suite.mockTransaction.EXPECT().
					BeginTx(gomock.Any()).
					Return(ctx, nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(gomock.Any(), order.RefundSettleID).
					Return(&model.RefundSettle{
						ID:     order.RefundSettleID,
						Status: model.RefundSettleStatusInit,
					}, nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(gomock.Any(), order.RefundSettleID, gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleEventData(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockInstallmentAdapter.EXPECT().
					NotifyInstallmentRefund(gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockTransaction.EXPECT().
					CommitTx(gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleEvent(gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockTransaction.EXPECT().
					RollbackTx(gomock.Any()).
					Return(nil).
					AnyTimes()
			},
			expectedError: "",
		},
		{
			name: "order_not_found",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, req.AppTransID, req.AppID).
					Return(nil, model.ErrOrderNotFound)
			},
			expectedError: "",
		},
		{
			name: "error_get_topup_order",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, req.AppTransID, req.AppID).
					Return(nil, errors.New("database error"))
			},
			expectedError: "database error",
		},
		{
			name: "order_is_final",
			setupMocks: func() {
				order := &model.RefundTopupOrder{
					ID:             1,
					ZalopayID:      12345,
					AppTransID:     req.AppTransID,
					AppID:          req.AppID,
					Amount:         1000,
					Status:         model.OrderStatusSucceeded, // Already final
					RefundSettleID: 123,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, req.AppTransID, req.AppID).
					Return(order, nil)
			},
			expectedError: "",
		},
		{
			name: "error_update_order_progress",
			setupMocks: func() {
				order := &model.RefundTopupOrder{
					ID:             1,
					ZalopayID:      12345,
					AppTransID:     req.AppTransID,
					AppID:          req.AppID,
					Amount:         1000,
					Status:         model.OrderStatusPending,
					RefundSettleID: 123,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, req.AppTransID, req.AppID).
					Return(order, nil)

				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(errors.New("update error"))
			},
			expectedError: "update error",
		},
		{
			name: "order_not_success",
			setupMocks: func() {
				failedReq := &TopupOrderUpdateRequest{
					AppID:        789,
					AppTransID:   "app_trans_123",
					ZPTransID:    456,
					OrderStatus:  model.OrderStatusFailed,
					OriginStatus: 1,
					ReasonStatus: "failed",
				}

				order := &model.RefundTopupOrder{
					ID:             1,
					ZalopayID:      12345,
					AppTransID:     failedReq.AppTransID,
					AppID:          failedReq.AppID,
					Amount:         1000,
					Status:         model.OrderStatusPending,
					RefundSettleID: 123,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundTopupOrder(ctx, failedReq.AppTransID, failedReq.AppID).
					Return(order, nil)

				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(nil)

				err := suite.usecase.ProcessTopupOrderUpdate(ctx, failedReq)
				suite.NoError(err)
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			if tt.name == "order_not_success" {
				return // This test case handles its own execution
			}

			tt.setupMocks()

			err := suite.usecase.ProcessTopupOrderUpdate(ctx, req)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *TopupTestSuite) TestCalcAndUpdateSettleTopups() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_calc_and_update",
			setupMocks: func() {
				orders := []*model.RefundTopupOrder{
					{
						ID:             1,
						Amount:         500,
						Status:         model.OrderStatusSucceeded,
						RefundSettleID: settleID,
					},
					{
						ID:             2,
						Amount:         300,
						Status:         model.OrderStatusSucceeded,
						RefundSettleID: settleID,
					},
					{
						ID:             3,
						Amount:         200,
						Status:         model.OrderStatusFailed,
						RefundSettleID: settleID,
					},
				}
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settleID).
					Return(orders, nil)

				// Expected topup amount = 500 + 300 = 800 (only succeeded orders)
				suite.mockRefundRepo.EXPECT().
					UpdateRefundTopupsAmount(ctx, settleID, int64(800)).
					Return(nil)

				// Mock for ProcessRefundSettleInfo (async call)
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(gomock.Any(), settleID).
					Return(&model.RefundSettle{
						ID:     settleID,
						Status: model.RefundSettleStatusInit,
					}, nil).
					AnyTimes()

				suite.mockTransaction.EXPECT().
					BeginTx(gomock.Any()).
					Return(ctx, nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(gomock.Any(), settleID).
					Return(&model.RefundSettle{
						ID:     settleID,
						Status: model.RefundSettleStatusInit,
					}, nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(gomock.Any(), settleID, model.RefundSettleStatusPending).
					Return(nil).
					AnyTimes()

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleEventData(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockInstallmentAdapter.EXPECT().
					NotifyInstallmentRefund(gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockTransaction.EXPECT().
					CommitTx(gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleEvent(gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				suite.mockTransaction.EXPECT().
					RollbackTx(gomock.Any()).
					Return(nil).
					AnyTimes()
			},
			expectedError: "",
		},
		{
			name: "error_list_topup_orders",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settleID).
					Return(nil, errors.New("list orders error"))
			},
			expectedError: "list orders error",
		},
		{
			name: "error_update_topup_amount",
			setupMocks: func() {
				orders := []*model.RefundTopupOrder{
					{
						ID:             1,
						Amount:         500,
						Status:         model.OrderStatusSucceeded,
						RefundSettleID: settleID,
					},
				}
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, settleID).
					Return(orders, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundTopupsAmount(ctx, settleID, int64(500)).
					Return(errors.New("update amount error"))
			},
			expectedError: "update amount error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.calcAndUpdateSettleTopups(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *TopupTestSuite) TestPrecheckTopupCondition() {
	ctx := context.Background()

	tests := []struct {
		name          string
		settle        *model.RefundSettle
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_no_existing_orders",
			settle: &model.RefundSettle{
				ID:               123,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 2000,
				NetRefundAmount:  1000,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, int64(123)).
					Return([]*model.RefundTopupOrder{}, nil)
			},
			expectedError: "",
		},
		{
			name: "success_no_processing_orders",
			settle: &model.RefundSettle{
				ID:               123,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 2000,
				NetRefundAmount:  1000,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				orders := []*model.RefundTopupOrder{
					{
						ID:     1,
						Status: model.OrderStatusSucceeded,
					},
					{
						ID:     2,
						Status: model.OrderStatusFailed,
					},
				}
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, int64(123)).
					Return(orders, nil)
			},
			expectedError: "",
		},
		{
			name: "error_not_topup_required_state",
			settle: &model.RefundSettle{
				ID:               123,
				Status:           model.RefundSettleStatusCompleted,
				SettlementAmount: 2000,
				NetRefundAmount:  2000, // Has sufficient funds
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				// No mocks needed as it should fail before calling repo
			},
			expectedError: "Settlement is not in user topup required state",
		},
		{
			name: "error_get_topup_orders",
			settle: &model.RefundSettle{
				ID:               123,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 2000,
				NetRefundAmount:  1000,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, int64(123)).
					Return(nil, errors.New("database error"))
			},
			expectedError: "Failed to get topup orders",
		},
		{
			name: "error_has_processing_order",
			settle: &model.RefundSettle{
				ID:               123,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 2000,
				NetRefundAmount:  1000,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				orders := []*model.RefundTopupOrder{
					{
						ID:     1,
						Status: model.OrderStatusPending, // Processing order
					},
				}
				suite.mockOrderRepo.EXPECT().
					ListRefundTopupOrder(ctx, int64(123)).
					Return(orders, nil)
			},
			expectedError: "There is a processing topup order",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.precheckTopupCondition(ctx, tt.settle)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *TopupTestSuite) TestValidateAndSyncSettlementForTopup() {
	ctx := context.Background()

	tests := []struct {
		name          string
		settle        *model.RefundSettle
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_validate_and_sync",
			settle: &model.RefundSettle{
				ID:               123,
				ZPTransID:        456,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 1000,
				NetRefundAmount:  500,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 1500,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, int64(456), true).
					Return(earlyDischarge, nil)

				// Mock syncSettlementAmountIfChanged - when amount changes, it calls GetRefundSettleByID
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{
						ID:               123,
						ZPTransID:        456,
						Status:           model.RefundSettleStatusPending,
						SettlementAmount: 1000,
						NetRefundAmount:  500,
						UserTopupAmount:  0,
					}, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettlementAmount(ctx, int64(123), int64(1500)).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "success_no_amount_change",
			settle: &model.RefundSettle{
				ID:               123,
				ZPTransID:        456,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 1000,
				NetRefundAmount:  500,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 1000, // Same amount, no change
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, int64(456), true).
					Return(earlyDischarge, nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_early_discharge",
			settle: &model.RefundSettle{
				ID:               123,
				ZPTransID:        456,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 1000,
				NetRefundAmount:  500,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, int64(456), true).
					Return(model.EarlyDischarge{}, errors.New("get early discharge error"))
			},
			expectedError: "get early discharge error",
		},
		{
			name: "error_validate_early_discharge",
			settle: &model.RefundSettle{
				ID:               123,
				ZPTransID:        456,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 1000,
				NetRefundAmount:  500,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  false, // Invalid state
					InSession:   true,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, int64(456), true).
					Return(earlyDischarge, nil)
			},
			expectedError: "invalid status",
		},
		{
			name: "error_session_not_available",
			settle: &model.RefundSettle{
				ID:               123,
				ZPTransID:        456,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 1000,
				NetRefundAmount:  500,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   false, // Session not available
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, int64(456), true).
					Return(earlyDischarge, nil)
			},
			expectedError: "session not available for topup",
		},
		{
			name: "error_sync_settlement_amount",
			settle: &model.RefundSettle{
				ID:               123,
				ZPTransID:        456,
				Status:           model.RefundSettleStatusPending,
				SettlementAmount: 1000,
				NetRefundAmount:  500,
				UserTopupAmount:  0,
			},
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					InSession:   true,
					TotalAmount: 1500,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, int64(456), true).
					Return(earlyDischarge, nil)

				// Mock syncSettlementAmountIfChanged - when amount changes, it calls GetRefundSettleByID
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{
						ID:               123,
						ZPTransID:        456,
						Status:           model.RefundSettleStatusPending,
						SettlementAmount: 1000,
						NetRefundAmount:  500,
						UserTopupAmount:  0,
					}, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettlementAmount(ctx, int64(123), int64(1500)).
					Return(errors.New("update amount error"))
			},
			expectedError: "update amount error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.ValidateAndSyncSettlementForTopup(ctx, tt.settle)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(result)
			} else {
				suite.NoError(err)
				suite.NotNil(result)
				suite.Equal(tt.settle.ID, result.ID)
				suite.Equal(tt.settle.ZPTransID, result.ZPTransID)
			}
		})
	}
}

func TestTopupTestSuite(t *testing.T) {
	suite.Run(t, new(TopupTestSuite))
}
