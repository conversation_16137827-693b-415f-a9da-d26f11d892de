package refund

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

func (uc *Usecase) PollingEarlyDischargeStatus(ctx context.Context, req *model.EarlyDischargeStatusWorkflowRequest) (*model.EarlyDischargeStatusWorkflowResponse, error) {
	var span trace.Span
	ctx, span = otel.GetTracerProvider().Tracer("payment-service").Start(ctx, "early-discharge-polling-status")
	defer span.End()

	logger := uc.logger.WithContext(ctx)
	logger.Infow("msg", "PollingEarlyDischargeStatus", "req", req)

	dischargeLog, err := uc.paymentRepo.GetEarlyDischargeLog(ctx, req.PaymentID, req.ZalopayID)
	if err != nil {
		logger.Errorw("msg", "[PollingEarlyDischargeStatus] get early discharge log fail", "req", req, "error", err)
		return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, get early discharge log fail")
	}
	if dischargeLog.Status.IsFinalStatus() {
		logger.Infow("msg", "PollingEarlyDischargeStatus, discharge already in final status", "discharge_id", dischargeLog.ID, "status", dischargeLog.Status)
		return &model.EarlyDischargeStatusWorkflowResponse{DischargeStatus: dischargeLog.Status.String()}, nil
	}

	cimbTrans, err := uc.connector.InquiryTransaction(ctx, dischargeLog.PartnerReqID)
	if err != nil {
		// Handle inquiry transaction error, including "transaction not found" case
		return uc.handleDischargeTransactionError(ctx, err, dischargeLog)
	}

	if !cimbTrans.PaymentStatus.IsFinalStatus() {
		logger.Infow("msg", "PollingEarlyDischargeStatus still processing, will retry", "discharge_id", dischargeLog.ID, "cimb_status", cimbTrans.PaymentStatus)
		return nil, errors.Errorf("PollingEarlyDischargeStatus still processing, will retry, cimb_status: %s", cimbTrans.PaymentStatus)
	}

	dischargeLog.Status = model.FromCIMBPaymentStatusToPaymentStatus(cimbTrans.PaymentStatus)
	dischargeLog.PartnerData.RepaymentResult.RepaymentStatus = cimbTrans.PaymentStatus.String()

	switch dischargeLog.Status {
	case model.PaymentStatusSucceeded:
		dischargeLog.PartnerData.RepaymentResult.CIMBError = model.CIMBError{}
		dischargeLog.PartnerData.RepaymentResult.BankTransID = cimbTrans.BankTransactionSequenceID
	}

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin transaction fail", "error", err)
		return nil, errors.Wrap(err, "begin transaction fail")
	}
	defer uc.txn.RollbackTx(tCtx)

	_, err = uc.paymentRepo.UpdateEarlyDischargeLog(tCtx, *dischargeLog)
	if err != nil {
		logger.Errorw("msg", "PollingEarlyDischargeStatus, update early discharge log fail", "discharge_id", dischargeLog.ID, "status", dischargeLog.Status, "error", err)
		return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, update early discharge log fail")
	}

	if err := uc.PublishRefundSettleResult(tCtx, dischargeLog); err != nil {
		logger.Errorw("msg", "PollingEarlyDischargeStatus, publish refund settle result fail", "discharge_id", dischargeLog.ID, "status", dischargeLog.Status, "error", err)
		return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, publish refund settle result fail")
	}

	if err := uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit transaction fail", "error", err)
		return nil, errors.Wrap(err, "commit transaction fail")
	}

	response := &model.EarlyDischargeStatusWorkflowResponse{
		DischargeStatus:        dischargeLog.Status.String(),
		PartnerDischargeStatus: cimbTrans.PaymentStatus.String(),
	}

	logger.Infow("msg", "PollingEarlyDischargeStatus completed",
		"discharge_id", dischargeLog.ID,
		"status", dischargeLog.Status,
	)

	return response, nil
}

func (uc *Usecase) PollRepaymentStatus(ctx context.Context, req *model.RepaymentStatusPollingRequest) (*model.RepaymentStatusPollingResponse, error) {
	logger := uc.logger.WithContext(ctx)

	repayLog, err := uc.paymentRepo.GetRepaymentByID(ctx, req.PaymentID)
	if err != nil {
		logger.Errorw("msg", "get repayment log fail", "error", err)
		return nil, err
	}

	// Check if repayment is already in final status
	if repayLog.Status.IsFinalStatus() {
		logger.Infow("msg", "repayment already in final status", "repay_log_id", repayLog.ID, "status", repayLog.Status)
		return &model.RepaymentStatusPollingResponse{RepayStatus: repayLog.Status.String()}, nil
	}

	transStatus, err := uc.connector.InquiryTransaction(ctx, repayLog.PartnerReqID)
	if err != nil {
		logger.Errorw("msg", "inquiry transaction fail", "error", err)
		return uc.handleRepaymentTransactionError(ctx, err, repayLog)
	}
	if !transStatus.PaymentStatus.IsFinalStatus() {
		logger.Infow("msg", "PollingRepaymentStatus still processing, will retry", "cimb_status", transStatus.PaymentStatus)
		return nil, errors.Errorf("PollingRepaymentStatus still processing, will retry, cimb_status: %s", transStatus.PaymentStatus)
	}

	// Update repayment log with new status
	repayLog.Status = model.FromCIMBPaymentStatusToPaymentStatus(transStatus.PaymentStatus)
	repayLog.PartnerData.RepaymentResult.RepaymentStatus = transStatus.PaymentStatus.String()

	switch repayLog.Status {
	case model.PaymentStatusSucceeded:
		repayLog.PartnerData.RepaymentResult.CIMBError = model.CIMBError{}
		repayLog.PartnerData.RepaymentResult.BankTransID = transStatus.BankTransactionSequenceID
	}

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin transaction fail", "error", err)
		return nil, errors.Wrap(err, "begin transaction fail")
	}
	defer uc.txn.RollbackTx(tCtx)

	repayLog, err = uc.paymentRepo.GetRepaymentByID(tCtx, req.PaymentID)
	if err != nil {
		logger.Errorw("msg", "get repayment log fail", "error", err)
		return nil, errors.Wrap(err, "get repayment log fail")
	}

	if err := uc.paymentRepo.UpdateRepaymentLogStatus(tCtx, *repayLog); err != nil {
		logger.Errorw("msg", "update repayment log status fail", "error", err)
		return nil, errors.Wrap(err, "update repayment log status fail")
	}

	if err := uc.PublishRefundExpiredResult(tCtx, repayLog); err != nil {
		logger.Errorw("msg", "publish refund expired result fail", "error", err)
		return nil, errors.Wrap(err, "publish refund expired result fail")
	}

	if err := uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit transaction fail", "error", err)
		return nil, errors.Wrap(err, "commit transaction fail")
	}

	logger.Infow("msg", "publish refund expired result success", "repay_log_id", repayLog.ID)

	return &model.RepaymentStatusPollingResponse{
		RepayStatus:   repayLog.Status.String(),
		PartnerStatus: transStatus.PaymentStatus.String(),
	}, nil
}

func (uc *Usecase) handleDischargeTransactionError(ctx context.Context, iErr error, dischargeLog *model.EarlyDischargeLog) (*model.EarlyDischargeStatusWorkflowResponse, error) {
	logger := uc.logger.WithContext(ctx)

	logger.Errorw("msg", "[PollingEarlyDischargeStatus] inquiry transaction fail", "discharge_id", dischargeLog.ID, "error", iErr)

	if !errors.Is(iErr, model.ErrPartnerTransactionNotFound) {
		logger.Errorw("msg", "[PollingEarlyDischargeStatus] inquiry transaction fail", "discharge_id", dischargeLog.ID, "error", iErr)
		return nil, errors.Wrap(iErr, "PollingEarlyDischargeStatus, inquiry transaction fail")
	}

	// Check if the error is specifically a transaction not found error
	logger.Warnw("msg", "[PollingEarlyDischargeStatus] transaction not found in partner system, marking as failed",
		"discharge_id", dischargeLog.ID, "error", iErr)

	// Update the discharge log status to failed
	dischargeLog.Status = model.PaymentStatusFailed
	dischargeLog.PartnerData.RepaymentResult.BankTransID = ""
	dischargeLog.PartnerData.RepaymentResult.RepaymentStatus = ""
	dischargeLog.PartnerData.RepaymentResult.CIMBError.
		SetErrorCodeIfNotExist("transaction_not_found").
		AppendErrorDescription(iErr.Error())

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "[PollingEarlyDischargeStatus] begin transaction fail", "error", err)
		return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, begin transaction fail")
	}
	defer uc.txn.RollbackTx(tCtx)

	_, updErr := uc.paymentRepo.UpdateEarlyDischargeLog(tCtx, *dischargeLog)
	if updErr != nil {
		logger.Errorw("msg", "[PollingEarlyDischargeStatus] update early discharge log to failed status failed",
			"discharge_id", dischargeLog.ID, "error", updErr)
		return nil, errors.Wrap(updErr, "PollingEarlyDischargeStatus, update early discharge log to failed status failed")
	}

	if err := uc.PublishRefundSettleResult(tCtx, dischargeLog); err != nil {
		logger.Errorw("msg", "[PollingEarlyDischargeStatus] publish refund settle result failed", "discharge_id", dischargeLog.ID, "error", err)
		return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, publish refund settle result failed")
	}

	if err := uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "[PollingEarlyDischargeStatus] commit transaction fail", "error", err)
		return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, commit transaction fail")
	}

	logger.Infow("msg", "[PollingEarlyDischargeStatus] publish refund settle result success", "discharge_id", dischargeLog.ID)

	return &model.EarlyDischargeStatusWorkflowResponse{
		DischargeStatus:        dischargeLog.Status.String(),
		ErrorDescription:       iErr.Error(),
		PartnerDischargeStatus: model.CIMBPaymentStatusTransferFailed.String(),
	}, nil
}

func (uc *Usecase) handleRepaymentTransactionError(ctx context.Context, iErr error, repayLog *model.RepaymentLog) (*model.RepaymentStatusPollingResponse, error) {
	logger := uc.logger.WithContext(ctx)

	logger.Errorw("msg", "[PollRepaymentStatus] inquiry transaction fail", "repay_log_id", repayLog.ID, "error", iErr)

	if !errors.Is(iErr, model.ErrPartnerTransactionNotFound) {
		logger.Errorw("msg", "[PollRepaymentStatus] inquiry transaction fail", "repay_log_id", repayLog.ID, "error", iErr)
		return nil, errors.Wrap(iErr, "PollRepaymentStatus, inquiry transaction fail")
	}

	// Check if the error is specifically a transaction not found error
	logger.Warnw("msg", "[PollRepaymentStatus] transaction not found in partner system, marking as failed",
		"repay_log_id", repayLog.ID, "error", iErr)

	// Update the repayment log status to failed
	repayLog.Status = model.PaymentStatusFailed
	repayLog.PartnerData.RepaymentResult.BankTransID = ""
	repayLog.PartnerData.RepaymentResult.RepaymentStatus = ""
	repayLog.PartnerData.RepaymentResult.CIMBError.
		SetErrorCodeIfNotExist("transaction_not_found").
		AppendErrorDescription(iErr.Error())

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "[PollRepaymentStatus] begin transaction fail", "error", err)
		return nil, errors.Wrap(err, "PollRepaymentStatus, begin transaction fail")
	}
	defer uc.txn.RollbackTx(tCtx)

	if err := uc.paymentRepo.UpdateRepaymentLogStatus(tCtx, *repayLog); err != nil {
		logger.Errorw("msg", "[PollRepaymentStatus] update repayment log to failed status failed",
			"repay_log_id", repayLog.ID, "error", err)
		return nil, errors.Wrap(err, "PollRepaymentStatus, update repayment log to failed status failed")
	}

	if err := uc.PublishRefundExpiredResult(tCtx, repayLog); err != nil {
		logger.Errorw("msg", "[PollRepaymentStatus] publish refund expired result failed", "repay_log_id", repayLog.ID, "error", err)
		return nil, errors.Wrap(err, "PollRepaymentStatus, publish refund expired result failed")
	}

	logger.Infow("msg", "[PollRepaymentStatus] publish refund expired result success", "repay_log_id", repayLog.ID)

	return &model.RepaymentStatusPollingResponse{
		RepayStatus:      repayLog.Status.String(),
		PartnerStatus:    model.CIMBPaymentStatusTransferFailed.String(),
		ErrorDescription: iErr.Error(),
	}, nil
}
