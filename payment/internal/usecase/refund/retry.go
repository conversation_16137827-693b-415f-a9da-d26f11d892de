package refund

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

func (uc *Usecase) RetrySubmitSettleOrder(ctx context.Context, settleID int64) error {
	logger := uc.logger.WithContext(ctx)

	settle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
	if err != nil {
		logger.Errorw("msg", "Failed to get settle info", "error", err)
		return err
	}
	if settle.Status != model.RefundSettleStatusProcessing {
		logger.Warnw("msg", "Settle order not in processing status")
		return nil
	}

	orders, err := uc.orderRepo.ListRefundSettleOrder(ctx, settleID)
	if err != nil {
		logger.Errorw("msg", "Failed to get settle orders", "error", err)
		return err
	}

	ordersCast := model.RefundSettleOrders(orders).SortByIDAsc()
	if !ordersCast.HasLastOrderFailed() {
		logger.Warnw("msg", "No failed order to retry")
		return nil
	}
	if !ordersCast.EligibleToRetrySubmit() {
		logger.Warnw("msg", "Settle order not eligible to retry submit")
		return nil
	}

	err = uc.txn.WithTx(ctx, func(tCtx context.Context) error {
		lastOrder := ordersCast.MustGetLastOrder()
		newOrder := lastOrder.CloneForResubmission()
		acOrder, tErr := uc.orderAdapter.CreateRefundSettleOrder(tCtx, &model.CreateRefundOrderRequest{
			Amount:       newOrder.Amount,
			ZaloPayID:    newOrder.ZalopayID,
			AppTransID:   idgen.GenAppTransID(),
			Description:  newOrder.Description,
			RefZPTransID: newOrder.GetRefZPTransID(),
			PartnerCode:  partner.PartnerCode(newOrder.PartnerCode),
		})
		if tErr != nil {
			logger.Errorw("msg", "Failed to create refund settle order", "error", tErr)
			return tErr
		}

		newOrder = newOrder.WithStatus(model.OrderStatusPending).FulfillSettleOrder(acOrder)
		newOrder, tErr = uc.orderRepo.CreateRefundSettleOrder(tCtx, newOrder)
		if tErr != nil {
			logger.Errorw("msg", "Failed to create refund settle order", "error", tErr)
			return tErr
		}

		eadLog, err := uc.paymentRepo.GetEarlyDischargeLogByOrderID(tCtx, lastOrder.ID)
		if err != nil {
			logger.Errorw("msg", "Failed to get early discharge log", "error", err)
			return err
		}

		eadLog = eadLog.WithRefundSettleOrder(newOrder)
		err = uc.paymentRepo.UpdatePaymentLogOrderIDs(tCtx, eadLog.ID, eadLog.Order)
		if err != nil {
			logger.Errorw("msg", "Failed to update early discharge log", "error", err)
			return err
		}

		err = uc.orderAdapter.SubmitRefundSettleOrder(tCtx, newOrder)
		if err != nil {
			logger.Errorw("msg", "Failed to submit refund settle order", "error", err)
			return err
		}

		return nil
	})
	if err != nil {
		logger.Errorw("msg", "Failed to retry submit settle order", "error", err)
		return err
	}

	logger.Infow("msg", "Retry submit settle order success", "settle_id", settleID)

	return nil
}
