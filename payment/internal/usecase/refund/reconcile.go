package refund

import (
	"context"
	"time"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

func (uc *Usecase) TriggerJobReconcileRefundSettlements(ctx context.Context, zpTransID int64) error {
	logger := uc.logger.WithContext(ctx)
	retryOpts := []retry.Option{
		retry.Attempts(3),
		retry.Delay(3 * time.Second),
	}

	err := retry.Do(func() error {
		params := &model.RefundSettleReconParams{ZPTransID: zpTransID}
		if err := uc.taskJob.ExecuteReconcileRefundSettleJob(ctx, params); err != nil {
			logger.Errorw("msg", "trigger reconcile refund settlements fail", "error", err)
			return err
		}
		return nil
	}, retryOpts...)
	if err != nil {
		logger.Errorw("msg", "trigger reconcile refund settlements fail", "error", err)
		return err
	}
	return nil
}

func (uc *Usecase) ReconcileRefundSettlements(ctx context.Context, zpTransID int64) error {
	logger := uc.logger.WithContext(ctx)

	earlyDischarge, err := uc.installmentAdapter.GetEarlyDischarge(ctx, zpTransID, true)
	if err != nil {
		logger.Errorw("msg", "get early discharge fail", "error", err)
		return err
	}
	if err = earlyDischarge.Validate(); err != nil {
		logger.Errorw("msg", "validate early discharge fail", "error", err)
		return err
	}

	refundLogs, err := uc.repo.GetRefundLogsByZPTransID(ctx, zpTransID)
	if err != nil {
		logger.Errorw("msg", "get refund log settlements fail", "error", err)
		return err
	}
	if len(refundLogs) == 0 {
		logger.Infow("msg", "no refund log settlements found")
		return errors.New("no refund log settlements found")
	}

	// Calculate total refund amount
	refundLogsCast := model.RefundOrders(refundLogs)
	dischargeAmount := earlyDischarge.TotalAmount
	netRefundAmount := refundLogsCast.SumNetRefundAmount()
	totalRefundAmount := refundLogsCast.SumTotalRefundAmount()
	refundRecon := &model.RefundSettleRecon{
		NetRefundAmount:   netRefundAmount,
		TotalRefundAmount: totalRefundAmount,
		SettlementAmount:  dischargeAmount,
	}

	// Get current refund settle
	refundSettle, err := uc.repo.GetRefundSettleByZPTransID(ctx, zpTransID)
	if err != nil && !errors.Is(err, model.ErrRefundSettleNotFound) {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return err
	}
	if refundSettle == nil {
		if refundRecon.NetRefundAmount == 0 {
			logger.Infow("msg", "refund amount is zero, no need to create reconcile")
			return nil
		}

		orgPurchase, err := uc.paymentRepo.GetPaymentByTransID(ctx, zpTransID)
		if err != nil {
			logger.Errorw("msg", "get purchase fail", "error", err)
			return err
		}

		refundSettle := model.NewRefundSettle(zpTransID, refundRecon)
		refundSettle = refundSettle.StorePurchaseSnapshot(orgPurchase)
		refundSettle, err = uc.createRefundSettle(ctx, refundSettle)
		if err != nil {
			logger.Errorw("msg", "create refund settle fail", "error", err)
			return err
		}
		logger.Infow("msg", "create refund settle success", "refundSettle", refundSettle)
		return nil
	}

	logger.Infow("msg", "current refund settle info", "refundSettle", refundSettle)

	if !refundSettle.HasSnapshotChanged() && !refundSettle.HasReconAmountsChanged(refundRecon) {
		logger.Infow("msg", "refund settle snapshot and recon amount not changed")
		return nil
	}

	// Update refund settle after recon
	refundSettle, err = uc.updateRefundAfterRecon(ctx, refundSettle, refundRecon)
	if err != nil {
		logger.Errorw("msg", "update refund settle fail", "error", err)
		return err
	}

	logger.Infow("msg", "reconcile refund settle success", "refundSettle", refundSettle)

	return nil
}

func (uc *Usecase) createRefundSettle(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) {
	logger := uc.logger.WithContext(ctx)

	if settle == nil {
		logger.Errorw("msg", "settle is nil")
		return nil, errors.New("settle is nil")
	}

	settle, err := uc.repo.CreateRefundSettle(ctx, settle)
	if err != nil {
		logger.Errorw("msg", "create refund settle fail", "error", err)
		return nil, err
	}

	err = uc.DispatchRefundSettleChanged(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "dispatch refund settle changed fail", "error", err)
		return nil, err
	}

	err = uc.ProcessRefundSettleInfo(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "process refund settle info fail", "error", err)
		return nil, err
	}

	return settle, nil
}

func (uc *Usecase) updateRefundAfterRecon(
	ctx context.Context,
	settle *model.RefundSettle,
	recon *model.RefundSettleRecon) (*model.RefundSettle, error) {
	logger := uc.logger.WithContext(ctx)

	if settle == nil {
		logger.Errorw("msg", "settle is nil")
		return nil, errors.New("settle is nil")
	}

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorw("msg", "begin tx fail", "error", err)
		return nil, err
	}
	defer uc.txn.RollbackTx(tCtx)

	settle, err = uc.repo.GetRefundSettleForUpdate(tCtx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "error", err)
		return nil, err
	}

	settle = settle.SyncReconAmounts(recon)
	err = uc.repo.UpdateRefundSettleReconAmts(tCtx, settle)
	if err != nil {
		logger.Errorw("msg", "update refund settle fail", "error", err)
		return nil, err
	}

	if err = uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorw("msg", "commit tx fail", "error", err)
		return nil, err
	}

	err = uc.DispatchRefundSettleChanged(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "dispatch refund settle changed fail", "error", err)
		return nil, err
	}

	err = uc.ProcessRefundSettleInfo(ctx, settle.ID)
	if err != nil {
		logger.Errorw("msg", "process refund settle info fail", "error", err)
		return nil, err
	}

	logger.Infow("msg", "update refund settle success", "refundSettle", settle)

	return settle, nil
}
