package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.uber.org/mock/gomock"
)

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_Success_AlreadyFinalStatus() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusSucceeded,
		PartnerReqID: "partner_req_123",
	}

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusSucceeded), result.DischargeStatus)
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_GetEarlyDischargeLogError() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	expectedError := errors.New("database error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(nil, expectedError)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "get early discharge log fail")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_InquiryTransactionError_NotTransactionNotFound() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
	}

	inquiryError := errors.New("network error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(nil, inquiryError)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "inquiry transaction fail")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_InquiryTransactionError_TransactionNotFound() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(nil, model.ErrPartnerTransactionNotFound)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
			assert.Equal(suite.T(), model.PaymentStatusFailed, log.Status)
			assert.Equal(suite.T(), "transaction_not_found", log.PartnerData.RepaymentResult.CIMBError.ErrorCode)
			return &log, nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusFailed), result.DischargeStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusTransferFailed), result.PartnerDischargeStatus)
	assert.Contains(suite.T(), result.ErrorDescription, "partner transaction not found")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_StillProcessing() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusProcessing,
	}

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "still processing, will retry")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_Success_CompletedTransaction() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus:             model.CIMBPaymentStatusComplete,
		BankTransactionSequenceID: "bank_trans_123",
	}

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
			assert.Equal(suite.T(), model.PaymentStatusSucceeded, log.Status)
			assert.Equal(suite.T(), "bank_trans_123", log.PartnerData.RepaymentResult.BankTransID)
			assert.Equal(suite.T(), model.CIMBPaymentStatusComplete.String(), log.PartnerData.RepaymentResult.RepaymentStatus)
			return &log, nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusSucceeded), result.DischargeStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusComplete), result.PartnerDischargeStatus)
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_Success_FailedTransaction() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusTransferFailed,
	}

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
			assert.Equal(suite.T(), model.PaymentStatusFailed, log.Status)
			assert.Equal(suite.T(), model.CIMBPaymentStatusTransferFailed.String(), log.PartnerData.RepaymentResult.RepaymentStatus)
			return &log, nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusFailed), result.DischargeStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusTransferFailed), result.PartnerDischargeStatus)
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_BeginTransactionError() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	txnError := errors.New("transaction begin error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(nil, txnError)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "begin transaction fail")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_UpdateEarlyDischargeLogError() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	updateError := errors.New("update error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		Return(nil, updateError)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "update early discharge log fail")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_PublishRefundSettleResultError() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	publishError := errors.New("publish error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		Return(&model.EarlyDischargeLog{}, nil)

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(publishError)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "publish refund settle result fail")
}

func (suite *RefundTestSuite) TestPollingEarlyDischargeStatus_CommitTransactionError() {
	ctx := context.Background()
	req := &model.EarlyDischargeStatusWorkflowRequest{
		PaymentID: 12345,
		ZalopayID: 67890,
	}

	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	commitError := errors.New("commit error")

	suite.mockPaymentRepo.EXPECT().
		GetEarlyDischargeLog(gomock.Any(), req.PaymentID, req.ZalopayID).
		Return(dischargeLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), dischargeLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		Return(&model.EarlyDischargeLog{}, nil)

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil)

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil)

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(commitError)

	result, err := suite.usecase.PollingEarlyDischargeStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "commit transaction fail")
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_Success_AlreadyFinalStatus() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusSucceeded,
		PartnerReqID: "partner_req_123",
	}

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusSucceeded), result.RepayStatus)
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_GetRepaymentByIDError() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	expectedError := errors.New("database error")

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(nil, expectedError)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), expectedError, err)
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_InquiryTransactionError_NotTransactionNotFound() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
	}

	inquiryError := errors.New("network error")

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(nil, inquiryError)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "inquiry transaction fail")
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_StillProcessing() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusProcessing,
	}

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(cimbTrans, nil)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "still processing, will retry")
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_Success_CompletedTransaction() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus:             model.CIMBPaymentStatusComplete,
		BankTransactionSequenceID: "bank_trans_123",
	}

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.RepaymentLog) error {
			assert.Equal(suite.T(), model.PaymentStatusSucceeded, log.Status)
			assert.Equal(suite.T(), "bank_trans_123", log.PartnerData.RepaymentResult.BankTransID)
			return nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusSucceeded), result.RepayStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusComplete), result.PartnerStatus)
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_InquiryTransactionError_TransactionNotFound() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(nil, model.ErrPartnerTransactionNotFound)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.RepaymentLog) error {
			assert.Equal(suite.T(), model.PaymentStatusFailed, log.Status)
			assert.Equal(suite.T(), "transaction_not_found", log.PartnerData.RepaymentResult.CIMBError.ErrorCode)
			return nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusFailed), result.RepayStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusTransferFailed), result.PartnerStatus)
	assert.Contains(suite.T(), result.ErrorDescription, "partner transaction not found")
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_BeginTransactionError() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	txnError := errors.New("transaction begin error")

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(nil, txnError)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "begin transaction fail")
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_GetRepaymentByIDInTransactionError() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	getError := errors.New("get repayment error")

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(nil, getError)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "get repayment log fail")
}

func (suite *RefundTestSuite) TestPollRepaymentStatus_UpdateRepaymentLogStatusError() {
	ctx := context.Background()
	req := &model.RepaymentStatusPollingRequest{
		PaymentID: 12345,
	}

	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	cimbTrans := &model.CIMBTransaction{
		PaymentStatus: model.CIMBPaymentStatusComplete,
	}

	updateError := errors.New("update error")

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPartnerConnector.EXPECT().
		InquiryTransaction(gomock.Any(), repayLog.PartnerReqID).
		Return(cimbTrans, nil)

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		GetRepaymentByID(gomock.Any(), req.PaymentID).
		Return(repayLog, nil)

	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		Return(updateError)

	result, err := suite.usecase.PollRepaymentStatus(ctx, req)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "update repayment log status fail")
}

func (suite *RefundTestSuite) TestHandleDischargeTransactionError_NotTransactionNotFound() {
	ctx := context.Background()
	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	networkError := errors.New("network connection failed")

	result, err := suite.usecase.handleDischargeTransactionError(ctx, networkError, dischargeLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollingEarlyDischargeStatus, inquiry transaction fail")
}

func (suite *RefundTestSuite) TestHandleDischargeTransactionError_TransactionNotFound_BeginTxError() {
	ctx := context.Background()
	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	beginTxError := errors.New("begin transaction failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(nil, beginTxError)

	result, err := suite.usecase.handleDischargeTransactionError(ctx, model.ErrPartnerTransactionNotFound, dischargeLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollingEarlyDischargeStatus, begin transaction fail")
}

func (suite *RefundTestSuite) TestHandleDischargeTransactionError_TransactionNotFound_UpdateError() {
	ctx := context.Background()
	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	updateError := errors.New("update failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		Return(nil, updateError)

	result, err := suite.usecase.handleDischargeTransactionError(ctx, model.ErrPartnerTransactionNotFound, dischargeLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollingEarlyDischargeStatus, update early discharge log to failed status failed")
}

func (suite *RefundTestSuite) TestHandleDischargeTransactionError_TransactionNotFound_PublishError() {
	ctx := context.Background()
	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	publishError := errors.New("publish failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		Return(&model.EarlyDischargeLog{}, nil)

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(publishError).
		AnyTimes()

	result, err := suite.usecase.handleDischargeTransactionError(ctx, model.ErrPartnerTransactionNotFound, dischargeLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollingEarlyDischargeStatus, publish refund settle result failed")
}

func (suite *RefundTestSuite) TestHandleDischargeTransactionError_TransactionNotFound_CommitError() {
	ctx := context.Background()
	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	commitError := errors.New("commit failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		Return(&model.EarlyDischargeLog{}, nil)

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(commitError)

	result, err := suite.usecase.handleDischargeTransactionError(ctx, model.ErrPartnerTransactionNotFound, dischargeLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollingEarlyDischargeStatus, commit transaction fail")
}

func (suite *RefundTestSuite) TestHandleDischargeTransactionError_TransactionNotFound_Success() {
	ctx := context.Background()
	dischargeLog := &model.EarlyDischargeLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateEarlyDischargeLog(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
			assert.Equal(suite.T(), model.PaymentStatusFailed, log.Status)
			assert.Equal(suite.T(), "transaction_not_found", log.PartnerData.RepaymentResult.CIMBError.ErrorCode)
			return &log, nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundSettleResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	suite.mockTransaction.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	result, err := suite.usecase.handleDischargeTransactionError(ctx, model.ErrPartnerTransactionNotFound, dischargeLog)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusFailed), result.DischargeStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusTransferFailed), result.PartnerDischargeStatus)
	assert.Contains(suite.T(), result.ErrorDescription, "partner transaction not found")
}

func (suite *RefundTestSuite) TestHandleRepaymentTransactionError_NotTransactionNotFound() {
	ctx := context.Background()
	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	networkError := errors.New("network connection failed")

	result, err := suite.usecase.handleRepaymentTransactionError(ctx, networkError, repayLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollRepaymentStatus, inquiry transaction fail")
}

func (suite *RefundTestSuite) TestHandleRepaymentTransactionError_TransactionNotFound_BeginTxError() {
	ctx := context.Background()
	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	beginTxError := errors.New("begin transaction failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(nil, beginTxError)

	result, err := suite.usecase.handleRepaymentTransactionError(ctx, model.ErrPartnerTransactionNotFound, repayLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollRepaymentStatus, begin transaction fail")
}

func (suite *RefundTestSuite) TestHandleRepaymentTransactionError_TransactionNotFound_UpdateError() {
	ctx := context.Background()
	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	updateError := errors.New("update failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		Return(updateError)

	result, err := suite.usecase.handleRepaymentTransactionError(ctx, model.ErrPartnerTransactionNotFound, repayLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollRepaymentStatus, update repayment log to failed status failed")
}

func (suite *RefundTestSuite) TestHandleRepaymentTransactionError_TransactionNotFound_PublishError() {
	ctx := context.Background()
	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	publishError := errors.New("publish failed")

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		Return(nil)

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(publishError).
		AnyTimes()

	result, err := suite.usecase.handleRepaymentTransactionError(ctx, model.ErrPartnerTransactionNotFound, repayLog)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "PollRepaymentStatus, publish refund expired result failed")
}

func (suite *RefundTestSuite) TestHandleRepaymentTransactionError_TransactionNotFound_Success() {
	ctx := context.Background()
	repayLog := &model.RepaymentLog{
		ID:           1,
		Status:       model.PaymentStatusProcessing,
		PartnerReqID: "partner_req_123",
		PartnerData: model.PartnerRepaymentData{
			RepaymentResult: model.RepaymentResult{},
		},
	}

	suite.mockTransaction.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	suite.mockTransaction.EXPECT().
		RollbackTx(gomock.Any())

	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.RepaymentLog) error {
			assert.Equal(suite.T(), model.PaymentStatusFailed, log.Status)
			assert.Equal(suite.T(), "transaction_not_found", log.PartnerData.RepaymentResult.CIMBError.ErrorCode)
			return nil
		})

	suite.mockOrderRepo.EXPECT().
		GetRefundSettleOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{RefundSettleID: 1}, nil).
		AnyTimes()

	suite.mockRefundRepo.EXPECT().
		GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{}, nil).
		AnyTimes()

	suite.mockRefundSettleNotifier.EXPECT().
		PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	result, err := suite.usecase.handleRepaymentTransactionError(ctx, model.ErrPartnerTransactionNotFound, repayLog)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), string(model.PaymentStatusFailed), result.RepayStatus)
	assert.Equal(suite.T(), string(model.CIMBPaymentStatusTransferFailed), result.PartnerStatus)
	assert.Contains(suite.T(), result.ErrorDescription, "partner transaction not found")
}
