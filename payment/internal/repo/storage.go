package repo

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
)

const defaultDialTimeout = 10 * time.Second

func NewSQLDatabase(conf *configs.Payment, kLogger log.Logger) (*sql.DB, func()) {
	logger := log.NewHelper(log.With(kLogger, "module", "repo"))
	db, err := sql.Open("mysql", buildDSN(conf.GetData().GetDatabase()))
	if err != nil {
		logger.Fatalf("failed to establish connection to mysql %v", err)
	}
	db.SetMaxOpenConns(int(conf.GetData().GetDatabase().GetMaxOpenConn()))
	db.SetMaxIdleConns(int(conf.GetData().GetDatabase().GetMaxIdleConn()))
	db.SetConnMaxLifetime(conf.GetData().GetDatabase().GetConnMaxLifeTime().AsDuration())

	cleanup := func() {
		logger.Warnw("msg", "closing the mysql database connection")
		_ = db.Close()
	}
	return db, cleanup
}

func buildDSN(conf *configs.Data_Database) string {
	mysqlConf := mysql.Config{
		User:                 conf.GetUsername(),
		Passwd:               conf.GetPassword(),
		Net:                  "tcp",
		Addr:                 fmt.Sprintf("%s:%d", conf.GetHost(), conf.GetPort()),
		DBName:               conf.GetDbName(),
		Loc:                  time.Now().Location(),
		Timeout:              defaultDialTimeout,
		ParseTime:            true,
		AllowNativePasswords: conf.GetAllowNativePasswords(),
	}

	return mysqlConf.FormatDSN()
}
