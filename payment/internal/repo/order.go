package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-sql-driver/mysql"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo/sqlc"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	sqlhelper "gitlab.zalopay.vn/fin/installment/payment-service/pkg/sql"
)

type orderRepo struct {
	repos  *Repository
	logger *log.Helper
}

func NewOrderRepo(repos *Repository, logger log.Logger) types.OrderRepo {
	return &orderRepo{
		repos:  repos,
		logger: log.NewHelper(log.With(logger, "receiver", "orderRepo")),
	}
}

func (o *orderRepo) UpdateOrderStatus(ctx context.Context, orderID int64, status model.OrderStatus, zpTransID int64) error {
	transID := ""
	if zpTransID != 0 {
		transID = cast.ToString(zpTransID)
	}

	err := o.repos.Queries(ctx).UpdateOrderStatus(ctx, &sqlc.UpdateOrderStatusParams{
		ID:        orderID,
		Status:    fromOrderStatus(status),
		ZpTransID: transID,
	})

	if err != nil {
		o.logger.WithContext(ctx).Errorw("msg", "update order status fail", "orderID", orderID, "status", status, "error", err)
		return err
	}

	o.logger.WithContext(ctx).Infow("msg", "update order status success", "orderID", orderID, "status", status)
	return nil
}

func (o *orderRepo) UpdateOrderProgress(ctx context.Context, params model.OrderProgressUpdate) error {
	extraData, err := json.Marshal(params.ExtraData)
	if err != nil {
		o.logger.WithContext(ctx).Errorw("msg", "marshal refund topup order metadata fail", "error", err)
		return err
	}

	transID := ""
	if params.ZpTransID != 0 {
		transID = cast.ToString(params.ZpTransID)
	}

	err = o.repos.Queries(ctx).UpdateOrderStatusAndExtra(ctx, &sqlc.UpdateOrderStatusAndExtraParams{
		ID:        params.OrderID,
		ZpTransID: transID,
		Extra:     extraData,
		Status:    fromOrderStatus(params.Status),
	})
	if err != nil {
		o.logger.WithContext(ctx).Errorw("msg", "update order status and extra fail", "orderID", params.OrderID, "status", params.Status, "error", err)
		return err
	}
	return nil
}

func (o *orderRepo) GetRepayOrder(ctx context.Context, appTransID string, appID int32, orderType model.OrderType) (*model.RepayOrder, error) {
	order, err := o.repos.Queries(ctx).GetOrder(ctx, &sqlc.GetOrderParams{
		AppTransID: appTransID,
		AppID:      appID,
		Type:       fromOrderType(orderType),
	})

	if err != nil {
		o.logger.WithContext(ctx).Errorw("msg", "get order fail", "appTransID", appTransID, "appID", appID, "orderType", orderType, "error", err)
		return nil, err
	}

	o.logger.WithContext(ctx).Infow("msg", "get order success", "appTransID", appTransID, "appID", appID, "orderType", orderType, "order", order)

	return &model.RepayOrder{
		ID:            order.ID,
		ZalopayID:     order.ZalopayID,
		AppTransID:    order.AppTransID,
		AppID:         order.AppID,
		Amount:        order.Amount,
		ZPTransToken:  order.ZpTransToken,
		Status:        toOrderStatus(order.Status),
		Type:          toOrderType(order.Type),
		Description:   order.Description,
		ZPTransID:     cast.ToInt64(order.ZpTransID),
		StatementDate: order.StatementDate.Time,
		StatementID:   order.StatementID,
		PartnerCode:   order.PartnerCode,
	}, nil
}

func (o *orderRepo) CreateRepayOrder(ctx context.Context, repayOrder model.RepayOrder) (*model.RepayOrder, error) {
	result, err := o.repos.Queries(ctx).CreateRepaymentOrder(ctx, &sqlc.CreateRepaymentOrderParams{
		ZalopayID:     repayOrder.ZalopayID,
		AppTransID:    repayOrder.AppTransID,
		AppID:         repayOrder.AppID,
		Amount:        repayOrder.Amount,
		Status:        fromOrderStatus(repayOrder.Status),
		Type:          fromOrderType(repayOrder.Type),
		ZpTransToken:  repayOrder.ZPTransToken,
		Description:   repayOrder.Description,
		StatementID:   repayOrder.StatementID,
		StatementDate: sqlhelper.NewNullTime(repayOrder.StatementDate),
		Extra:         []byte("{}"),
		PartnerCode:   repayOrder.PartnerCode,
	})

	if err != nil {
		var me *mysql.MySQLError
		ok := errors.As(err, &me)
		if ok && me.Number == ER_DUP_ENTRY {
			o.logger.Warnw("msg", "duplicate transaction", "repayOrder", repayOrder, "error", err)
			return nil, fmt.Errorf(model.ErrDuplicateTransaction)
		}

		o.logger.WithContext(ctx).Errorw("msg", "insert order fail", "repayOrder", repayOrder, "error", err)
		return nil, err
	}

	orderID, err := result.LastInsertId()
	if err != nil {
		o.logger.WithContext(ctx).Errorw("msg", "insert order, LastInsertID fail", "repayOrder", repayOrder, "error", err)
		return nil, err
	}

	o.logger.WithContext(ctx).Infow("msg", "insert order success", "repayOrder", repayOrder, "orderID", orderID)
	repayOrder.ID = orderID
	return &repayOrder, nil
}

func (o *orderRepo) GetRefundTopupOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundTopupOrder, error) {
	logger := o.logger.WithContext(ctx)

	order, err := o.repos.Queries(ctx).GetOrderByAppTransTypeAndStage(ctx, &sqlc.GetOrderByAppTransTypeAndStageParams{
		Type:       sqlc.OrderTypeRefund,
		AppID:      appID,
		AppTransID: appTransID,
		Stage:      sqlhelper.NewNullString(model.OrderStageTopup.String()),
	})
	if errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrap(model.ErrOrderNotFound, "refund topup order not found")
	}
	if err != nil {
		logger.Errorw("msg", "get topup order fail", "appTransID", appTransID, "appID", appID, "error", err)
		return nil, err
	}

	logger.Infow("msg", "get topup order success", "appTransID", appTransID, "appID", appID, "order", order)

	return toRefundTopupOrder(ctx, order), nil
}

func (o *orderRepo) CreateRefundTopupOrder(ctx context.Context, topupOrder *model.RefundTopupOrder) (*model.RefundTopupOrder, error) {
	logger := o.logger.WithContext(ctx)

	extra, err := json.Marshal(topupOrder.ExtraData)
	if err != nil {
		logger.Errorw("msg", "marshal refund topup order metadata fail", "error", err)
		return nil, err
	}

	params := &sqlc.CreateRefundTopupOrderParams{
		AppID:        topupOrder.AppID,
		ZalopayID:    topupOrder.ZalopayID,
		AppTransID:   topupOrder.AppTransID,
		PartnerCode:  topupOrder.PartnerCode,
		Amount:       topupOrder.Amount,
		ZpTransToken: topupOrder.ZPTransToken,
		Type:         fromOrderType(topupOrder.Type),
		Status:       fromOrderStatus(topupOrder.Status),
		Description:  topupOrder.Description,
		Stage:        sql.NullString{String: model.OrderStageTopup.String(), Valid: true},
		SettleID:     topupOrder.RefundSettleID,
		Extra:        extra,
		SystemID:     0,
	}
	result, err := o.repos.Queries(ctx).CreateRefundTopupOrder(ctx, params)
	if err != nil {
		logger.Errorw("msg", "insert refund topup order fail", "error", err)
		return nil, err
	}
	if result == nil {
		logger.Errorw("msg", "insert refund topup order fail", "error", "result is nil")
		return nil, errors.New("result is nil")
	}

	topupOrder.ID, err = result.LastInsertId()
	if err != nil {
		logger.Errorw("msg", "insert refund topup order, LastInsertID fail", "error", err)
		return nil, err
	}

	logger.Infow("msg", "insert refund topup order success", "order", topupOrder)

	return topupOrder, nil
}

func (o *orderRepo) ListRefundTopupOrder(ctx context.Context, settleID int64) ([]*model.RefundTopupOrder, error) {
	logger := o.logger.WithContext(ctx)

	params := &sqlc.ListOrderByTypeSettleAndStageParams{
		Type:     sqlc.OrderTypeRefund,
		SettleID: settleID,
		Stage:    sql.NullString{String: model.OrderStageTopup.String(), Valid: true},
	}
	topupOrders, err := o.repos.Queries(ctx).ListOrderByTypeSettleAndStage(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		return []*model.RefundTopupOrder{}, nil
	}
	if err != nil {
		logger.Errorw("msg", "failed to get refund topup orders", "error", err)
		return nil, err
	}

	result := make([]*model.RefundTopupOrder, 0, len(topupOrders))
	for _, order := range topupOrders {
		result = append(result, toRefundTopupOrder(ctx, order))
	}

	return result, nil
}

func (o *orderRepo) GetRefundSettleOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundSettleOrder, error) {
	logger := o.logger.WithContext(ctx)

	order, err := o.repos.Queries(ctx).GetOrder(ctx, &sqlc.GetOrderParams{
		Type:       sqlc.OrderTypeRefund,
		AppID:      appID,
		AppTransID: appTransID,
	})
	if errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrap(model.ErrOrderNotFound, "refund settle order not found")
	}
	if err != nil {
		logger.Errorw("msg", "get refund settle order fail", "appTransID", appTransID, "appID", appID, "error", err)
		return nil, err
	}

	logger.Infow("msg", "get refund settle order success", "appTransID", appTransID, "appID", appID, "order", order)

	return toRefundSettleOrder(ctx, order), nil
}

func (o *orderRepo) GetRefundSettleOrderByID(ctx context.Context, orderID int64) (*model.RefundSettleOrder, error) {
	logger := o.logger.WithContext(ctx)

	order, err := o.repos.Queries(ctx).GetOrderByID(ctx, orderID)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrap(model.ErrOrderNotFound, "refund settle order not found")
	}
	if err != nil {
		logger.Errorw("msg", "get refund settle order fail", "orderID", orderID, "error", err)
		return nil, err
	}

	logger.Infow("msg", "get refund settle order success", "orderID", orderID, "order", order)

	return toRefundSettleOrder(ctx, order), nil
}

func (o *orderRepo) GetRefundSettleOrderByRefundID(ctx context.Context, refundID int64) (*model.RefundSettleOrder, error) {
	logger := o.logger.WithContext(ctx)

	params := &sqlc.GetOrderByRefundIDAndTypeAndStageParams{
		RefundID: refundID,
		Type:     sqlc.OrderTypeRefund,
		Stage:    sqlhelper.NewNullString(model.OrderStageSettle.String()),
	}
	order, err := o.repos.Queries(ctx).GetOrderByRefundIDAndTypeAndStage(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrap(model.ErrOrderNotFound, "refund settle order not found")
	}
	if err != nil {
		logger.Errorw("msg", "get refund settle order fail", "refundID", refundID, "error", err)
		return nil, err
	}

	logger.Infow("msg", "get refund settle order success", "refundID", refundID, "order", order)

	return toRefundSettleOrder(ctx, order), nil
}

func (o *orderRepo) ListRefundSettleOrder(ctx context.Context, settleID int64) ([]*model.RefundSettleOrder, error) {
	logger := o.logger.WithContext(ctx)

	params := &sqlc.ListOrderByTypeSettleAndStageParams{
		Type:     sqlc.OrderTypeRefund,
		Stage:    sqlhelper.NewNullString(model.OrderStageSettle.String()),
		SettleID: settleID,
	}
	data, err := o.repos.Queries(ctx).ListOrderByTypeSettleAndStage(ctx, params)
	if err != nil {
		logger.Errorw("msg", "list refund settle order fail", "error", err)
		return nil, err
	}

	var orders []*model.RefundSettleOrder
	for _, order := range data {
		orders = append(orders, toRefundSettleOrder(ctx, order))
	}

	return orders, nil
}

func (o *orderRepo) CreateRefundSettleOrder(ctx context.Context, settleOrder *model.RefundSettleOrder) (*model.RefundSettleOrder, error) {
	logger := o.logger.WithContext(ctx)

	extra, err := json.Marshal(settleOrder.ExtraData)
	if err != nil {
		logger.Errorw("msg", "marshal refund settle order metadata fail", "error", err)
		return nil, err
	}

	params := &sqlc.CreateRefundSettleOrderParams{
		SystemID:    0,
		AppID:       settleOrder.AppID,
		RefundID:    settleOrder.RefundID,
		SettleID:    settleOrder.RefundSettleID,
		ZalopayID:   settleOrder.ZalopayID,
		AppTransID:  settleOrder.AppTransID,
		Description: settleOrder.Description,
		PartnerCode: settleOrder.PartnerCode,
		Amount:      settleOrder.Amount,
		Type:        fromOrderType(settleOrder.Type),
		Status:      fromOrderStatus(settleOrder.Status),
		Stage:       sql.NullString{String: settleOrder.Stage.String(), Valid: true},
		Extra:       extra,
	}
	result, err := o.repos.Queries(ctx).CreateRefundSettleOrder(ctx, params)
	if err != nil {
		logger.Errorw("msg", "insert refund settle order fail", "error", err)
		return nil, err
	}

	settleOrder.ID, err = result.LastInsertId()
	if err != nil {
		logger.Errorw("msg", "insert refund settle order, LastInsertID fail", "error", err)
		return nil, err
	}

	logger.Infow("msg", "insert refund settle order success", "order", settleOrder)

	return settleOrder, nil
}

func (o *orderRepo) CreateRefundFundbackOrder(ctx context.Context, fundbackOrder *model.RefundFundbackOrder) (*model.RefundFundbackOrder, error) {
	logger := o.logger.WithContext(ctx)

	extra, err := json.Marshal(fundbackOrder.ExtraData)
	if err != nil {
		logger.Errorw("msg", "marshal refund fundback order metadata fail", "error", err)
		return nil, err
	}

	params := &sqlc.CreateRefundFundbackOrderParams{
		AppID:        fundbackOrder.AppID,
		ZalopayID:    fundbackOrder.ZalopayID,
		AppTransID:   fundbackOrder.AppTransID,
		PartnerCode:  fundbackOrder.PartnerCode,
		Amount:       fundbackOrder.Amount,
		ZpTransToken: fundbackOrder.ZPTransToken,
		Type:         fromOrderType(fundbackOrder.Type),
		Status:       fromOrderStatus(fundbackOrder.Status),
		Description:  fundbackOrder.Description,
		Stage:        sql.NullString{String: model.OrderStageFundback.String(), Valid: true},
		SettleID:     fundbackOrder.RefundSettleID,
		Extra:        extra,
		SystemID:     0,
	}
	result, err := o.repos.Queries(ctx).CreateRefundFundbackOrder(ctx, params)
	if err != nil {
		logger.Errorw("msg", "insert refund fundback order fail", "error", err)
		return nil, err
	}
	if result == nil {
		logger.Errorw("msg", "insert refund fundback order fail", "error", "result is nil")
		return nil, errors.New("result is nil")
	}

	fundbackOrder.ID, err = result.LastInsertId()
	if err != nil {
		logger.Errorw("msg", "insert refund fundback order, LastInsertID fail", "error", err)
		return nil, err
	}

	logger.Infow("msg", "insert refund fundback order success", "order", fundbackOrder)

	return fundbackOrder, nil
}

func (o *orderRepo) GetRefundFundbackOrder(ctx context.Context, appTransID string, appID int32) (*model.RefundFundbackOrder, error) {
	logger := o.logger.WithContext(ctx)

	params := &sqlc.GetOrderByAppTransTypeAndStageParams{
		Type:       sqlc.OrderTypeRefund,
		AppID:      appID,
		AppTransID: appTransID,
		Stage:      sqlhelper.NewNullString(model.OrderStageFundback.String()),
	}
	order, err := o.repos.Queries(ctx).GetOrderByAppTransTypeAndStage(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorw("msg", "get refund fundback order not found", "appTransID", appTransID, "appID", appID)
		return nil, err
	}

	if err != nil {
		logger.Errorw("msg", "get refund fundback order fail", "appTransID", appTransID, "appID", appID, "error", err)
		return nil, err
	}
	return toRefundFundbackOrder(ctx, order), nil
}

func (o *orderRepo) ListRefundFundbackOrder(ctx context.Context, settleID int64) ([]*model.RefundFundbackOrder, error) {
	logger := o.logger.WithContext(ctx)

	params := &sqlc.ListOrderByTypeSettleAndStageParams{
		Type:     sqlc.OrderTypeRefund,
		Stage:    sqlhelper.NewNullString(model.OrderStageFundback.String()),
		SettleID: settleID,
	}
	data, err := o.repos.Queries(ctx).ListOrderByTypeSettleAndStage(ctx, params)
	if err != nil {
		logger.Errorw("msg", "list refund fundback order fail", "error", err)
		return nil, err
	}

	var orders []*model.RefundFundbackOrder
	for _, order := range data {
		orders = append(orders, toRefundFundbackOrder(ctx, order))
	}

	return orders, nil
}

func fromOrderType(orderType model.OrderType) sqlc.OrderType {
	switch orderType {
	case model.OrderTypeRepayment:
		return sqlc.OrderTypeRepayment
	case model.OrderTypeRefund:
		return sqlc.OrderTypeRefund
	default:
		return sqlc.OrderTypeRepayment
	}
}

func fromOrderStatus(status model.OrderStatus) sqlc.OrderStatus {
	switch status {
	case model.OrderStatusInit:
		return sqlc.OrderStatusInit
	case model.OrderStatusPending:
		return sqlc.OrderStatusPending
	case model.OrderStatusSucceeded:
		return sqlc.OrderStatusSucceeded
	case model.OrderStatusFailed:
		return sqlc.OrderStatusFailed
	case model.OrderStatusCancelled:
		return sqlc.OrderStatusCancelled
	default:
		return sqlc.OrderStatusInit
	}
}

func toOrderType(orderType sqlc.OrderType) model.OrderType {
	switch orderType {
	case sqlc.OrderTypeRepayment:
		return model.OrderTypeRepayment
	case sqlc.OrderTypeRefund:
		return model.OrderTypeRefund
	default:
		return model.OrderTypeRepayment
	}
}

func toOrderStatus(status sqlc.OrderStatus) model.OrderStatus {
	switch status {
	case sqlc.OrderStatusInit:
		return model.OrderStatusInit
	case sqlc.OrderStatusPending:
		return model.OrderStatusPending
	case sqlc.OrderStatusProcessing:
		return model.OrderStatusProcessing
	case sqlc.OrderStatusSucceeded:
		return model.OrderStatusSucceeded
	case sqlc.OrderStatusFailed:
		return model.OrderStatusFailed
	case sqlc.OrderStatusCancelled:
		return model.OrderStatusCancelled
	default:
		return model.OrderStatusInit
	}
}

func toRefundTopupOrder(ctx context.Context, order *sqlc.Order) *model.RefundTopupOrder {
	var metadata model.RefundOrderExtra
	if err := json.Unmarshal(order.Extra, &metadata); err != nil {
		log.Context(ctx).Errorw("msg", "failed to unmarshal order extra", "error", err)
		metadata = model.RefundOrderExtra{}
	}

	return &model.RefundTopupOrder{
		ID:             order.ID,
		ZalopayID:      order.ZalopayID,
		AppID:          order.AppID,
		AppTransID:     order.AppTransID,
		PartnerCode:    order.PartnerCode,
		Amount:         order.Amount,
		ZPTransToken:   order.ZpTransToken,
		Type:           toOrderType(order.Type),
		Status:         toOrderStatus(order.Status),
		Description:    order.Description,
		Stage:          model.OrderStage(order.Stage.String),
		ZPTransID:      cast.ToInt64(order.ZpTransID),
		RefundSettleID: order.SettleID,
		ExtraData:      metadata,
	}
}

func toRefundSettleOrder(ctx context.Context, order *sqlc.Order) *model.RefundSettleOrder {
	var metadata model.RefundOrderExtra
	if err := json.Unmarshal(order.Extra, &metadata); err != nil {
		log.Context(ctx).Errorw("msg", "failed to unmarshal order extra", "error", err)
		metadata = model.RefundOrderExtra{}
	}

	return &model.RefundSettleOrder{
		ID:             order.ID,
		ZalopayID:      order.ZalopayID,
		AppID:          order.AppID,
		AppTransID:     order.AppTransID,
		PartnerCode:    order.PartnerCode,
		Amount:         order.Amount,
		ZPTransToken:   order.ZpTransToken,
		Type:           toOrderType(order.Type),
		Status:         toOrderStatus(order.Status),
		Description:    order.Description,
		Stage:          model.OrderStage(order.Stage.String),
		ZPTransID:      cast.ToInt64(order.ZpTransID),
		RefundID:       order.RefundID,
		RefundSettleID: order.SettleID,
		ExtraData:      metadata,
	}
}

func toRefundFundbackOrder(ctx context.Context, order *sqlc.Order) *model.RefundFundbackOrder {
	var metadata model.RefundOrderExtra
	if err := json.Unmarshal(order.Extra, &metadata); err != nil {
		log.Context(ctx).Errorw("msg", "failed to unmarshal order extra", "error", err)
		metadata = model.RefundOrderExtra{}
	}

	return &model.RefundFundbackOrder{
		ID:             order.ID,
		ZalopayID:      order.ZalopayID,
		AppID:          order.AppID,
		AppTransID:     order.AppTransID,
		PartnerCode:    order.PartnerCode,
		Amount:         order.Amount,
		ZPTransToken:   order.ZpTransToken,
		Type:           toOrderType(order.Type),
		Status:         toOrderStatus(order.Status),
		Description:    order.Description,
		Stage:          model.OrderStage(order.Stage.String),
		ZPTransID:      cast.ToInt64(order.ZpTransID),
		RefundSettleID: order.SettleID,
		ExtraData:      metadata,
	}
}
