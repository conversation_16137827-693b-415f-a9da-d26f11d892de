package repo

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo/sqlc"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
)

var ProviderSet = wire.NewSet(
	NewSQLDatabase,
	NewRepository,
	NewTransaction,
	NewPaymentRepo,
	NewOrderRepo,
	NewRefundRepo,
)

type Repository struct {
	sqlDB  *sql.DB
	logger *log.Helper
}

type ctxTxKey struct{}

func NewRepository(sqlDB *sql.DB, kLogger log.Logger) *Repository {
	logger := log.NewHelper(log.With(kLogger, "module", "repository"))
	return &Repository{
		sqlDB:  sqlDB,
		logger: logger,
	}
}

func NewTransaction(d *Repository) types.Transaction {
	return d
}

func (r *Repository) BeginTx(ctx context.Context) (context.Context, error) {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		r.logger.Errorf("BeginTx failed: %v", err)
		return nil, err
	}
	return context.WithValue(ctx, ctxTxKey{}, tx), nil
}

func (r *Repository) BeginOrReuseTx(ctx context.Context) (context.Context, error) {
	tx := getTxFromCtx(ctx)
	if tx != nil {
		return ctx, nil
	}
	return r.BeginTx(ctx)
}

func (r *Repository) BeginTxRepeatableRead(ctx context.Context) (context.Context, error) {
	tx, err := r.sqlDB.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		r.logger.Errorf("BeginTxRepeatableRead failed: %v", err)
		return nil, err
	}
	return context.WithValue(ctx, ctxTxKey{}, tx), nil
}

func (r *Repository) BeginTxReadCommited(ctx context.Context) (context.Context, error) {
	tx, err := r.sqlDB.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		r.logger.Errorf("BeginTxReadCommited failed: %v", err)
		return nil, err
	}
	return context.WithValue(ctx, ctxTxKey{}, tx), nil
}

func (r *Repository) CommitTx(ctx context.Context) error {
	tx := getTxFromCtx(ctx)
	if tx == nil {
		return nil
	}
	return tx.Commit()
}

func (r *Repository) RollbackTx(ctx context.Context) error {
	tx := getTxFromCtx(ctx)
	if tx == nil {
		return nil
	}
	return tx.Rollback()
}

func (r *Repository) IsInTx(ctx context.Context) bool {
	tx := getTxFromCtx(ctx)
	return tx != nil
}

func (r *Repository) IsTxActive(ctx context.Context) bool {
	return getTxFromCtx(ctx) != nil
}

func (r *Repository) WithTx(ctx context.Context, exec func(ctx context.Context) error) error {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	err = exec(context.WithValue(ctx, ctxTxKey{}, tx))
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func getTxFromCtx(ctx context.Context) *sql.Tx {
	tx, ok := ctx.Value(ctxTxKey{}).(*sql.Tx)
	if !ok {
		return nil
	}
	return tx
}

func (r *Repository) Queries(ctx context.Context) *sqlc.Queries {
	tx, ok := ctx.Value(ctxTxKey{}).(*sql.Tx)
	if ok {
		return sqlc.New(tx)
	}
	return sqlc.New(r.sqlDB)
}

func buildOrderStmt(isDesc bool, column string) string {
	orderStmt := fmt.Sprintf("ORDER BY %s", column)
	if isDesc {
		return orderStmt + " DESC"
	}
	return orderStmt + " ASC"
}

func buildPagingAndOrderStmt(isDesc bool, pagination *model.Pagination, column string) (string, []any) {
	params := make([]any, 0)
	orderStmt := buildOrderStmt(isDesc, column)
	pagingStmt := ""

	// Priority to use offset for querying
	if pagination.Cursor == nil {
		offsetNum := cast.ToInt(pagination.Offset)
		pagingStmt = fmt.Sprintf("LIMIT ? OFFSET ?")
		params = append(params, pagination.Limit, offsetNum)
		return fmt.Sprintf("1 = 1 %s %s", orderStmt, pagingStmt), params
	}

	// Use cursor for querying
	switch {
	case isDesc && pagination.Direct == model.CursorDirectionNext,
		!isDesc && pagination.Direct == model.CursorDirectionPrev:
		orderStmt = buildOrderStmt(isDesc, column)
		cursorStmt := fmt.Sprintf("%s < ?", column)
		pagingStmt = fmt.Sprintf("LIMIT ?")
		params = append(params, pagination.Cursor, pagination.Limit)
		return fmt.Sprintf("%s %s %s", cursorStmt, orderStmt, pagingStmt), params
	case isDesc && pagination.Direct == model.CursorDirectionPrev,
		!isDesc && pagination.Direct == model.CursorDirectionNext:
		orderStmt = buildOrderStmt(!isDesc, column)
		cursorStmt := fmt.Sprintf("%s > ?", column)
		pagingStmt = fmt.Sprintf("LIMIT ?")
		params = append(params, pagination.Cursor, pagination.Limit)
		return fmt.Sprintf("%s %s %s", cursorStmt, orderStmt, pagingStmt), params
	default:
		return "", nil
	}
}
