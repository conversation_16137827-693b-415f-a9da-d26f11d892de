package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-sql-driver/mysql"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo/sqlc"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
	sqlhelper "gitlab.zalopay.vn/fin/installment/payment-service/pkg/sql"
)

const (
	listPaymentAdvQuery = `SELECT 
       id, zp_trans_id, pe_request_id, system_id, 
       trans_type, partner_trans_id, app_trans_id, account_id, zalopay_id, 
       user_bank_account_number, bank_code, bank_routing_number, app_id, amount, status, 
       bank_status, error_code, error_message, current_available_balance, payment_description, 
       extra, installment_tenor, installment_interest_rate, created_at, updated_at 
	   FROM payment_logs WHERE 1 = 1`
)

type paymentRepo struct {
	repos  *Repository
	logger *log.Helper
}

// NewPaymentRepo .
func NewPaymentRepo(repos *Repository, logger log.Logger) types.PaymentRepo {
	return &paymentRepo{
		repos:  repos,
		logger: log.NewHelper(logger),
	}
}

func (p *paymentRepo) GetPayment(ctx context.Context, purchaseOrderID int64, zalopayID int64) (*model.PurchaseOrder, error) {
	paymentLog, err := p.repos.Queries(ctx).GetPaymentLogByID(ctx, purchaseOrderID)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get payment log by ID fail", "purchaseOrderID", purchaseOrderID, "error", err)
		return nil, err
	}

	var purchaseOrder *model.PurchaseOrder
	purchaseOrder = convertPaymentLogToPurchaseOrder(paymentLog)

	p.logger.WithContext(ctx).Infow("msg", "get payment log by ID success", "purchaseOrder", purchaseOrder, "purchaseOrderID", purchaseOrderID)

	return purchaseOrder, nil
}

func (p *paymentRepo) GetPaymentByTransID(ctx context.Context, transID int64) (*model.PurchaseOrder, error) {
	q := p.repos.Queries(ctx)
	paymentLog, err := q.GetPaymentLogByZPTransID(ctx, transID)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get payment log by transID fail", "transID", transID, "error", err)
		return nil, err
	}

	// Convert to model.PurchaseOrder
	var purchaseOrder *model.PurchaseOrder
	purchaseOrder = convertPaymentLogToPurchaseOrder(paymentLog)

	return purchaseOrder, nil
}

func (p *paymentRepo) GetPaymentByPaymentNo(ctx context.Context, paymentNo string) (*model.PurchaseOrder, error) {
	paymentLog, err := p.repos.Queries(ctx).GetPaymentLogByPaymentNo(ctx, paymentNo)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get payment log by payment_no fail", "paymentNo", paymentNo, "error", err)
		return nil, err
	}

	var purchaseOrder *model.PurchaseOrder
	purchaseOrder = convertPaymentLogToPurchaseOrder(paymentLog)

	p.logger.WithContext(ctx).Infow("msg", "get payment log by payment_no success", "purchaseOrder", purchaseOrder, "paymentNo", paymentNo)

	return purchaseOrder, nil
}

func (p *paymentRepo) UpdatePayment(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.PurchaseOrder, error) {
	partnerDataRaw, _ := json.Marshal(purchaseOrder.PartnerData)

	result, err := p.repos.Queries(ctx).UpdatePaymentLog(ctx, &sqlc.UpdatePaymentLogParams{
		PartnerTransID:          purchaseOrder.PartnerData.BankTransactionSequenceID,
		BankTransID:             purchaseOrder.PartnerData.InitPurchasingResult.CIMBTransID,
		AccountID:               purchaseOrder.AccountInfo.ID,
		UserBankAccountNumber:   purchaseOrder.AccountInfo.PartnerAccountId,
		BankCode:                purchaseOrder.AccountInfo.PartnerCode,
		BankRoutingNumber:       purchaseOrder.BankRoute.BankAccountNumber,
		Status:                  purchaseOrder.Status.String(),
		BankStatus:              purchaseOrder.PartnerData.Status.String(),
		ErrorCode:               purchaseOrder.ErrorCode,
		ErrorMessage:            purchaseOrder.ErrorMessage,
		CurrentAvailableBalance: purchaseOrder.AccountInfo.AvailableBalance,
		PaymentDescription:      purchaseOrder.Order.Description,
		Extra:                   partnerDataRaw,
		BankConnectorCode:       "ZPCIMB2",
		EscrowAccountNo:         fmt.Sprintf("%s_%s", purchaseOrder.AccountInfo.PartnerCode, purchaseOrder.BankRoute.BankAccountNumber),
		ID:                      purchaseOrder.ID,
	})
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "update payment log fail", "purchaseOrder", purchaseOrder, "error", err)
		return nil, err
	}

	p.logger.WithContext(ctx).Infow("msg", "update payment log success", "purchaseOrder", purchaseOrder, "result", result)

	return &purchaseOrder, nil
}

func (p *paymentRepo) UpdatePartnerTransID(ctx context.Context, paymentID int64, partnerTransID string) error {
	logger := p.logger.WithContext(ctx)

	logger.Infow("msg", "update payment log partner transID", "paymentID", paymentID, "partnerTransID", partnerTransID)

	err := p.repos.Queries(ctx).UpdatePartnerTransID(ctx, &sqlc.UpdatePartnerTransIDParams{
		PartnerTransID: partnerTransID,
		ID:             paymentID,
	})
	if err != nil {
		logger.Errorw("msg", "update payment log partner transID fail", "paymentID", paymentID, "partnerTransID", partnerTransID, "error", err)
		return err
	}

	logger.Infow("msg", "update payment log partner transID success", "paymentID", paymentID, "partnerTransID", partnerTransID)
	return nil
}

func (p *paymentRepo) CreatePayment(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.PurchaseOrder, error) {
	q := p.repos.Queries(ctx)
	result, err := q.CreatePaymentLog(ctx, &sqlc.CreatePaymentLogParams{
		ZpTransID:               purchaseOrder.Order.TransID,
		SystemID:                0, //TODO
		TransType:               int32(model.TransTypePayment),
		PartnerTransID:          purchaseOrder.PartnerData.BankTransactionSequenceID,
		AppTransID:              purchaseOrder.Order.AppTransID,
		AccountID:               purchaseOrder.AccountInfo.ID,
		ZalopayID:               purchaseOrder.AccountInfo.ZalopayID,
		UserBankAccountNumber:   purchaseOrder.AccountInfo.PartnerAccountId,
		BankCode:                purchaseOrder.AccountInfo.PartnerCode,
		BankRoutingNumber:       purchaseOrder.BankRoute.BankAccountNumber,
		AppID:                   purchaseOrder.Order.AppID,
		Amount:                  purchaseOrder.Order.Amount,
		Status:                  string(purchaseOrder.Status),
		BankStatus:              purchaseOrder.PartnerData.Status.String(),
		CurrentAvailableBalance: purchaseOrder.AccountInfo.AvailableBalance,
		PaymentDescription:      purchaseOrder.Order.Description,
		Extra:                   []byte("{}"),
		InstallmentTenor:        purchaseOrder.InstallmentInstruction.Tenor,
		InstallmentInterestRate: purchaseOrder.InstallmentInstruction.InterestRate,
		PeRequestID:             purchaseOrder.PERequestID,
		FsChargeInfo:            json.RawMessage(purchaseOrder.FSChargeInfo),
		BankConnectorCode:       "",
		EscrowAccountNo:         "",
		PaymentNo:               purchaseOrder.PaymentNo,
		DeviceID:                purchaseOrder.DeviceID,
		UserIp:                  purchaseOrder.UserIP,
	})
	if err != nil {
		var me *mysql.MySQLError
		ok := errors.As(err, &me)
		if ok && me.Number == ER_DUP_ENTRY {
			p.logger.Warnw("msg", "duplicate transaction", "purchaseOrder", purchaseOrder, "error", err)
			return nil, fmt.Errorf(model.ErrDuplicateTransaction)
		}

		p.logger.WithContext(ctx).Errorw("msg", "insert payment_logs fail", "purchaseOrder", purchaseOrder, "error", err)
		return nil, err
	}

	paymentID, err := result.LastInsertId()
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "insert payment_logs, LastInsertID fail", "purchaseOrder", purchaseOrder, "error", err)
		return nil, err
	}

	p.logger.WithContext(ctx).Infow("msg", "insert payment_logs success", "purchaseOrder", purchaseOrder, "paymentID", paymentID)

	purchaseOrder.ID = paymentID
	return &purchaseOrder, nil
}

func (p *paymentRepo) GetBankRoute(ctx context.Context, partnerCode string, transType model.TransType) (*model.BankRoute, error) {
	q := p.repos.Queries(ctx)
	p.logger.WithContext(ctx).Infow("msg", "get bank route", "partnerCode", partnerCode, "transType", transType)
	bankRoute, err := q.GetBankRoute(ctx, &sqlc.GetBankRouteParams{
		InternalBankCode: partnerCode,
		TransType:        int32(transType),
		Status:           sqlc.BankRouteStatusActive,
	})
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get bank route fail", "partnerCode", partnerCode, "transType", transType, "error", err)
		return nil, err
	}

	p.logger.WithContext(ctx).Infow("msg", "get bank route success", "partnerCode", partnerCode, "transType", transType, "bankRoute", *bankRoute)

	return &model.BankRoute{
		ID:                bankRoute.ID,
		InternalBankCode:  bankRoute.InternalBankCode,
		PartnerBankCode:   bankRoute.PartnerBankCode,
		BankAccountName:   bankRoute.BankAccountName,
		BankAccountNumber: bankRoute.BankAccountNumber,
		//TransType:         bankRoute.TransType,
		Status: model.BankRouteStatus(bankRoute.Status),
	}, nil
}

func (p *paymentRepo) ListPaymentAdvance(ctx context.Context,
	params *model.ListPaymentQuery) ([]*model.Transaction, error) {
	query, args, err := buildListPaymentAdvanceStmt(listPaymentAdvQuery, params)
	if err != nil {
		return nil, fmt.Errorf("build query stmt failed: %w", err)
	}

	stmt, err := p.repos.sqlDB.PrepareContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("prepare query stmt failed: %w", err)
	}

	rows, err := stmt.QueryContext(ctx, args...)
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()

	var items []*sqlc.PaymentLogs
	for rows.Next() {
		var row sqlc.PaymentLogs
		err = rows.Scan(
			&row.ID, &row.ZpTransID,
			&row.PeRequestID, &row.SystemID, &row.TransType,
			&row.PartnerTransID, &row.AppTransID,
			&row.AccountID, &row.ZalopayID,
			&row.UserBankAccountNumber, &row.BankCode,
			&row.BankRoutingNumber, &row.AppID,
			&row.Amount, &row.Status, &row.BankStatus,
			&row.ErrorCode, &row.ErrorMessage,
			&row.CurrentAvailableBalance, &row.PaymentDescription, &row.Extra,
			&row.InstallmentTenor, &row.InstallmentInterestRate,
			&row.CreatedAt, &row.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("scan failed: %w", err)
		}
		items = append(items, &row)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}

	// Sort by ID desc before pagination for ensuring the order
	slices.SortFunc(items, func(i, j *sqlc.PaymentLogs) int {
		return int(j.ID - i.ID)
	})

	// Convert to model.PurchaseOrder
	var transactions []*model.Transaction
	for _, item := range items {
		transactions = append(transactions, convertPaymentLogToTransaction(item))
	}

	return transactions, nil
}

func (p *paymentRepo) CreateRepaymentLog(ctx context.Context, repaymentLog model.RepaymentLog) (*model.RepaymentLog, error) {
	partnerDataRaw, _ := json.Marshal(repaymentLog.PartnerData)

	q := p.repos.Queries(ctx)
	result, err := q.CreatePaymentLog(ctx, &sqlc.CreatePaymentLogParams{
		OrderID:                 sqlhelper.NewNullInt64(repaymentLog.Order.ID),
		ZpTransID:               repaymentLog.Order.TransID,
		SystemID:                0, //TODO
		TransType:               int32(model.TransTypeRePayment),
		PartnerReqID:            sqlhelper.NewNullString(repaymentLog.PartnerReqID),
		PartnerTransID:          repaymentLog.PartnerData.RepaymentResult.BankTransID,
		AppTransID:              repaymentLog.Order.AppTransID,
		AccountID:               repaymentLog.AccountInfo.ID,
		ZalopayID:               repaymentLog.AccountInfo.ZalopayID,
		UserBankAccountNumber:   repaymentLog.AccountInfo.PartnerAccountId,
		BankCode:                repaymentLog.AccountInfo.PartnerCode,
		BankRoutingNumber:       repaymentLog.BankRoute.BankAccountNumber,
		AppID:                   repaymentLog.Order.AppID,
		Amount:                  repaymentLog.Order.Amount,
		Status:                  string(repaymentLog.Status),
		BankStatus:              repaymentLog.PartnerData.RepaymentResult.RepaymentStatus,
		ErrorCode:               repaymentLog.PartnerData.RepaymentResult.CIMBError.ErrorCode,
		ErrorMessage:            repaymentLog.PartnerData.RepaymentResult.CIMBError.Description,
		CurrentAvailableBalance: repaymentLog.AccountInfo.AvailableBalance,
		PaymentDescription:      repaymentLog.Order.Description,
		Extra:                   partnerDataRaw,
		InstallmentTenor:        0,
		InstallmentInterestRate: 0.0,
		FsChargeInfo:            []byte("{}"),
		BankConnectorCode:       "ZPCIMB2",
		EscrowAccountNo:         fmt.Sprintf("%s_%s", repaymentLog.AccountInfo.PartnerCode, repaymentLog.BankRoute.BankAccountNumber),
	})
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "insert repayment_logs, LastInsertID fail", "repaymentLog", repaymentLog, "error", err)
		return nil, err
	}

	paymentID, err := result.LastInsertId()
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "insert repayment_logs, LastInsertID fail", "repaymentLog", repaymentLog, "error", err)
		return nil, err
	}

	p.logger.WithContext(ctx).Infow("msg", "insert repayment_logs success", "repaymentLog", repaymentLog, "paymentID", paymentID)

	repaymentLog.ID = paymentID
	return &repaymentLog, nil
}

func (p *paymentRepo) GetRepaymentByID(ctx context.Context, paymentID int64) (*model.RepaymentLog, error) {
	logger := p.logger.WithContext(ctx)

	payment, err := p.repos.Queries(ctx).GetPaymentLogByID(ctx, paymentID)
	if err != nil {

		logger.Errorw("msg", "get repayment log by ID fail", "paymentID", paymentID, "error", err)
		return nil, err
	}

	return convertPaymentLogToRepaymentLog(payment), nil
}

func (p *paymentRepo) GetRepaymentByOrderID(ctx context.Context, orderID int64) (*model.RepaymentLog, error) {
	logger := p.logger.WithContext(ctx)

	payment, err := p.repos.Queries(ctx).GetPaymentLogByOrderID(ctx, sqlhelper.NewNullInt64(orderID))
	if err != nil {
		logger.Errorw("msg", "get repayment log by orderID fail", "orderID", orderID, "error", err)
		return nil, err
	}

	return convertPaymentLogToRepaymentLog(payment), nil
}

// UpdateRepaymentLogStatus implements types.PaymentRepo.
func (p *paymentRepo) UpdateRepaymentLogStatus(ctx context.Context, repaymentLog model.RepaymentLog) error {
	logger := p.logger.WithContext(ctx)

	extra, err := json.Marshal(repaymentLog.PartnerData)
	if err != nil {
		logger.Errorw("msg", "marshal partner data fail", "repaymentLog", repaymentLog, "error", err)
		return err
	}

	params := &sqlc.UpdatePaymentLogStatusParams{
		ID:           repaymentLog.ID,
		Extra:        extra,
		Status:       string(repaymentLog.Status),
		BankStatus:   repaymentLog.PartnerData.RepaymentResult.RepaymentStatus,
		ErrorCode:    repaymentLog.PartnerData.RepaymentResult.CIMBError.ErrorCode,
		ErrorMessage: repaymentLog.PartnerData.RepaymentResult.CIMBError.Description,
	}

	if err = p.repos.Queries(ctx).UpdatePaymentLogStatus(ctx, params); err != nil {
		logger.Errorw("msg", "update repayment log fail", "repaymentLog", repaymentLog, "error", err)
		return err
	}

	logger.Infow("msg", "update repayment log success", "repaymentLog", repaymentLog)

	return nil
}

func (p *paymentRepo) CreateEarlyDischargeLog(ctx context.Context, dischargeLog model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
	partnerDataRaw, err := json.Marshal(dischargeLog.PartnerData)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "marshal partner data fail", "dischargeLog", dischargeLog, "error", err)
		return nil, err
	}

	q := p.repos.Queries(ctx)
	result, err := q.CreatePaymentLog(ctx, &sqlc.CreatePaymentLogParams{
		ZpTransID:               cast.ToInt64(dischargeLog.ZpTransID),
		SystemID:                0,
		TransType:               int32(dischargeLog.TransType),
		OrderID:                 sqlhelper.NewNullInt64(dischargeLog.Order.ID),
		PartnerReqID:            sqlhelper.NewNullString(dischargeLog.PartnerReqID),
		PartnerTransID:          dischargeLog.PartnerData.RepaymentResult.BankTransID,
		AppTransID:              dischargeLog.Order.AppTransID,
		AccountID:               dischargeLog.AccountInfo.ID,
		ZalopayID:               dischargeLog.AccountInfo.ZalopayID,
		UserBankAccountNumber:   dischargeLog.AccountInfo.PartnerAccountId,
		BankCode:                dischargeLog.AccountInfo.PartnerCode,
		BankRoutingNumber:       dischargeLog.BankRoute.BankAccountNumber,
		AppID:                   dischargeLog.Order.AppID,
		Amount:                  dischargeLog.Order.Amount,
		Status:                  string(dischargeLog.Status),
		BankStatus:              dischargeLog.PartnerData.RepaymentResult.RepaymentStatus,
		ErrorCode:               dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode,
		ErrorMessage:            dischargeLog.PartnerData.RepaymentResult.CIMBError.Description,
		CurrentAvailableBalance: dischargeLog.AccountInfo.AvailableBalance,
		PaymentDescription:      dischargeLog.Order.Description,
		Extra:                   partnerDataRaw,
		InstallmentTenor:        0,
		InstallmentInterestRate: 0.0,
		FsChargeInfo:            []byte("{}"),
		BankConnectorCode:       "ZPCIMB2",
		EscrowAccountNo:         fmt.Sprintf("%s_%s", dischargeLog.AccountInfo.PartnerCode, dischargeLog.BankRoute.BankAccountNumber),
	})
	if err != nil {
		var me *mysql.MySQLError
		ok := errors.As(err, &me)
		if ok && me.Number == ER_DUP_ENTRY {
			p.logger.WithContext(ctx).Warnw("msg", "duplicate transaction", "dischargeLog", dischargeLog, "error", err)
			return &dischargeLog, nil
		}
		p.logger.WithContext(ctx).Errorw("msg", "insert payment_logs fail", "dischargeLog", dischargeLog, "error", err)
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "insert payment_logs, LastInsertID fail", "dischargeLog", dischargeLog, "error", err)
		return nil, err
	}

	dischargeLog.ID = id
	p.logger.WithContext(ctx).Infow("msg", "insert payment_logs success", "dischargeLog", dischargeLog, "paymentID", id)
	return &dischargeLog, nil
}

func (p *paymentRepo) GetEarlyDischargeLog(ctx context.Context, dischargeID int64, zalopayID int64) (*model.EarlyDischargeLog, error) {
	q := p.repos.Queries(ctx)
	paymentLog, err := q.GetPaymentLogByID(ctx, dischargeID)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get early discharge log fail", "dischargeID", dischargeID, "zalopayID", zalopayID, "error", err)
		return nil, err
	}

	if model.TransType(paymentLog.TransType) != model.TransTypeEarlyDischarge {
		p.logger.WithContext(ctx).Warnw("msg", "wrong transaction type", "dischargeID", dischargeID, "transType", paymentLog.TransType)
		return nil, fmt.Errorf("wrong transaction type: id=%d, expected=%d, actual=%d",
			dischargeID, model.TransTypeEarlyDischarge, paymentLog.TransType)
	}

	if paymentLog.ZalopayID != zalopayID {
		p.logger.WithContext(ctx).Warnw("msg", "wrong zalopay ID", "dischargeID", dischargeID, "zalopayID", zalopayID, "actualZalopayID", paymentLog.ZalopayID)
		return nil, fmt.Errorf("wrong zalopay ID: id=%d, expected=%d, actual=%d",
			dischargeID, zalopayID, paymentLog.ZalopayID)
	}

	return p.convertPaymentLogToEarlyDischargeLog(ctx, paymentLog)
}

func (p *paymentRepo) GetEarlyDischargeLogByID(ctx context.Context, paymentID int64) (*model.EarlyDischargeLog, error) {
	paymentLog, err := p.repos.Queries(ctx).GetPaymentLogByID(ctx, paymentID)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get early discharge log fail", "paymentID", paymentID, "error", err)
		return nil, err
	}
	return p.convertPaymentLogToEarlyDischargeLog(ctx, paymentLog)
}

func (p *paymentRepo) GetEarlyDischargeLogByOrderID(ctx context.Context, orderID int64) (*model.EarlyDischargeLog, error) {
	paymentLog, err := p.repos.Queries(ctx).GetPaymentLogByOrderID(ctx, sqlhelper.NewNullInt64(orderID))
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get early discharge log fail", "orderID", orderID, "error", err)
		return nil, err
	}

	return p.convertPaymentLogToEarlyDischargeLog(ctx, paymentLog)
}

func (p *paymentRepo) UpdateEarlyDischargeLog(ctx context.Context, dischargeLog model.EarlyDischargeLog) (*model.EarlyDischargeLog, error) {
	partnerDataRaw, err := json.Marshal(dischargeLog.PartnerData)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "marshal partner data fail", "dischargeLog", dischargeLog, "error", err)
		return nil, err
	}

	q := p.repos.Queries(ctx)
	_, err = q.UpdatePaymentLog(ctx, &sqlc.UpdatePaymentLogParams{
		PartnerTransID:          dischargeLog.PartnerData.RepaymentResult.BankTransID,
		BankTransID:             dischargeLog.PartnerData.RepaymentResult.BankTransID,
		AccountID:               dischargeLog.AccountInfo.ID,
		UserBankAccountNumber:   dischargeLog.AccountInfo.PartnerAccountId,
		BankCode:                dischargeLog.AccountInfo.PartnerCode,
		BankRoutingNumber:       dischargeLog.BankRoute.BankAccountNumber,
		Status:                  string(dischargeLog.Status),
		BankStatus:              dischargeLog.PartnerData.RepaymentResult.RepaymentStatus,
		ErrorCode:               dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode,
		ErrorMessage:            dischargeLog.PartnerData.RepaymentResult.CIMBError.Description,
		CurrentAvailableBalance: dischargeLog.AccountInfo.AvailableBalance,
		PaymentDescription:      dischargeLog.Order.Description,
		Extra:                   partnerDataRaw,
		BankConnectorCode:       "ZPCIMB2",
		EscrowAccountNo:         fmt.Sprintf("%s_%s", dischargeLog.AccountInfo.PartnerCode, dischargeLog.BankRoute.BankAccountNumber),
		ID:                      dischargeLog.ID,
	})
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "update early discharge log fail", "dischargeLog", dischargeLog, "error", err)
		return nil, err
	}

	p.logger.WithContext(ctx).Infow("msg", "update early discharge log success", "dischargeLog", dischargeLog)
	return &dischargeLog, nil
}

func (p *paymentRepo) UpdateEarlyDischargeLogStatus(ctx context.Context, paymentID int64, dischargeLog model.EarlyDischargeLog) error {
	logger := p.logger.WithContext(ctx)

	extra, err := json.Marshal(dischargeLog.PartnerData)
	if err != nil {
		logger.Errorw("msg", "marshal partner data fail", "dischargeLog", dischargeLog, "error", err)
		return err
	}

	params := &sqlc.UpdatePaymentLogStatusParams{
		ID:           paymentID,
		Extra:        extra,
		Status:       string(dischargeLog.Status),
		BankStatus:   dischargeLog.PartnerData.RepaymentResult.RepaymentStatus,
		ErrorCode:    dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode,
		ErrorMessage: dischargeLog.PartnerData.RepaymentResult.CIMBError.Description,
	}

	if err = p.repos.Queries(ctx).UpdatePaymentLogStatus(ctx, params); err != nil {
		logger.Errorw("msg", "update early discharge log status fail", "dischargeLog", dischargeLog, "error", err)
		return err
	}

	logger.Infow("msg", "update early discharge log status success", "dischargeLog", dischargeLog)

	return nil
}

func (p *paymentRepo) UpdatePaymentLogOrderIDs(ctx context.Context, paymentID int64, orderInfo model.Order) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdatePaymentLogOrderIDsParams{
		ID:         paymentID,
		OrderID:    sqlhelper.NewNullInt64(orderInfo.ID),
		ZpTransID:  orderInfo.TransID,
		AppTransID: orderInfo.AppTransID,
	}
	if err := p.repos.Queries(ctx).UpdatePaymentLogOrderIDs(ctx, params); err != nil {
		logger.Errorw("msg", "update payment log order IDs fail", "orderInfo", orderInfo, "error", err)
		return err
	}
	logger.Infow("msg", "update payment log order IDs success", "orderInfo", orderInfo)
	return nil
}

func (p *paymentRepo) convertPaymentLogToEarlyDischargeLog(ctx context.Context, paymentLog *sqlc.PaymentLogs) (*model.EarlyDischargeLog, error) {
	var partnerData model.PartnerRepaymentData
	if len(paymentLog.Extra) > 0 {
		err := json.Unmarshal(paymentLog.Extra, &partnerData)
		if err != nil {
			p.logger.WithContext(ctx).Errorw("msg", "unmarshal partner data fail", "error", err, "extra", string(paymentLog.Extra))
			return nil, err
		}
	}

	return &model.EarlyDischargeLog{
		ID: paymentLog.ID,
		AccountInfo: model.Account{
			ID:                 paymentLog.AccountID,
			ZalopayID:          paymentLog.ZalopayID,
			PartnerCode:        paymentLog.BankCode,
			PartnerAccountId:   paymentLog.UserBankAccountNumber,
			AvailableBalance:   paymentLog.CurrentAvailableBalance,
			PartnerAccountName: paymentLog.UserBankAccountNumber,
		},
		BankRoute: model.BankRoute{
			BankAccountNumber: paymentLog.BankRoutingNumber,
		},
		Order: model.Order{
			TransID:     paymentLog.ZpTransID,
			AppID:       paymentLog.AppID,
			AppTransID:  paymentLog.AppTransID,
			Amount:      paymentLog.Amount,
			Description: paymentLog.PaymentDescription,
		},
		ZpTransID:   paymentLog.ZpTransID,
		TransType:   model.TransType(paymentLog.TransType),
		Status:      model.PaymentStatus(paymentLog.Status),
		PartnerData: partnerData,
		CreatedAt:   paymentLog.CreatedAt,
		UpdatedAt:   paymentLog.UpdatedAt,
	}, nil
}

func (p *paymentRepo) UpdatePaymentCommission(ctx context.Context, zpTransID int64, isCommission bool) error {
	_, err := p.repos.Queries(ctx).UpdateCommission(ctx, &sqlc.UpdateCommissionParams{
		IsCommission: isCommission,
		ZpTransID:    zpTransID,
	})
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "update payment commission fail", "zpTransID", zpTransID, "isCommission", isCommission, "error", err)
		return err
	}

	p.logger.WithContext(ctx).Infow("msg", "update payment commission success", "zpTransID", zpTransID, "isCommission", isCommission)
	return nil
}

func (p *paymentRepo) GetTransactionByZPTransID(ctx context.Context, zpTransID int64) (*model.Transaction, error) {
	q := p.repos.Queries(ctx)
	paymentLog, err := q.GetPaymentLogByZPTransID(ctx, zpTransID)
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get payment(transaction) log by transID fail", "transID", zpTransID, "error", err)
		return nil, err
	}

	// Convert to model.Transaction
	trans := convertPaymentLogToTransaction(paymentLog)

	return trans, nil
}

func convertPaymentLogToTransaction(paymentLog *sqlc.PaymentLogs) *model.Transaction {
	var partnerTransID string
	if paymentLog.TransType == int32(model.TransTypePayment) {
		partnerData := convertPartnerData(paymentLog.Extra)
		partnerTransID = partnerData.BankTransactionSequenceID
	} else if paymentLog.TransType == int32(model.TransTypeRePayment) {
		partnerData := convertRepaymentPartnerData(paymentLog.Extra)
		partnerTransID = partnerData.RepaymentResult.BankTransID
	}

	trans := model.Transaction{
		TransID:        paymentLog.ID,
		ZPTransID:      cast.ToString(paymentLog.ZpTransID),
		PartnerTransID: partnerTransID,
		AccountInfo: model.Account{
			ID:                 paymentLog.AccountID,
			ZalopayID:          paymentLog.ZalopayID,
			PartnerCode:        paymentLog.BankCode,
			Status:             "", //TODO
			AvailableBalance:   paymentLog.CurrentAvailableBalance,
			PartnerAccountId:   paymentLog.UserBankAccountNumber,
			PartnerAccountName: paymentLog.UserBankAccountNumber,
		},
		Amount:    paymentLog.Amount,
		Type:      model.TransType(paymentLog.TransType),
		Status:    model.TransStatus(paymentLog.Status),
		Remark:    paymentLog.PaymentDescription,
		CreatedAt: paymentLog.CreatedAt,
		UpdatedAt: paymentLog.UpdatedAt,
	}

	return &trans
}

func buildListPaymentAdvanceStmt(query string, args *model.ListPaymentQuery) (string, []any, error) {
	params := make([]any, 0)

	if args.ZalopayID != 0 {
		query += " AND zalopay_id=?"
		params = append(params, args.ZalopayID)
	}

	if len(args.PartnerCodes) > 0 {
		slots := strings.Repeat(",?", len(args.PartnerCodes))[1:]
		query += fmt.Sprintf(" AND bank_code IN (%s)", slots)
		params = append(params, partner.ListToAnys(args.PartnerCodes)...)
	}

	if len(args.TransTypes) > 0 {
		slots := strings.Repeat(",?", len(args.TransTypes))[1:]
		query += fmt.Sprintf(" AND trans_type IN (%s)", slots)
		params = append(params, model.ListTransTypeToAnys(args.TransTypes)...)
	}

	if args.TimeRange != nil {
		if !args.TimeRange.From.IsZero() {
			query += " AND created_at >= ?"
			params = append(params, args.TimeRange.From)
		}
		if !args.TimeRange.To.IsZero() {
			query += " AND created_at <= ?"
			params = append(params, args.TimeRange.To)
		}
	}

	pagingStmt, pagingParams := buildPagingAndOrderStmt(true, args.Pagination, "id")
	if pagingStmt == "" {
		return "", nil, errors.New("invalid pagination")
	}

	query += fmt.Sprintf(" AND %s", pagingStmt)
	params = append(params, pagingParams...)

	return query, params, nil
}

func convertPaymentLogToPurchaseOrder(paymentLog *sqlc.PaymentLogs) *model.PurchaseOrder {
	return &model.PurchaseOrder{
		ID:        paymentLog.ID,
		ZpTransID: cast.ToString(paymentLog.ZpTransID),
		AccountInfo: model.Account{
			ID:                 paymentLog.AccountID,
			ZalopayID:          paymentLog.ZalopayID,
			PartnerCode:        paymentLog.BankCode,
			Status:             "", //TODO
			AvailableBalance:   paymentLog.CurrentAvailableBalance,
			PartnerAccountId:   paymentLog.UserBankAccountNumber,
			PartnerAccountName: paymentLog.UserBankAccountNumber,
		},
		BankRoute: model.BankRoute{
			ID:                0,
			InternalBankCode:  "",
			PartnerBankCode:   "",
			BankAccountName:   "",
			BankAccountNumber: paymentLog.BankRoutingNumber,
			Status:            "",
			CreatedAt:         time.Time{},
			UpdatedAt:         time.Time{},
		},
		//BankReceivableAccountingId: paymentLog.,
		Order: model.Order{
			TransID:     paymentLog.ZpTransID,
			AppID:       paymentLog.AppID,
			AppTransID:  paymentLog.AppTransID,
			Amount:      paymentLog.Amount,
			Description: paymentLog.PaymentDescription,
		},
		TransType:   model.TransType(paymentLog.TransType),
		Status:      model.PaymentStatus(paymentLog.Status),
		PartnerData: convertPartnerData(paymentLog.Extra),
		InstallmentInstruction: model.InstallmentInstruction{
			Tenor:        paymentLog.InstallmentTenor,
			InterestRate: paymentLog.InstallmentInterestRate,
		},
		CreatedAt:    paymentLog.CreatedAt,
		UpdatedAt:    paymentLog.UpdatedAt,
		PERequestID:  paymentLog.PeRequestID,
		ErrorMessage: paymentLog.ErrorMessage,
		ErrorCode:    paymentLog.ErrorCode,
		FSChargeInfo: string(paymentLog.FsChargeInfo),
		PaymentNo:    paymentLog.PaymentNo,
		DeviceID:     paymentLog.DeviceID,
		UserIP:       paymentLog.UserIp,
	}
}

func convertPaymentLogToRepaymentLog(paymentLog *sqlc.PaymentLogs) *model.RepaymentLog {
	return &model.RepaymentLog{
		ID:        paymentLog.ID,
		ZpTransID: cast.ToString(paymentLog.ZpTransID),
		AccountInfo: model.Account{
			ID:                 paymentLog.AccountID,
			ZalopayID:          paymentLog.ZalopayID,
			PartnerCode:        paymentLog.BankCode,
			AvailableBalance:   paymentLog.CurrentAvailableBalance,
			PartnerAccountId:   paymentLog.UserBankAccountNumber,
			PartnerAccountName: paymentLog.UserBankAccountNumber,
		},
		BankRoute: model.BankRoute{
			InternalBankCode:  paymentLog.BankCode,
			PartnerBankCode:   paymentLog.BankCode,
			BankAccountNumber: paymentLog.BankRoutingNumber,
		},
		Order: model.Order{
			ID:          paymentLog.OrderID.Int64,
			TransID:     paymentLog.ZpTransID,
			AppID:       paymentLog.AppID,
			AppTransID:  paymentLog.AppTransID,
			Amount:      paymentLog.Amount,
			Description: paymentLog.PaymentDescription,
		},
		TransType:   model.TransType(paymentLog.TransType),
		Status:      model.PaymentStatus(paymentLog.Status),
		PartnerData: convertRepaymentPartnerData(paymentLog.Extra),
		CreatedAt:   paymentLog.CreatedAt,
		UpdatedAt:   paymentLog.UpdatedAt,
	}
}

func convertPartnerData(extra json.RawMessage) model.PurchaseOrderData {
	var partnerData model.PurchaseOrderData
	err := json.Unmarshal(extra, &partnerData)
	if err != nil {
		log.Errorw("msg", "unmarshal partner data fail", "error", err, "extra", string(extra))
	}
	return partnerData
}

func convertRepaymentPartnerData(extra json.RawMessage) model.PartnerRepaymentData {
	var partnerData model.PartnerRepaymentData
	err := json.Unmarshal(extra, &partnerData)
	if err != nil {
		log.Errorw("msg", "unmarshal partner data fail", "error", err, "extra", string(extra))
	}
	return partnerData
}
