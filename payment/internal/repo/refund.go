package repo

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-sql-driver/mysql"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo/sqlc"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	sqlhelper "gitlab.zalopay.vn/fin/installment/payment-service/pkg/sql"
)

type refundRepo struct {
	repos  *Repository
	logger *log.Helper
}

func NewRefundRepo(repos *Repository, logger log.Logger) types.RefundRepo {
	return &refundRepo{
		repos:  repos,
		logger: log.NewHelper(log.With(logger, "module", "refundRepo")),
	}
}

func (p *refundRepo) CreateRefundLog(ctx context.Context, refundOrder model.RefundOrder) (*model.RefundOrder, error) {
	q := p.repos.Queries(ctx)
	ret, err := q.CreateRefundLog(ctx, &sqlc.CreateRefundLogParams{
		ZpTransID:          refundOrder.ZPTransID,
		RefundID:           refundOrder.RefundID,
		Amount:             refundOrder.Amount,
		AppTransID:         refundOrder.AppTransID,
		AppID:              refundOrder.AppID,
		PaymentDescription: refundOrder.PaymentDescription,
		RefundType:         toRefundTypeDA(refundOrder.RefundType),
		ProcessType:        toRefundProcessTypeDA(refundOrder.ProcessType),
		Status:             fromRefundStatus(refundOrder.Status),
		Extra:              []byte("{}"),
	})
	if err != nil {
		var me *mysql.MySQLError
		ok := errors.As(err, &me)
		if ok && me.Number == ER_DUP_ENTRY {
			p.logger.Warnw("msg", "duplicate refund order", "refundOrder", refundOrder, "error", err)
			return nil, fmt.Errorf(model.ErrDuplicateTransaction)
		}

		p.logger.WithContext(ctx).Errorw("msg", "insert refund_logs fail", "refundOrder", refundOrder, "error", err)
		return nil, err
	}

	id, _ := ret.LastInsertId()
	refundOrder.ID = id
	p.logger.WithContext(ctx).Infow("msg", "insert refund_logs success", "refundOrder", refundOrder)

	return &refundOrder, nil
}

func (p *refundRepo) CompleteRefundLog(ctx context.Context, refundOrder *model.RefundOrder) error {
	logger := p.logger.WithContext(ctx)

	res, err := p.repos.Queries(ctx).UpdateRefundLogComplete(ctx, &sqlc.UpdateRefundLogCompleteParams{
		ID:         refundOrder.ID,
		Status:     fromRefundStatus(refundOrder.Status),
		DeadlineAt: sqlhelper.NewNullTime(refundOrder.DeadlineAt),
		Extra:      []byte("{}"),
	})
	if err != nil {
		logger.Errorw("msg", "update refund log fail", "refundOrder", refundOrder, "error", err)
		return err
	}
	affected, err := res.RowsAffected()
	if err != nil {
		logger.Errorw("msg", "get affected rows fail", "refundOrder", refundOrder, "error", err)
		return err
	}
	if affected == 0 {
		logger.Errorw("msg", "refund log not found", "refundOrder", refundOrder)
		return fmt.Errorf("refund log not found")
	}
	return nil
}

func (p *refundRepo) GetRefundLogForUpdate(ctx context.Context, id int64) (*model.RefundOrder, error) {
	refundLogDA, err := p.repos.Queries(ctx).GetRefundLogForUpdate(ctx, id)
	if errors.Is(err, sql.ErrNoRows) {
		p.logger.WithContext(ctx).Errorw("msg", "refund log not found", "id", id)
		return nil, fmt.Errorf("refund log not found id: %d, %w", id, model.ErrRefundNotFound)
	}
	if err != nil {
		p.logger.WithContext(ctx).Errorw("msg", "get refund log fail", "id", id, "error", err)
		return nil, err
	}

	refundLog := toRefundModel(refundLogDA)
	p.logger.WithContext(ctx).Infow("msg", "get refund log success", "id", id, "refundLog", refundLog)
	return refundLog, nil
}

// Implement GetRefundLogByRefundID
func (p *refundRepo) GetRefundLogByRefundID(ctx context.Context, refundID int64) (*model.RefundOrder, error) {
	logger := p.logger.WithContext(ctx)
	refundLogDA, err := p.repos.Queries(ctx).GetRefundLogByRefundID(ctx, refundID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnw("msg", "refund log not found by refundID", "refundID", refundID)
		return nil, fmt.Errorf("refund log not found by refundID: %d, %w", refundID, model.ErrRefundNotFound)
	}
	if err != nil {
		logger.Errorw("msg", "get refund log by refundID fail", "refundID", refundID, "error", err)
		return nil, err
	}
	refundLog := toRefundModel(refundLogDA)
	logger.Infow("msg", "get refund log by refundID success", "refundID", refundID, "refundLog", refundLog)
	return refundLog, nil
}

func (p *refundRepo) GetRefundLogsByZPTransID(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error) {
	logger := p.logger.WithContext(ctx)

	refundLogsDA, err := p.repos.Queries(ctx).GetRefundLogsByZPTransID(ctx, zpTransID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnw("msg", "refund logs not found", "zpTransID", zpTransID)
		return nil, nil
	}
	if err != nil {
		logger.Errorw("msg", "get refund logs fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	refundLogs := make([]*model.RefundOrder, 0, len(refundLogsDA))
	for _, refundLogDA := range refundLogsDA {
		refundLogs = append(refundLogs, toRefundModel(refundLogDA))
	}
	return refundLogs, nil
}

func (p *refundRepo) GetRefundLogsForSettlement(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error) {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.GetRefundLogsByProcessTypeAndZpTransIDParams{
		ZpTransID:   zpTransID,
		ProcessType: toRefundProcessTypeDA(model.RefundProcessTypeSettlement),
	}
	refundLogsDA, err := p.repos.Queries(ctx).GetRefundLogsByProcessTypeAndZpTransID(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnw("msg", "refund logs for settlement not found", "zpTransID", zpTransID)
		return nil, nil
	}
	if err != nil {
		logger.Errorw("msg", "get refund logs for settlement fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	refundLogs := make([]*model.RefundOrder, 0, len(refundLogsDA))
	for _, refundLogDA := range refundLogsDA {
		refundLogs = append(refundLogs, toRefundModel(refundLogDA))
	}
	return refundLogs, nil
}

func (p *refundRepo) GetRefundLogsExpiredByZPTransIDForUpdate(ctx context.Context, zpTransID int64) ([]*model.RefundOrder, error) {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.GetRefundLogsExpiredByZPTransIDForUpdateParams{
		ZpTransID:   zpTransID,
		ProcessType: toRefundProcessTypeDA(model.RefundProcessTypeSettlement),
	}
	refundLogsDA, err := p.repos.Queries(ctx).GetRefundLogsExpiredByZPTransIDForUpdate(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnw("msg", "refund logs expired not found", "zpTransID", zpTransID)
		return nil, nil
	}
	if err != nil {
		logger.Errorw("msg", "get refund logs expired fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	refundLogs := make([]*model.RefundOrder, 0, len(refundLogsDA))
	for _, refundLogDA := range refundLogsDA {
		refundLogs = append(refundLogs, toRefundModel(refundLogDA))
	}
	return refundLogs, nil
}

// MarkRefundLogsAsExpiredByIDs implements types.RefundRepo.
func (p *refundRepo) MarkRefundLogsAsExpiredByIDs(ctx context.Context, ids []int64) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdateRefundLogsProcessTypeByIDsParams{
		Ids:         ids,
		ProcessType: toRefundProcessTypeDA(model.RefundProcessTypeRepayment),
	}
	err := p.repos.Queries(ctx).UpdateRefundLogsProcessTypeByIDs(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund logs expired fail", "ids", ids, "error", err)
		return err
	}

	logger.Infow("msg", "update refund logs expired success", "ids", ids)

	return nil
}

func (p *refundRepo) GetRefundSettleIDsHasExpiredItem(ctx context.Context, paging *model.Pagination) ([]int64, error) {
	logger := p.logger.WithContext(ctx)

	proType := sqlc.NullRefundLogsProcessType{
		Valid:                 true,
		RefundLogsProcessType: sqlc.RefundLogsProcessTypeSETTLEMENT,
	}
	params := &sqlc.GetListRefundSettleIDHasExpiredParams{
		Limit:       int32(paging.Limit),
		IDGt:        cast.ToInt64(paging.Cursor),
		Status:      sqlc.RefundSettleStatusINIT,
		ProcessType: proType,
	}
	listIds, err := p.repos.Queries(ctx).GetListRefundSettleIDHasExpired(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		return []int64{}, nil
	}
	if err != nil {
		logger.Errorw("msg", "get list refund log has expired fail", "error", err)
		return nil, err
	}

	return listIds, nil
}

func (p *refundRepo) CreateRefundSettle(ctx context.Context, params *model.RefundSettle) (*model.RefundSettle, error) {
	logger := p.logger.WithContext(ctx)

	metadataBin, err := jsoniter.Marshal(params.ExtraData)
	if err != nil {
		logger.Errorw("msg", "marshal refund settle extra data fail", "params", params, "error", err)
		metadataBin = []byte("{}")
	}

	data, err := p.repos.Queries(ctx).CreateRefundSettle(ctx, &sqlc.CreateRefundSettleParams{
		ZpTransID:         params.ZPTransID,
		EventVersion:      0,
		NetRefundAmount:   params.NetRefundAmount,
		TotalRefundAmount: params.TotalRefundAmount,
		SettlementAmount:  params.SettlementAmount,
		Metadata:          metadataBin,
	})
	if err != nil {
		logger.Errorw("msg", "create refund settle fail", "params", params, "error", err)
		return nil, err
	}

	id, err := data.LastInsertId()
	if err != nil {
		logger.Errorw("msg", "get refund settle id fail", "params", params, "error", err)
		return nil, err
	}

	item, err := p.repos.Queries(ctx).GetRefundSettleByID(ctx, id)
	if err != nil {
		logger.Errorw("msg", "get refund settle fail", "refundSettleID", id, "error", err)
		return nil, err
	}

	logger.Infow("msg", "create refund settle success", "params", params)

	return p.toRefundSettle(item), nil
}

func (p *refundRepo) UpdateRefundSettleEventData(ctx context.Context, refundSettle *model.RefundSettle, originVersion int32) error {
	logger := p.logger.WithContext(ctx)

	metadataBin, err := jsoniter.Marshal(refundSettle.ExtraData)
	if err != nil {
		logger.Errorw("msg", "marshal refund settle extra data fail", "refundSettle", refundSettle, "error", err)
		return err
	}

	params := &sqlc.UpdateRefundSettleEventParams{
		ID:         refundSettle.ID,
		Metadata:   metadataBin,
		OldVersion: originVersion,
		NewVersion: refundSettle.EventVersion,
	}
	res, err := p.repos.Queries(ctx).UpdateRefundSettleEvent(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund settle event fail", "refundSettle", refundSettle, "error", err)
		return err
	}

	rows, err := res.RowsAffected()
	if err != nil {
		logger.Errorw("msg", "get affected rows fail", "refundSettle", refundSettle, "error", err)
		return err
	}
	if rows != 1 {
		return model.ErrRefundSettleNotFound
	}

	logger.Infow("msg", "update refund settle event success", "refundSettle", refundSettle)

	return nil
}

func (p *refundRepo) GetRefundSettleByZPTransID(ctx context.Context, zpTransID int64) (*model.RefundSettle, error) {
	logger := p.logger.WithContext(ctx)

	data, err := p.repos.Queries(ctx).GetRefundSettle(ctx, zpTransID)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrRefundSettleNotFound
	}
	if err != nil {
		logger.Errorw("msg", "get refund logs for settlement fail", "zpTransID", zpTransID, "error", err)
		return nil, err
	}

	result := p.toRefundSettle(data)

	logger.Infow("msg", "get refund settle success", "zpTransID", zpTransID, "refundSettle", result)

	return result, nil
}

func (p *refundRepo) GetRefundSettleByID(ctx context.Context, id int64) (*model.RefundSettle, error) {
	logger := p.logger.WithContext(ctx)

	data, err := p.repos.Queries(ctx).GetRefundSettleByID(ctx, id)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrRefundSettleNotFound
	}
	if err != nil {
		logger.Errorw("msg", "get refund settle by id fail", "id", id, "error", err)
		return nil, err
	}

	result := p.toRefundSettle(data)

	logger.Infow("msg", "get refund settle by id success", "id", id, "refundSettle", result)

	return result, nil
}

func (p *refundRepo) GetRefundSettleForUpdate(ctx context.Context, id int64) (*model.RefundSettle, error) {
	logger := p.logger.WithContext(ctx)

	data, err := p.repos.Queries(ctx).GetRefundSettleForUpdate(ctx, id)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrRefundSettleNotFound
	}
	if err != nil {
		logger.Errorw("msg", "get refund settle for update fail", "id", id, "error", err)
		return nil, err
	}

	result := p.toRefundSettle(data)

	logger.Infow("msg", "get refund settle for update success", "id", id, "refundSettle", result)

	return result, nil
}

func (p *refundRepo) GetListRefundSettleByStatus(ctx context.Context, status model.RefundSettleStatus, paging *model.Pagination) ([]*model.RefundSettle, error) {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.GetListRefundSettleByStatusParams{
		Status: fromRefundSettleStatus(status),
		Limit:  int32(paging.Limit),
		IDGt:   cast.ToInt64(paging.Cursor),
	}
	refundSettles, err := p.repos.Queries(ctx).GetListRefundSettleByStatus(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		return []*model.RefundSettle{}, nil
	}
	if err != nil {
		logger.Errorw("msg", "failed to get refund settles", "error", err)
		return nil, err
	}

	result := make([]*model.RefundSettle, len(refundSettles))
	for i, settle := range refundSettles {
		result[i] = p.toRefundSettle(settle)
	}

	return result, nil
}

func (p *refundRepo) UpdateRefundSettleStatusByID(ctx context.Context, id int64, status model.RefundSettleStatus) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdateRefundSettleStatusParams{
		ID:     id,
		Status: fromRefundSettleStatus(status),
	}
	err := p.repos.Queries(ctx).UpdateRefundSettleStatus(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund settle status fail", "id", id, "status", status, "error", err)
		return err
	}

	logger.Infow("msg", "update refund settle status success", "id", id, "status", status)

	return nil
}

func (p *refundRepo) UpdateRefundSettleReconAmts(ctx context.Context, refundSettle *model.RefundSettle) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdateRefundSettleReconParams{
		ID:                refundSettle.ID,
		NetRefundAmount:   refundSettle.NetRefundAmount,
		TotalRefundAmount: refundSettle.TotalRefundAmount,
		SettlementAmount:  refundSettle.SettlementAmount,
		Status:            fromRefundSettleStatus(refundSettle.Status),
	}
	err := p.repos.Queries(ctx).UpdateRefundSettleRecon(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund settle recon amounts fail", "refundSettle", refundSettle, "error", err)
		return err
	}

	logger.Infow("msg", "update refund settle recon amounts success", "refundSettle", refundSettle)

	return nil
}

func (p *refundRepo) UpdateRefundSettlementAmount(ctx context.Context, id int64, settlementAmount int64) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdateRefundSettlementAmountParams{
		ID:               id,
		SettlementAmount: settlementAmount,
	}
	err := p.repos.Queries(ctx).UpdateRefundSettlementAmount(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund settle settlement amount fail", "id", id, "settlementAmount", settlementAmount, "error", err)
		return err
	}

	logger.Infow("msg", "update refund settle settlement amount success", "id", id, "settlementAmount", settlementAmount)

	return nil
}

func (p *refundRepo) UpdateRefundTopupsAmount(ctx context.Context, id int64, topupsAmount int64) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdateRefundTopupsAmountParams{
		ID:              id,
		UserTopupAmount: topupsAmount,
	}
	err := p.repos.Queries(ctx).UpdateRefundTopupsAmount(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund settle top-ups amount fail", "id", id, "topupsAmount", topupsAmount, "error", err)
		return err
	}

	logger.Infow("msg", "update refund settle top-ups amount success", "id", id, "topupsAmount", topupsAmount)

	return nil
}

func (p *refundRepo) UpdateRefundSettlePaybackInfo(ctx context.Context, id int64, paybackAmount int64, status model.RefundSettleStatus) error {
	logger := p.logger.WithContext(ctx)

	params := &sqlc.UpdateRefundPaybackInfoParams{
		ID:                id,
		Status:            fromRefundSettleStatus(status),
		UserPaybackAmount: paybackAmount,
	}
	err := p.repos.Queries(ctx).UpdateRefundPaybackInfo(ctx, params)
	if err != nil {
		logger.Errorw("msg", "update refund settle payback info fail", "id", id, "paybackAmount", paybackAmount, "status", status, "error", err)
		return err
	}

	logger.Infow("msg", "update refund settle payback info success", "id", id, "paybackAmount", paybackAmount, "status", status)

	return nil
}

func toRefundModel(refundLogDA *sqlc.RefundLogs) *model.RefundOrder {
	return &model.RefundOrder{
		ID:                 refundLogDA.ID,
		ZPTransID:          refundLogDA.ZpTransID,
		RefundID:           refundLogDA.RefundID,
		Amount:             refundLogDA.Amount,
		AppTransID:         refundLogDA.AppTransID,
		AppID:              refundLogDA.AppID,
		PaymentDescription: refundLogDA.PaymentDescription,
		RefundType:         toRefundTypeModel(refundLogDA.RefundType),
		Status:             toRefundStatus(refundLogDA.Status),
		ErrorCode:          refundLogDA.ErrorCode,
		ErrorMessage:       refundLogDA.ErrorMessage,
		ProcessType:        toRefundProcessTypeModel(refundLogDA.ProcessType),
		DeadlineAt:         refundLogDA.DeadlineAt.Time,
		CreatedAt:          refundLogDA.CreatedAt,
		UpdatedAt:          refundLogDA.UpdatedAt,
	}
}

func toRefundStatus(status sqlc.RefundLogsStatus) model.RefundStatus {
	switch status {
	case sqlc.RefundLogsStatusINIT:
		return model.RefundStatusInit
	case sqlc.RefundLogsStatusPENDING:
		return model.RefundStatusPending
	case sqlc.RefundLogsStatusSUCCESS:
		return model.RefundStatusSuccess
	case sqlc.RefundLogsStatusFAILED:
		return model.RefundStatusFailed
	case sqlc.RefundLogsStatusPROCESSING:
		return model.RefundStatusProcessing
	}
	return model.RefundStatusInit
}

func toRefundTypeDA(refundType model.RefundType) sqlc.RefundLogsRefundType {
	switch refundType {
	case model.RefundTypeAuto:
		return sqlc.RefundLogsRefundTypeAUTO
	case model.RefundTypeManual:
		return sqlc.RefundLogsRefundTypeMANUAL
	}
	return sqlc.RefundLogsRefundTypeMANUAL
}

func toRefundTypeModel(rtDA sqlc.RefundLogsRefundType) model.RefundType {
	switch rtDA {
	case sqlc.RefundLogsRefundTypeAUTO:
		return model.RefundTypeAuto
	case sqlc.RefundLogsRefundTypeMANUAL:
		return model.RefundTypeManual
	}
	return model.RefundTypeManual
}

func fromRefundStatus(status model.RefundStatus) sqlc.RefundLogsStatus {
	switch status {
	case model.RefundStatusInit:
		return sqlc.RefundLogsStatusINIT
	case model.RefundStatusPending:
		return sqlc.RefundLogsStatusPENDING
	case model.RefundStatusSuccess:
		return sqlc.RefundLogsStatusSUCCESS
	case model.RefundStatusFailed:
		return sqlc.RefundLogsStatusFAILED
	case model.RefundStatusProcessing:
		return sqlc.RefundLogsStatusPROCESSING
	}
	return sqlc.RefundLogsStatusINIT
}

func toRefundProcessTypeDA(processType model.RefundProcessType) sqlc.NullRefundLogsProcessType {
	var processTypeDA sqlc.RefundLogsProcessType

	switch processType {
	case model.RefundProcessTypeFundback:
		processTypeDA = sqlc.RefundLogsProcessTypeFUNDBACK
	case model.RefundProcessTypeSettlement:
		processTypeDA = sqlc.RefundLogsProcessTypeSETTLEMENT
	case model.RefundProcessTypeRepayment:
		processTypeDA = sqlc.RefundLogsProcessTypeREPAYMENT
	case model.RefundProcessTypeManual:
		processTypeDA = sqlc.RefundLogsProcessTypeMANUAL
	}
	if processTypeDA == "" {
		return sqlc.NullRefundLogsProcessType{}
	}
	return sqlc.NullRefundLogsProcessType{
		RefundLogsProcessType: processTypeDA,
		Valid:                 true,
	}
}

func toRefundProcessTypeModel(processTypeDA sqlc.NullRefundLogsProcessType) model.RefundProcessType {
	if !processTypeDA.Valid {
		return ""
	}
	switch processTypeDA.RefundLogsProcessType {
	case sqlc.RefundLogsProcessTypeFUNDBACK:
		return model.RefundProcessTypeFundback
	case sqlc.RefundLogsProcessTypeSETTLEMENT:
		return model.RefundProcessTypeSettlement
	case sqlc.RefundLogsProcessTypeREPAYMENT:
		return model.RefundProcessTypeRepayment
	case sqlc.RefundLogsProcessTypeMANUAL:
		return model.RefundProcessTypeManual
	}
	return ""
}

func (p *refundRepo) toRefundSettle(data *sqlc.RefundSettle) *model.RefundSettle {
	var extraData model.RefundSettleExtra
	if err := jsoniter.Unmarshal(data.Metadata, &extraData); err != nil {
		p.logger.Warnw("msg", "unmarshal refund settle extra data fail", "refundSettleID", data.ID, "error", err)
	}

	return &model.RefundSettle{
		ID:                data.ID,
		Status:            toRefundSettleStatus(data.Status),
		ZPTransID:         data.ZpTransID,
		NetRefundAmount:   data.NetRefundAmount,
		TotalRefundAmount: data.TotalRefundAmount,
		SettlementAmount:  data.SettlementAmount,
		UserTopupAmount:   data.UserTopupAmount,
		UserPaybackAmount: data.UserPaybackAmount,
		EventVersion:      data.EventVersion,
		ExtraData:         extraData,
	}
}

func toRefundSettleStatus(status sqlc.RefundSettleStatus) model.RefundSettleStatus {
	switch status {
	case sqlc.RefundSettleStatusINIT:
		return model.RefundSettleStatusInit
	case sqlc.RefundSettleStatusPENDING:
		return model.RefundSettleStatusPending
	case sqlc.RefundSettleStatusPROCESSING:
		return model.RefundSettleStatusProcessing
	case sqlc.RefundSettleStatusCOMPLETED:
		return model.RefundSettleStatusCompleted
	case sqlc.RefundSettleStatusSETTLED:
		return model.RefundSettleStatusSettled
	default:
		return model.RefundSettleStatusInit
	}
}

func fromRefundSettleStatus(status model.RefundSettleStatus) sqlc.RefundSettleStatus {
	switch status {
	case model.RefundSettleStatusInit:
		return sqlc.RefundSettleStatusINIT
	case model.RefundSettleStatusPending:
		return sqlc.RefundSettleStatusPENDING
	case model.RefundSettleStatusProcessing:
		return sqlc.RefundSettleStatusPROCESSING
	case model.RefundSettleStatusCompleted:
		return sqlc.RefundSettleStatusCOMPLETED
	case model.RefundSettleStatusSettled:
		return sqlc.RefundSettleStatusSETTLED
	default:
		return sqlc.RefundSettleStatusINIT
	}
}
