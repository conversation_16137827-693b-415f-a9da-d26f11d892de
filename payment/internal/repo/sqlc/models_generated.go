// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

type BankRouteStatus string

const (
	BankRouteStatusActive   BankRouteStatus = "active"
	BankRouteStatusInactive BankRouteStatus = "inactive"
)

func (e *BankRouteStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = BankRouteStatus(s)
	case string:
		*e = BankRouteStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for BankRouteStatus: %T", src)
	}
	return nil
}

type NullBankRouteStatus struct {
	BankRouteStatus BankRouteStatus `json:"bank_route_status"`
	Valid           bool            `json:"valid"` // Valid is true if BankRouteStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullBankRouteStatus) Scan(value interface{}) error {
	if value == nil {
		ns.BankRouteStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.BankRouteStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullBankRouteStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.BankRouteStatus), nil
}

type OrderStatus string

const (
	OrderStatusInit       OrderStatus = "init"
	OrderStatusPending    OrderStatus = "pending"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusSucceeded  OrderStatus = "succeeded"
	OrderStatusFailed     OrderStatus = "failed"
	OrderStatusCancelled  OrderStatus = "cancelled"
)

func (e *OrderStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = OrderStatus(s)
	case string:
		*e = OrderStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for OrderStatus: %T", src)
	}
	return nil
}

type NullOrderStatus struct {
	OrderStatus OrderStatus `json:"order_status"`
	Valid       bool        `json:"valid"` // Valid is true if OrderStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullOrderStatus) Scan(value interface{}) error {
	if value == nil {
		ns.OrderStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.OrderStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullOrderStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.OrderStatus), nil
}

type OrderType string

const (
	OrderTypeRepayment OrderType = "repayment"
	OrderTypeRefund    OrderType = "refund"
)

func (e *OrderType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = OrderType(s)
	case string:
		*e = OrderType(s)
	default:
		return fmt.Errorf("unsupported scan type for OrderType: %T", src)
	}
	return nil
}

type NullOrderType struct {
	OrderType OrderType `json:"order_type"`
	Valid     bool      `json:"valid"` // Valid is true if OrderType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullOrderType) Scan(value interface{}) error {
	if value == nil {
		ns.OrderType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.OrderType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullOrderType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.OrderType), nil
}

type PartnerStatus string

const (
	PartnerStatusEnable  PartnerStatus = "enable"
	PartnerStatusDisable PartnerStatus = "disable"
)

func (e *PartnerStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = PartnerStatus(s)
	case string:
		*e = PartnerStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for PartnerStatus: %T", src)
	}
	return nil
}

type NullPartnerStatus struct {
	PartnerStatus PartnerStatus `json:"partner_status"`
	Valid         bool          `json:"valid"` // Valid is true if PartnerStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullPartnerStatus) Scan(value interface{}) error {
	if value == nil {
		ns.PartnerStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.PartnerStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullPartnerStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.PartnerStatus), nil
}

type RefundLogsProcessType string

const (
	RefundLogsProcessTypeSETTLEMENT RefundLogsProcessType = "SETTLEMENT"
	RefundLogsProcessTypeREPAYMENT  RefundLogsProcessType = "REPAYMENT"
	RefundLogsProcessTypeFUNDBACK   RefundLogsProcessType = "FUNDBACK"
	RefundLogsProcessTypeMANUAL     RefundLogsProcessType = "MANUAL"
)

func (e *RefundLogsProcessType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RefundLogsProcessType(s)
	case string:
		*e = RefundLogsProcessType(s)
	default:
		return fmt.Errorf("unsupported scan type for RefundLogsProcessType: %T", src)
	}
	return nil
}

type NullRefundLogsProcessType struct {
	RefundLogsProcessType RefundLogsProcessType `json:"refund_logs_process_type"`
	Valid                 bool                  `json:"valid"` // Valid is true if RefundLogsProcessType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRefundLogsProcessType) Scan(value interface{}) error {
	if value == nil {
		ns.RefundLogsProcessType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RefundLogsProcessType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRefundLogsProcessType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RefundLogsProcessType), nil
}

type RefundLogsRefundType string

const (
	RefundLogsRefundTypeAUTO   RefundLogsRefundType = "AUTO"
	RefundLogsRefundTypeMANUAL RefundLogsRefundType = "MANUAL"
)

func (e *RefundLogsRefundType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RefundLogsRefundType(s)
	case string:
		*e = RefundLogsRefundType(s)
	default:
		return fmt.Errorf("unsupported scan type for RefundLogsRefundType: %T", src)
	}
	return nil
}

type NullRefundLogsRefundType struct {
	RefundLogsRefundType RefundLogsRefundType `json:"refund_logs_refund_type"`
	Valid                bool                 `json:"valid"` // Valid is true if RefundLogsRefundType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRefundLogsRefundType) Scan(value interface{}) error {
	if value == nil {
		ns.RefundLogsRefundType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RefundLogsRefundType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRefundLogsRefundType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RefundLogsRefundType), nil
}

type RefundLogsStatus string

const (
	RefundLogsStatusINIT       RefundLogsStatus = "INIT"
	RefundLogsStatusSUCCESS    RefundLogsStatus = "SUCCESS"
	RefundLogsStatusFAILED     RefundLogsStatus = "FAILED"
	RefundLogsStatusPENDING    RefundLogsStatus = "PENDING"
	RefundLogsStatusPROCESSING RefundLogsStatus = "PROCESSING"
)

func (e *RefundLogsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RefundLogsStatus(s)
	case string:
		*e = RefundLogsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for RefundLogsStatus: %T", src)
	}
	return nil
}

type NullRefundLogsStatus struct {
	RefundLogsStatus RefundLogsStatus `json:"refund_logs_status"`
	Valid            bool             `json:"valid"` // Valid is true if RefundLogsStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRefundLogsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.RefundLogsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RefundLogsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRefundLogsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RefundLogsStatus), nil
}

type RefundSettleStatus string

const (
	RefundSettleStatusINIT       RefundSettleStatus = "INIT"
	RefundSettleStatusPENDING    RefundSettleStatus = "PENDING"
	RefundSettleStatusPROCESSING RefundSettleStatus = "PROCESSING"
	RefundSettleStatusSETTLED    RefundSettleStatus = "SETTLED"
	RefundSettleStatusCOMPLETED  RefundSettleStatus = "COMPLETED"
)

func (e *RefundSettleStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RefundSettleStatus(s)
	case string:
		*e = RefundSettleStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for RefundSettleStatus: %T", src)
	}
	return nil
}

type NullRefundSettleStatus struct {
	RefundSettleStatus RefundSettleStatus `json:"refund_settle_status"`
	Valid              bool               `json:"valid"` // Valid is true if RefundSettleStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRefundSettleStatus) Scan(value interface{}) error {
	if value == nil {
		ns.RefundSettleStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RefundSettleStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRefundSettleStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RefundSettleStatus), nil
}

type BankRoute struct {
	ID                int64           `json:"id"`
	InternalBankCode  string          `json:"internal_bank_code"`
	PartnerBankCode   string          `json:"partner_bank_code"`
	BankAccountName   string          `json:"bank_account_name"`
	BankAccountNumber string          `json:"bank_account_number"`
	TransType         int32           `json:"trans_type"`
	Status            BankRouteStatus `json:"status"`
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         time.Time       `json:"updated_at"`
}

type Order struct {
	ID            int64           `json:"id"`
	ZalopayID     int64           `json:"zalopay_id"`
	PartnerCode   string          `json:"partner_code"`
	SystemID      int32           `json:"system_id"`
	AppID         int32           `json:"app_id"`
	AppTransID    string          `json:"app_trans_id"`
	ZpTransID     string          `json:"zp_trans_id"`
	ZpTransToken  string          `json:"zp_trans_token"`
	RefundID      int64           `json:"refund_id"`
	SettleID      int64           `json:"settle_id"`
	Amount        int64           `json:"amount"`
	Status        OrderStatus     `json:"status"`
	Type          OrderType       `json:"type"`
	Stage         sql.NullString  `json:"stage"`
	Extra         json.RawMessage `json:"extra"`
	Description   string          `json:"description"`
	StatementID   int64           `json:"statement_id"`
	StatementDate sql.NullTime    `json:"statement_date"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

type Partner struct {
	ID        int64         `json:"id"`
	Code      string        `json:"code"`
	Status    PartnerStatus `json:"status"`
	CreatedAt time.Time     `json:"created_at"`
	UpdatedAt time.Time     `json:"updated_at"`
}

type PaymentLogs struct {
	ID                      int64           `json:"id"`
	OrderID                 sql.NullInt64   `json:"order_id"`
	ZpTransID               int64           `json:"zp_trans_id"`
	PaymentNo               string          `json:"payment_no"`
	SystemID                int32           `json:"system_id"`
	TransType               int32           `json:"trans_type"`
	PartnerReqID            sql.NullString  `json:"partner_req_id"`
	PartnerTransID          string          `json:"partner_trans_id"`
	BankTransID             string          `json:"bank_trans_id"`
	AppTransID              string          `json:"app_trans_id"`
	AccountID               int64           `json:"account_id"`
	ZalopayID               int64           `json:"zalopay_id"`
	UserBankAccountNumber   string          `json:"user_bank_account_number"`
	BankCode                string          `json:"bank_code"`
	BankRoutingNumber       string          `json:"bank_routing_number"`
	AppID                   int32           `json:"app_id"`
	Amount                  int64           `json:"amount"`
	Status                  string          `json:"status"`
	BankStatus              string          `json:"bank_status"`
	ErrorCode               string          `json:"error_code"`
	ErrorMessage            string          `json:"error_message"`
	CurrentAvailableBalance int64           `json:"current_available_balance"`
	PaymentDescription      string          `json:"payment_description"`
	Extra                   json.RawMessage `json:"extra"`
	InstallmentTenor        int32           `json:"installment_tenor"`
	InstallmentInterestRate float64         `json:"installment_interest_rate"`
	PeRequestID             string          `json:"pe_request_id"`
	FsChargeInfo            json.RawMessage `json:"fs_charge_info"`
	BankConnectorCode       string          `json:"bank_connector_code"`
	EscrowAccountNo         string          `json:"escrow_account_no"`
	CreatedAt               time.Time       `json:"created_at"`
	UpdatedAt               time.Time       `json:"updated_at"`
	IsCommission            bool            `json:"is_commission"`
	IsCommissionUpdatedAt   time.Time       `json:"is_commission_updated_at"`
	DeviceID                string          `json:"device_id"`
	UserIp                  string          `json:"user_ip"`
}

type RefundLogs struct {
	ID                 int64                     `json:"id"`
	ZpTransID          int64                     `json:"zp_trans_id"`
	RefundID           int64                     `json:"refund_id"`
	Amount             int64                     `json:"amount"`
	AppTransID         string                    `json:"app_trans_id"`
	AppID              int32                     `json:"app_id"`
	PaymentDescription string                    `json:"payment_description"`
	RefundType         RefundLogsRefundType      `json:"refund_type"`
	Status             RefundLogsStatus          `json:"status"`
	ErrorCode          string                    `json:"error_code"`
	ErrorMessage       string                    `json:"error_message"`
	ProcessType        NullRefundLogsProcessType `json:"process_type"`
	DeadlineAt         sql.NullTime              `json:"deadline_at"`
	Extra              json.RawMessage           `json:"extra"`
	CreatedAt          time.Time                 `json:"created_at"`
	UpdatedAt          time.Time                 `json:"updated_at"`
}

type RefundSettle struct {
	ID                int64              `json:"id"`
	Status            RefundSettleStatus `json:"status"`
	ZpTransID         int64              `json:"zp_trans_id"`
	NetRefundAmount   int64              `json:"net_refund_amount"`
	TotalRefundAmount int64              `json:"total_refund_amount"`
	SettlementAmount  int64              `json:"settlement_amount"`
	UserTopupAmount   int64              `json:"user_topup_amount"`
	UserPaybackAmount int64              `json:"user_payback_amount"`
	EventVersion      int32              `json:"event_version"`
	Metadata          json.RawMessage    `json:"metadata"`
	CreatedAt         time.Time          `json:"created_at"`
	UpdatedAt         time.Time          `json:"updated_at"`
}
