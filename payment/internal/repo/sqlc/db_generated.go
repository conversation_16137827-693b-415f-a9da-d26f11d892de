// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
	"database/sql"
	"fmt"
)

type DBTX interface {
	ExecContext(context.Context, string, ...interface{}) (sql.Result, error)
	PrepareContext(context.Context, string) (*sql.Stmt, error)
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
	QueryRowContext(context.Context, string, ...interface{}) *sql.Row
}

func New(db DBTX) *Queries {
	return &Queries{db: db}
}

func Prepare(ctx context.Context, db DBTX) (*Queries, error) {
	q := Queries{db: db}
	var err error
	if q.createBankRouteStmt, err = db.PrepareContext(ctx, CreateBankRoute); err != nil {
		return nil, fmt.Errorf("error preparing query CreateBankRoute: %w", err)
	}
	if q.createPartnerStmt, err = db.PrepareContext(ctx, CreatePartner); err != nil {
		return nil, fmt.Errorf("error preparing query CreatePartner: %w", err)
	}
	if q.createPaymentLogStmt, err = db.PrepareContext(ctx, CreatePaymentLog); err != nil {
		return nil, fmt.Errorf("error preparing query CreatePaymentLog: %w", err)
	}
	if q.createRefundFundbackOrderStmt, err = db.PrepareContext(ctx, CreateRefundFundbackOrder); err != nil {
		return nil, fmt.Errorf("error preparing query CreateRefundFundbackOrder: %w", err)
	}
	if q.createRefundLogStmt, err = db.PrepareContext(ctx, CreateRefundLog); err != nil {
		return nil, fmt.Errorf("error preparing query CreateRefundLog: %w", err)
	}
	if q.createRefundSettleStmt, err = db.PrepareContext(ctx, CreateRefundSettle); err != nil {
		return nil, fmt.Errorf("error preparing query CreateRefundSettle: %w", err)
	}
	if q.createRefundSettleOrderStmt, err = db.PrepareContext(ctx, CreateRefundSettleOrder); err != nil {
		return nil, fmt.Errorf("error preparing query CreateRefundSettleOrder: %w", err)
	}
	if q.createRefundTopupOrderStmt, err = db.PrepareContext(ctx, CreateRefundTopupOrder); err != nil {
		return nil, fmt.Errorf("error preparing query CreateRefundTopupOrder: %w", err)
	}
	if q.createRepaymentOrderStmt, err = db.PrepareContext(ctx, CreateRepaymentOrder); err != nil {
		return nil, fmt.Errorf("error preparing query CreateRepaymentOrder: %w", err)
	}
	if q.getBankRouteStmt, err = db.PrepareContext(ctx, GetBankRoute); err != nil {
		return nil, fmt.Errorf("error preparing query GetBankRoute: %w", err)
	}
	if q.getListRefundSettleByStatusStmt, err = db.PrepareContext(ctx, GetListRefundSettleByStatus); err != nil {
		return nil, fmt.Errorf("error preparing query GetListRefundSettleByStatus: %w", err)
	}
	if q.getListRefundSettleIDHasExpiredStmt, err = db.PrepareContext(ctx, GetListRefundSettleIDHasExpired); err != nil {
		return nil, fmt.Errorf("error preparing query GetListRefundSettleIDHasExpired: %w", err)
	}
	if q.getOrderStmt, err = db.PrepareContext(ctx, GetOrder); err != nil {
		return nil, fmt.Errorf("error preparing query GetOrder: %w", err)
	}
	if q.getOrderByAppTransTypeAndStageStmt, err = db.PrepareContext(ctx, GetOrderByAppTransTypeAndStage); err != nil {
		return nil, fmt.Errorf("error preparing query GetOrderByAppTransTypeAndStage: %w", err)
	}
	if q.getOrderByIDStmt, err = db.PrepareContext(ctx, GetOrderByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetOrderByID: %w", err)
	}
	if q.getOrderByRefundIDAndTypeAndStageStmt, err = db.PrepareContext(ctx, GetOrderByRefundIDAndTypeAndStage); err != nil {
		return nil, fmt.Errorf("error preparing query GetOrderByRefundIDAndTypeAndStage: %w", err)
	}
	if q.getOrderBySettleIDAndTypeAndStageStmt, err = db.PrepareContext(ctx, GetOrderBySettleIDAndTypeAndStage); err != nil {
		return nil, fmt.Errorf("error preparing query GetOrderBySettleIDAndTypeAndStage: %w", err)
	}
	if q.getPartnerByIDStmt, err = db.PrepareContext(ctx, GetPartnerByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetPartnerByID: %w", err)
	}
	if q.getPartnersStmt, err = db.PrepareContext(ctx, GetPartners); err != nil {
		return nil, fmt.Errorf("error preparing query GetPartners: %w", err)
	}
	if q.getPaymentLogByIDStmt, err = db.PrepareContext(ctx, GetPaymentLogByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetPaymentLogByID: %w", err)
	}
	if q.getPaymentLogByOrderIDStmt, err = db.PrepareContext(ctx, GetPaymentLogByOrderID); err != nil {
		return nil, fmt.Errorf("error preparing query GetPaymentLogByOrderID: %w", err)
	}
	if q.getPaymentLogByPaymentNoStmt, err = db.PrepareContext(ctx, GetPaymentLogByPaymentNo); err != nil {
		return nil, fmt.Errorf("error preparing query GetPaymentLogByPaymentNo: %w", err)
	}
	if q.getPaymentLogByZPTransIDStmt, err = db.PrepareContext(ctx, GetPaymentLogByZPTransID); err != nil {
		return nil, fmt.Errorf("error preparing query GetPaymentLogByZPTransID: %w", err)
	}
	if q.getRefundLogStmt, err = db.PrepareContext(ctx, GetRefundLog); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLog: %w", err)
	}
	if q.getRefundLogByRefundIDStmt, err = db.PrepareContext(ctx, GetRefundLogByRefundID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLogByRefundID: %w", err)
	}
	if q.getRefundLogForUpdateStmt, err = db.PrepareContext(ctx, GetRefundLogForUpdate); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLogForUpdate: %w", err)
	}
	if q.getRefundLogsByProcessTypeAndZpTransIDStmt, err = db.PrepareContext(ctx, GetRefundLogsByProcessTypeAndZpTransID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLogsByProcessTypeAndZpTransID: %w", err)
	}
	if q.getRefundLogsByZPTransIDStmt, err = db.PrepareContext(ctx, GetRefundLogsByZPTransID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLogsByZPTransID: %w", err)
	}
	if q.getRefundLogsExpiredByZPTransIDStmt, err = db.PrepareContext(ctx, GetRefundLogsExpiredByZPTransID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLogsExpiredByZPTransID: %w", err)
	}
	if q.getRefundLogsExpiredByZPTransIDForUpdateStmt, err = db.PrepareContext(ctx, GetRefundLogsExpiredByZPTransIDForUpdate); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundLogsExpiredByZPTransIDForUpdate: %w", err)
	}
	if q.getRefundSettleStmt, err = db.PrepareContext(ctx, GetRefundSettle); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundSettle: %w", err)
	}
	if q.getRefundSettleByIDStmt, err = db.PrepareContext(ctx, GetRefundSettleByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundSettleByID: %w", err)
	}
	if q.getRefundSettleForUpdateStmt, err = db.PrepareContext(ctx, GetRefundSettleForUpdate); err != nil {
		return nil, fmt.Errorf("error preparing query GetRefundSettleForUpdate: %w", err)
	}
	if q.listOrderByTypeSettleAndStageStmt, err = db.PrepareContext(ctx, ListOrderByTypeSettleAndStage); err != nil {
		return nil, fmt.Errorf("error preparing query ListOrderByTypeSettleAndStage: %w", err)
	}
	if q.updateBankRouteStmt, err = db.PrepareContext(ctx, UpdateBankRoute); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateBankRoute: %w", err)
	}
	if q.updateCommissionStmt, err = db.PrepareContext(ctx, UpdateCommission); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateCommission: %w", err)
	}
	if q.updateOrderStatusStmt, err = db.PrepareContext(ctx, UpdateOrderStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateOrderStatus: %w", err)
	}
	if q.updateOrderStatusAndExtraStmt, err = db.PrepareContext(ctx, UpdateOrderStatusAndExtra); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateOrderStatusAndExtra: %w", err)
	}
	if q.updatePartnerTransIDStmt, err = db.PrepareContext(ctx, UpdatePartnerTransID); err != nil {
		return nil, fmt.Errorf("error preparing query UpdatePartnerTransID: %w", err)
	}
	if q.updatePaymentLogStmt, err = db.PrepareContext(ctx, UpdatePaymentLog); err != nil {
		return nil, fmt.Errorf("error preparing query UpdatePaymentLog: %w", err)
	}
	if q.updatePaymentLogOrderIDsStmt, err = db.PrepareContext(ctx, UpdatePaymentLogOrderIDs); err != nil {
		return nil, fmt.Errorf("error preparing query UpdatePaymentLogOrderIDs: %w", err)
	}
	if q.updatePaymentLogStatusStmt, err = db.PrepareContext(ctx, UpdatePaymentLogStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdatePaymentLogStatus: %w", err)
	}
	if q.updateRefundLogStmt, err = db.PrepareContext(ctx, UpdateRefundLog); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundLog: %w", err)
	}
	if q.updateRefundLogCompleteStmt, err = db.PrepareContext(ctx, UpdateRefundLogComplete); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundLogComplete: %w", err)
	}
	if q.updateRefundLogsProcessTypeByIDsStmt, err = db.PrepareContext(ctx, UpdateRefundLogsProcessTypeByIDs); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundLogsProcessTypeByIDs: %w", err)
	}
	if q.updateRefundPaybackInfoStmt, err = db.PrepareContext(ctx, UpdateRefundPaybackInfo); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundPaybackInfo: %w", err)
	}
	if q.updateRefundSettleStmt, err = db.PrepareContext(ctx, UpdateRefundSettle); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundSettle: %w", err)
	}
	if q.updateRefundSettleEventStmt, err = db.PrepareContext(ctx, UpdateRefundSettleEvent); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundSettleEvent: %w", err)
	}
	if q.updateRefundSettleReconStmt, err = db.PrepareContext(ctx, UpdateRefundSettleRecon); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundSettleRecon: %w", err)
	}
	if q.updateRefundSettleStatusStmt, err = db.PrepareContext(ctx, UpdateRefundSettleStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundSettleStatus: %w", err)
	}
	if q.updateRefundSettlementAmountStmt, err = db.PrepareContext(ctx, UpdateRefundSettlementAmount); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundSettlementAmount: %w", err)
	}
	if q.updateRefundTopupsAmountStmt, err = db.PrepareContext(ctx, UpdateRefundTopupsAmount); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRefundTopupsAmount: %w", err)
	}
	return &q, nil
}

func (q *Queries) Close() error {
	var err error
	if q.createBankRouteStmt != nil {
		if cerr := q.createBankRouteStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createBankRouteStmt: %w", cerr)
		}
	}
	if q.createPartnerStmt != nil {
		if cerr := q.createPartnerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createPartnerStmt: %w", cerr)
		}
	}
	if q.createPaymentLogStmt != nil {
		if cerr := q.createPaymentLogStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createPaymentLogStmt: %w", cerr)
		}
	}
	if q.createRefundFundbackOrderStmt != nil {
		if cerr := q.createRefundFundbackOrderStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createRefundFundbackOrderStmt: %w", cerr)
		}
	}
	if q.createRefundLogStmt != nil {
		if cerr := q.createRefundLogStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createRefundLogStmt: %w", cerr)
		}
	}
	if q.createRefundSettleStmt != nil {
		if cerr := q.createRefundSettleStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createRefundSettleStmt: %w", cerr)
		}
	}
	if q.createRefundSettleOrderStmt != nil {
		if cerr := q.createRefundSettleOrderStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createRefundSettleOrderStmt: %w", cerr)
		}
	}
	if q.createRefundTopupOrderStmt != nil {
		if cerr := q.createRefundTopupOrderStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createRefundTopupOrderStmt: %w", cerr)
		}
	}
	if q.createRepaymentOrderStmt != nil {
		if cerr := q.createRepaymentOrderStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createRepaymentOrderStmt: %w", cerr)
		}
	}
	if q.getBankRouteStmt != nil {
		if cerr := q.getBankRouteStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getBankRouteStmt: %w", cerr)
		}
	}
	if q.getListRefundSettleByStatusStmt != nil {
		if cerr := q.getListRefundSettleByStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getListRefundSettleByStatusStmt: %w", cerr)
		}
	}
	if q.getListRefundSettleIDHasExpiredStmt != nil {
		if cerr := q.getListRefundSettleIDHasExpiredStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getListRefundSettleIDHasExpiredStmt: %w", cerr)
		}
	}
	if q.getOrderStmt != nil {
		if cerr := q.getOrderStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOrderStmt: %w", cerr)
		}
	}
	if q.getOrderByAppTransTypeAndStageStmt != nil {
		if cerr := q.getOrderByAppTransTypeAndStageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOrderByAppTransTypeAndStageStmt: %w", cerr)
		}
	}
	if q.getOrderByIDStmt != nil {
		if cerr := q.getOrderByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOrderByIDStmt: %w", cerr)
		}
	}
	if q.getOrderByRefundIDAndTypeAndStageStmt != nil {
		if cerr := q.getOrderByRefundIDAndTypeAndStageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOrderByRefundIDAndTypeAndStageStmt: %w", cerr)
		}
	}
	if q.getOrderBySettleIDAndTypeAndStageStmt != nil {
		if cerr := q.getOrderBySettleIDAndTypeAndStageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOrderBySettleIDAndTypeAndStageStmt: %w", cerr)
		}
	}
	if q.getPartnerByIDStmt != nil {
		if cerr := q.getPartnerByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPartnerByIDStmt: %w", cerr)
		}
	}
	if q.getPartnersStmt != nil {
		if cerr := q.getPartnersStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPartnersStmt: %w", cerr)
		}
	}
	if q.getPaymentLogByIDStmt != nil {
		if cerr := q.getPaymentLogByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPaymentLogByIDStmt: %w", cerr)
		}
	}
	if q.getPaymentLogByOrderIDStmt != nil {
		if cerr := q.getPaymentLogByOrderIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPaymentLogByOrderIDStmt: %w", cerr)
		}
	}
	if q.getPaymentLogByPaymentNoStmt != nil {
		if cerr := q.getPaymentLogByPaymentNoStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPaymentLogByPaymentNoStmt: %w", cerr)
		}
	}
	if q.getPaymentLogByZPTransIDStmt != nil {
		if cerr := q.getPaymentLogByZPTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPaymentLogByZPTransIDStmt: %w", cerr)
		}
	}
	if q.getRefundLogStmt != nil {
		if cerr := q.getRefundLogStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogStmt: %w", cerr)
		}
	}
	if q.getRefundLogByRefundIDStmt != nil {
		if cerr := q.getRefundLogByRefundIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogByRefundIDStmt: %w", cerr)
		}
	}
	if q.getRefundLogForUpdateStmt != nil {
		if cerr := q.getRefundLogForUpdateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogForUpdateStmt: %w", cerr)
		}
	}
	if q.getRefundLogsByProcessTypeAndZpTransIDStmt != nil {
		if cerr := q.getRefundLogsByProcessTypeAndZpTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogsByProcessTypeAndZpTransIDStmt: %w", cerr)
		}
	}
	if q.getRefundLogsByZPTransIDStmt != nil {
		if cerr := q.getRefundLogsByZPTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogsByZPTransIDStmt: %w", cerr)
		}
	}
	if q.getRefundLogsExpiredByZPTransIDStmt != nil {
		if cerr := q.getRefundLogsExpiredByZPTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogsExpiredByZPTransIDStmt: %w", cerr)
		}
	}
	if q.getRefundLogsExpiredByZPTransIDForUpdateStmt != nil {
		if cerr := q.getRefundLogsExpiredByZPTransIDForUpdateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundLogsExpiredByZPTransIDForUpdateStmt: %w", cerr)
		}
	}
	if q.getRefundSettleStmt != nil {
		if cerr := q.getRefundSettleStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundSettleStmt: %w", cerr)
		}
	}
	if q.getRefundSettleByIDStmt != nil {
		if cerr := q.getRefundSettleByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundSettleByIDStmt: %w", cerr)
		}
	}
	if q.getRefundSettleForUpdateStmt != nil {
		if cerr := q.getRefundSettleForUpdateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRefundSettleForUpdateStmt: %w", cerr)
		}
	}
	if q.listOrderByTypeSettleAndStageStmt != nil {
		if cerr := q.listOrderByTypeSettleAndStageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listOrderByTypeSettleAndStageStmt: %w", cerr)
		}
	}
	if q.updateBankRouteStmt != nil {
		if cerr := q.updateBankRouteStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateBankRouteStmt: %w", cerr)
		}
	}
	if q.updateCommissionStmt != nil {
		if cerr := q.updateCommissionStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateCommissionStmt: %w", cerr)
		}
	}
	if q.updateOrderStatusStmt != nil {
		if cerr := q.updateOrderStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateOrderStatusStmt: %w", cerr)
		}
	}
	if q.updateOrderStatusAndExtraStmt != nil {
		if cerr := q.updateOrderStatusAndExtraStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateOrderStatusAndExtraStmt: %w", cerr)
		}
	}
	if q.updatePartnerTransIDStmt != nil {
		if cerr := q.updatePartnerTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updatePartnerTransIDStmt: %w", cerr)
		}
	}
	if q.updatePaymentLogStmt != nil {
		if cerr := q.updatePaymentLogStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updatePaymentLogStmt: %w", cerr)
		}
	}
	if q.updatePaymentLogOrderIDsStmt != nil {
		if cerr := q.updatePaymentLogOrderIDsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updatePaymentLogOrderIDsStmt: %w", cerr)
		}
	}
	if q.updatePaymentLogStatusStmt != nil {
		if cerr := q.updatePaymentLogStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updatePaymentLogStatusStmt: %w", cerr)
		}
	}
	if q.updateRefundLogStmt != nil {
		if cerr := q.updateRefundLogStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundLogStmt: %w", cerr)
		}
	}
	if q.updateRefundLogCompleteStmt != nil {
		if cerr := q.updateRefundLogCompleteStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundLogCompleteStmt: %w", cerr)
		}
	}
	if q.updateRefundLogsProcessTypeByIDsStmt != nil {
		if cerr := q.updateRefundLogsProcessTypeByIDsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundLogsProcessTypeByIDsStmt: %w", cerr)
		}
	}
	if q.updateRefundPaybackInfoStmt != nil {
		if cerr := q.updateRefundPaybackInfoStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundPaybackInfoStmt: %w", cerr)
		}
	}
	if q.updateRefundSettleStmt != nil {
		if cerr := q.updateRefundSettleStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundSettleStmt: %w", cerr)
		}
	}
	if q.updateRefundSettleEventStmt != nil {
		if cerr := q.updateRefundSettleEventStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundSettleEventStmt: %w", cerr)
		}
	}
	if q.updateRefundSettleReconStmt != nil {
		if cerr := q.updateRefundSettleReconStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundSettleReconStmt: %w", cerr)
		}
	}
	if q.updateRefundSettleStatusStmt != nil {
		if cerr := q.updateRefundSettleStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundSettleStatusStmt: %w", cerr)
		}
	}
	if q.updateRefundSettlementAmountStmt != nil {
		if cerr := q.updateRefundSettlementAmountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundSettlementAmountStmt: %w", cerr)
		}
	}
	if q.updateRefundTopupsAmountStmt != nil {
		if cerr := q.updateRefundTopupsAmountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRefundTopupsAmountStmt: %w", cerr)
		}
	}
	return err
}

func (q *Queries) exec(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (sql.Result, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).ExecContext(ctx, args...)
	case stmt != nil:
		return stmt.ExecContext(ctx, args...)
	default:
		return q.db.ExecContext(ctx, query, args...)
	}
}

func (q *Queries) query(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (*sql.Rows, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryContext(ctx, args...)
	default:
		return q.db.QueryContext(ctx, query, args...)
	}
}

func (q *Queries) queryRow(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) *sql.Row {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryRowContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryRowContext(ctx, args...)
	default:
		return q.db.QueryRowContext(ctx, query, args...)
	}
}

type Queries struct {
	db                                           DBTX
	tx                                           *sql.Tx
	createBankRouteStmt                          *sql.Stmt
	createPartnerStmt                            *sql.Stmt
	createPaymentLogStmt                         *sql.Stmt
	createRefundFundbackOrderStmt                *sql.Stmt
	createRefundLogStmt                          *sql.Stmt
	createRefundSettleStmt                       *sql.Stmt
	createRefundSettleOrderStmt                  *sql.Stmt
	createRefundTopupOrderStmt                   *sql.Stmt
	createRepaymentOrderStmt                     *sql.Stmt
	getBankRouteStmt                             *sql.Stmt
	getListRefundSettleByStatusStmt              *sql.Stmt
	getListRefundSettleIDHasExpiredStmt          *sql.Stmt
	getOrderStmt                                 *sql.Stmt
	getOrderByAppTransTypeAndStageStmt           *sql.Stmt
	getOrderByIDStmt                             *sql.Stmt
	getOrderByRefundIDAndTypeAndStageStmt        *sql.Stmt
	getOrderBySettleIDAndTypeAndStageStmt        *sql.Stmt
	getPartnerByIDStmt                           *sql.Stmt
	getPartnersStmt                              *sql.Stmt
	getPaymentLogByIDStmt                        *sql.Stmt
	getPaymentLogByOrderIDStmt                   *sql.Stmt
	getPaymentLogByPaymentNoStmt                 *sql.Stmt
	getPaymentLogByZPTransIDStmt                 *sql.Stmt
	getRefundLogStmt                             *sql.Stmt
	getRefundLogByRefundIDStmt                   *sql.Stmt
	getRefundLogForUpdateStmt                    *sql.Stmt
	getRefundLogsByProcessTypeAndZpTransIDStmt   *sql.Stmt
	getRefundLogsByZPTransIDStmt                 *sql.Stmt
	getRefundLogsExpiredByZPTransIDStmt          *sql.Stmt
	getRefundLogsExpiredByZPTransIDForUpdateStmt *sql.Stmt
	getRefundSettleStmt                          *sql.Stmt
	getRefundSettleByIDStmt                      *sql.Stmt
	getRefundSettleForUpdateStmt                 *sql.Stmt
	listOrderByTypeSettleAndStageStmt            *sql.Stmt
	updateBankRouteStmt                          *sql.Stmt
	updateCommissionStmt                         *sql.Stmt
	updateOrderStatusStmt                        *sql.Stmt
	updateOrderStatusAndExtraStmt                *sql.Stmt
	updatePartnerTransIDStmt                     *sql.Stmt
	updatePaymentLogStmt                         *sql.Stmt
	updatePaymentLogOrderIDsStmt                 *sql.Stmt
	updatePaymentLogStatusStmt                   *sql.Stmt
	updateRefundLogStmt                          *sql.Stmt
	updateRefundLogCompleteStmt                  *sql.Stmt
	updateRefundLogsProcessTypeByIDsStmt         *sql.Stmt
	updateRefundPaybackInfoStmt                  *sql.Stmt
	updateRefundSettleStmt                       *sql.Stmt
	updateRefundSettleEventStmt                  *sql.Stmt
	updateRefundSettleReconStmt                  *sql.Stmt
	updateRefundSettleStatusStmt                 *sql.Stmt
	updateRefundSettlementAmountStmt             *sql.Stmt
	updateRefundTopupsAmountStmt                 *sql.Stmt
}

func (q *Queries) WithTx(tx *sql.Tx) *Queries {
	return &Queries{
		db:                                           tx,
		tx:                                           tx,
		createBankRouteStmt:                          q.createBankRouteStmt,
		createPartnerStmt:                            q.createPartnerStmt,
		createPaymentLogStmt:                         q.createPaymentLogStmt,
		createRefundFundbackOrderStmt:                q.createRefundFundbackOrderStmt,
		createRefundLogStmt:                          q.createRefundLogStmt,
		createRefundSettleStmt:                       q.createRefundSettleStmt,
		createRefundSettleOrderStmt:                  q.createRefundSettleOrderStmt,
		createRefundTopupOrderStmt:                   q.createRefundTopupOrderStmt,
		createRepaymentOrderStmt:                     q.createRepaymentOrderStmt,
		getBankRouteStmt:                             q.getBankRouteStmt,
		getListRefundSettleByStatusStmt:              q.getListRefundSettleByStatusStmt,
		getListRefundSettleIDHasExpiredStmt:          q.getListRefundSettleIDHasExpiredStmt,
		getOrderStmt:                                 q.getOrderStmt,
		getOrderByAppTransTypeAndStageStmt:           q.getOrderByAppTransTypeAndStageStmt,
		getOrderByIDStmt:                             q.getOrderByIDStmt,
		getOrderByRefundIDAndTypeAndStageStmt:        q.getOrderByRefundIDAndTypeAndStageStmt,
		getOrderBySettleIDAndTypeAndStageStmt:        q.getOrderBySettleIDAndTypeAndStageStmt,
		getPartnerByIDStmt:                           q.getPartnerByIDStmt,
		getPartnersStmt:                              q.getPartnersStmt,
		getPaymentLogByIDStmt:                        q.getPaymentLogByIDStmt,
		getPaymentLogByOrderIDStmt:                   q.getPaymentLogByOrderIDStmt,
		getPaymentLogByPaymentNoStmt:                 q.getPaymentLogByPaymentNoStmt,
		getPaymentLogByZPTransIDStmt:                 q.getPaymentLogByZPTransIDStmt,
		getRefundLogStmt:                             q.getRefundLogStmt,
		getRefundLogByRefundIDStmt:                   q.getRefundLogByRefundIDStmt,
		getRefundLogForUpdateStmt:                    q.getRefundLogForUpdateStmt,
		getRefundLogsByProcessTypeAndZpTransIDStmt:   q.getRefundLogsByProcessTypeAndZpTransIDStmt,
		getRefundLogsByZPTransIDStmt:                 q.getRefundLogsByZPTransIDStmt,
		getRefundLogsExpiredByZPTransIDStmt:          q.getRefundLogsExpiredByZPTransIDStmt,
		getRefundLogsExpiredByZPTransIDForUpdateStmt: q.getRefundLogsExpiredByZPTransIDForUpdateStmt,
		getRefundSettleStmt:                          q.getRefundSettleStmt,
		getRefundSettleByIDStmt:                      q.getRefundSettleByIDStmt,
		getRefundSettleForUpdateStmt:                 q.getRefundSettleForUpdateStmt,
		listOrderByTypeSettleAndStageStmt:            q.listOrderByTypeSettleAndStageStmt,
		updateBankRouteStmt:                          q.updateBankRouteStmt,
		updateCommissionStmt:                         q.updateCommissionStmt,
		updateOrderStatusStmt:                        q.updateOrderStatusStmt,
		updateOrderStatusAndExtraStmt:                q.updateOrderStatusAndExtraStmt,
		updatePartnerTransIDStmt:                     q.updatePartnerTransIDStmt,
		updatePaymentLogStmt:                         q.updatePaymentLogStmt,
		updatePaymentLogOrderIDsStmt:                 q.updatePaymentLogOrderIDsStmt,
		updatePaymentLogStatusStmt:                   q.updatePaymentLogStatusStmt,
		updateRefundLogStmt:                          q.updateRefundLogStmt,
		updateRefundLogCompleteStmt:                  q.updateRefundLogCompleteStmt,
		updateRefundLogsProcessTypeByIDsStmt:         q.updateRefundLogsProcessTypeByIDsStmt,
		updateRefundPaybackInfoStmt:                  q.updateRefundPaybackInfoStmt,
		updateRefundSettleStmt:                       q.updateRefundSettleStmt,
		updateRefundSettleEventStmt:                  q.updateRefundSettleEventStmt,
		updateRefundSettleReconStmt:                  q.updateRefundSettleReconStmt,
		updateRefundSettleStatusStmt:                 q.updateRefundSettleStatusStmt,
		updateRefundSettlementAmountStmt:             q.updateRefundSettlementAmountStmt,
		updateRefundTopupsAmountStmt:                 q.updateRefundTopupsAmountStmt,
	}
}
