// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: bank_route.sql

package sqlc

import (
	"context"
)

const CreateBankRoute = `-- name: CreateBankRoute :exec
INSERT INTO bank_route (internal_bank_code,
                        partner_bank_code,
                        bank_account_name,
                        bank_account_number,
                        trans_type,
                        status)
VALUES (?, ?, ?, ?, ?, ?)
`

type CreateBankRouteParams struct {
	InternalBankCode  string          `json:"internal_bank_code"`
	PartnerBankCode   string          `json:"partner_bank_code"`
	BankAccountName   string          `json:"bank_account_name"`
	BankAccountNumber string          `json:"bank_account_number"`
	TransType         int32           `json:"trans_type"`
	Status            BankRouteStatus `json:"status"`
}

func (q *Queries) CreateBankRoute(ctx context.Context, arg *CreateBankRouteParams) error {
	_, err := q.exec(ctx, q.createBankRouteStmt, CreateBankRoute,
		arg.InternalBankCode,
		arg.PartnerBankCode,
		arg.BankAccountName,
		arg.BankAccountNumber,
		arg.TransType,
		arg.Status,
	)
	return err
}

const GetBankRoute = `-- name: GetBankRoute :one
SELECT id, internal_bank_code, partner_bank_code, bank_account_name, bank_account_number, trans_type, status, created_at, updated_at
FROM bank_route
WHERE internal_bank_code = ?
  AND trans_type = ?
  AND status = ?
`

type GetBankRouteParams struct {
	InternalBankCode string          `json:"internal_bank_code"`
	TransType        int32           `json:"trans_type"`
	Status           BankRouteStatus `json:"status"`
}

func (q *Queries) GetBankRoute(ctx context.Context, arg *GetBankRouteParams) (*BankRoute, error) {
	row := q.queryRow(ctx, q.getBankRouteStmt, GetBankRoute, arg.InternalBankCode, arg.TransType, arg.Status)
	var i BankRoute
	err := row.Scan(
		&i.ID,
		&i.InternalBankCode,
		&i.PartnerBankCode,
		&i.BankAccountName,
		&i.BankAccountNumber,
		&i.TransType,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateBankRoute = `-- name: UpdateBankRoute :exec
UPDATE bank_route
SET status = ?
WHERE internal_bank_code = ?
  AND trans_type = ?
`

type UpdateBankRouteParams struct {
	Status           BankRouteStatus `json:"status"`
	InternalBankCode string          `json:"internal_bank_code"`
	TransType        int32           `json:"trans_type"`
}

func (q *Queries) UpdateBankRoute(ctx context.Context, arg *UpdateBankRouteParams) error {
	_, err := q.exec(ctx, q.updateBankRouteStmt, UpdateBankRoute, arg.Status, arg.InternalBankCode, arg.TransType)
	return err
}
