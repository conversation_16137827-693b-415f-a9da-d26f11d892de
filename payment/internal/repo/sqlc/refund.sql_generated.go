// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: refund.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
	"strings"
)

const CreateRefundLog = `-- name: CreateRefundLog :execresult
INSERT INTO refund_logs(
                        zp_trans_id,
                        refund_id,
                        amount,
                        app_trans_id,
                        app_id,
                        payment_description,
                        refund_type,
                        status,
                        error_code,
                        error_message,
                        process_type,
                        extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateRefundLogParams struct {
	ZpTransID          int64                     `json:"zp_trans_id"`
	RefundID           int64                     `json:"refund_id"`
	Amount             int64                     `json:"amount"`
	AppTransID         string                    `json:"app_trans_id"`
	AppID              int32                     `json:"app_id"`
	PaymentDescription string                    `json:"payment_description"`
	RefundType         RefundLogsRefundType      `json:"refund_type"`
	Status             RefundLogsStatus          `json:"status"`
	ErrorCode          string                    `json:"error_code"`
	ErrorMessage       string                    `json:"error_message"`
	ProcessType        NullRefundLogsProcessType `json:"process_type"`
	Extra              json.RawMessage           `json:"extra"`
}

func (q *Queries) CreateRefundLog(ctx context.Context, arg *CreateRefundLogParams) (sql.Result, error) {
	return q.exec(ctx, q.createRefundLogStmt, CreateRefundLog,
		arg.ZpTransID,
		arg.RefundID,
		arg.Amount,
		arg.AppTransID,
		arg.AppID,
		arg.PaymentDescription,
		arg.RefundType,
		arg.Status,
		arg.ErrorCode,
		arg.ErrorMessage,
		arg.ProcessType,
		arg.Extra,
	)
}

const CreateRefundSettle = `-- name: CreateRefundSettle :execresult
INSERT INTO refund_settle(
  zp_trans_id,
  net_refund_amount,
  total_refund_amount,
  settlement_amount,
  event_version,
  metadata
) VALUES (?, ?, ?, ?, ?, ?)
`

type CreateRefundSettleParams struct {
	ZpTransID         int64           `json:"zp_trans_id"`
	NetRefundAmount   int64           `json:"net_refund_amount"`
	TotalRefundAmount int64           `json:"total_refund_amount"`
	SettlementAmount  int64           `json:"settlement_amount"`
	EventVersion      int32           `json:"event_version"`
	Metadata          json.RawMessage `json:"metadata"`
}

func (q *Queries) CreateRefundSettle(ctx context.Context, arg *CreateRefundSettleParams) (sql.Result, error) {
	return q.exec(ctx, q.createRefundSettleStmt, CreateRefundSettle,
		arg.ZpTransID,
		arg.NetRefundAmount,
		arg.TotalRefundAmount,
		arg.SettlementAmount,
		arg.EventVersion,
		arg.Metadata,
	)
}

const GetListRefundSettleByStatus = `-- name: GetListRefundSettleByStatus :many
SELECT id, status, zp_trans_id, net_refund_amount, total_refund_amount, settlement_amount, user_topup_amount, user_payback_amount, event_version, metadata, created_at, updated_at
FROM refund_settle
WHERE status = ? AND id > ? 
LIMIT ?
`

type GetListRefundSettleByStatusParams struct {
	Status RefundSettleStatus `json:"status"`
	IDGt   int64              `json:"id_gt"`
	Limit  int32              `json:"limit"`
}

func (q *Queries) GetListRefundSettleByStatus(ctx context.Context, arg *GetListRefundSettleByStatusParams) ([]*RefundSettle, error) {
	rows, err := q.query(ctx, q.getListRefundSettleByStatusStmt, GetListRefundSettleByStatus, arg.Status, arg.IDGt, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RefundSettle
	for rows.Next() {
		var i RefundSettle
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.ZpTransID,
			&i.NetRefundAmount,
			&i.TotalRefundAmount,
			&i.SettlementAmount,
			&i.UserTopupAmount,
			&i.UserPaybackAmount,
			&i.EventVersion,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetListRefundSettleIDHasExpired = `-- name: GetListRefundSettleIDHasExpired :many
SELECT rs.id
FROM refund_settle rs
WHERE rs.status = ?
    AND rs.id > ?
    AND EXISTS(
    SELECT 1 FROM refund_logs rl
    WHERE rl.zp_trans_id = rs.zp_trans_id
        AND rl.process_type = ?
        AND rl.deadline_at <= NOW())
LIMIT ?
`

type GetListRefundSettleIDHasExpiredParams struct {
	Status      RefundSettleStatus        `json:"status"`
	IDGt        int64                     `json:"id_gt"`
	ProcessType NullRefundLogsProcessType `json:"process_type"`
	Limit       int32                     `json:"limit"`
}

func (q *Queries) GetListRefundSettleIDHasExpired(ctx context.Context, arg *GetListRefundSettleIDHasExpiredParams) ([]int64, error) {
	rows, err := q.query(ctx, q.getListRefundSettleIDHasExpiredStmt, GetListRefundSettleIDHasExpired,
		arg.Status,
		arg.IDGt,
		arg.ProcessType,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		items = append(items, id)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRefundLog = `-- name: GetRefundLog :one
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE id = ?
`

func (q *Queries) GetRefundLog(ctx context.Context, id int64) (*RefundLogs, error) {
	row := q.queryRow(ctx, q.getRefundLogStmt, GetRefundLog, id)
	var i RefundLogs
	err := row.Scan(
		&i.ID,
		&i.ZpTransID,
		&i.RefundID,
		&i.Amount,
		&i.AppTransID,
		&i.AppID,
		&i.PaymentDescription,
		&i.RefundType,
		&i.Status,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.ProcessType,
		&i.DeadlineAt,
		&i.Extra,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetRefundLogByRefundID = `-- name: GetRefundLogByRefundID :one
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE refund_id = ?
`

func (q *Queries) GetRefundLogByRefundID(ctx context.Context, refundID int64) (*RefundLogs, error) {
	row := q.queryRow(ctx, q.getRefundLogByRefundIDStmt, GetRefundLogByRefundID, refundID)
	var i RefundLogs
	err := row.Scan(
		&i.ID,
		&i.ZpTransID,
		&i.RefundID,
		&i.Amount,
		&i.AppTransID,
		&i.AppID,
		&i.PaymentDescription,
		&i.RefundType,
		&i.Status,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.ProcessType,
		&i.DeadlineAt,
		&i.Extra,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetRefundLogForUpdate = `-- name: GetRefundLogForUpdate :one
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE id = ? FOR UPDATE
`

func (q *Queries) GetRefundLogForUpdate(ctx context.Context, id int64) (*RefundLogs, error) {
	row := q.queryRow(ctx, q.getRefundLogForUpdateStmt, GetRefundLogForUpdate, id)
	var i RefundLogs
	err := row.Scan(
		&i.ID,
		&i.ZpTransID,
		&i.RefundID,
		&i.Amount,
		&i.AppTransID,
		&i.AppID,
		&i.PaymentDescription,
		&i.RefundType,
		&i.Status,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.ProcessType,
		&i.DeadlineAt,
		&i.Extra,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetRefundLogsByProcessTypeAndZpTransID = `-- name: GetRefundLogsByProcessTypeAndZpTransID :many
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE zp_trans_id = ? AND process_type = ?
`

type GetRefundLogsByProcessTypeAndZpTransIDParams struct {
	ZpTransID   int64                     `json:"zp_trans_id"`
	ProcessType NullRefundLogsProcessType `json:"process_type"`
}

func (q *Queries) GetRefundLogsByProcessTypeAndZpTransID(ctx context.Context, arg *GetRefundLogsByProcessTypeAndZpTransIDParams) ([]*RefundLogs, error) {
	rows, err := q.query(ctx, q.getRefundLogsByProcessTypeAndZpTransIDStmt, GetRefundLogsByProcessTypeAndZpTransID, arg.ZpTransID, arg.ProcessType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RefundLogs
	for rows.Next() {
		var i RefundLogs
		if err := rows.Scan(
			&i.ID,
			&i.ZpTransID,
			&i.RefundID,
			&i.Amount,
			&i.AppTransID,
			&i.AppID,
			&i.PaymentDescription,
			&i.RefundType,
			&i.Status,
			&i.ErrorCode,
			&i.ErrorMessage,
			&i.ProcessType,
			&i.DeadlineAt,
			&i.Extra,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRefundLogsByZPTransID = `-- name: GetRefundLogsByZPTransID :many
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE zp_trans_id = ?
`

func (q *Queries) GetRefundLogsByZPTransID(ctx context.Context, zpTransID int64) ([]*RefundLogs, error) {
	rows, err := q.query(ctx, q.getRefundLogsByZPTransIDStmt, GetRefundLogsByZPTransID, zpTransID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RefundLogs
	for rows.Next() {
		var i RefundLogs
		if err := rows.Scan(
			&i.ID,
			&i.ZpTransID,
			&i.RefundID,
			&i.Amount,
			&i.AppTransID,
			&i.AppID,
			&i.PaymentDescription,
			&i.RefundType,
			&i.Status,
			&i.ErrorCode,
			&i.ErrorMessage,
			&i.ProcessType,
			&i.DeadlineAt,
			&i.Extra,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRefundLogsExpiredByZPTransID = `-- name: GetRefundLogsExpiredByZPTransID :many
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE zp_trans_id = ? 
    AND process_type = ? 
    AND deadline_at <= NOW()
`

type GetRefundLogsExpiredByZPTransIDParams struct {
	ZpTransID   int64                     `json:"zp_trans_id"`
	ProcessType NullRefundLogsProcessType `json:"process_type"`
}

func (q *Queries) GetRefundLogsExpiredByZPTransID(ctx context.Context, arg *GetRefundLogsExpiredByZPTransIDParams) ([]*RefundLogs, error) {
	rows, err := q.query(ctx, q.getRefundLogsExpiredByZPTransIDStmt, GetRefundLogsExpiredByZPTransID, arg.ZpTransID, arg.ProcessType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RefundLogs
	for rows.Next() {
		var i RefundLogs
		if err := rows.Scan(
			&i.ID,
			&i.ZpTransID,
			&i.RefundID,
			&i.Amount,
			&i.AppTransID,
			&i.AppID,
			&i.PaymentDescription,
			&i.RefundType,
			&i.Status,
			&i.ErrorCode,
			&i.ErrorMessage,
			&i.ProcessType,
			&i.DeadlineAt,
			&i.Extra,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRefundLogsExpiredByZPTransIDForUpdate = `-- name: GetRefundLogsExpiredByZPTransIDForUpdate :many
SELECT id, zp_trans_id, refund_id, amount, app_trans_id, app_id, payment_description, refund_type, status, error_code, error_message, process_type, deadline_at, extra, created_at, updated_at
FROM refund_logs
WHERE zp_trans_id = ? 
    AND process_type = ? 
    AND deadline_at <= NOW() 
FOR UPDATE
`

type GetRefundLogsExpiredByZPTransIDForUpdateParams struct {
	ZpTransID   int64                     `json:"zp_trans_id"`
	ProcessType NullRefundLogsProcessType `json:"process_type"`
}

func (q *Queries) GetRefundLogsExpiredByZPTransIDForUpdate(ctx context.Context, arg *GetRefundLogsExpiredByZPTransIDForUpdateParams) ([]*RefundLogs, error) {
	rows, err := q.query(ctx, q.getRefundLogsExpiredByZPTransIDForUpdateStmt, GetRefundLogsExpiredByZPTransIDForUpdate, arg.ZpTransID, arg.ProcessType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RefundLogs
	for rows.Next() {
		var i RefundLogs
		if err := rows.Scan(
			&i.ID,
			&i.ZpTransID,
			&i.RefundID,
			&i.Amount,
			&i.AppTransID,
			&i.AppID,
			&i.PaymentDescription,
			&i.RefundType,
			&i.Status,
			&i.ErrorCode,
			&i.ErrorMessage,
			&i.ProcessType,
			&i.DeadlineAt,
			&i.Extra,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRefundSettle = `-- name: GetRefundSettle :one
SELECT id, status, zp_trans_id, net_refund_amount, total_refund_amount, settlement_amount, user_topup_amount, user_payback_amount, event_version, metadata, created_at, updated_at
FROM refund_settle
WHERE zp_trans_id = ?
`

func (q *Queries) GetRefundSettle(ctx context.Context, zpTransID int64) (*RefundSettle, error) {
	row := q.queryRow(ctx, q.getRefundSettleStmt, GetRefundSettle, zpTransID)
	var i RefundSettle
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.ZpTransID,
		&i.NetRefundAmount,
		&i.TotalRefundAmount,
		&i.SettlementAmount,
		&i.UserTopupAmount,
		&i.UserPaybackAmount,
		&i.EventVersion,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetRefundSettleByID = `-- name: GetRefundSettleByID :one
SELECT id, status, zp_trans_id, net_refund_amount, total_refund_amount, settlement_amount, user_topup_amount, user_payback_amount, event_version, metadata, created_at, updated_at
FROM refund_settle
WHERE id = ?
`

func (q *Queries) GetRefundSettleByID(ctx context.Context, id int64) (*RefundSettle, error) {
	row := q.queryRow(ctx, q.getRefundSettleByIDStmt, GetRefundSettleByID, id)
	var i RefundSettle
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.ZpTransID,
		&i.NetRefundAmount,
		&i.TotalRefundAmount,
		&i.SettlementAmount,
		&i.UserTopupAmount,
		&i.UserPaybackAmount,
		&i.EventVersion,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetRefundSettleForUpdate = `-- name: GetRefundSettleForUpdate :one
SELECT id, status, zp_trans_id, net_refund_amount, total_refund_amount, settlement_amount, user_topup_amount, user_payback_amount, event_version, metadata, created_at, updated_at
FROM refund_settle
WHERE id = ? FOR UPDATE
`

func (q *Queries) GetRefundSettleForUpdate(ctx context.Context, id int64) (*RefundSettle, error) {
	row := q.queryRow(ctx, q.getRefundSettleForUpdateStmt, GetRefundSettleForUpdate, id)
	var i RefundSettle
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.ZpTransID,
		&i.NetRefundAmount,
		&i.TotalRefundAmount,
		&i.SettlementAmount,
		&i.UserTopupAmount,
		&i.UserPaybackAmount,
		&i.EventVersion,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateRefundLog = `-- name: UpdateRefundLog :execresult
UPDATE refund_logs
SET
    status=?,
    error_code=?,
    error_message=?,
    extra=?
WHERE id = ?
`

type UpdateRefundLogParams struct {
	Status       RefundLogsStatus `json:"status"`
	ErrorCode    string           `json:"error_code"`
	ErrorMessage string           `json:"error_message"`
	Extra        json.RawMessage  `json:"extra"`
	ID           int64            `json:"id"`
}

func (q *Queries) UpdateRefundLog(ctx context.Context, arg *UpdateRefundLogParams) (sql.Result, error) {
	return q.exec(ctx, q.updateRefundLogStmt, UpdateRefundLog,
		arg.Status,
		arg.ErrorCode,
		arg.ErrorMessage,
		arg.Extra,
		arg.ID,
	)
}

const UpdateRefundLogComplete = `-- name: UpdateRefundLogComplete :execresult
UPDATE refund_logs
SET status=?,
    deadline_at=?,
    extra=?
WHERE id = ?
`

type UpdateRefundLogCompleteParams struct {
	Status     RefundLogsStatus `json:"status"`
	DeadlineAt sql.NullTime     `json:"deadline_at"`
	Extra      json.RawMessage  `json:"extra"`
	ID         int64            `json:"id"`
}

func (q *Queries) UpdateRefundLogComplete(ctx context.Context, arg *UpdateRefundLogCompleteParams) (sql.Result, error) {
	return q.exec(ctx, q.updateRefundLogCompleteStmt, UpdateRefundLogComplete,
		arg.Status,
		arg.DeadlineAt,
		arg.Extra,
		arg.ID,
	)
}

const UpdateRefundLogsProcessTypeByIDs = `-- name: UpdateRefundLogsProcessTypeByIDs :exec
UPDATE refund_logs
SET process_type = ?
WHERE id IN (/*SLICE:ids*/?)
`

type UpdateRefundLogsProcessTypeByIDsParams struct {
	ProcessType NullRefundLogsProcessType `json:"process_type"`
	Ids         []int64                   `json:"ids"`
}

func (q *Queries) UpdateRefundLogsProcessTypeByIDs(ctx context.Context, arg *UpdateRefundLogsProcessTypeByIDsParams) error {
	query := UpdateRefundLogsProcessTypeByIDs
	var queryParams []interface{}
	queryParams = append(queryParams, arg.ProcessType)
	if len(arg.Ids) > 0 {
		for _, v := range arg.Ids {
			queryParams = append(queryParams, v)
		}
		query = strings.Replace(query, "/*SLICE:ids*/?", strings.Repeat(",?", len(arg.Ids))[1:], 1)
	} else {
		query = strings.Replace(query, "/*SLICE:ids*/?", "NULL", 1)
	}
	_, err := q.exec(ctx, nil, query, queryParams...)
	return err
}

const UpdateRefundPaybackInfo = `-- name: UpdateRefundPaybackInfo :exec
UPDATE refund_settle
SET status = ?,
    user_payback_amount = ?
WHERE id = ?
`

type UpdateRefundPaybackInfoParams struct {
	Status            RefundSettleStatus `json:"status"`
	UserPaybackAmount int64              `json:"user_payback_amount"`
	ID                int64              `json:"id"`
}

func (q *Queries) UpdateRefundPaybackInfo(ctx context.Context, arg *UpdateRefundPaybackInfoParams) error {
	_, err := q.exec(ctx, q.updateRefundPaybackInfoStmt, UpdateRefundPaybackInfo, arg.Status, arg.UserPaybackAmount, arg.ID)
	return err
}

const UpdateRefundSettle = `-- name: UpdateRefundSettle :execresult
UPDATE refund_settle
SET status=?,
    net_refund_amount = ?,
    total_refund_amount = ?,
    settlement_amount = ?,
    user_topup_amount = ?,
    user_payback_amount = ?,
    event_version = ?,
    metadata = ?
WHERE id = ?
`

type UpdateRefundSettleParams struct {
	Status            RefundSettleStatus `json:"status"`
	NetRefundAmount   int64              `json:"net_refund_amount"`
	TotalRefundAmount int64              `json:"total_refund_amount"`
	SettlementAmount  int64              `json:"settlement_amount"`
	UserTopupAmount   int64              `json:"user_topup_amount"`
	UserPaybackAmount int64              `json:"user_payback_amount"`
	EventVersion      int32              `json:"event_version"`
	Metadata          json.RawMessage    `json:"metadata"`
	ID                int64              `json:"id"`
}

func (q *Queries) UpdateRefundSettle(ctx context.Context, arg *UpdateRefundSettleParams) (sql.Result, error) {
	return q.exec(ctx, q.updateRefundSettleStmt, UpdateRefundSettle,
		arg.Status,
		arg.NetRefundAmount,
		arg.TotalRefundAmount,
		arg.SettlementAmount,
		arg.UserTopupAmount,
		arg.UserPaybackAmount,
		arg.EventVersion,
		arg.Metadata,
		arg.ID,
	)
}

const UpdateRefundSettleEvent = `-- name: UpdateRefundSettleEvent :execresult
UPDATE refund_settle
SET event_version = ?,
    metadata = ?
WHERE id = ? and event_version = ?
`

type UpdateRefundSettleEventParams struct {
	NewVersion int32           `json:"new_version"`
	Metadata   json.RawMessage `json:"metadata"`
	ID         int64           `json:"id"`
	OldVersion int32           `json:"old_version"`
}

func (q *Queries) UpdateRefundSettleEvent(ctx context.Context, arg *UpdateRefundSettleEventParams) (sql.Result, error) {
	return q.exec(ctx, q.updateRefundSettleEventStmt, UpdateRefundSettleEvent,
		arg.NewVersion,
		arg.Metadata,
		arg.ID,
		arg.OldVersion,
	)
}

const UpdateRefundSettleRecon = `-- name: UpdateRefundSettleRecon :exec
UPDATE refund_settle
SET status = ?,
    net_refund_amount = ?,
    total_refund_amount = ?,
    settlement_amount = ?
WHERE id = ?
`

type UpdateRefundSettleReconParams struct {
	Status            RefundSettleStatus `json:"status"`
	NetRefundAmount   int64              `json:"net_refund_amount"`
	TotalRefundAmount int64              `json:"total_refund_amount"`
	SettlementAmount  int64              `json:"settlement_amount"`
	ID                int64              `json:"id"`
}

func (q *Queries) UpdateRefundSettleRecon(ctx context.Context, arg *UpdateRefundSettleReconParams) error {
	_, err := q.exec(ctx, q.updateRefundSettleReconStmt, UpdateRefundSettleRecon,
		arg.Status,
		arg.NetRefundAmount,
		arg.TotalRefundAmount,
		arg.SettlementAmount,
		arg.ID,
	)
	return err
}

const UpdateRefundSettleStatus = `-- name: UpdateRefundSettleStatus :exec
UPDATE refund_settle
SET status = ?
WHERE id = ?
`

type UpdateRefundSettleStatusParams struct {
	Status RefundSettleStatus `json:"status"`
	ID     int64              `json:"id"`
}

func (q *Queries) UpdateRefundSettleStatus(ctx context.Context, arg *UpdateRefundSettleStatusParams) error {
	_, err := q.exec(ctx, q.updateRefundSettleStatusStmt, UpdateRefundSettleStatus, arg.Status, arg.ID)
	return err
}

const UpdateRefundSettlementAmount = `-- name: UpdateRefundSettlementAmount :exec
UPDATE refund_settle
SET settlement_amount = ?
WHERE id = ?
`

type UpdateRefundSettlementAmountParams struct {
	SettlementAmount int64 `json:"settlement_amount"`
	ID               int64 `json:"id"`
}

func (q *Queries) UpdateRefundSettlementAmount(ctx context.Context, arg *UpdateRefundSettlementAmountParams) error {
	_, err := q.exec(ctx, q.updateRefundSettlementAmountStmt, UpdateRefundSettlementAmount, arg.SettlementAmount, arg.ID)
	return err
}

const UpdateRefundTopupsAmount = `-- name: UpdateRefundTopupsAmount :exec
UPDATE refund_settle
SET user_topup_amount = ?
WHERE id = ?
`

type UpdateRefundTopupsAmountParams struct {
	UserTopupAmount int64 `json:"user_topup_amount"`
	ID              int64 `json:"id"`
}

func (q *Queries) UpdateRefundTopupsAmount(ctx context.Context, arg *UpdateRefundTopupsAmountParams) error {
	_, err := q.exec(ctx, q.updateRefundTopupsAmountStmt, UpdateRefundTopupsAmount, arg.UserTopupAmount, arg.ID)
	return err
}
