// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: partner.sql

package sqlc

import (
	"context"
)

const CreatePartner = `-- name: CreatePartner :exec
INSERT INTO partner (code, status)
values (?, ?)
`

type CreatePartnerParams struct {
	Code   string        `json:"code"`
	Status PartnerStatus `json:"status"`
}

func (q *Queries) CreatePartner(ctx context.Context, arg *CreatePartnerParams) error {
	_, err := q.exec(ctx, q.createPartnerStmt, CreatePartner, arg.Code, arg.Status)
	return err
}

const GetPartnerByID = `-- name: GetPartnerByID :one
SELECT id, code, status, created_at, updated_at
FROM partner
WHERE id = ?
`

func (q *Queries) GetPartnerByID(ctx context.Context, id int64) (*Partner, error) {
	row := q.queryRow(ctx, q.getPartnerByIDStmt, GetPartnerByID, id)
	var i Partner
	err := row.Scan(
		&i.ID,
		&i.Code,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetPartners = `-- name: GetPartners :many
SELECT id, code, status, created_at, updated_at
FROM partner
ORDER BY id
`

func (q *Queries) GetPartners(ctx context.Context) ([]*Partner, error) {
	rows, err := q.query(ctx, q.getPartnersStmt, GetPartners)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Partner
	for rows.Next() {
		var i Partner
		if err := rows.Scan(
			&i.ID,
			&i.Code,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
