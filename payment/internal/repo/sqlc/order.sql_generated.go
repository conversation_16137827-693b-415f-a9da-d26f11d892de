// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: order.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
)

const CreateRefundFundbackOrder = `-- name: CreateRefundFundbackOrder :execresult
INSERT INTO ` + "`" + `order` + "`" + ` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     type,
                     stage,
                     status,
                     settle_id,
                     zp_trans_token,
                     description,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateRefundFundbackOrderParams struct {
	ZalopayID    int64           `json:"zalopay_id"`
	PartnerCode  string          `json:"partner_code"`
	SystemID     int32           `json:"system_id"`
	AppTransID   string          `json:"app_trans_id"`
	AppID        int32           `json:"app_id"`
	Amount       int64           `json:"amount"`
	Type         OrderType       `json:"type"`
	Stage        sql.NullString  `json:"stage"`
	Status       OrderStatus     `json:"status"`
	SettleID     int64           `json:"settle_id"`
	ZpTransToken string          `json:"zp_trans_token"`
	Description  string          `json:"description"`
	Extra        json.RawMessage `json:"extra"`
}

func (q *Queries) CreateRefundFundbackOrder(ctx context.Context, arg *CreateRefundFundbackOrderParams) (sql.Result, error) {
	return q.exec(ctx, q.createRefundFundbackOrderStmt, CreateRefundFundbackOrder,
		arg.ZalopayID,
		arg.PartnerCode,
		arg.SystemID,
		arg.AppTransID,
		arg.AppID,
		arg.Amount,
		arg.Type,
		arg.Stage,
		arg.Status,
		arg.SettleID,
		arg.ZpTransToken,
		arg.Description,
		arg.Extra,
	)
}

const CreateRefundSettleOrder = `-- name: CreateRefundSettleOrder :execresult
INSERT INTO ` + "`" + `order` + "`" + ` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     type,
                     stage,
                     status,
                     refund_id,
                     settle_id,
                     zp_trans_token,
                     description,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateRefundSettleOrderParams struct {
	ZalopayID    int64           `json:"zalopay_id"`
	PartnerCode  string          `json:"partner_code"`
	SystemID     int32           `json:"system_id"`
	AppTransID   string          `json:"app_trans_id"`
	AppID        int32           `json:"app_id"`
	Amount       int64           `json:"amount"`
	Type         OrderType       `json:"type"`
	Stage        sql.NullString  `json:"stage"`
	Status       OrderStatus     `json:"status"`
	RefundID     int64           `json:"refund_id"`
	SettleID     int64           `json:"settle_id"`
	ZpTransToken string          `json:"zp_trans_token"`
	Description  string          `json:"description"`
	Extra        json.RawMessage `json:"extra"`
}

func (q *Queries) CreateRefundSettleOrder(ctx context.Context, arg *CreateRefundSettleOrderParams) (sql.Result, error) {
	return q.exec(ctx, q.createRefundSettleOrderStmt, CreateRefundSettleOrder,
		arg.ZalopayID,
		arg.PartnerCode,
		arg.SystemID,
		arg.AppTransID,
		arg.AppID,
		arg.Amount,
		arg.Type,
		arg.Stage,
		arg.Status,
		arg.RefundID,
		arg.SettleID,
		arg.ZpTransToken,
		arg.Description,
		arg.Extra,
	)
}

const CreateRefundTopupOrder = `-- name: CreateRefundTopupOrder :execresult
INSERT INTO ` + "`" + `order` + "`" + ` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     type,
                     stage,
                     status,
                     settle_id,
                     zp_trans_token,
                     description,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateRefundTopupOrderParams struct {
	ZalopayID    int64           `json:"zalopay_id"`
	PartnerCode  string          `json:"partner_code"`
	SystemID     int32           `json:"system_id"`
	AppTransID   string          `json:"app_trans_id"`
	AppID        int32           `json:"app_id"`
	Amount       int64           `json:"amount"`
	Type         OrderType       `json:"type"`
	Stage        sql.NullString  `json:"stage"`
	Status       OrderStatus     `json:"status"`
	SettleID     int64           `json:"settle_id"`
	ZpTransToken string          `json:"zp_trans_token"`
	Description  string          `json:"description"`
	Extra        json.RawMessage `json:"extra"`
}

func (q *Queries) CreateRefundTopupOrder(ctx context.Context, arg *CreateRefundTopupOrderParams) (sql.Result, error) {
	return q.exec(ctx, q.createRefundTopupOrderStmt, CreateRefundTopupOrder,
		arg.ZalopayID,
		arg.PartnerCode,
		arg.SystemID,
		arg.AppTransID,
		arg.AppID,
		arg.Amount,
		arg.Type,
		arg.Stage,
		arg.Status,
		arg.SettleID,
		arg.ZpTransToken,
		arg.Description,
		arg.Extra,
	)
}

const CreateRepaymentOrder = `-- name: CreateRepaymentOrder :execresult
INSERT INTO ` + "`" + `order` + "`" + ` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     status,
                     type,
                     zp_trans_token,
                     description,
                     statement_id,
                     statement_date,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateRepaymentOrderParams struct {
	ZalopayID     int64           `json:"zalopay_id"`
	PartnerCode   string          `json:"partner_code"`
	SystemID      int32           `json:"system_id"`
	AppTransID    string          `json:"app_trans_id"`
	AppID         int32           `json:"app_id"`
	Amount        int64           `json:"amount"`
	Status        OrderStatus     `json:"status"`
	Type          OrderType       `json:"type"`
	ZpTransToken  string          `json:"zp_trans_token"`
	Description   string          `json:"description"`
	StatementID   int64           `json:"statement_id"`
	StatementDate sql.NullTime    `json:"statement_date"`
	Extra         json.RawMessage `json:"extra"`
}

func (q *Queries) CreateRepaymentOrder(ctx context.Context, arg *CreateRepaymentOrderParams) (sql.Result, error) {
	return q.exec(ctx, q.createRepaymentOrderStmt, CreateRepaymentOrder,
		arg.ZalopayID,
		arg.PartnerCode,
		arg.SystemID,
		arg.AppTransID,
		arg.AppID,
		arg.Amount,
		arg.Status,
		arg.Type,
		arg.ZpTransToken,
		arg.Description,
		arg.StatementID,
		arg.StatementDate,
		arg.Extra,
	)
}

const GetOrder = `-- name: GetOrder :one
SELECT id, zalopay_id, partner_code, system_id, app_id, app_trans_id, zp_trans_id, zp_trans_token, refund_id, settle_id, amount, status, type, stage, extra, description, statement_id, statement_date, created_at, updated_at
FROM ` + "`" + `order` + "`" + `
WHERE app_trans_id = ?
  AND app_id = ?
  AND type = ?
`

type GetOrderParams struct {
	AppTransID string    `json:"app_trans_id"`
	AppID      int32     `json:"app_id"`
	Type       OrderType `json:"type"`
}

func (q *Queries) GetOrder(ctx context.Context, arg *GetOrderParams) (*Order, error) {
	row := q.queryRow(ctx, q.getOrderStmt, GetOrder, arg.AppTransID, arg.AppID, arg.Type)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.SystemID,
		&i.AppID,
		&i.AppTransID,
		&i.ZpTransID,
		&i.ZpTransToken,
		&i.RefundID,
		&i.SettleID,
		&i.Amount,
		&i.Status,
		&i.Type,
		&i.Stage,
		&i.Extra,
		&i.Description,
		&i.StatementID,
		&i.StatementDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetOrderByAppTransTypeAndStage = `-- name: GetOrderByAppTransTypeAndStage :one
SELECT id, zalopay_id, partner_code, system_id, app_id, app_trans_id, zp_trans_id, zp_trans_token, refund_id, settle_id, amount, status, type, stage, extra, description, statement_id, statement_date, created_at, updated_at
FROM ` + "`" + `order` + "`" + `
WHERE app_trans_id = ?
  AND app_id = ?
  AND type = ?
  AND stage = ?
`

type GetOrderByAppTransTypeAndStageParams struct {
	AppTransID string         `json:"app_trans_id"`
	AppID      int32          `json:"app_id"`
	Type       OrderType      `json:"type"`
	Stage      sql.NullString `json:"stage"`
}

func (q *Queries) GetOrderByAppTransTypeAndStage(ctx context.Context, arg *GetOrderByAppTransTypeAndStageParams) (*Order, error) {
	row := q.queryRow(ctx, q.getOrderByAppTransTypeAndStageStmt, GetOrderByAppTransTypeAndStage,
		arg.AppTransID,
		arg.AppID,
		arg.Type,
		arg.Stage,
	)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.SystemID,
		&i.AppID,
		&i.AppTransID,
		&i.ZpTransID,
		&i.ZpTransToken,
		&i.RefundID,
		&i.SettleID,
		&i.Amount,
		&i.Status,
		&i.Type,
		&i.Stage,
		&i.Extra,
		&i.Description,
		&i.StatementID,
		&i.StatementDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetOrderByID = `-- name: GetOrderByID :one
SELECT id, zalopay_id, partner_code, system_id, app_id, app_trans_id, zp_trans_id, zp_trans_token, refund_id, settle_id, amount, status, type, stage, extra, description, statement_id, statement_date, created_at, updated_at
FROM ` + "`" + `order` + "`" + `
WHERE id = ?
`

func (q *Queries) GetOrderByID(ctx context.Context, id int64) (*Order, error) {
	row := q.queryRow(ctx, q.getOrderByIDStmt, GetOrderByID, id)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.SystemID,
		&i.AppID,
		&i.AppTransID,
		&i.ZpTransID,
		&i.ZpTransToken,
		&i.RefundID,
		&i.SettleID,
		&i.Amount,
		&i.Status,
		&i.Type,
		&i.Stage,
		&i.Extra,
		&i.Description,
		&i.StatementID,
		&i.StatementDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetOrderByRefundIDAndTypeAndStage = `-- name: GetOrderByRefundIDAndTypeAndStage :one
SELECT id, zalopay_id, partner_code, system_id, app_id, app_trans_id, zp_trans_id, zp_trans_token, refund_id, settle_id, amount, status, type, stage, extra, description, statement_id, statement_date, created_at, updated_at
FROM ` + "`" + `order` + "`" + `
WHERE refund_id = ?
  AND type = ?
  AND stage = ?
`

type GetOrderByRefundIDAndTypeAndStageParams struct {
	RefundID int64          `json:"refund_id"`
	Type     OrderType      `json:"type"`
	Stage    sql.NullString `json:"stage"`
}

func (q *Queries) GetOrderByRefundIDAndTypeAndStage(ctx context.Context, arg *GetOrderByRefundIDAndTypeAndStageParams) (*Order, error) {
	row := q.queryRow(ctx, q.getOrderByRefundIDAndTypeAndStageStmt, GetOrderByRefundIDAndTypeAndStage, arg.RefundID, arg.Type, arg.Stage)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.SystemID,
		&i.AppID,
		&i.AppTransID,
		&i.ZpTransID,
		&i.ZpTransToken,
		&i.RefundID,
		&i.SettleID,
		&i.Amount,
		&i.Status,
		&i.Type,
		&i.Stage,
		&i.Extra,
		&i.Description,
		&i.StatementID,
		&i.StatementDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetOrderBySettleIDAndTypeAndStage = `-- name: GetOrderBySettleIDAndTypeAndStage :one
SELECT id, zalopay_id, partner_code, system_id, app_id, app_trans_id, zp_trans_id, zp_trans_token, refund_id, settle_id, amount, status, type, stage, extra, description, statement_id, statement_date, created_at, updated_at
FROM ` + "`" + `order` + "`" + `
WHERE refund_id = ?
  AND type = ?
  AND stage = ?
`

type GetOrderBySettleIDAndTypeAndStageParams struct {
	RefundID int64          `json:"refund_id"`
	Type     OrderType      `json:"type"`
	Stage    sql.NullString `json:"stage"`
}

func (q *Queries) GetOrderBySettleIDAndTypeAndStage(ctx context.Context, arg *GetOrderBySettleIDAndTypeAndStageParams) (*Order, error) {
	row := q.queryRow(ctx, q.getOrderBySettleIDAndTypeAndStageStmt, GetOrderBySettleIDAndTypeAndStage, arg.RefundID, arg.Type, arg.Stage)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.SystemID,
		&i.AppID,
		&i.AppTransID,
		&i.ZpTransID,
		&i.ZpTransToken,
		&i.RefundID,
		&i.SettleID,
		&i.Amount,
		&i.Status,
		&i.Type,
		&i.Stage,
		&i.Extra,
		&i.Description,
		&i.StatementID,
		&i.StatementDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const ListOrderByTypeSettleAndStage = `-- name: ListOrderByTypeSettleAndStage :many
SELECT id, zalopay_id, partner_code, system_id, app_id, app_trans_id, zp_trans_id, zp_trans_token, refund_id, settle_id, amount, status, type, stage, extra, description, statement_id, statement_date, created_at, updated_at
FROM ` + "`" + `order` + "`" + `
WHERE ` + "`" + `settle_id` + "`" + ` = ?
  AND ` + "`" + `type` + "`" + ` = ?
  AND ` + "`" + `stage` + "`" + ` = ?
`

type ListOrderByTypeSettleAndStageParams struct {
	SettleID int64          `json:"settle_id"`
	Type     OrderType      `json:"type"`
	Stage    sql.NullString `json:"stage"`
}

func (q *Queries) ListOrderByTypeSettleAndStage(ctx context.Context, arg *ListOrderByTypeSettleAndStageParams) ([]*Order, error) {
	rows, err := q.query(ctx, q.listOrderByTypeSettleAndStageStmt, ListOrderByTypeSettleAndStage, arg.SettleID, arg.Type, arg.Stage)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Order
	for rows.Next() {
		var i Order
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.PartnerCode,
			&i.SystemID,
			&i.AppID,
			&i.AppTransID,
			&i.ZpTransID,
			&i.ZpTransToken,
			&i.RefundID,
			&i.SettleID,
			&i.Amount,
			&i.Status,
			&i.Type,
			&i.Stage,
			&i.Extra,
			&i.Description,
			&i.StatementID,
			&i.StatementDate,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateOrderStatus = `-- name: UpdateOrderStatus :exec
UPDATE ` + "`" + `order` + "`" + `
SET status = ?,
    zp_trans_id = ?
WHERE id = ?
`

type UpdateOrderStatusParams struct {
	Status    OrderStatus `json:"status"`
	ZpTransID string      `json:"zp_trans_id"`
	ID        int64       `json:"id"`
}

func (q *Queries) UpdateOrderStatus(ctx context.Context, arg *UpdateOrderStatusParams) error {
	_, err := q.exec(ctx, q.updateOrderStatusStmt, UpdateOrderStatus, arg.Status, arg.ZpTransID, arg.ID)
	return err
}

const UpdateOrderStatusAndExtra = `-- name: UpdateOrderStatusAndExtra :exec
UPDATE ` + "`" + `order` + "`" + `
SET status = ?,
    zp_trans_id = ?,
    extra = ?
WHERE id = ?
`

type UpdateOrderStatusAndExtraParams struct {
	Status    OrderStatus     `json:"status"`
	ZpTransID string          `json:"zp_trans_id"`
	Extra     json.RawMessage `json:"extra"`
	ID        int64           `json:"id"`
}

func (q *Queries) UpdateOrderStatusAndExtra(ctx context.Context, arg *UpdateOrderStatusAndExtraParams) error {
	_, err := q.exec(ctx, q.updateOrderStatusAndExtraStmt, UpdateOrderStatusAndExtra,
		arg.Status,
		arg.ZpTransID,
		arg.Extra,
		arg.ID,
	)
	return err
}
