// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
	"database/sql"
)

type Querier interface {
	CreateBankRoute(ctx context.Context, arg *CreateBankRouteParams) error
	CreatePartner(ctx context.Context, arg *CreatePartnerParams) error
	CreatePaymentLog(ctx context.Context, arg *CreatePaymentLogParams) (sql.Result, error)
	CreateRefundFundbackOrder(ctx context.Context, arg *CreateRefundFundbackOrderParams) (sql.Result, error)
	CreateRefundLog(ctx context.Context, arg *CreateRefundLogParams) (sql.Result, error)
	CreateRefundSettle(ctx context.Context, arg *CreateRefundSettleParams) (sql.Result, error)
	CreateRefundSettleOrder(ctx context.Context, arg *CreateRefundSettleOrderParams) (sql.Result, error)
	CreateRefundTopupOrder(ctx context.Context, arg *CreateRefundTopupOrderParams) (sql.Result, error)
	CreateRepaymentOrder(ctx context.Context, arg *CreateRepaymentOrderParams) (sql.Result, error)
	GetBankRoute(ctx context.Context, arg *GetBankRouteParams) (*BankRoute, error)
	GetListRefundSettleByStatus(ctx context.Context, arg *GetListRefundSettleByStatusParams) ([]*RefundSettle, error)
	GetListRefundSettleIDHasExpired(ctx context.Context, arg *GetListRefundSettleIDHasExpiredParams) ([]int64, error)
	GetOrder(ctx context.Context, arg *GetOrderParams) (*Order, error)
	GetOrderByAppTransTypeAndStage(ctx context.Context, arg *GetOrderByAppTransTypeAndStageParams) (*Order, error)
	GetOrderByID(ctx context.Context, id int64) (*Order, error)
	GetOrderByRefundIDAndTypeAndStage(ctx context.Context, arg *GetOrderByRefundIDAndTypeAndStageParams) (*Order, error)
	GetOrderBySettleIDAndTypeAndStage(ctx context.Context, arg *GetOrderBySettleIDAndTypeAndStageParams) (*Order, error)
	GetPartnerByID(ctx context.Context, id int64) (*Partner, error)
	GetPartners(ctx context.Context) ([]*Partner, error)
	GetPaymentLogByID(ctx context.Context, id int64) (*PaymentLogs, error)
	GetPaymentLogByOrderID(ctx context.Context, orderID sql.NullInt64) (*PaymentLogs, error)
	GetPaymentLogByPaymentNo(ctx context.Context, paymentNo string) (*PaymentLogs, error)
	GetPaymentLogByZPTransID(ctx context.Context, zpTransID int64) (*PaymentLogs, error)
	GetRefundLog(ctx context.Context, id int64) (*RefundLogs, error)
	GetRefundLogByRefundID(ctx context.Context, refundID int64) (*RefundLogs, error)
	GetRefundLogForUpdate(ctx context.Context, id int64) (*RefundLogs, error)
	GetRefundLogsByProcessTypeAndZpTransID(ctx context.Context, arg *GetRefundLogsByProcessTypeAndZpTransIDParams) ([]*RefundLogs, error)
	GetRefundLogsByZPTransID(ctx context.Context, zpTransID int64) ([]*RefundLogs, error)
	GetRefundLogsExpiredByZPTransID(ctx context.Context, arg *GetRefundLogsExpiredByZPTransIDParams) ([]*RefundLogs, error)
	GetRefundLogsExpiredByZPTransIDForUpdate(ctx context.Context, arg *GetRefundLogsExpiredByZPTransIDForUpdateParams) ([]*RefundLogs, error)
	GetRefundSettle(ctx context.Context, zpTransID int64) (*RefundSettle, error)
	GetRefundSettleByID(ctx context.Context, id int64) (*RefundSettle, error)
	GetRefundSettleForUpdate(ctx context.Context, id int64) (*RefundSettle, error)
	ListOrderByTypeSettleAndStage(ctx context.Context, arg *ListOrderByTypeSettleAndStageParams) ([]*Order, error)
	UpdateBankRoute(ctx context.Context, arg *UpdateBankRouteParams) error
	UpdateCommission(ctx context.Context, arg *UpdateCommissionParams) (sql.Result, error)
	UpdateOrderStatus(ctx context.Context, arg *UpdateOrderStatusParams) error
	UpdateOrderStatusAndExtra(ctx context.Context, arg *UpdateOrderStatusAndExtraParams) error
	UpdatePartnerTransID(ctx context.Context, arg *UpdatePartnerTransIDParams) error
	UpdatePaymentLog(ctx context.Context, arg *UpdatePaymentLogParams) (sql.Result, error)
	UpdatePaymentLogOrderIDs(ctx context.Context, arg *UpdatePaymentLogOrderIDsParams) error
	UpdatePaymentLogStatus(ctx context.Context, arg *UpdatePaymentLogStatusParams) error
	UpdateRefundLog(ctx context.Context, arg *UpdateRefundLogParams) (sql.Result, error)
	UpdateRefundLogComplete(ctx context.Context, arg *UpdateRefundLogCompleteParams) (sql.Result, error)
	UpdateRefundLogsProcessTypeByIDs(ctx context.Context, arg *UpdateRefundLogsProcessTypeByIDsParams) error
	UpdateRefundPaybackInfo(ctx context.Context, arg *UpdateRefundPaybackInfoParams) error
	UpdateRefundSettle(ctx context.Context, arg *UpdateRefundSettleParams) (sql.Result, error)
	UpdateRefundSettleEvent(ctx context.Context, arg *UpdateRefundSettleEventParams) (sql.Result, error)
	UpdateRefundSettleRecon(ctx context.Context, arg *UpdateRefundSettleReconParams) error
	UpdateRefundSettleStatus(ctx context.Context, arg *UpdateRefundSettleStatusParams) error
	UpdateRefundSettlementAmount(ctx context.Context, arg *UpdateRefundSettlementAmountParams) error
	UpdateRefundTopupsAmount(ctx context.Context, arg *UpdateRefundTopupsAmountParams) error
}

var _ Querier = (*Queries)(nil)
