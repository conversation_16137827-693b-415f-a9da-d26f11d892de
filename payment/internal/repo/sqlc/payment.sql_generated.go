// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: payment.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
)

const CreatePaymentLog = `-- name: CreatePaymentLog :execresult
INSERT INTO payment_logs(zp_trans_id,
                         order_id,
                         system_id,
                         trans_type,
                         partner_req_id,
                         partner_trans_id,
                         app_trans_id,
                         account_id,
                         zalopay_id,
                         user_bank_account_number,
                         bank_code,
                         bank_routing_number,
                         app_id,
                         amount,
                         status,
                         bank_status,
                         error_code,
                         error_message,
                         current_available_balance,
                         payment_description,
                         extra,
                         installment_tenor,
                         installment_interest_rate,
                         pe_request_id,
                         fs_charge_info,
                         bank_connector_code,
                         escrow_account_no,
                         payment_no,
                         device_id,
                         user_ip)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreatePaymentLogParams struct {
	ZpTransID               int64           `json:"zp_trans_id"`
	OrderID                 sql.NullInt64   `json:"order_id"`
	SystemID                int32           `json:"system_id"`
	TransType               int32           `json:"trans_type"`
	PartnerReqID            sql.NullString  `json:"partner_req_id"`
	PartnerTransID          string          `json:"partner_trans_id"`
	AppTransID              string          `json:"app_trans_id"`
	AccountID               int64           `json:"account_id"`
	ZalopayID               int64           `json:"zalopay_id"`
	UserBankAccountNumber   string          `json:"user_bank_account_number"`
	BankCode                string          `json:"bank_code"`
	BankRoutingNumber       string          `json:"bank_routing_number"`
	AppID                   int32           `json:"app_id"`
	Amount                  int64           `json:"amount"`
	Status                  string          `json:"status"`
	BankStatus              string          `json:"bank_status"`
	ErrorCode               string          `json:"error_code"`
	ErrorMessage            string          `json:"error_message"`
	CurrentAvailableBalance int64           `json:"current_available_balance"`
	PaymentDescription      string          `json:"payment_description"`
	Extra                   json.RawMessage `json:"extra"`
	InstallmentTenor        int32           `json:"installment_tenor"`
	InstallmentInterestRate float64         `json:"installment_interest_rate"`
	PeRequestID             string          `json:"pe_request_id"`
	FsChargeInfo            json.RawMessage `json:"fs_charge_info"`
	BankConnectorCode       string          `json:"bank_connector_code"`
	EscrowAccountNo         string          `json:"escrow_account_no"`
	PaymentNo               string          `json:"payment_no"`
	DeviceID                string          `json:"device_id"`
	UserIp                  string          `json:"user_ip"`
}

func (q *Queries) CreatePaymentLog(ctx context.Context, arg *CreatePaymentLogParams) (sql.Result, error) {
	return q.exec(ctx, q.createPaymentLogStmt, CreatePaymentLog,
		arg.ZpTransID,
		arg.OrderID,
		arg.SystemID,
		arg.TransType,
		arg.PartnerReqID,
		arg.PartnerTransID,
		arg.AppTransID,
		arg.AccountID,
		arg.ZalopayID,
		arg.UserBankAccountNumber,
		arg.BankCode,
		arg.BankRoutingNumber,
		arg.AppID,
		arg.Amount,
		arg.Status,
		arg.BankStatus,
		arg.ErrorCode,
		arg.ErrorMessage,
		arg.CurrentAvailableBalance,
		arg.PaymentDescription,
		arg.Extra,
		arg.InstallmentTenor,
		arg.InstallmentInterestRate,
		arg.PeRequestID,
		arg.FsChargeInfo,
		arg.BankConnectorCode,
		arg.EscrowAccountNo,
		arg.PaymentNo,
		arg.DeviceID,
		arg.UserIp,
	)
}

const GetPaymentLogByID = `-- name: GetPaymentLogByID :one
SELECT id, order_id, zp_trans_id, payment_no, system_id, trans_type, partner_req_id, partner_trans_id, bank_trans_id, app_trans_id, account_id, zalopay_id, user_bank_account_number, bank_code, bank_routing_number, app_id, amount, status, bank_status, error_code, error_message, current_available_balance, payment_description, extra, installment_tenor, installment_interest_rate, pe_request_id, fs_charge_info, bank_connector_code, escrow_account_no, created_at, updated_at, is_commission, is_commission_updated_at, device_id, user_ip
FROM payment_logs
WHERE id = ?
`

func (q *Queries) GetPaymentLogByID(ctx context.Context, id int64) (*PaymentLogs, error) {
	row := q.queryRow(ctx, q.getPaymentLogByIDStmt, GetPaymentLogByID, id)
	var i PaymentLogs
	err := row.Scan(
		&i.ID,
		&i.OrderID,
		&i.ZpTransID,
		&i.PaymentNo,
		&i.SystemID,
		&i.TransType,
		&i.PartnerReqID,
		&i.PartnerTransID,
		&i.BankTransID,
		&i.AppTransID,
		&i.AccountID,
		&i.ZalopayID,
		&i.UserBankAccountNumber,
		&i.BankCode,
		&i.BankRoutingNumber,
		&i.AppID,
		&i.Amount,
		&i.Status,
		&i.BankStatus,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.CurrentAvailableBalance,
		&i.PaymentDescription,
		&i.Extra,
		&i.InstallmentTenor,
		&i.InstallmentInterestRate,
		&i.PeRequestID,
		&i.FsChargeInfo,
		&i.BankConnectorCode,
		&i.EscrowAccountNo,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsCommission,
		&i.IsCommissionUpdatedAt,
		&i.DeviceID,
		&i.UserIp,
	)
	return &i, err
}

const GetPaymentLogByOrderID = `-- name: GetPaymentLogByOrderID :one
SELECT id, order_id, zp_trans_id, payment_no, system_id, trans_type, partner_req_id, partner_trans_id, bank_trans_id, app_trans_id, account_id, zalopay_id, user_bank_account_number, bank_code, bank_routing_number, app_id, amount, status, bank_status, error_code, error_message, current_available_balance, payment_description, extra, installment_tenor, installment_interest_rate, pe_request_id, fs_charge_info, bank_connector_code, escrow_account_no, created_at, updated_at, is_commission, is_commission_updated_at, device_id, user_ip
FROM payment_logs
WHERE order_id IS NOT NULL AND order_id = ?
`

func (q *Queries) GetPaymentLogByOrderID(ctx context.Context, orderID sql.NullInt64) (*PaymentLogs, error) {
	row := q.queryRow(ctx, q.getPaymentLogByOrderIDStmt, GetPaymentLogByOrderID, orderID)
	var i PaymentLogs
	err := row.Scan(
		&i.ID,
		&i.OrderID,
		&i.ZpTransID,
		&i.PaymentNo,
		&i.SystemID,
		&i.TransType,
		&i.PartnerReqID,
		&i.PartnerTransID,
		&i.BankTransID,
		&i.AppTransID,
		&i.AccountID,
		&i.ZalopayID,
		&i.UserBankAccountNumber,
		&i.BankCode,
		&i.BankRoutingNumber,
		&i.AppID,
		&i.Amount,
		&i.Status,
		&i.BankStatus,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.CurrentAvailableBalance,
		&i.PaymentDescription,
		&i.Extra,
		&i.InstallmentTenor,
		&i.InstallmentInterestRate,
		&i.PeRequestID,
		&i.FsChargeInfo,
		&i.BankConnectorCode,
		&i.EscrowAccountNo,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsCommission,
		&i.IsCommissionUpdatedAt,
		&i.DeviceID,
		&i.UserIp,
	)
	return &i, err
}

const GetPaymentLogByPaymentNo = `-- name: GetPaymentLogByPaymentNo :one
SELECT id, order_id, zp_trans_id, payment_no, system_id, trans_type, partner_req_id, partner_trans_id, bank_trans_id, app_trans_id, account_id, zalopay_id, user_bank_account_number, bank_code, bank_routing_number, app_id, amount, status, bank_status, error_code, error_message, current_available_balance, payment_description, extra, installment_tenor, installment_interest_rate, pe_request_id, fs_charge_info, bank_connector_code, escrow_account_no, created_at, updated_at, is_commission, is_commission_updated_at, device_id, user_ip
FROM payment_logs
WHERE payment_no = ?
`

func (q *Queries) GetPaymentLogByPaymentNo(ctx context.Context, paymentNo string) (*PaymentLogs, error) {
	row := q.queryRow(ctx, q.getPaymentLogByPaymentNoStmt, GetPaymentLogByPaymentNo, paymentNo)
	var i PaymentLogs
	err := row.Scan(
		&i.ID,
		&i.OrderID,
		&i.ZpTransID,
		&i.PaymentNo,
		&i.SystemID,
		&i.TransType,
		&i.PartnerReqID,
		&i.PartnerTransID,
		&i.BankTransID,
		&i.AppTransID,
		&i.AccountID,
		&i.ZalopayID,
		&i.UserBankAccountNumber,
		&i.BankCode,
		&i.BankRoutingNumber,
		&i.AppID,
		&i.Amount,
		&i.Status,
		&i.BankStatus,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.CurrentAvailableBalance,
		&i.PaymentDescription,
		&i.Extra,
		&i.InstallmentTenor,
		&i.InstallmentInterestRate,
		&i.PeRequestID,
		&i.FsChargeInfo,
		&i.BankConnectorCode,
		&i.EscrowAccountNo,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsCommission,
		&i.IsCommissionUpdatedAt,
		&i.DeviceID,
		&i.UserIp,
	)
	return &i, err
}

const GetPaymentLogByZPTransID = `-- name: GetPaymentLogByZPTransID :one
SELECT id, order_id, zp_trans_id, payment_no, system_id, trans_type, partner_req_id, partner_trans_id, bank_trans_id, app_trans_id, account_id, zalopay_id, user_bank_account_number, bank_code, bank_routing_number, app_id, amount, status, bank_status, error_code, error_message, current_available_balance, payment_description, extra, installment_tenor, installment_interest_rate, pe_request_id, fs_charge_info, bank_connector_code, escrow_account_no, created_at, updated_at, is_commission, is_commission_updated_at, device_id, user_ip
FROM payment_logs
WHERE zp_trans_id = ?
`

func (q *Queries) GetPaymentLogByZPTransID(ctx context.Context, zpTransID int64) (*PaymentLogs, error) {
	row := q.queryRow(ctx, q.getPaymentLogByZPTransIDStmt, GetPaymentLogByZPTransID, zpTransID)
	var i PaymentLogs
	err := row.Scan(
		&i.ID,
		&i.OrderID,
		&i.ZpTransID,
		&i.PaymentNo,
		&i.SystemID,
		&i.TransType,
		&i.PartnerReqID,
		&i.PartnerTransID,
		&i.BankTransID,
		&i.AppTransID,
		&i.AccountID,
		&i.ZalopayID,
		&i.UserBankAccountNumber,
		&i.BankCode,
		&i.BankRoutingNumber,
		&i.AppID,
		&i.Amount,
		&i.Status,
		&i.BankStatus,
		&i.ErrorCode,
		&i.ErrorMessage,
		&i.CurrentAvailableBalance,
		&i.PaymentDescription,
		&i.Extra,
		&i.InstallmentTenor,
		&i.InstallmentInterestRate,
		&i.PeRequestID,
		&i.FsChargeInfo,
		&i.BankConnectorCode,
		&i.EscrowAccountNo,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsCommission,
		&i.IsCommissionUpdatedAt,
		&i.DeviceID,
		&i.UserIp,
	)
	return &i, err
}

const UpdateCommission = `-- name: UpdateCommission :execresult
UPDATE payment_logs
SET is_commission = ?,
    is_commission_updated_at = CURRENT_TIMESTAMP
WHERE zp_trans_id = ? and trans_type = 200
`

type UpdateCommissionParams struct {
	IsCommission bool  `json:"is_commission"`
	ZpTransID    int64 `json:"zp_trans_id"`
}

func (q *Queries) UpdateCommission(ctx context.Context, arg *UpdateCommissionParams) (sql.Result, error) {
	return q.exec(ctx, q.updateCommissionStmt, UpdateCommission, arg.IsCommission, arg.ZpTransID)
}

const UpdatePartnerTransID = `-- name: UpdatePartnerTransID :exec
UPDATE payment_logs
SET partner_trans_id = ?
WHERE id = ?
`

type UpdatePartnerTransIDParams struct {
	PartnerTransID string `json:"partner_trans_id"`
	ID             int64  `json:"id"`
}

func (q *Queries) UpdatePartnerTransID(ctx context.Context, arg *UpdatePartnerTransIDParams) error {
	_, err := q.exec(ctx, q.updatePartnerTransIDStmt, UpdatePartnerTransID, arg.PartnerTransID, arg.ID)
	return err
}

const UpdatePaymentLog = `-- name: UpdatePaymentLog :execresult
UPDATE payment_logs
SET partner_trans_id= ?,
    bank_trans_id=?,
    account_id= ?,
    user_bank_account_number= ?,
    bank_code= ?,
    bank_routing_number= ?,
    status= ?,
    bank_status= ?,
    error_code= ?,
    error_message= ?,
    current_available_balance= ?,
    payment_description= ?,
    extra= ?,
    bank_connector_code=?,
    escrow_account_no=?,
    updated_at = CURRENT_TIMESTAMP
WHERE id = ?
`

type UpdatePaymentLogParams struct {
	PartnerTransID          string          `json:"partner_trans_id"`
	BankTransID             string          `json:"bank_trans_id"`
	AccountID               int64           `json:"account_id"`
	UserBankAccountNumber   string          `json:"user_bank_account_number"`
	BankCode                string          `json:"bank_code"`
	BankRoutingNumber       string          `json:"bank_routing_number"`
	Status                  string          `json:"status"`
	BankStatus              string          `json:"bank_status"`
	ErrorCode               string          `json:"error_code"`
	ErrorMessage            string          `json:"error_message"`
	CurrentAvailableBalance int64           `json:"current_available_balance"`
	PaymentDescription      string          `json:"payment_description"`
	Extra                   json.RawMessage `json:"extra"`
	BankConnectorCode       string          `json:"bank_connector_code"`
	EscrowAccountNo         string          `json:"escrow_account_no"`
	ID                      int64           `json:"id"`
}

func (q *Queries) UpdatePaymentLog(ctx context.Context, arg *UpdatePaymentLogParams) (sql.Result, error) {
	return q.exec(ctx, q.updatePaymentLogStmt, UpdatePaymentLog,
		arg.PartnerTransID,
		arg.BankTransID,
		arg.AccountID,
		arg.UserBankAccountNumber,
		arg.BankCode,
		arg.BankRoutingNumber,
		arg.Status,
		arg.BankStatus,
		arg.ErrorCode,
		arg.ErrorMessage,
		arg.CurrentAvailableBalance,
		arg.PaymentDescription,
		arg.Extra,
		arg.BankConnectorCode,
		arg.EscrowAccountNo,
		arg.ID,
	)
}

const UpdatePaymentLogOrderIDs = `-- name: UpdatePaymentLogOrderIDs :exec
UPDATE payment_logs
SET order_id = ?,
    zp_trans_id = ?,
    app_trans_id = ?
WHERE id = ?
`

type UpdatePaymentLogOrderIDsParams struct {
	OrderID    sql.NullInt64 `json:"order_id"`
	ZpTransID  int64         `json:"zp_trans_id"`
	AppTransID string        `json:"app_trans_id"`
	ID         int64         `json:"id"`
}

func (q *Queries) UpdatePaymentLogOrderIDs(ctx context.Context, arg *UpdatePaymentLogOrderIDsParams) error {
	_, err := q.exec(ctx, q.updatePaymentLogOrderIDsStmt, UpdatePaymentLogOrderIDs,
		arg.OrderID,
		arg.ZpTransID,
		arg.AppTransID,
		arg.ID,
	)
	return err
}

const UpdatePaymentLogStatus = `-- name: UpdatePaymentLogStatus :exec
UPDATE payment_logs
SET status = ?,
    bank_status = ?,
    error_code = ?,
    error_message = ?,
    extra = ?
WHERE id = ?
`

type UpdatePaymentLogStatusParams struct {
	Status       string          `json:"status"`
	BankStatus   string          `json:"bank_status"`
	ErrorCode    string          `json:"error_code"`
	ErrorMessage string          `json:"error_message"`
	Extra        json.RawMessage `json:"extra"`
	ID           int64           `json:"id"`
}

func (q *Queries) UpdatePaymentLogStatus(ctx context.Context, arg *UpdatePaymentLogStatusParams) error {
	_, err := q.exec(ctx, q.updatePaymentLogStatusStmt, UpdatePaymentLogStatus,
		arg.Status,
		arg.BankStatus,
		arg.ErrorCode,
		arg.ErrorMessage,
		arg.Extra,
		arg.ID,
	)
	return err
}
