package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service"
	maintMgr "gitlab.zalopay.vn/fin/installment/payment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/logging"
	maintMw "gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/maintenance"
	appmetrics "gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/metrics"
	"go.opentelemetry.io/otel"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *configs.Payment,
	payment *service.PaymentService,
	fee *service.FeeService,
	refund *service.RefundService,
	maintManager maintMgr.Handler,
	logger log.Logger) *grpc.Server {
	serverMetrics := appmetrics.NewOtelRequestMetrics(appmetrics.Options{
		Module:    appmetrics.MetricModuleGrpcServer,
		Namespace: appmetrics.MetricPaymentMeter,
	})

	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			tracing.Server(
				tracing.WithTracerProvider(otel.GetTracerProvider()),
				tracing.WithPropagator(otel.GetTextMapPropagator()),
				tracing.WithTracerName(c.GetApp().GetName()),
			),
			metrics.Server(
				metrics.WithRequests(serverMetrics.HandledCounter),
				metrics.WithSeconds(serverMetrics.HandledHistogram),
			),
			logging.Server(logger),
			maintMw.Server(maintManager, MaintenanceElements),
		),
	}
	if c.GetServer().GetGrpc().GetNetwork() != "" {
		opts = append(opts, grpc.Network(c.GetServer().Grpc.Network))
	}
	if c.GetServer().Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.GetServer().Grpc.Addr))
	}
	if c.GetServer().Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.GetServer().Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	v1.RegisterAssetProviderServer(srv, payment)
	v1.RegisterFeeServiceServer(srv, fee)
	v1.RegisterRefundServiceServer(srv, refund)
	v1.RegisterRefundOpsServiceServer(srv, refund)
	return srv
}
