package server

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service"
	"golang.org/x/sync/errgroup"
)

type RefundConsumer struct {
	transport.Server
	logger   log.Logger
	consumer *KafkaConsumer
}

func NewRefundConsumer(config *configs.Payment, logger log.Logger, refundSvc *service.RefundService) *RefundConsumer {
	consConf := config.GetServer().GetConsumer()
	consumer := NewKafkaConsumer(consConf.GetAcRefundInfoUpdated(), refundSvc.HandleRefundStatusEvent)
	return &RefundConsumer{
		logger:   logger,
		consumer: consumer,
	}
}

func (c *RefundConsumer) Start(ctx context.Context) error {
	return c.consumer.Start(ctx)
}

func (c *RefundConsumer) Stop(ctx context.Context) error {
	return c.consumer.Stop(ctx)
}

type OrderStatusConsumer struct {
	transport.Server
	logger    log.Logger
	consumers []*KafkaConsumer
}

func NewOrderStatusConsumer(
	config *configs.Payment, logger log.Logger,
	refundService *service.RefundService) *OrderStatusConsumer {
	consConf := config.GetServer().GetConsumer()
	topupConsumer := NewKafkaConsumer(consConf.GetTopupOrderStatusUpdated(), refundService.HandleTopupStatusEvent)
	settleConsumer := NewKafkaConsumer(consConf.GetSettleOrderStatusUpdated(), refundService.HandleSettleStatusEvent)
	fundbackConsumer := NewKafkaConsumer(consConf.GetFundbackOrderStatusUpdated(), refundService.HandleFundbackStatusEvent)
	return &OrderStatusConsumer{
		logger:    logger,
		consumers: []*KafkaConsumer{topupConsumer, settleConsumer, fundbackConsumer},
	}
}

func (c *OrderStatusConsumer) Start(ctx context.Context) error {
	g, ctx := errgroup.WithContext(ctx)

	for _, consumer := range c.consumers {
		g.Go(func() error {
			return consumer.Start(ctx)
		})
	}

	return g.Wait()
}

func (c *OrderStatusConsumer) Stop(ctx context.Context) error {
	for _, consumer := range c.consumers {
		if err := consumer.Stop(ctx); err != nil {
			c.logger.Log(log.LevelError, "msg", "stop consumer error", "error", err)
		}
	}
	return nil
}

type RefundSettleProcessConsumer struct {
	transport.Server
	logger    log.Logger
	consumers []*KafkaConsumer
}

func NewRefundSettleProcessConsumer(config *configs.Payment, logger log.Logger, refundSvc *service.RefundService) *RefundSettleProcessConsumer {
	consConf := config.GetServer().GetConsumer()
	requestConsumer := NewKafkaConsumer(consConf.GetRefundSettleRequest(), refundSvc.HandleRefundSettleRequests)
	responseConsumer := NewKafkaConsumer(consConf.GetRefundSettleResponse(), refundSvc.HandleRefundSettleResults)
	return &RefundSettleProcessConsumer{
		logger:    logger,
		consumers: []*KafkaConsumer{requestConsumer, responseConsumer},
	}
}

func (c *RefundSettleProcessConsumer) Start(ctx context.Context) error {
	g, ctx := errgroup.WithContext(ctx)

	for _, consumer := range c.consumers {
		consumer := consumer // capture consumer variable for goroutine
		g.Go(func() error {
			return consumer.Start(ctx)
		})
	}

	return g.Wait()
}

func (c *RefundSettleProcessConsumer) Stop(ctx context.Context) error {
	for _, consumer := range c.consumers {
		if err := consumer.Stop(ctx); err != nil {
			c.logger.Log(log.LevelError, "msg", "stop consumer error", "error", err)
		}
	}
	return nil
}
