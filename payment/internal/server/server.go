package server

import (
	"github.com/google/wire"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/maintenance"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(
	NewGRPCServer,
	NewHTTPServer,
	NewTemporalWorker,
	NewRefundConsumer,
	NewOrderStatusConsumer,
	NewRefundSettleProcessConsumer,
)

var MaintenanceElements = []maintenance.Element{
	{
		Operation: v1.RePaymentService_CreateOrder_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatRepayment},
	},
}
