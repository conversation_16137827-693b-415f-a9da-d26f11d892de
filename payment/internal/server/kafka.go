package server

import (
	"context"
	"io"
	"math"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	kafka_client "zalopay.io/zgo/kafka-client"
)

type KafkaConsumer struct {
	transport.Server
	workerNums  int32
	subscribers []kafka_client.RetryWorker
}

func NewKafkaConsumer(config *configs.Consumer_Kafka, processFunc kafka_client.ProcessFunc) *KafkaConsumer {
	ctx := context.Background()
	workerNums := int32(math.Max(1, float64(config.GetNumWorkers())))
	subscribers := make([]kafka_client.RetryWorker, workerNums)
	startOffset := kafka_client.LastOffset
	if config.GetUseFirstOffset() {
		startOffset = kafka_client.FirstOffset
	}

	for i := int32(0); i < workerNums; i++ {
		inst, err := kafka_client.NewRetryWorker(ctx, kafka_client.RetryWorkerConfig{
			Brokers:      strings.Split(config.GetBrokers(), ","),
			Topic:        config.GetTopic(),
			GroupId:      config.GetGroupId(),
			ConsumerName: config.GetGroupId() + "-" + cast.ToString(i),
			StartOffset:  startOffset,
			ProcessFunc:  processFunc,
		})
		if err != nil {
			log.Fatalf("consumer worker can not init: %v, groupID=%v", err, config.GetGroupId())
			continue
		}
		subscribers[i] = inst
	}

	return &KafkaConsumer{
		workerNums:  workerNums,
		subscribers: subscribers,
	}
}

// Start starts the Kafka workers
func (t *KafkaConsumer) Start(ctx context.Context) error {
	for _, worker := range t.subscribers {
		worker := worker
		go func() {
			err := worker.Start()
			if errors.Is(err, io.EOF) ||
				strings.Contains(err.Error(), io.EOF.Error()) {
				return
			}
			log.Fatal("consumer worker run has error: ", err)
		}()
	}
	return nil
}

// Stop stops the Temporal worker
func (t *KafkaConsumer) Stop(ctx context.Context) error {
	for _, worker := range t.subscribers {
		go worker.Stop()
	}
	return nil
}
