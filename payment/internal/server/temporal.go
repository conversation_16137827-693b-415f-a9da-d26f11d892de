package server

import (
	"context"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service/polling"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	tempWorker "go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"
)

type TemporalWorker struct {
	transport.Server
	logger  *log.Helper
	client  types.WorkflowAdapter
	workers []tempWorker.Worker
}

func NewTemporalWorker(
	conf *configs.Payment,
	kLogger log.Logger,
	refundSvc *service.RefundService,
	pollingSvc *polling.PollingWorkflow,
	workflowAdapter types.WorkflowAdapter) *TemporalWorker {
	config := conf.GetSchedulers()
	logger := log.NewHelper(log.With(kLogger, "module", "temporal-worker"))

	logger.Info("InitTemporalWorker: ", config.GetPaymentStatus().GetQueueName())

	// Init worker payment
	workerPayment := tempWorker.New(workflowAdapter, config.GetPaymentStatus().GetQueueName(), tempWorker.Options{})
	// Register Workflow
	paymentSttOpts := workflow.RegisterOptions{Name: config.GetPaymentStatus().GetWorkflowType()}
	workerPayment.RegisterWorkflowWithOptions(pollingSvc.PaymentStatusWorkflow, paymentSttOpts)
	workerPayment.RegisterActivity(pollingSvc.GetUsecase().PollingPaymentStatus)
	workerPayment.RegisterActivity(pollingSvc.GetUsecase().PollingPartnerTransID)

	// Init worker refund
	workerRefund := tempWorker.New(workflowAdapter, config.RefundSettleRecon.GetQueueName(), tempWorker.Options{})

	// Register Workflow
	refundOpts := workflow.RegisterOptions{Name: config.GetRefundSettleRecon().GetWorkflowType()}
	workerRefund.RegisterWorkflowWithOptions(refundSvc.ReconcileRefundSettleWorkflow, refundOpts)
	workerRefund.RegisterActivity(refundSvc.ReconcileRefundSettleActivity)

	// Register Early Discharge Workflow
	earlyDischargeOpts := workflow.RegisterOptions{Name: config.GetRefundDischargePoll().GetWorkflowType()}
	workerRefund.RegisterWorkflowWithOptions(refundSvc.PollEarlyDischargeStatusWorkflow, earlyDischargeOpts)
	workerRefund.RegisterActivity(refundSvc.PollEarlyDischargeStatusActivity)

	// Register Refund Fundback Workflow
	execFundbackOpts := workflow.RegisterOptions{Name: config.GetRefundFundbackProcess().GetWorkflowType()}
	workerRefund.RegisterWorkflowWithOptions(refundSvc.FundbackAfterSettlementWorkflow, execFundbackOpts)
	workerRefund.RegisterActivity(refundSvc.FundbackAfterSettlementsActivity)

	// Register Expired Refund Repay Workflow
	expiredRepayProcOpts := workflow.RegisterOptions{Name: config.GetRefundExpiredProcess().GetWorkflowType()}
	workerRefund.RegisterWorkflowWithOptions(refundSvc.ProcessExpiredRefundLogsWorkflow, expiredRepayProcOpts)
	workerRefund.RegisterActivity(refundSvc.ProcessExpiredRefundLogsActivity)

	expiredRepayPollOpts := workflow.RegisterOptions{Name: config.GetRefundExpiredRepayPoll().GetWorkflowType()}
	workerRefund.RegisterWorkflowWithOptions(refundSvc.PollExpiredRefundRepayStatusWorkflow, expiredRepayPollOpts)
	workerRefund.RegisterActivity(refundSvc.PollExpiredRefundRepayStatusActivity)

	return &TemporalWorker{
		logger:  log.NewHelper(log.With(kLogger, "module", "temporal-worker")),
		client:  workflowAdapter,
		workers: []tempWorker.Worker{workerPayment, workerRefund},
	}
}

// Start starts the Temporal worker
func (t *TemporalWorker) Start(ctx context.Context) error {
	wGroup := sync.WaitGroup{}
	stopCh := tempWorker.InterruptCh()

	for _, worker := range t.workers {
		wGroup.Add(1)
		go func(w tempWorker.Worker) {
			defer wGroup.Done()
			err := w.Run(stopCh)
			if err != nil {
				t.logger.Errorf("Failed to start Temporal worker", err)
			}
		}(worker)
	}

	wGroup.Wait()
	return nil
}

// Stop stops the Temporal worker
func (t *TemporalWorker) Stop(ctx context.Context) error {
	for _, worker := range t.workers {
		go worker.Stop()
	}
	return nil
}
