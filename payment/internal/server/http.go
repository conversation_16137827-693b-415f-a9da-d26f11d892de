package server

import (
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service"
	maintMgr "gitlab.zalopay.vn/fin/installment/payment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/logging"
	maintMw "gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/maintenance"
	appmetrics "gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/metrics"
	"go.opentelemetry.io/otel"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *configs.Payment,
	repay *service.RePaymentService,
	refund *service.RefundService,
	transaction *service.TransactionService,
	authenticator auth.Authenticator,
	maintManager maintMgr.Handler,
	logger log.Logger) *http.Server {
	httpConfig := c.GetServer().GetHttp()
	serverMetrics := appmetrics.NewOtelRequestMetrics(appmetrics.Options{
		Module:    appmetrics.MetricModuleHttpServer,
		Namespace: appmetrics.MetricPaymentMeter,
	})

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(
				tracing.WithTracerProvider(otel.GetTracerProvider()),
				tracing.WithPropagator(otel.GetTextMapPropagator()),
				tracing.WithTracerName(c.GetApp().GetName()),
			),
			metrics.Server(
				metrics.WithRequests(serverMetrics.HandledCounter),
				metrics.WithSeconds(serverMetrics.HandledHistogram),
			),
			logging.Server(logger),
			auth.Server(authenticator),
			maintMw.Server(maintManager, MaintenanceElements),
		),
	}
	if c.GetServer().GetHttp().GetNetwork() != "" {
		opts = append(opts, http.Network(httpConfig.GetNetwork()))
	}
	if httpConfig.GetAddr() != "" {
		opts = append(opts, http.Address(httpConfig.GetAddr()))
	}
	if httpConfig.GetTimeout() != nil {
		opts = append(opts, http.Timeout(httpConfig.GetTimeout().AsDuration()))
	}
	srv := http.NewServer(opts...)

	v1.RegisterRefundServiceHTTPServer(srv, refund)
	v1.RegisterRePaymentServiceHTTPServer(srv, repay)
	v1.RegisterTransactionServiceHTTPServer(srv, transaction)

	registerPrometheusExporter(srv)
	return srv
}

func registerPrometheusExporter(server *http.Server) {
	server.Handle("/metrics", promhttp.Handler())
}
