package service

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/segmentio/kafka-go"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/purchase"
	"go.opentelemetry.io/otel"
)

type PurchasingProcessHandler struct {
	logger *log.Helper
	uc     *purchase.PurchaseUsecase
}

func NewPurchasingProcessHandler(uc *purchase.PurchaseUsecase, logger log.Logger) *PurchasingProcessHandler {
	return &PurchasingProcessHandler{
		logger: log.NewHelper(logger),
		uc:     uc,
	}
}

func (h *PurchasingProcessHandler) Process(m kafka.Message) (err error) {
	ctx, span := otel.GetTracerProvider().Tracer("payment-service").Start(context.Background(), "purchasing-init-process")
	defer span.End()

	h.logger.WithContext(ctx).Infow("msg", "receiver tpe-log data", "data", map[string]interface{}{"value": string(m.Value), "topic": m.Topic, "par": m.Partition, "offset": m.Offset})
	var pe model.PurchaseEvent

	err = json.Unmarshal(m.Value, &pe)
	if err != nil {
		log.Errorw("log", "PurchasingProcessHandler, json unmarshal PurchaseEvent fail", "error", err, "value", string(m.Value))
		return nil
	}

	err = h.uc.AuthProcess(ctx, pe)
	if err != nil {
		log.Errorw("log", "PurchasingProcessHandler, AuthProcess fail", "error", err, "value", string(m.Value))
		return nil
	}

	return nil
}

func (h *PurchasingProcessHandler) ConfirmProcess(m kafka.Message) (err error) {
	ctx, span := otel.GetTracerProvider().Tracer("payment-service").Start(context.Background(), "purchasing-confirm-process")
	defer span.End()

	h.logger.WithContext(ctx).Debugw("msg", "receiver tpe-log data", "data", map[string]interface{}{"value": string(m.Value), "topic": m.Topic, "par": m.Partition, "offset": m.Offset})
	var pe model.PurchaseEvent
	err = json.Unmarshal(m.Value, &pe)
	if err != nil {
		h.logger.WithContext(ctx).Errorw("msg", "PurchasingProcessHandler, json unmarshal PurchaseEvent fail", "error", err, "value", string(m.Value))
		return nil
	}

	err = h.uc.ConfirmPurchase(context.Background(), pe)
	if err != nil {
		h.logger.WithContext(ctx).Errorw("msg", "PurchasingProcessHandler, ConfirmPurchase fail", "error", err, "pe", pe)
		return nil
	}

	_ = h.uc.ConfirmDisbursement(context.Background(), pe)
	if err != nil {
		h.logger.WithContext(ctx).Errorw("msg", "PurchasingProcessHandler, ConfirmDisbursement fail", "error", err, "pe", pe)
		return nil
	}

	h.logger.WithContext(ctx).Infow("msg", "PurchasingProcessHandler, ConfirmProcess success", "pe", pe)
	return nil
}

func (h *PurchasingProcessHandler) UpdatePaymentProcess(m kafka.Message) error {
	ctx, span := otel.GetTracerProvider().Tracer("payment-service").Start(context.Background(), "payment-update-process")
	defer span.End()

	h.logger.WithContext(ctx).Debugw("msg", "UpdatePaymentProcess, receiver payment-update-process data", "data", map[string]interface{}{"value": string(m.Value), "topic": m.Topic, "par": m.Partition, "offset": m.Offset})

	var event model.UpdatePaymentEvent
	err := json.Unmarshal(m.Value, &event)
	if err != nil {
		h.logger.WithContext(ctx).Errorw("msg", "UpdatePaymentProcess, json unmarshal UpdatePaymentEvent fail", "error", err, "value", string(m.Value))
		return nil
	}

	switch event.Type {
	case model.UpdatePaymentTypeCommission:
		_ = h.uc.UpdateCommission(ctx, event)
		return nil
	default:
		h.logger.WithContext(ctx).Errorw("msg", "UpdatePaymentProcess, unknown event type", "event", event)
	}

	return nil
}

func (h *PurchasingProcessHandler) PaymentAuthProcess(m kafka.Message) error {
	ctx, span := otel.GetTracerProvider().Tracer("payment-service").Start(context.Background(), "payment-auth-process")
	defer span.End()
	h.logger.WithContext(ctx).Debugw("msg", "PaymentAuthProcess, receiver payment-auth-process data", "data", map[string]interface{}{"value": string(m.Value), "topic": m.Topic, "par": m.Partition, "offset": m.Offset})

	var event model.AuthSessionStatusEvent
	err := json.Unmarshal(m.Value, &event)
	if err != nil {
		h.logger.WithContext(ctx).Errorw("msg", "PaymentAuthProcess, json unmarshal AuthSessionStatusEvent fail", "error", err)
		return nil
	}

	_ = h.uc.PaymentAuthProcess(ctx, event)

	return nil
}
