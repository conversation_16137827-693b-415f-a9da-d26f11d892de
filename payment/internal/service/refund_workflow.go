package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

func (s *RefundService) ReconcileRefundSettleWorkflow(ctx workflow.Context, params *model.RefundSettleReconParams) error {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start ReconcileRefundSettleWorkflow", "params", params)

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts:    3,
			InitialInterval:    time.Second * 3,
			BackoffCoefficient: 2.0,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	err := workflow.ExecuteActivity(ctx, s.ReconcileRefundSettleActivity, params).Get(ctx, nil)
	if err != nil {
		logger.Error("ReconcileRefundSettleActivity failed", "error", err)
		return err
	}

	logger.Info("ReconcileRefundSettleWorkflow done")

	return nil
}

func (s *RefundService) ReconcileRefundSettleActivity(ctx context.Context, params *model.RefundSettleReconParams) error {
	actInfo := activity.GetInfo(ctx)
	logging := log.With(s.kLogger,
		"service", "account",
		"attempt", actInfo.Attempt,
		"activity_id", actInfo.ActivityID,
		"workflow_id", actInfo.WorkflowExecution.ID,
		"workflow_run_id", actInfo.WorkflowExecution.RunID,
	)
	logger := log.NewHelper(logging).WithContext(ctx)

	logger.Info("Start ReconcileRefundSettleActivity", "params", params)

	lockKey, err := s.distLock.AcquireReconcileRefundSettle(ctx, params.ZPTransID)
	if err != nil {
		logger.Errorw("msg", "acquire lock fail", "error", err)
		return err
	}
	defer s.distLock.Release(ctx, lockKey)

	if err := s.uc.ReconcileRefundSettlements(ctx, params.ZPTransID); err != nil {
		logger.Error("ReconcileRefundSettleActivity failed", "error", err)
		return err
	}

	logger.Info("Process ReconcileRefundSettleActivity done")

	return nil
}

func (w *RefundService) PollEarlyDischargeStatusWorkflow(ctx workflow.Context, req *model.EarlyDischargeStatusWorkflowRequest) (*model.EarlyDischargeStatusWorkflowResponse, error) {
	logger := workflow.GetLogger(ctx)

	ao := workflow.ActivityOptions{
		StartToCloseTimeout:    30 * time.Second,
		ScheduleToCloseTimeout: 45 * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    1 * time.Second,
			BackoffCoefficient: 1.5,
			MaximumInterval:    30 * time.Second,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	var pollResult *model.EarlyDischargeStatusWorkflowResponse
	err := workflow.ExecuteActivity(ctx, w.PollEarlyDischargeStatusActivity, req).Get(ctx, &pollResult)
	if err != nil {
		logger.Error("Early discharge polling failed.", "Error", err)
		return nil, err
	}

	logger.Info("Early discharge polling success", "result", pollResult, "request", req)

	return pollResult, nil
}

func (w *RefundService) PollEarlyDischargeStatusActivity(ctx context.Context, req *model.EarlyDischargeStatusWorkflowRequest) (*model.EarlyDischargeStatusWorkflowResponse, error) {
	actInfo := activity.GetInfo(ctx)
	logging := log.With(w.kLogger,
		"service", "account",
		"attempt", actInfo.Attempt,
		"activity_id", actInfo.ActivityID,
		"workflow_id", actInfo.WorkflowExecution.ID,
		"workflow_run_id", actInfo.WorkflowExecution.RunID,
	)
	logger := log.NewHelper(logging).WithContext(ctx)

	logger.Info("Start PollEarlyDischargeStatusActivity", "req", req)

	dischargeLog, err := w.uc.PollingEarlyDischargeStatus(ctx, req)
	if err != nil {
		logger.Error("Failed to get early discharge log", "error", err)
		return nil, temporal.NewApplicationErrorWithCause("PollingEarlyDischargeStatus, get early discharge log fail", "", err)
	}

	logger.Info("Discharge log retrieved", "log", dischargeLog)

	return dischargeLog, nil
}

func (w *RefundService) ProcessExpiredRefundLogsWorkflow(ctx workflow.Context) error {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start ProcessExpiredRefundLogsWorkflow")

	ao := workflow.ActivityOptions{
		StartToCloseTimeout:    3 * time.Hour,
		ScheduleToCloseTimeout: 3 * time.Hour,
		RetryPolicy:            &temporal.RetryPolicy{MaximumAttempts: 1},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	err := workflow.ExecuteActivity(ctx, w.ProcessExpiredRefundLogsActivity).Get(ctx, nil)
	if err != nil {
		logger.Error("ProcessExpiredRefundLogsActivity failed", "error", err)
		return err
	}

	logger.Info("ProcessExpiredRefundLogsWorkflow done")

	return nil
}

func (w *RefundService) ProcessExpiredRefundLogsActivity(ctx context.Context) error {
	actInfo := activity.GetInfo(ctx)
	logging := log.With(w.kLogger,
		"service", "account",
		"attempt", actInfo.Attempt,
		"activity_id", actInfo.ActivityID,
		"workflow_id", actInfo.WorkflowExecution.ID,
		"workflow_run_id", actInfo.WorkflowExecution.RunID,
	)
	logger := log.NewHelper(logging).WithContext(ctx)

	logger.Info("Start ProcessExpiredRefundLogsActivity")

	if err := w.uc.ProcessExpiredRefund(ctx); err != nil {
		logger.Error("ProcessExpiredRefund failed", "error", err)
		return temporal.NewApplicationErrorWithCause("ProcessExpiredRefund failed", "", err)
	}

	logger.Info("ProcessExpiredRefund done")

	return nil
}

func (w *RefundService) PollExpiredRefundRepayStatusWorkflow(ctx workflow.Context, req *model.RepaymentStatusPollingRequest) error {
	logger := workflow.GetLogger(ctx)

	ao := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: time.Minute * 30,
		RetryPolicy: &temporal.RetryPolicy{
			BackoffCoefficient: 2.0,
			InitialInterval:    5 * time.Second,
			MaximumInterval:    45 * time.Second,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	err := workflow.ExecuteActivity(ctx, w.PollExpiredRefundRepayStatusActivity, req).Get(ctx, nil)
	if err != nil {
		logger.Error("Early discharge polling failed.", "Error", err)
		return err
	}

	logger.Info("Early discharge polling success", "request", req)

	return nil
}

func (w *RefundService) PollExpiredRefundRepayStatusActivity(ctx context.Context, req *model.RepaymentStatusPollingRequest) (*model.RepaymentStatusPollingResponse, error) {
	actInfo := activity.GetInfo(ctx)
	logging := log.With(w.kLogger,
		"service", "account",
		"attempt", actInfo.Attempt,
		"activity_id", actInfo.ActivityID,
		"workflow_id", actInfo.WorkflowExecution.ID,
		"workflow_run_id", actInfo.WorkflowExecution.RunID,
	)
	logger := log.NewHelper(logging).WithContext(ctx)

	logger.Info("Start PollExpiredRefundRepayStatusActivity", "req", req)

	pollResult, err := w.uc.PollRepaymentStatus(ctx, req)
	if err != nil {
		logger.Error("Failed to get early discharge log", "error", err)
		return nil, temporal.NewApplicationErrorWithCause("PollingEarlyDischargeStatus, get early discharge log fail", "", err)
	}

	logger.Infow("msg", "Discharge log retrieved", "pollResult", pollResult)

	return pollResult, nil
}

func (w *RefundService) FundbackAfterSettlementWorkflow(ctx workflow.Context) error {
	logger := workflow.GetLogger(ctx)

	ao := workflow.ActivityOptions{
		ScheduleToCloseTimeout: 1 * time.Hour,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts:    2,
			InitialInterval:    time.Minute,
			BackoffCoefficient: 2.0,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	err := workflow.ExecuteActivity(ctx, w.FundbackAfterSettlementsActivity).Get(ctx, nil)
	if err != nil {
		logger.Error("FundbackAfterSettleActivity failed", "error", err)
		return err
	}

	logger.Info("ProcessFundbackAfterSettleWorkflow done")

	return nil
}

func (w *RefundService) FundbackAfterSettlementsActivity(ctx context.Context) error {
	actInfo := activity.GetInfo(ctx)
	logging := log.With(w.kLogger,
		"service", "account",
		"attempt", actInfo.Attempt,
		"activity_id", actInfo.ActivityID,
		"workflow_id", actInfo.WorkflowExecution.ID,
		"workflow_run_id", actInfo.WorkflowExecution.RunID,
	)
	logger := log.NewHelper(logging).WithContext(ctx)

	logger.Info("Start FundbackAfterSettleActivity")

	if err := w.uc.ProcessFundbackAfterSettlements(ctx); err != nil {
		logger.Error("ProcessFundbackAfterSettlements failed", "error", err)
		return temporal.NewApplicationErrorWithCause("ProcessFundbackAfterSettlements failed", "", err)
	}

	logger.Info("ProcessFundbackAfterSettlements done")

	return nil
}
