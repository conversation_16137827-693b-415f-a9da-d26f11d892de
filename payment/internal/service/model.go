package service

type ACOrderStatus struct {
	OrderNo    string `json:"order_no"`
	AppId      int32  `json:"app_id"`
	AppTransId string `json:"app_trans_id"`
	Amount     string `json:"amount"`
	//DestAsset    *ac.DestAsset
	Status string `json:"status"` //CREATED = 1; PROCESSING = 2; PENDING = 3; SUCCESS = 4; FAILED = 5;
	//ReasonStatus *ac.ReasonStatus
	//Payment        *ac.Payment
	//Metadata       *ac.OrderMeta
	DeprecatedData map[string]string `json:"deprecated_data"`
	Payment        Payment           `json:"payment"`
}

func (s *ACOrderStatus) IsValid() bool {
	return s.OrderNo != "" &&
		s.AppId != 0 &&
		s.AppTransId != "" &&
		s.Amount != ""
}

type Payment struct {
	Transactions []Transaction `json:"transactions"`
}

type Transaction struct {
	TransID     string          `json:"trans_id"`
	TransStatus string          `json:"trans_status"`
	Metadata    TransactionMeta `json:"metadata"`
}

type TransactionMeta struct {
	AppID int32 `json:"app_id"`
}

const (
	ACOrderStatusCreated    = "CREATED"
	ACOrderStatusProcessing = "PROCESSING"
	ACOrderStatusPending    = "PENDING"
	ACOrderStatusSuccess    = "SUCCESS"
	ACOrderStatusFailed     = "FAILED"
)

const (
	ACLogTypeRefund = 2
)

type ACRefundStatus int

const (
	ACRefundStatusPending ACRefundStatus = iota - 1
	ACRefundStatusFailed
	ACRefundStatusSuccess
	ACRefundStatusProcessing
)

type ACRefundKafkaEvent struct {
	LogType int               `json:"logType"`
	LogData ACRefundOrderData `json:"data"`
}

type ACRefundOrderData struct {
	UserID              string         `json:"userId"`
	AppID               int32          `json:"appID"`
	TransID             int64          `json:"transID"`
	TransStatus         int            `json:"transStatus"`
	RefundType          int            `json:"refundType"`
	RefundStatus        ACRefundStatus `json:"refundStatus"`
	RefundCaller        string         `json:"refundCaller"`
	RefundID            int64          `json:"refundID"`
	RefundAmount        int64          `json:"refundAmount"`
	RequestRefundAmount int64          `json:"requestRefundAmount"`
	RefundBeginDate     int64          `json:"refundBeginDate"`
	RefundEndDate       int64          `json:"refundEndDate"`
	MRefundID           string         `json:"mRefundID"`
	RefundDescription   string         `json:"refundDescription"`
}
