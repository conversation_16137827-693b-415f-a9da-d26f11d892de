package service

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/segmentio/kafka-go"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/acquiring_core"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/repay"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/auth"
	"go.opentelemetry.io/otel"
	"google.golang.org/protobuf/encoding/protojson"
)

const (
	InstallmentRepayAppID = 3721
)

type RePaymentService struct {
	v1.UnimplementedRePaymentServiceServer
	//uc     *purchase.PurchaseUsecase
	uc     *repay.RepayUsecase
	logger *log.Helper
}

func NewRePaymentService(logger log.Logger,
	uc *repay.RepayUsecase) *RePaymentService {
	return &RePaymentService{
		uc:     uc,
		logger: log.NewHelper(log.With(logger, "module", "RePaymentService")),
	}
}

func (s *RePaymentService) CreateOrder(ctx context.Context, req *v1.CreateOrderRequest) (*v1.CreateOrderResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	// Validate request
	if err := req.Validate(); err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "create order req is invalid", "error", err, "req", req)
		return nil, errors.BadRequest("invalid_argument", err.Error())
	}

	statementDate, err := time.Parse("2006-01-02", req.GetStatementDate())
	if err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "create order req is invalid: statement_date", "error", err, "req", req)
		return nil, errors.BadRequest("invalid_argument", err.Error())
	}

	// Call usecase to create order
	resp, err := s.uc.CreateOrder(ctx, repay.CreateOrderRequest{
		UserID:        zalopayID,
		Amount:        req.GetAmount(),
		PartnerCode:   req.GetPartnerCode(),
		StatementDate: statementDate,
		StatementID:   req.GetStatementId(),
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "create order failed", "error", err, "req", req)
		return nil, errors.BadRequest("create_order_failed", err.Error())
	}

	s.logger.WithContext(ctx).Infow("msg", "create order success", "response", resp, "req", req)

	return &v1.CreateOrderResponse{
		AppTransId:   resp.AppTransID,
		AppId:        resp.AppID,
		ZpTransToken: resp.ZpTransToken,
		//TransId:      0,
		OrderNo: resp.OrderNo,
	}, nil
}

func (s *RePaymentService) OrderStatusChangeProcess(m kafka.Message) (err error) {
	ctx, span := otel.GetTracerProvider().Tracer("payment-service").Start(context.Background(), "repayment-order-status-change-process")
	defer span.End()

	var acOrder ACOrderStatus
	if err = json.Unmarshal(m.Value, &acOrder); err != nil {
		s.logger.WithContext(ctx).Errorw(
			"msg", "OrderStatusChangeProcess, json unmarshal Order fail",
			"error", err, "value", string(m.Value),
		)
		return nil
	}
	if !acOrder.IsValid() {
		s.logger.WithContext(ctx).Errorw(
			"msg", "OrderStatusChangeProcess, order is invalid",
			"error", err, "value", string(m.Value),
		)
		return nil
	}

	if acOrder.AppId == InstallmentRepayAppID {
		s.logger.WithContext(ctx).Infow("msg", "OrderStatusChangeProcess", "acOrder", string(m.Value))

		if acOrder.Status == ACOrderStatusSuccess ||
			acOrder.Status == ACOrderStatusFailed {
			listTrans := acOrder.Payment.Transactions
			zpTransID := s.getRepayTransactionID(listTrans, acOrder.AppId)

			err := s.uc.ProcessRepay(context.Background(), repay.ProcessRepayRequest{
				OrderNo:    cast.ToString(acOrder.OrderNo),
				AppId:      acOrder.AppId,
				AppTransId: acOrder.AppTransId,
				ZPTransID:  zpTransID,
				Amount:     cast.ToString(acOrder.Amount),
				Status:     toOrderStatus(acOrder.Status),
			})
			if err != nil && err.IsRetry() {
				// need retry
				s.logger.WithContext(ctx).Errorw("msg", "OrderStatusChangeProcess, ProcessRepayFailed fail, need retry",
					"error", err)
				return nil
			}

			return nil
		} else {
			// PROCESSING, PENDING
			// bypass, not process
			s.logger.Warnw("msg", "OrderStatusChangeProcess, bypass order status",
				"order_status", acOrder.Status, "acOrder", acOrder)
			return nil
		}

	}

	return nil
}

func toOrderStatus(status string) model.OrderStatus {
	switch status {
	case ACOrderStatusSuccess:
		return model.OrderStatusSucceeded
	case ACOrderStatusFailed:
		return model.OrderStatusFailed
	case ACOrderStatusPending:
		return model.OrderStatusPending
	default:
		return model.OrderStatusProcessing
	}
}

func (s *RePaymentService) getRepayTransactionID(transactions []Transaction, appID int32) int64 {
	var transID int64 = 0

	s.logger.Infow("msg", "getRepayTransactionID", "transactions", transactions)

	if len(transactions) == 0 {
		return transID
	}

	for _, transaction := range transactions {
		if transaction.Metadata.AppID == appID {
			return cast.ToInt64(transaction.TransID)
		}
	}

	// fallback to last transaction
	transID = cast.ToInt64(transactions[len(transactions)-1].TransID)

	return transID
}

func (s *RePaymentService) canaryParseOrderWithNewApproach(ctx context.Context, data []byte) {
	var acOrder acquiring_core.Order
	if err := protojson.Unmarshal(data, &acOrder); err == nil {
		s.logger.WithContext(ctx).Infow("msg", "parse ac order using protojson", "acOrder", acOrder.String())
		return
	}
	if err := json.Unmarshal(data, &acOrder); err == nil {
		s.logger.WithContext(ctx).Errorw("msg", "parse ac order using normal encoding", "acOrder", acOrder.String())
		return
	}
}
