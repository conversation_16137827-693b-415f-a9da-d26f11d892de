package service

import (
	"context"
	"sync"
	"sync/atomic"

	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
)

// TriggerReconcileSettleJobs processes the request to trigger reconciliation jobs for the given transaction IDs
func (s *RefundService) TriggerReconcileSettleJobs(
	ctx context.Context,
	req *v1.TriggerReconcileSettleJobsRequest,
) (*v1.TriggerReconcileSettleJobsResponse, error) {
	logger := s.logger.WithContext(ctx)
	requestIDs := req.GetZpTransIds()
	successIDs := make([]int64, 0, len(requestIDs))

	logger.Infow("msg", "Received TriggerReconcileSettleJobs request", "transactionCount", len(requestIDs))

	// Use mutex to protect the concurrent access to successIDs slice
	var mu sync.Mutex
	// Create a wait group to synchronize goroutines
	var wg sync.WaitGroup
	// Use atomic counter to track success count
	var successCount int32

	// Process each transaction ID in parallel
	for _, zpTransID := range requestIDs {
		wg.Add(1)
		go func(id int64) {
			defer wg.Done()

			// Call the use case to process the reconciliation job
			err := s.uc.TriggerJobReconcileRefundSettlements(ctx, id)
			if err != nil {
				logger.Errorw("msg", "Failed to trigger reconcile settle job", "zpTransID", id, "error", err)
				return
			}

			// Increment success counter atomically
			atomic.AddInt32(&successCount, 1)

			// Add to successful IDs list with mutex protection
			mu.Lock()
			successIDs = append(successIDs, id)
			mu.Unlock()

			logger.Infow("msg", "Successfully triggered reconcile settle job", "zpTransID", id)
		}(zpTransID)
	}

	// Wait for all goroutines to finish
	wg.Wait()

	logger.Infow("msg", "Completed processing reconcile settle jobs",
		"totalRequested", len(requestIDs),
		"totalSuccessful", int(successCount))

	return &v1.TriggerReconcileSettleJobsResponse{
		ZpTransIds: successIDs,
	}, nil
}

// TriggerProcessSettleInfo processes the request to trigger settlement info processing for the given transaction IDs
func (s *RefundService) TriggerProcessSettleInfo(
	ctx context.Context,
	req *v1.TriggerProcessSettleInfoRequest,
) (*v1.TriggerProcessSettleInfoResponse, error) {
	logger := s.logger.WithContext(ctx)
	requestIDs := req.GetZpTransIds()
	successIDs := make([]int64, 0, len(req.GetZpTransIds()))

	logger.Infow("msg", "Received TriggerProcessSettleInfo request", "transactionCount", len(requestIDs))

	// Use mutex to protect the concurrent access to successIDs slice
	var mu sync.Mutex
	// Create a wait group to synchronize goroutines
	var wg sync.WaitGroup
	// Use atomic counter to track success count
	var successCount int32

	// Process each transaction ID in parallel
	for _, zpTransID := range requestIDs {
		wg.Add(1)
		go func(id int64) {
			defer wg.Done()

			// First, get the refund settle info for this transaction
			refundSettle, err := s.uc.GetRefundSettleByZPTransID(ctx, id)
			if err != nil {
				logger.Errorw("msg", "Failed to get refund settle", "zpTransID", id, "error", err)
				return
			}

			// Process the settlement info for this settle ID
			err = s.uc.ProcessRefundSettleInfo(ctx, refundSettle.ID)
			if err != nil {
				logger.Errorw("msg", "Failed to process settle info", "zpTransID", id, "settleID", refundSettle.ID, "error", err)
				return
			}

			// Increment success counter atomically
			atomic.AddInt32(&successCount, 1)

			// Add to successful IDs list with mutex protection
			mu.Lock()
			successIDs = append(successIDs, id)
			mu.Unlock()

			logger.Infow("msg", "Successfully processed settle info", "zpTransID", id, "settleID", refundSettle.ID)
		}(zpTransID)
	}

	// Wait for all goroutines to finish
	wg.Wait()

	logger.Infow("msg", "Completed processing settle info",
		"totalRequested", len(requestIDs),
		"totalSuccessful", int(successCount))

	return &v1.TriggerProcessSettleInfoResponse{
		ZpTransIds:     successIDs,
		ProcessedCount: int32(successCount),
	}, nil
}

func (s *RefundService) TriggerPollingEarlyDischargeJobs(
	ctx context.Context,
	req *v1.TriggerPollingEarlyDischargeJobsRequest,
) (*v1.TriggerPollingEarlyDischargeJobsResponse, error) {
	logger := s.logger.WithContext(ctx)

	// Prepare for concurrent processing
	requestIDs := req.GetPaymentIds()
	processedIDs := make([]int64, 0, len(requestIDs))
	errorMessages := make([]string, 0)

	var mu sync.Mutex
	var wg sync.WaitGroup
	var processedCount int32

	// Process each transaction ID in parallel
	for _, paymentID := range requestIDs {
		wg.Add(1)
		go func(id int64) {
			defer wg.Done()

			// Call the usecase method to poll for early discharge
			dischargeLog, err := s.uc.GetEarlyDischargeLogByID(ctx, id)
			if err != nil {
				logger.Errorw("msg", "Failed to get early discharge log", "paymentID", id, "error", err)
				return
			}

			err = s.uc.TriggerPollingEarlyDischargeStatus(ctx, dischargeLog.ID, dischargeLog.AccountInfo.ZalopayID)
			if err != nil {
				logger.Errorw("msg", "Failed to trigger polling early discharge status", "paymentID", id, "error", err)
				mu.Lock()
				errorMessages = append(errorMessages, err.Error())
				mu.Unlock()
				return
			}

			// Increment success counter atomically
			atomic.AddInt32(&processedCount, 1)

			// Add to successful IDs list with mutex protection
			mu.Lock()
			processedIDs = append(processedIDs, id)
			mu.Unlock()

			logger.Infow("msg", "Successfully polled early discharge status", "paymentID", id)
		}(paymentID)
	}

	// Wait for all goroutines to finish
	wg.Wait()

	logger.Infow("msg", "Completed processing early discharge polling jobs",
		"totalRequested", len(requestIDs),
		"totalSuccessful", int(processedCount),
		"errorCount", len(errorMessages))

	return &v1.TriggerPollingEarlyDischargeJobsResponse{
		PaymentIds:     processedIDs,
		ProcessedCount: int32(processedCount),
		ErrorMessages:  errorMessages,
	}, nil
}

// RetryRefundSettleOrder processes the request to retry settlement orders for the given transaction IDs
// using a worker pool pattern for efficient concurrent processing
func (s *RefundService) RetryRefundSettleOrder(
	ctx context.Context,
	req *v1.RetryRefundSettleOrderRequest,
) (*v1.RetryRefundSettleOrderResponse, error) {
	logger := s.logger.WithContext(ctx)
	requestIDs := req.GetZpTransIds()

	logger.Infow("msg", "Received RetryRefundSettleOrder request", "transactionCount", len(requestIDs))

	if len(requestIDs) == 0 {
		return &v1.RetryRefundSettleOrderResponse{
			ZpTransIds:     []int64{},
			ProcessedCount: 0,
		}, nil
	}

	// Define worker pool size - can be adjusted based on load characteristics
	// or made configurable through service configuration
	workerCount := 5
	if len(requestIDs) < workerCount {
		workerCount = len(requestIDs)
	}

	// Channels for job distribution and result collection
	jobs := make(chan int64, len(requestIDs))
	results := make(chan processResult, len(requestIDs))

	// Create a cancellable context for the worker pool
	workerCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Start worker pool
	var wg sync.WaitGroup
	for range workerCount {
		wg.Add(1)
		go s.retryRefundWorker(workerCtx, &wg, jobs, results)
	}

	// Send jobs to workers
	for _, id := range requestIDs {
		jobs <- id
	}
	close(jobs) // No more jobs to send

	// Wait for all workers to complete in a separate goroutine
	go func() {
		wg.Wait()
		close(results) // All workers done, no more results expected
	}()

	// Collect results
	successIDs := make([]int64, 0, len(requestIDs))
	var successCount int32

	for result := range results {
		if result.success {
			successIDs = append(successIDs, result.id)
			successCount++
		}
	}

	logger.Infow("msg", "Completed retrying refund settle orders",
		"totalRequested", len(requestIDs),
		"totalSuccessful", int(successCount))

	return &v1.RetryRefundSettleOrderResponse{
		ZpTransIds:     successIDs,
		ProcessedCount: successCount,
	}, nil
}

// processResult represents the result of processing a single transaction
type processResult struct {
	id      int64
	success bool
}

// retryRefundWorker handles retry operations for refund settlement in a worker pool
func (s *RefundService) retryRefundWorker(
	ctx context.Context,
	wg *sync.WaitGroup,
	jobs <-chan int64,
	results chan<- processResult,
) {
	defer wg.Done()
	logger := s.logger.WithContext(ctx)

	// Range over jobs channel - will automatically exit when channel is closed
	for id := range jobs {
		// Process the individual transaction
		result := processResult{id: id, success: false}

		// Check for context cancellation
		select {
		case <-ctx.Done():
			logger.Warnw("msg", "Worker context cancelled", "error", ctx.Err())
			return
		default:
			// Continue processing
		}

		// Get the refund settle info for this transaction
		refundSettle, err := s.uc.GetRefundSettleByZPTransID(ctx, id)
		if err != nil {
			logger.Errorw("msg", "Failed to get refund settle", "zpTransID", id, "error", err)
			results <- result
			continue
		}

		// Call the use case to retry processing the refund settle order
		err = s.uc.RetrySubmitSettleOrder(ctx, refundSettle.ID)
		if err != nil {
			logger.Errorw("msg", "Failed to retry refund settle order",
				"zpTransID", id,
				"settleID", refundSettle.ID,
				"error", err)
			results <- result
			continue
		}

		// Mark as successful
		result.success = true
		results <- result

		logger.Infow("msg", "Successfully retried refund settle order",
			"zpTransID", id,
			"settleID", refundSettle.ID)
	}
}
