package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/purchase"
)

type PaymentService struct {
	v1.UnimplementedAssetProviderServer
	uc        *purchase.PurchaseUsecase
	logger    *log.Helper
	CIMBZasID int64
}

func NewPaymentService(uc *purchase.PurchaseUsecase, logger log.Logger, conf *configs.Payment) *PaymentService {
	return &PaymentService{
		UnimplementedAssetProviderServer: v1.UnimplementedAssetProviderServer{},
		uc:                               uc,
		logger:                           log.<PERSON><PERSON>elper(logger),
		CIMBZasID:                        conf.GetApp().GetCimbZasId(),
	}
}

func (s *PaymentService) Exchange(ctx context.Context, req *v1.ExchangeRequest) (*v1.ExchangeResponse, error) {
	//validate request
	if err := req.Validate(); err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "exchange req is invalid", "error", err)
		//return nil, errors.BadRequest("invalid_argument", err.Error())
		return buildExchangeResponse(
			req.GetTransId(),
			req.GetRequestId(),
			v1.ReturnCode_SYSTEM_ERROR,
			v1.ReturnCode_CODE_UNSPECIFIED,
			"invalid_argument",
			err.Error()), nil
	}

	// Includes fs_charge_info, user_id
	assetData := req.GetAsset().GetData()
	// Includes app_id, description
	extraData := req.GetExtraData()

	zalopayID := getFromKey(assetData, "user_id", "")
	chargeInfo := getFromKey(assetData, "fs_charge_info", "")
	appID := getFromKey(extraData, "app_id", "")
	description := getFromKey(extraData, "description", "")
	paymentNo := getFromKey(extraData, "payment_no", "")
	deviceID := getFromKey(extraData, "device_id", "")
	userIP := getFromKey(extraData, "user_ip", "")

	if zalopayID == "" || chargeInfo == "" || appID == "" || paymentNo == "" {
		s.logger.WithContext(ctx).Errorw("msg", "exchange req is invalid", "error", "missing required fields", "req", req,
			"zalopayID", zalopayID, "chargeInfo", chargeInfo, "appID", appID, "description", description, "payment_no", paymentNo)
		return buildExchangeResponse(
			req.GetTransId(),
			req.GetRequestId(),
			v1.ReturnCode_SYSTEM_ERROR,
			v1.ReturnCode_CODE_UNSPECIFIED,
			"invalid_argument",
			"missing required fields"), nil
	}

	//init purchase
	resp, err := s.uc.InitPurchase(ctx, purchase.InitPurchaseRequest{
		ZalopayID: cast.ToInt64(zalopayID),
		Order: model.Order{
			TransID:     req.GetTransId(),
			AppID:       cast.ToInt32(appID),
			AppTransID:  "",
			Amount:      req.GetAmount(),
			Description: description,
		},
		ChargeInfo: chargeInfo,
		RequestID:  req.GetRequestId(),
		PaymentNo:  paymentNo,
		DeviceID:   deviceID,
		UserIP:     userIP,
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "create purchase fail", "error", err)
		if err.Error() == model.ErrDuplicateTransaction {
			return buildExchangeResponse(
				req.GetTransId(),
				req.GetRequestId(),
				v1.ReturnCode_SYSTEM_ERROR,
				v1.ReturnCode_CODE_UNSPECIFIED,
				model.ErrDuplicateTransaction,
				err.Error()), nil
		}

		return buildExchangeResponse(
			req.GetTransId(),
			req.GetRequestId(),
			v1.ReturnCode_SYSTEM_ERROR,
			v1.ReturnCode_CODE_UNSPECIFIED,
			"create_purchase_fail",
			err.Error()), nil
	}

	return &v1.ExchangeResponse{
		TransId:   req.GetTransId(),
		RequestId: req.GetRequestId(),
		RefSofId:  cast.ToString(resp.TransactionID),
		ReturnCode: &v1.ReturnCode{
			Status:  v1.ReturnCode_OK,
			Code:    v1.ReturnCode_SUBMITTED,
			SubCode: "",
			Message: "",
		},
	}, nil
}

func (s *PaymentService) QueryStatus(ctx context.Context, req *v1.QueryStatusRequest) (*v1.QueryStatusResponse, error) {
	purchase, err := s.uc.QueryStatus(ctx, purchase.QueryStatusRequest{
		TransID: req.GetTransId(),
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "query status fail", "error", err)
		return &v1.QueryStatusResponse{
			TransId:   req.GetTransId(),
			RequestId: req.GetRequestId(),
			ReturnCode: &v1.ReturnCode{
				Status:  v1.ReturnCode_SYSTEM_ERROR,
				Code:    v1.ReturnCode_CODE_UNSPECIFIED,
				SubCode: "query_status_fail",
				Message: err.Error(),
			},
		}, nil
	}

	peStatus := toPEStatus(purchase.PurchaseOrder.Status)

	return &v1.QueryStatusResponse{
		TransId:   req.GetTransId(),
		RequestId: req.GetRequestId(),
		RefSofId:  cast.ToString(purchase.PurchaseOrder.ID),
		ReturnCode: &v1.ReturnCode{
			Status:  v1.ReturnCode_OK,
			Code:    peStatus,
			SubCode: purchase.PurchaseOrder.ErrorCode,
			Message: purchase.PurchaseOrder.ErrorMessage,
		},
	}, nil
}

func toPEStatus(status model.PaymentStatus) v1.ReturnCode_Code {
	switch status {
	case model.PaymentStatusInit:
		return v1.ReturnCode_SUBMITTED
	case model.PaymentStatusSucceeded:
		return v1.ReturnCode_SUCCEEDED
	case model.PaymentStatusFailed:
		return v1.ReturnCode_FAILED
	default:
		return v1.ReturnCode_SUBMITTED
	}
}

func (s *PaymentService) ConfirmAuthentication(ctx context.Context, req *v1.ConfirmAuthenticationRequest) (*v1.ConfirmAuthenticationResponse, error) {
	if err := req.Validate(); err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "confirm authentication req is invalid", "error", err)
		return nil, errors.BadRequest("invalid_argument", err.Error())
	}

	return nil, errors.InternalServer("not_implemented", "method ConfirmAuthentication not implemented")
}

func (s *PaymentService) QueryZas(context.Context, *v1.ZasIdQueryRequest) (*v1.ZasIdQueryResponse, error) {
	return &v1.ZasIdQueryResponse{
		ReturnCode: &v1.ReturnCode{
			Status: v1.ReturnCode_OK,
		},
		ZasId: s.CIMBZasID,
	}, nil
}

func (s *PaymentService) CancelTransaction(ctx context.Context, req *v1.CancelTransactionRequest) (*v1.CancelTransactionResponse, error) {
	resp, err := s.uc.CancelTransaction(ctx, req.GetZpTransId())
	if err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "cancel transaction fail", "error", err)
		return nil, errors.InternalServer("cancel_transaction_fail", err.Error())
	}

	return &v1.CancelTransactionResponse{
		RefSofId:       resp.RefSofId,
		PartnerTransId: resp.PartnerTransId,
	}, nil
}

func buildExchangeResponse(transID int64, requestID string, status v1.ReturnCode_Status, code v1.ReturnCode_Code, subCode string, message string) *v1.ExchangeResponse {
	return &v1.ExchangeResponse{
		TransId:   transID,
		RequestId: requestID,
		ReturnCode: &v1.ReturnCode{
			Status:  status,
			Code:    code,
			SubCode: subCode,
			Message: message,
		},
	}
}

// Implement getFromKey func
func getFromKey(data map[string]string, key string, defaultValue string) string {
	if value, ok := data[key]; ok {
		return value
	}
	return defaultValue
}
