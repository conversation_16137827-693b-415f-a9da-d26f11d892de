package polling

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/purchase"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

var ProviderSet = wire.NewSet(NewPollingWorkflow)

type PollingWorkflow struct {
	uc     *purchase.PurchaseUsecase
	logger *log.Helper
}

type ApprovalPollingRequest struct {
}

func NewPollingWorkflow(uc *purchase.PurchaseUsecase, logger log.Logger) *PollingWorkflow {
	return &PollingWorkflow{
		uc:     uc,
		logger: log.NewHelper(log.With(logger, "module", "polling-workflow")),
	}
}

func (w *PollingWorkflow) GetUsecase() *purchase.PurchaseUsecase {
	return w.uc
}

func (w *PollingWorkflow) PaymentStatusWorkflow(ctx workflow.Context, req *model.PaymentStatusWorkflowRequest) (*model.PaymentStatusWorkflowResponse, error) {
	logger := workflow.GetLogger(ctx)

	ao := workflow.ActivityOptions{
		StartToCloseTimeout:    30 * time.Second,
		ScheduleToCloseTimeout: 2 * 24 * time.Hour,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    1 * time.Second,
			BackoffCoefficient: 1.5,
			MaximumInterval:    30 * time.Second,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	var a *purchase.PurchaseUsecase

	var pollResult *model.PaymentStatusWorkflowResponse
	err := workflow.ExecuteActivity(ctx, a.PollingPaymentStatus, req).Get(ctx, &pollResult)
	if err != nil {
		logger.Error("Polling failed.", "Error", err)
		return nil, err
	}

	logger.Info("Polling success", "Result", pollResult, "req", req)

	if !pollResult.AwaitingPartnerTransID {
		return pollResult, nil
	}

	ao2 := workflow.ActivityOptions{
		StartToCloseTimeout:    30 * time.Second,
		ScheduleToCloseTimeout: 30 * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    5 * time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao2)
	err = workflow.ExecuteActivity(ctx, w.uc.PollingPartnerTransID, req).Get(ctx, nil)
	if err != nil {
		logger.Error("Polling failed.", "Error", err)
		return nil, err
	}

	logger.Info("Polling success", "Result", pollResult, "req", req)

	return pollResult, nil
}
