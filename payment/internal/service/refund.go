package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/auth"
)

type RefundService struct {
	v1.UnimplementedRefundServiceServer
	v1.UnimplementedRefundOpsServiceServer
	uc       *refund.Usecase
	logger   *log.Helper
	kLogger  log.Logger
	distLock types.DistributedLock
	orderCfg *configs.OrderConfigsHelper
}

func NewRefundService(
	logger log.Logger,
	uc *refund.Usecase,
	distLock types.DistributedLock,
	orderCfg *configs.OrderConfigsHelper) *RefundService {
	return &RefundService{
		uc:       uc,
		kLogger:  logger,
		orderCfg: orderCfg,
		distLock: distLock,
		logger:   log.NewHelper(log.With(logger, "module", "RefundService")),
	}
}

func (s *RefundService) Refund(ctx context.Context, req *v1.RefundRequest) (*v1.RefundResponse, error) {
	resp, err := s.uc.Refund(ctx, model.RefundOrder{
		ZPTransID:          cast.ToInt64(req.GetZpTransId()),
		RefundID:           req.GetRefundId(),
		Amount:             req.GetAmount(),
		AppTransID:         req.GetAppTransId(),
		AppID:              req.GetAppId(),
		PaymentDescription: req.GetPaymentDescription(),
	})
	if err != nil {
		return nil, errors.BadRequest("refund_failed", err.Error())
	}

	return &v1.RefundResponse{
		Timestamp:  time.Now().Unix(),
		RefundId:   req.GetRefundId(),
		RefSofId:   resp.ID,
		RefundType: v1.RefundType_REFUND_TYPE_MANUAL,
	}, nil
}

func (s *RefundService) RefundQuery(ctx context.Context, req *v1.RefundQueryRequest) (*v1.RefundQueryResponse, error) {
	resp, err := s.uc.RefundQuery(ctx, req.GetRefundId())
	if err != nil {
		return nil, errors.BadRequest("refund_query_failed", err.Error())
	}

	return &v1.RefundQueryResponse{
		RefSofId: resp.ID,
		RefundId: resp.RefundID,
	}, nil
}

func (s *RefundService) PreCheck(ctx context.Context,
	req *v1.PreCheckRequest) (*v1.PreCheckResponse, error) {
	resp, err := s.uc.PreCheck(ctx, model.RefundOrder{
		ZPTransID:          cast.ToInt64(req.GetZpTransId()),
		RefundID:           req.GetRefundId(),
		Amount:             req.GetAmount(),
		AppTransID:         req.GetAppTransId(),
		AppID:              req.GetAppId(),
		PaymentDescription: req.GetPaymentDescription(),
	})
	if err != nil {
		return nil, errors.BadRequest("refund_estimation_failed", err.Error())
	}

	return &v1.PreCheckResponse{
		RefSofId:     resp.RefundOrder.ID,
		RefundId:     resp.RefundOrder.RefundID,
		RefundType:   v1.RefundType_REFUND_TYPE_AUTO,
		SettleStatus: convertSettleStatus(resp.SettleStatus),
	}, nil
}

func (s *RefundService) CreateTopup(ctx context.Context, req *v1.CreateTopupRequest) (*v1.CreateTopupResponse, error) {
	logger := s.logger.WithContext(ctx)
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	s.logger.Infow("msg", "CreateTopup", "zalopayID", zalopayID)

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest("BAD_REQUEST", err.Error())
	}

	params := &refund.CreateTopupRequest{
		Amount:       req.GetAmount(),
		ZaloPayID:    zalopayID,
		RefZPTransID: req.GetZpTransId(),
	}
	resp, err := s.uc.CreateTopup(ctx, params)
	if err != nil {
		logger.Errorw("msg", "Failed to create topup order", "error", err)
		return nil, errors.InternalServer("create_topup_failed", err.Error())
	}

	return &v1.CreateTopupResponse{
		TransId:      resp.ID,
		AppId:        resp.AppID,
		AppTransId:   resp.AppTransID,
		ZpTransToken: resp.ZPTransToken,
	}, nil
}

func convertSettleStatus(status model.SettleStatus) v1.SettleStatus {
	switch status {
	case model.SettleStatusSettled:
		return v1.SettleStatus_SETTLE_STATUS_SETTLED
	case model.SettleStatusUnsettled:
		return v1.SettleStatus_SETTLE_STATUS_UNSETTLED
	default:
		return v1.SettleStatus_SETTLE_STATUS_UNSPECIFIED
	}
}
