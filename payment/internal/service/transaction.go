package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/transaction"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type TransactionService struct {
	v1.UnimplementedTransactionServiceServer
	logger  *log.Helper
	transUc *transaction.Usecase
}

func NewTransactionService(logger log.Logger, transUc *transaction.Usecase) *TransactionService {
	return &TransactionService{
		transUc: transUc,
		logger:  log.<PERSON><PERSON><PERSON>per(logger),
	}
}

func (s *TransactionService) GetTransaction(ctx context.Context,
	req *v1.GetTransactionRequest) (*v1.GetTransactionResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest("BAD_REQUEST", err.Error())
	}

	params := &transaction.GetTransactionRequest{
		ZalopayID: zalopayID,
		ZPTransID: req.ZpTransId,
	}
	resp, err := s.transUc.GetTransaction(ctx, params)
	if err != nil {
		return nil, errors.InternalServer("INTERNAL_SERVER_ERROR", err.Error())
	}

	return &v1.GetTransactionResponse{Transaction: toTransProto(resp)}, nil
}

func (s *TransactionService) ListTransaction(ctx context.Context,
	req *v1.ListTransactionRequest) (*v1.ListTransactionResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest("BAD_REQUEST", err.Error())
	}

	params := &transaction.ListTransactionRequest{
		ZalopayID:    zalopayID,
		AccountID:    req.AccountId,
		PartnerCodes: []partner.PartnerCode{partner.PartnerCIMB},
		TimeRange:    toTimeRange(req.FromDate, req.ToDate),
		TransTypes:   toTransTypes(req.TransTypes),
		Pagination:   toPagination(req.Pagination),
	}
	resp, err := s.transUc.ListTransaction(ctx, params)
	if err != nil {
		return nil, errors.InternalServer("INTERNAL_SERVER_ERROR", err.Error())
	}

	return &v1.ListTransactionResponse{
		Transactions: toListTransProto(resp.Transactions),
		Pagination: &v1.PaginationData{
			HasNext:    resp.PaginationRes.HasNext,
			HasPrev:    resp.PaginationRes.HasPrev,
			NextCursor: resp.PaginationRes.NextCursor,
			PrevCursor: resp.PaginationRes.PrevCursor,
		},
	}, nil
}

func toListTransProto(transactions []*model.Transaction) []*v1.Transaction {
	result := make([]*v1.Transaction, 0, len(transactions))

	for _, t := range transactions {
		result = append(result, toTransProto(t))
	}
	return result
}

func toTransProto(t *model.Transaction) *v1.Transaction {
	transType := toTransTypeProto(t.Type)
	transStatus := toTransStatusProto(t.Status)
	return &v1.Transaction{
		ZpTransId:      cast.ToInt64(t.ZPTransID),
		PartnerTransId: t.PartnerTransID,
		Type:           transType,
		Amount:         t.Amount,
		Remark:         t.Remark,
		Status:         transStatus,
		ProductIcon:    t.Product.IconUrl,
		CreatedAt:      t.CreatedAt.Format(time.RFC3339),
		UpdatedAt:      t.UpdatedAt.Format(time.RFC3339),
	}
}

func toTimeRange(fromDate, toDate *timestamppb.Timestamp) *model.TimeRangeFilter {
	if fromDate == nil && toDate == nil {
		return nil
	}

	result := &model.TimeRangeFilter{}
	if fromDate != nil {
		result.From = fromDate.AsTime()
	}
	if toDate != nil {
		result.To = toDate.AsTime()
	}
	return result
}

func toTransTypeProto(value model.TransType) v1.TransType {
	return v1.TransType(value)
}

func toTransStatusProto(value model.TransStatus) v1.TransStatus {
	switch value {
	case model.TransStatusSucceeded:
		return v1.TransStatus_TRANS_STATUS_SUCCESS
	case model.TransStatusFailed:
		return v1.TransStatus_TRANS_STATUS_FAILED
	case model.TransStatusPending:
		return v1.TransStatus_TRANS_STATUS_PENDING
	case model.TransStatusProcessing:
		return v1.TransStatus_TRANS_STATUS_PROCESSING
	default:
		return v1.TransStatus_TRANS_STATUS_UNSPECIFIED
	}
}

func toTransTypes(types []v1.TransType) []model.TransType {
	result := make([]model.TransType, 0)
	for _, t := range types {
		if t == v1.TransType_TRANS_TYPE_UNSPECIFIED {
			continue
		}
		result = append(result, model.TransType(t))
	}
	return result
}

func toPagination(pagination *v1.Pagination) *model.Pagination {
	result := &model.Pagination{
		Limit: 100,
	}

	if pagination == nil {
		return result
	}

	if pagination.Limit > 0 {
		result.Limit = int(pagination.Limit)
	}
	if pagination.Cursor != nil {
		result.Cursor = pagination.Cursor
	}
	if pagination.Offset != nil {
		value := new(int)
		*value = cast.ToInt(pagination.Offset)
		result.Offset = value
	}
	if pagination.Direction != nil {
		result.Direct = model.Direction(*pagination.Direction)
	}

	return result
}
