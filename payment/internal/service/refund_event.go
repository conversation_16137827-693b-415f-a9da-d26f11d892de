package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"

	"github.com/segmentio/kafka-go"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/acquiring_core"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/kafkara"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/encoding/protojson"
)

func (s *RefundService) HandleRefundStatusEvent(message kafka.Message) error {
	// Extract tracing context from B3 headers if present
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)

	var refundEvent ACRefundKafkaEvent
	if len(message.Value) == 0 {
		logger.Error("Received empty refund order event")
		return nil
	}
	if err := json.Unmarshal(message.Value, &refundEvent); err != nil {
		logger.Errorw("Failed to unmarshal refund order event", "error", err)
		return err
	}
	if refundEvent.LogType != ACLogTypeRefund {
		return nil
	}

	logger.Infow(
		"msg", "Received refund order event",
		"event_data", refundEvent,
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	refundData := refundEvent.LogData
	refundLog, err := s.uc.RefundQuery(ctx, refundData.RefundID)
	if errors.Is(err, model.ErrRefundNotFound) {
		logger.Infow("msg", "Refund order not found", "refund_id", refundData.RefundID)
		return nil
	}
	if refundLog.IsComplete() {
		logger.Infow("msg", "Refund order is already completed", "refund_id", refundData.RefundID)
		return nil
	}

	newStatus := fromEventToInternalStatus(refundData.RefundStatus)
	if !newStatus.IsFinal() {
		logger.Infow("msg", "Refund order status is not final", "refund_id", refundData.RefundID, "status", newStatus)
		return nil
	}

	if err := s.uc.ProcessCompletion(ctx, refundData.RefundID, newStatus); err != nil {
		logger.Errorw("msg", "Failed to process refund order completion", "refund_id", refundData.RefundID, "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed refund order completion", "refund_id", refundData.RefundID)

	return nil
}

func (s *RefundService) HandleTopupStatusEvent(message kafka.Message) error {
	// Extract tracing context from B3 headers if present
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)
	topupCfg, ok := s.orderCfg.GetConfigRefundTopup("")

	if !ok {
		logger.Errorw("msg", "Failed to get topup config")
		return fmt.Errorf("failed to get topup config")
	}

	if len(message.Value) == 0 {
		logger.Error("Received empty topup order event")
		return nil
	}

	var orderEvent acquiring_core.Order
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(message.Value, &orderEvent); err != nil {
		logger.Errorw("Failed to unmarshal topup order event", "error", err)
		return err
	}

	// Check if the event is for the correct app ID and product code
	if orderEvent.GetAppId() != topupCfg.GetAppId() ||
		orderEvent.GetMetadata().GetProductCode() != topupCfg.GetProductCode() {
		return nil
	}

	logger.Infow(
		"msg", "Received topup order event",
		"event_data", orderEvent.String(),
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	paymentNo := orderEvent.GetPayment().GetPaymentNo()
	lockKey, err := s.distLock.AcquireRefundTopupOrderProcess(ctx, paymentNo)
	if err != nil {
		logger.Errorw("msg", "Failed to acquire lock", "error", err)
		return err
	}
	defer s.distLock.Release(ctx, lockKey)

	params := buildTopupOrderUpdateRequest(&orderEvent)
	if err = s.uc.ProcessTopupOrderUpdate(ctx, params); err != nil {
		logger.Errorw("msg", "Failed to process topup order update", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed topup order update", "params", params)

	return nil
}

func (s *RefundService) HandleSettleStatusEvent(message kafka.Message) error {
	// Extract tracing context from B3 headers if present
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)
	settleCfg, ok := s.orderCfg.GetConfigRefundSettle("")

	if !ok {
		logger.Errorw("msg", "Failed to get settle config")
		return fmt.Errorf("failed to get settle config")
	}

	if len(message.Value) == 0 {
		logger.Error("Received empty settle order event")
		return nil
	}

	var orderEvent acquiring_core.Order
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(message.Value, &orderEvent); err != nil {
		log.Printf("Failed to unmarshal settle order event: %v", err)
		return err
	}

	if orderEvent.GetAppId() != settleCfg.GetAppId() {
		return nil
	}

	logger.Infow(
		"msg", "Received settle order event",
		"event_data", orderEvent.String(),
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	paymentNo := orderEvent.GetPayment().GetPaymentNo()
	lockKey, err := s.distLock.AcquireRefundSettleOrderProcess(ctx, paymentNo)
	if err != nil {
		logger.Errorw("msg", "Failed to acquire lock", "error", err)
		return err
	}
	defer s.distLock.Release(ctx, lockKey)

	params := buildSettleOrderUpdateRequest(&orderEvent)
	if err = s.uc.ProcessSettleOrderUpdate(ctx, params); err != nil {
		logger.Errorw("msg", "Failed to process settle order update", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed settle order update", "params", params)

	return nil
}

func (s *RefundService) HandleFundbackStatusEvent(message kafka.Message) error {
	// Extract tracing context from B3 headers if present
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)

	// Log trace information for debugging
	traceID := trace.SpanContextFromContext(ctx).TraceID().String()
	spanID := trace.SpanContextFromContext(ctx).SpanID().String()
	logger.Infow("msg", "Tracing context from Kafka headers",
		"trace_id", traceID,
		"span_id", spanID,
		"raw_headers", message.Headers)

	funbackCfg, ok := s.orderCfg.GetConfigRefundFundback("")

	if !ok {
		logger.Errorw("msg", "Failed to get fundback config")
		return fmt.Errorf("failed to get fundback config")
	}

	if len(message.Value) == 0 {
		logger.Error("Received empty fundback order event")
		return nil
	}

	var orderEvent acquiring_core.Order
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(message.Value, &orderEvent); err != nil {
		logger.Errorf("Failed to unmarshal settle order event: %v", err)
		return err
	}

	if orderEvent.GetAppId() != funbackCfg.GetAppId() ||
		orderEvent.GetMetadata().GetProductCode() != funbackCfg.GetProductCode() {
		return nil
	}

	logger.Infow(
		"msg", "Received fundback order event",
		"event_data", orderEvent.String(),
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	params := buildFundbackOrderUpdateRequest(&orderEvent)
	if err := s.uc.HandleFundbackOrderUpdate(ctx, params); err != nil {
		logger.Errorw("msg", "Failed to process fundback order update", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed fundback order update", "params", params)

	return nil
}

func (s *RefundService) HandleRefundSettleRequests(message kafka.Message) error {
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)

	if len(message.Value) == 0 {
		log.Printf("Received empty refund settle event")
		return nil
	}

	var settleEvent v1.RefundSettleEvent
	if err := protojson.Unmarshal(message.Value, &settleEvent); err != nil {
		log.Printf("Failed to unmarshal refund settle event: %v", err)
		return err
	}
	if settleEvent.EventType != v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST {
		logger.Infow("msg", "Received non-request refund settle event", "event_type", settleEvent.EventType)
		return nil
	}

	logger.Infow(
		"msg", "Received refund settle event",
		"event_data", settleEvent.String(),
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	var err error
	switch settleEvent.GetSettleContext().(type) {
	case *v1.RefundSettleEvent_StandardSettle:
		err = s.HandleStandardSettleRequest(ctx, &settleEvent)
	case *v1.RefundSettleEvent_ExpiredSettle:
		err = s.HandleExpiredSettleRequest(ctx, &settleEvent)
	default:
		logger.Infow("msg", "Unknown settle context type", "settle_context", settleEvent.GetSettleContext())
		return nil
	}

	if err != nil {
		logger.Errorw("msg", "Failed to handle refund settle request", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully handled refund settle request", "settle_event", settleEvent.String())

	return nil
}

func (s *RefundService) HandleStandardSettleRequest(ctx context.Context, event *v1.RefundSettleEvent) error {
	logger := s.logger.WithContext(ctx)

	stdSettle := event.GetStandardSettle()
	settleID := stdSettle.GetSettleId()

	// Acquire distributed lock to prevent concurrent processing of the same refund settlement
	lockKey, err := s.distLock.AcquireProcessRefundSettle(ctx, settleID)
	if err != nil {
		logger.Infow("msg", "Failed to acquire lock for refund settlement", "settle_id", settleID, "error", err)
		return fmt.Errorf("failed to acquire lock for refund settlement: %w", err)
	}
	defer s.distLock.Release(ctx, lockKey)

	// Execute the refund settlement with the acquired lock
	err = s.uc.ExecuteRefundSettlement(ctx, settleID)
	if errors.Is(err, model.ErrRefundSettleNotFound) {
		logger.Infow("msg", "Refund settle not found", "settle_id", settleID)
		return nil
	}
	if err != nil {
		logger.Errorw("msg", "Failed to execute refund settlement", "settle_id", settleID, "error", err)
		return err
	}

	logger.Infow("msg", "Successfully executed refund settlement", "settle_id", settleID)
	return nil
}

func (s *RefundService) HandleExpiredSettleRequest(ctx context.Context, event *v1.RefundSettleEvent) error {
	logger := s.logger.WithContext(ctx)
	expSettle := event.GetExpiredSettle()

	params := &refund.ExpiredRefundSettleRequest{
		RefundID:     expSettle.GetRefundId(),
		RefZPTransID: event.GetRefZpTransId(),
		SettleAmount: event.GetSettleAmount(),
	}

	if err := s.uc.ExcuteExpiredRefundSettlement(ctx, params); err != nil {
		logger.Errorw("msg", "Failed to execute expired refund settlement", "error", err)
		return err
	}

	return nil
}

func (s *RefundService) HandleRefundSettleResults(message kafka.Message) error {
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)

	if len(message.Value) == 0 {
		logger.Infow("msg", "Received empty refund settle event")
		return nil
	}

	var settleEvent v1.RefundSettleEvent
	if err := protojson.Unmarshal(message.Value, &settleEvent); err != nil {
		logger.Errorw("msg", "Failed to unmarshal refund settle event", "error", err)
		return fmt.Errorf("failed to unmarshal refund settle event: %w", err)
	}

	// Check if the event type is RESPONSE
	if settleEvent.EventType != v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE {
		logger.Infow("msg", "Ignoring non-response event", "event_type", settleEvent.EventType)
		return nil
	}
	if settleEvent.GetSettleResult() == nil {
		logger.Infow("msg", "Ignoring event with nil settle result")
		return nil
	}

	logger.Infow(
		"msg", "Received refund settle response event",
		"event_data", settleEvent.String(),
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	var err error
	switch settleEvent.GetSettleContext().(type) {
	case *v1.RefundSettleEvent_StandardSettle:
		err = s.HandleStandardSettleResult(ctx, &settleEvent)
	case *v1.RefundSettleEvent_ExpiredSettle:
		err = s.HandleExpiredSettleResult(ctx, &settleEvent)
	default:
		logger.Infow("msg", "Unknown settle context type", "settle_context", settleEvent.GetSettleContext())
		return nil
	}

	if err != nil {
		logger.Errorw("msg", "Failed to handle refund settle response", "error", err)
		return err
	}

	logger.Infow("msg", "Successfully processed refund settle response", "settle_event", settleEvent.String())

	return nil
}

func (s *RefundService) HandleStandardSettleResult(ctx context.Context, event *v1.RefundSettleEvent) error {
	logger := s.logger.WithContext(ctx)
	stdSettle := event.GetStandardSettle()

	result := buildSettleEventResult(event)
	if err := s.uc.ProcessRefundSettleResult(ctx, result); err != nil {
		logger.Errorw("msg", "Failed to process standard refund settle response", "error", err, "settle_id", stdSettle.GetSettleId())
		return fmt.Errorf("failed to process refund standard settle response: %w", err)
	}

	logger.Infow("msg", "Successfully processed standard refund settle response", "settle_event", event)

	return nil
}

func (s *RefundService) HandleExpiredSettleResult(ctx context.Context, event *v1.RefundSettleEvent) error {
	logger := s.logger.WithContext(ctx)
	expSettle := event.GetExpiredSettle()

	result := buildSettleEventResult(event)
	if err := s.uc.ProcessRefundSettleResult(ctx, result); err != nil {
		logger.Errorw("msg", "Failed to process expired refund settle response", "error", err, "refund_id", expSettle.GetRefundId())
		return fmt.Errorf("failed to process expired refund settle response: %w", err)
	}

	logger.Infow("msg", "Successfully processed expired refund settle response", "settle_event", event)

	return nil
}

func buildSettleEventResult(params *v1.RefundSettleEvent) *refund.RefundSettleResult {
	return &refund.RefundSettleResult{
		OrderID:             params.SettleResult.GetOrderId(),
		AppID:               params.SettleResult.GetAppId(),
		AppTransID:          params.SettleResult.GetAppTransId(),
		RefTransID:          params.RefZpTransId,
		SettlePaymentID:     params.SettleResult.GetTransId(),
		SettlePaymentStatus: fromTransStatusToPaymentStatus(params.SettleResult.GetTransStatus()),
	}
}

func buildTopupOrderUpdateRequest(orderEvent *acquiring_core.Order) *refund.TopupOrderUpdateRequest {
	return &refund.TopupOrderUpdateRequest{
		AppID:        orderEvent.AppId,
		AppTransID:   orderEvent.AppTransId,
		ZPTransID:    extractOrderTransID(orderEvent),
		OriginStatus: cast.ToInt(orderEvent.Status),
		OrderStatus:  fromAcOrderToTopupStatus(orderEvent.Status),
		ReasonStatus: orderEvent.GetReasonStatus().GetReason(),
	}
}

func buildSettleOrderUpdateRequest(orderEvent *acquiring_core.Order) *refund.SettleOrderUpdateRequest {
	return &refund.SettleOrderUpdateRequest{
		AppID:        orderEvent.AppId,
		AppTransID:   orderEvent.AppTransId,
		ZPTransID:    extractOrderTransID(orderEvent),
		OriginStatus: cast.ToInt(orderEvent.Status),
		OrderStatus:  fromAcOrderToTopupStatus(orderEvent.Status),
		ReasonStatus: orderEvent.GetReasonStatus().GetReason(),
	}
}

func buildFundbackOrderUpdateRequest(orderEvent *acquiring_core.Order) *refund.FundbackOrderUpdateRequest {
	return &refund.FundbackOrderUpdateRequest{
		AppID:        orderEvent.AppId,
		AppTransID:   orderEvent.AppTransId,
		ZPTransID:    extractOrderTransID(orderEvent),
		OriginStatus: cast.ToInt(orderEvent.Status),
		OrderStatus:  fromAcOrderToTopupStatus(orderEvent.Status),
		ReasonStatus: orderEvent.GetReasonStatus().GetReason(),
	}
}

func fromAcOrderToTopupStatus(status acquiring_core.OrderStatus) model.OrderStatus {
	switch status {
	case acquiring_core.OrderStatus_SUCCESS:
		return model.OrderStatusSucceeded
	case acquiring_core.OrderStatus_FAILED:
		return model.OrderStatusFailed
	case acquiring_core.OrderStatus_CREATED,
		acquiring_core.OrderStatus_PENDING,
		acquiring_core.OrderStatus_PROCESSING:
		return model.OrderStatusPending
	default:
		return model.OrderStatusPending
	}
}

func fromTransStatusToPaymentStatus(status v1.TransStatus) model.PaymentStatus {
	switch status {
	case v1.TransStatus_TRANS_STATUS_PROCESSING:
		return model.PaymentStatusProcessing
	case v1.TransStatus_TRANS_STATUS_PENDING:
		return model.PaymentStatusPending
	case v1.TransStatus_TRANS_STATUS_SUCCESS:
		return model.PaymentStatusSucceeded
	case v1.TransStatus_TRANS_STATUS_FAILED:
		return model.PaymentStatusFailed
	default:
		return model.PaymentStatusInit
	}
}

func fromEventToInternalStatus(status ACRefundStatus) model.RefundStatus {
	switch status {
	case ACRefundStatusSuccess:
		return model.RefundStatusSuccess
	case ACRefundStatusFailed:
		return model.RefundStatusFailed
	case ACRefundStatusPending:
		return model.RefundStatusPending
	case ACRefundStatusProcessing:
		return model.RefundStatusProcessing
	default:
		if status < ACRefundStatusPending {
			return model.RefundStatusFailed
		}
		return model.RefundStatusInit
	}
}

func extractOrderTransID(order *acquiring_core.Order) int64 {
	txnList := order.
		GetPayment().
		GetTransactionList().
		GetTransactions()
	txnLen := len(txnList)

	if txnLen == 0 {
		return 0
	}

	// 1. Find the transaction with the same app ID
	for _, txn := range txnList {
		if txn.GetMetadata().GetAppId() == order.GetAppId() {
			return txn.GetTransId()
		}
	}

	// 2. Fallback by get latest transaction in the list
	return txnList[txnLen-1].GetTransId()
}
