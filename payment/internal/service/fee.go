package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
)

type FeeService struct {
	v1.UnimplementedFeeServiceServer
	//uc     *purchase.PurchaseUsecase
	logger *log.Helper
}

func NewFeeService(logger log.Logger) *FeeService {
	return &FeeService{
		logger: log.NewHelper(logger),
	}
}

func (s *FeeService) GetFee(ctx context.Context, req *v1.GetFeeRequest) (*v1.GetFeeResponse, error) {
	// validate request
	if err := req.Validate(); err != nil {
		s.logger.WithContext(ctx).Errorw("msg", "get fee req is invalid", "error", err)
		return nil, errors.BadRequest("invalid_argument", err.<PERSON>rror())
	}

	return &v1.GetFeeResponse{
		Fee: &v1.Fee{
			FeeType:   v1.FeeType_FEE_TYPE_CONVERSION,
			FeeAmount: 4000,
			Message:   "Phí nền tảng trả góp",
			MessageEn: "",
		},
	}, nil
}
