package settle_notifier

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	v1 "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/kafkara"
	"google.golang.org/protobuf/encoding/protojson"
	kafka_client "zalopay.io/zgo/kafka-client"
)

const (
	SettleEventKeyPattern  = "refund_settle:%d:%d"
	ExpiredEventKeyPattern = "refund_expired:%d"
	ExpiredEventIDPattern  = "refund_expired:%d:%d"
)

type settleNotifier struct {
	logger    *log.Helper
	kLogger   log.Logger
	publisher kafka_client.Publisher
}

func NewNotifier(publisher kafka_client.Publisher, kLogger log.Logger) types.RefundSettleNotifier {
	return &settleNotifier{
		kLogger:   kLogger,
		publisher: publisher,
		logger:    log.NewHelper(log.With(kLogger, "module", "settle_notifier")),
	}
}

// PublishRefundSettleEvent implements types.RefundSettleNotifier.
func (s *settleNotifier) PublishRefundSettleEvent(ctx context.Context, settle *model.RefundSettle) error {
	logger := s.logger.WithContext(ctx)

	eventKey := fmt.Sprintf(SettleEventKeyPattern, settle.ID, settle.ZPTransID)
	eventID := eventKey
	request := &v1.RefundSettleEvent{
		EventId:      eventID,
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST,
		RefZpTransId: settle.ZPTransID,
		SettleAmount: settle.SettlementAmount,
		SettleStatus: fromRefundSettleToSettleStatus(settle.Status),
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{SettleId: settle.ID},
		},
	}

	marshaller := protojson.MarshalOptions{UseProtoNames: true}
	requestBin, err := marshaller.Marshal(request)
	if err != nil {
		logger.Errorw("msg", "failed to marshal refund settle event", "error", err)
		return err
	}

	// Extract B3 tracing headers from context using the dedicated function for client headers
	kafkaHeaders := kafkara.InjectB3ToClientKafkaHeaders(ctx)

	// Publish message with tracing headers
	if err := s.publisher.PublishRaw(ctx, eventKey, requestBin, kafkaHeaders...); err != nil {
		logger.Errorw("msg", "publish refund settle event failed", "error", err)
		return err
	}

	logger.Infow("msg", "publish refund settle event success", "event_id", eventID)
	return nil
}

// PublishRefundSettleResult implements types.RefundSettleNotifier.
func (s *settleNotifier) PublishRefundSettleResult(ctx context.Context, settle *model.RefundSettle, ead *model.EarlyDischargeLog) error {
	logger := s.logger.WithContext(ctx)

	eventKey := fmt.Sprintf(SettleEventKeyPattern, settle.ID, settle.ZPTransID)
	eventID := eventKey
	request := &v1.RefundSettleEvent{
		EventId:      eventID,
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE,
		RefZpTransId: settle.ZPTransID,
		SettleAmount: settle.SettlementAmount,
		SettleStatus: fromRefundSettleToSettleStatus(settle.Status),
		SettleResult: &v1.RefundSettleEvent_RefundSettleResult{
			TransId:      ead.ID,
			OrderId:      ead.Order.ID,
			AppId:        ead.Order.AppID,
			AppTransId:   ead.Order.AppTransID,
			TransStatus:  fromTransStatus(ead.Status),
			ErrorMessage: buildEarlyDischargeErrorMessage(ead),
		},
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{SettleId: settle.ID},
		},
	}

	marshaller := protojson.MarshalOptions{UseProtoNames: true}
	requestBin, err := marshaller.Marshal(request)
	if err != nil {
		logger.Errorw("msg", "failed to marshal refund settle result", "error", err)
		return err
	}

	// Extract B3 tracing headers from context using the dedicated function for client headers
	kafkaHeaders := kafkara.InjectB3ToClientKafkaHeaders(ctx)

	// Publish message with tracing headers
	if err := s.publisher.PublishRaw(ctx, eventKey, requestBin, kafkaHeaders...); err != nil {
		logger.Errorw("msg", "publish refund settle result failed", "error", err)
		return err
	}

	logger.Infow("msg", "publish refund settle result success", "event_id", eventID)
	return nil
}

func (s *settleNotifier) PublishRefundExpiredEvents(ctx context.Context, zpTransID int64, refunds model.RefundOrders) error {
	logger := s.logger.WithContext(ctx)
	eventKey := fmt.Sprintf(ExpiredEventKeyPattern, zpTransID)

	// Build and publish events individually with tracing headers
	eventSources := make([]kafka_client.Source, len(refunds))
	for i, refund := range refunds {
		eventID := fmt.Sprintf(ExpiredEventIDPattern, refund.ID, refund.ZPTransID)
		eventData := &v1.RefundSettleEvent{
			EventId:      eventID,
			EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST,
			RefZpTransId: refund.ZPTransID,
			SettleAmount: refund.Amount,
			SettleStatus: v1.SettleStatus_SETTLE_STATUS_UNSETTLED,
			SettleContext: &v1.RefundSettleEvent_ExpiredSettle{
				ExpiredSettle: &v1.RefundExpiredSettle{RefundId: refund.ID},
			},
		}
		marshaller := protojson.MarshalOptions{UseProtoNames: true}
		eventByte, _ := marshaller.Marshal(eventData)
		eventSources[i] = kafka_client.Source(eventByte)
	}

	// PublishRaws doesn't support adding headers, so we need to use the existing method
	// Note: In a future enhancement, consider implementing individual PublishRaw calls with headers
	// if precise distributed tracing is critical for your use case
	if err := s.publisher.PublishRaws(ctx, eventKey, eventSources...); err != nil {
		logger.Errorw("msg", "publish refund expired events failed", "error", err)
		return err
	}

	logger.Infow(
		"msg", "published refund expired events successfully",
		"event_key", eventKey,
		"count", len(eventSources),
	)

	return nil
}

func (s *settleNotifier) PublishRefundExpiredResult(ctx context.Context, settleOrder *model.RefundSettleOrder, repayLog *model.RepaymentLog) error {
	logger := s.logger.WithContext(ctx)
	metadata := settleOrder.ExtraData
	eventKey := fmt.Sprintf(ExpiredEventKeyPattern, metadata.GetRefZPTransID())
	eventID := fmt.Sprintf(ExpiredEventIDPattern, settleOrder.RefundID, metadata.GetRefZPTransID())
	request := &v1.RefundSettleEvent{
		EventId:      eventID,
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE,
		RefZpTransId: metadata.GetRefZPTransID(),
		SettleAmount: repayLog.Order.Amount,
		SettleStatus: fromPaymentStatusToSettleStatus(repayLog.Status),
		SettleResult: &v1.RefundSettleEvent_RefundSettleResult{
			TransId:      repayLog.ID,
			OrderId:      repayLog.Order.ID,
			AppId:        repayLog.Order.AppID,
			AppTransId:   repayLog.Order.AppTransID,
			TransStatus:  fromTransStatus(repayLog.Status),
			ErrorMessage: buildExpiredRefundRepayErrorMessage(repayLog),
		},
		SettleContext: &v1.RefundSettleEvent_ExpiredSettle{
			ExpiredSettle: &v1.RefundExpiredSettle{RefundId: settleOrder.RefundID},
		},
	}

	marshaller := protojson.MarshalOptions{UseProtoNames: true}
	requestBin, err := marshaller.Marshal(request)
	if err != nil {
		logger.Errorw("msg", "failed to marshal refund expired result", "error", err)
		return err
	}

	// Extract B3 tracing headers from context using the dedicated function for client headers
	kafkaHeaders := kafkara.InjectB3ToClientKafkaHeaders(ctx)

	// Use PublishRaw with Kafka headers directly
	if err := s.publisher.PublishRaw(ctx, eventKey, requestBin, kafkaHeaders...); err != nil {
		logger.Errorw("msg", "publish refund settle result failed", "error", err)
		return err
	}

	logger.Infow("msg", "publish refund settle result success", "event_id", eventID)
	return nil
}

func fromRefundSettleToSettleStatus(status model.RefundSettleStatus) v1.SettleStatus {
	switch status {
	case model.RefundSettleStatusInit, model.RefundSettleStatusPending:
		return v1.SettleStatus_SETTLE_STATUS_UNSETTLED
	case model.RefundSettleStatusProcessing:
		return v1.SettleStatus_SETTLE_STATUS_PROCESSING
	case model.RefundSettleStatusSettled, model.RefundSettleStatusCompleted:
		return v1.SettleStatus_SETTLE_STATUS_SETTLED
	default:
		return v1.SettleStatus_SETTLE_STATUS_UNSETTLED
	}
}

func fromPaymentStatusToSettleStatus(status model.PaymentStatus) v1.SettleStatus {
	switch status {
	case model.PaymentStatusPending, model.PaymentStatusInit:
		return v1.SettleStatus_SETTLE_STATUS_UNSETTLED
	case model.PaymentStatusProcessing:
		return v1.SettleStatus_SETTLE_STATUS_PROCESSING
	case model.PaymentStatusSucceeded:
		return v1.SettleStatus_SETTLE_STATUS_SETTLED
	case model.PaymentStatusFailed:
		return v1.SettleStatus_SETTLE_STATUS_UNSETTLED
	default:
		return v1.SettleStatus_SETTLE_STATUS_UNSETTLED
	}
}

func fromTransStatus(status model.PaymentStatus) v1.TransStatus {
	switch status {
	case model.PaymentStatusSucceeded:
		return v1.TransStatus_TRANS_STATUS_SUCCESS
	case model.PaymentStatusFailed:
		return v1.TransStatus_TRANS_STATUS_FAILED
	default:
		return v1.TransStatus_TRANS_STATUS_PROCESSING
	}
}

func buildEarlyDischargeErrorMessage(ead *model.EarlyDischargeLog) string {
	if ead == nil || !ead.Status.IsFailedStatus() {
		return ""
	}
	errCode := ead.PartnerData.RepaymentResult.ErrorCode
	errDesc := ead.PartnerData.RepaymentResult.Description
	return fmt.Sprintf("ErrorCode: %s, ErrorMessage: %s", errCode, errDesc)
}

func buildExpiredRefundRepayErrorMessage(repay *model.RepaymentLog) string {
	if repay == nil || !repay.Status.IsFailedStatus() {
		return ""
	}
	errCode := repay.PartnerData.RepaymentResult.ErrorCode
	errDesc := repay.PartnerData.RepaymentResult.Description
	return fmt.Sprintf("ErrorCode: %s, ErrorMessage: %s", errCode, errDesc)
}
