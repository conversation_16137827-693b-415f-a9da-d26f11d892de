package payment_auth

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	pa "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
)

type client struct {
	logger     log.Logger
	grpcClient pa.PaymentAuthenticationServiceClient
}

func NewClient(grpcClient pa.PaymentAuthenticationServiceClient, kLogger log.Logger) types.PaymentAuthAdapter {
	logger := log.With(kLogger, "adapters", "payment_auth")
	return &client{
		logger:     logger,
		grpcClient: grpcClient,
	}
}

func (p *client) Authenticate(ctx context.Context, po model.PurchaseOrder, authType pa.AuthType) (string, error) {
	logger := log.NewHelper(
		log.With(p.logger, "payment_no", po.PaymentNo, "zp_trans_id", po.ZpTransID, "auth_type", authType)).
		WithContext(ctx)

	resp, err := p.grpcClient.TriggerAuthByFund(ctx, &pa.TriggerAuthByFundRequest{
		PaymentNo: po.PaymentNo,
		TransId:   cast.ToInt64(po.ZpTransID),
		AuthType:  authType,
	})
	if err != nil {
		logger.WithContext(ctx).Errorw("msg", "TriggerAuthByFund got error", "error", err)
		return "", fmt.Errorf("triggerAuthByFund got error: %w", err)
	}

	if resp.GetError() != nil && resp.GetError().GetCode() != pa.ErrorCode_ER_CODE_SUCCESS {
		logger.Errorw("msg", "TriggerAuthByFund got error", "code", resp.GetError().GetCode(), "msg", resp.GetError().GetMessage())
		return "", fmt.Errorf("triggerAuthByFund got error, code: %d, msg: %s", resp.GetError().GetCode(), resp.GetError().GetMessage())
	}

	logger.WithContext(ctx).Infow("msg", "TriggerAuthByFund successfully", "resp", resp)

	return resp.GetAuthSessionId(), nil
}
