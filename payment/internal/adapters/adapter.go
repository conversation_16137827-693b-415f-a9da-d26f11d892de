package adapters

import (
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/acquiring_core"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/cimb"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/installment_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/payment_auth"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/settle_notifier"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/task_job"
)

var ProviderSet = wire.NewSet(
	cimb.NewService,
	//cimb.NewMockService,
	task_job.NewTaskJob,
	dist_lock.NewDistLock,
	account_service.NewClient,
	acquiring_core.NewClient,
	auth_service.NewClient,
	payment_auth.NewClient,
	settle_notifier.NewNotifier,
	installment_service.NewClient,
)
