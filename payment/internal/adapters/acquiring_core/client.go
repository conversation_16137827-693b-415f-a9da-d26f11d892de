package acquiring_core

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/acquiring_core"
	ac "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/acquiring_core"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

const (
	SettleOrderProductCode   = "AC005"
	TopupOrderProductCode    = "TU010"
	FundbackOrderProductCode = "TF028"
	TopupOrderDescription    = "Nạp tiền vào tài khoản <PERSON>alo<PERSON>"
	DefaultOrderProviderCode = "TOPUP"
)

type client struct {
	logger       *log.Helper
	grpcClient   ac.UserPaymentClient
	orderConfigs *configs.OrderConfigsHelper
}

func NewClient(grpcClient ac.UserPaymentClient, kLogger log.Logger, orderConfigs *configs.OrderConfigsHelper) types.AcquiringCoreAdapter {
	logger := log.NewHelper(log.With(kLogger, "adapters", "account_service"))
	return &client{
		logger:       logger,
		grpcClient:   grpcClient,
		orderConfigs: orderConfigs,
	}
}

func (c *client) CreateOrder(ctx context.Context, req model.RepayOrderRequest) (*model.RepayOrderResponse, error) {
	orderCfg, ok := c.orderConfigs.GetConfigRepayment(partner.CodeFromString(req.PartnerCode))
	if !ok {
		c.logger.WithContext(ctx).Errorw("msg", "partner not found", "req", req)
		return nil, model.ErrPartnerNotFound
	}

	resp, err := c.grpcClient.CreateOrder(ctx, &ac.CreateOrderRequest{
		AppId:       orderCfg.AppId,
		AppTransId:  req.AppTransID,
		Amount:      req.Amount,
		PaymentType: ac.PaymentType_PAYMENT,
		DestAsset: &ac.DestAsset{
			DestAssetType: ac.DestAssetType_MERCHANT,
			DestAsset: &ac.DestAsset_Merchant{
				Merchant: &ac.MerchantAsset{
					AppId: orderCfg.AppId,
				},
			},
		},
		Metadata: &ac.OrderMeta{
			Labels:      nil,
			AppTime:     req.AppTime,
			AppUser:     req.AppUser,
			Description: req.Description,
			ProductCode: req.ProductCode,
			EmbedData:   req.EmbedData,
		},
	})
	if err != nil {
		c.logger.WithContext(ctx).Errorw("msg", "create order failed", "error", err)
		return nil, err
	}

	c.logger.WithContext(ctx).Infow("msg", "create order success", "response", resp, "req", req)

	return &model.RepayOrderResponse{
		OrderNo:      resp.GetOrderNo(),
		ZpTransToken: resp.GetOrderToken(),
		OrderUrl:     resp.GetOrderUrl(),
		OrderStatus:  ac.OrderStatus_name[int32(resp.GetStatus())],
		ReasonStatus: resp.GetReasonStatus().GetReason(),
		CreatedTime:  resp.GetCreateTime().AsTime(),
		AppTransID:   req.AppTransID,
		AppID:        orderCfg.AppId,
	}, nil
}

func (c *client) CreateRefundTopupOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error) {
	logger := c.logger.WithContext(ctx)
	orderCfg, ok := c.orderConfigs.GetConfigRefundTopup("")
	if !ok {
		logger.Errorw("msg", "config not found")
		return nil, errors.New("config not found")
	}

	req, err := buildRefundTopupOrderRequest(params, orderCfg)
	if err != nil {
		logger.Errorw("msg", "failed to build request", "error", err)
		return nil, err
	}

	resp, err := c.grpcClient.CreateOrder(ctx, req)
	if err != nil {
		logger.Errorw("msg", "failed to create order", "error", err)
		return nil, err
	}

	logger.Infow("msg", "create refund topup order success", "orderInfo", resp)

	return &model.CreateRefundOrderResponse{
		Amount:       params.Amount,
		AppID:        orderCfg.GetAppId(),
		OrderNo:      resp.GetOrderNo(),
		ZpTransToken: resp.GetOrderToken(),
		OrderStatus:  int32(resp.GetStatus()),
		Description:  params.Description,
		AppTransID:   params.AppTransID,
		DataChecksum: resp.GetChecksum(),
	}, nil
}

func buildRefundTopupOrderRequest(params *model.CreateRefundOrderRequest, orderCfg *configs.OrderConfig) (*acquiring_core.CreateOrderRequest, error) {
	userID := cast.ToString(params.ZaloPayID)
	embedData := &embedData{
		RefTransId: params.RefZPTransID,
	}
	embedDataBin, err := jsoniter.Marshal(embedData)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal embed data")
	}

	if params.Description == "" {
		params.Description = TopupOrderDescription
	}

	return &acquiring_core.CreateOrderRequest{
		AppId:         orderCfg.GetAppId(),
		AppTransId:    params.AppTransID,
		Amount:        params.Amount,
		PaymentType:   acquiring_core.PaymentType_PAYMENT,
		CaptureMethod: acquiring_core.CaptureMethod_CAPTURE_METHOD_AUTO,
		DestAsset: &acquiring_core.DestAsset{
			DestAssetType: acquiring_core.DestAssetType_USER,
			DestAsset: &acquiring_core.DestAsset_UserWallet{
				UserWallet: &acquiring_core.UserWalletAsset{
					UserId: userID,
					Type:   acquiring_core.WalletType_RESTRICTED,
				},
			},
		},
		Metadata: &acquiring_core.OrderMeta{
			AppUser:     userID,
			AppTime:     time.Now().UnixMilli(),
			Description: params.Description,
			ProductCode: TopupOrderProductCode,
			RefTransId:  params.RefZPTransID,
			EmbedData:   string(embedDataBin),
		},
	}, nil
}

func (c *client) CreateRefundSettleOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error) {
	logger := c.logger.WithContext(ctx)

	logger.Infow("msg", "CreateSettleOrder", "params", params)

	orderCfg, ok := c.orderConfigs.GetConfigRefundSettle(params.PartnerCode)
	if !ok {
		logger.Errorw("msg", "config not found")
		return nil, model.ErrPartnerNotFound
	}

	req, err := buildRefundSettleOrderRequest(params, orderCfg)
	if err != nil {
		logger.Errorw("msg", "failed to build request", "error", err)
		return nil, err
	}

	resp, err := c.grpcClient.CreateOrder(ctx, req)
	if err != nil {
		logger.Errorw("msg", "create settle order failed", "error", err)
		return nil, err
	}

	logger.Infow("msg", "create settle order success", "response", resp)

	return &model.CreateRefundOrderResponse{
		Amount:       params.Amount,
		OrderNo:      resp.GetOrderNo(),
		ZpTransToken: resp.GetOrderToken(),
		OrderStatus:  int32(resp.GetStatus()),
		Description:  params.Description,
		AppID:        orderCfg.GetAppId(),
		AppTransID:   params.AppTransID,
		DataChecksum: resp.GetChecksum(),
	}, nil
}

func buildRefundSettleOrderRequest(params *model.CreateRefundOrderRequest, orderCfg *configs.OrderConfig) (*acquiring_core.CreateOrderRequest, error) {
	userID := cast.ToString(params.ZaloPayID)
	embedData := &embedData{
		RefTransId: params.RefZPTransID,
	}
	embedDataBin, err := jsoniter.Marshal(embedData)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal embed data")
	}

	return &acquiring_core.CreateOrderRequest{
		AppId:         orderCfg.GetAppId(),
		AppTransId:    params.AppTransID,
		Amount:        params.Amount,
		PaymentType:   acquiring_core.PaymentType_PAYMENT,
		CaptureMethod: acquiring_core.CaptureMethod_CAPTURE_METHOD_UNSPECIFIED,
		DestAsset: &acquiring_core.DestAsset{
			DestAssetType: acquiring_core.DestAssetType_MERCHANT,
			DestAsset: &acquiring_core.DestAsset_Merchant{
				Merchant: &acquiring_core.MerchantAsset{
					AppId:      orderCfg.GetAppId(),
					MerchantId: orderCfg.GetMerchantId(),
				},
			},
		},
		Metadata: &acquiring_core.OrderMeta{
			AppUser:     userID,
			AppTime:     time.Now().UnixMilli(),
			Description: params.Description,
			ProductCode: SettleOrderProductCode,
			RefTransId:  params.RefZPTransID,
			EmbedData:   string(embedDataBin),
		},
	}, nil
}

func (c *client) SubmitRefundSettleOrder(ctx context.Context, order *model.RefundSettleOrder) error {
	logger := c.logger.WithContext(ctx)

	logger.Infow("msg", "SubmitRefundSettleOrder", "order", order)

	resp, err := c.grpcClient.ConfirmPay(ctx, &ac.ConfirmPayRequest{
		OrderNo:  order.ExtraData.ExtOrderNo,
		Checksum: order.ExtraData.ExtChecksum,
		Metadata: &ac.PaymentMeta{
			ProductCode:   SettleOrderProductCode,
			PaymentSystem: ac.PaymentSystem_WALLET,
		},
		Payer: &ac.Payer{
			Payer: &ac.Payer_User{
				User: &ac.User{
					UserId: cast.ToString(order.ZalopayID),
				},
			},
		},
		SourceAssets: []*ac.SourceAsset{{
			Amount:        order.Amount,
			PaymentMethod: ac.PaymentMethod_WBL,
			SourceAsset: &ac.SourceAsset_UserWallet{
				UserWallet: &ac.UserWalletAsset{
					Type:   ac.WalletType_RESTRICTED,
					UserId: cast.ToString(order.ZalopayID),
				},
			},
		}},
	})
	if err != nil {
		logger.Errorw("msg", "submit refund settle order failed", "error", err)
		return err
	}

	logger.Infow("msg", "submit refund settle order success", "response", resp)

	return nil
}

func (c *client) CreateRefundFundbackOrder(ctx context.Context, params *model.CreateRefundOrderRequest) (*model.CreateRefundOrderResponse, error) {
	logger := c.logger.WithContext(ctx)

	logger.Infow("msg", "CreateRefundFundbackOrder", "params", params)

	orderCfg, ok := c.orderConfigs.GetConfigRefundFundback("")
	if !ok {
		logger.Errorw("msg", "config not found")
		return nil, errors.New("config not found")
	}

	req, err := buildRefundFundbackOrderRequest(params, orderCfg)
	if err != nil {
		logger.Errorw("msg", "failed to build request", "error", err)
		return nil, err
	}

	resp, err := c.grpcClient.CreateOrder(ctx, req)
	if err != nil {
		logger.Errorw("msg", "failed to create order", "error", err)
		return nil, err
	}

	logger.Infow("msg", "create refund topup order success", "orderInfo", resp)

	return &model.CreateRefundOrderResponse{
		Amount:       params.Amount,
		AppID:        orderCfg.GetAppId(),
		OrderNo:      resp.GetOrderNo(),
		ZpTransToken: resp.GetOrderToken(),
		OrderStatus:  int32(resp.GetStatus()),
		Description:  params.Description,
		AppTransID:   params.AppTransID,
		DataChecksum: resp.GetChecksum(),
	}, nil
}

func (c *client) SubmitRefundFundbackOrder(ctx context.Context, order *model.RefundFundbackOrder) error {
	logger := c.logger.WithContext(ctx)

	logger.Infow("msg", "SubmitRefundFundbackOrder", "order", order)

	resp, err := c.grpcClient.ConfirmPay(ctx, &ac.ConfirmPayRequest{
		OrderNo:  order.ExtraData.ExtOrderNo,
		Checksum: order.ExtraData.ExtChecksum,
		Metadata: &ac.PaymentMeta{
			ProductCode:   FundbackOrderProductCode,
			PaymentSystem: ac.PaymentSystem_WALLET,
		},
		Payer: &ac.Payer{
			Payer: &ac.Payer_User{
				User: &ac.User{
					UserId: cast.ToString(order.ZalopayID),
				},
			},
		},
		SourceAssets: []*ac.SourceAsset{{
			Amount:        order.Amount,
			PaymentMethod: ac.PaymentMethod_WBL,
			SourceAsset: &ac.SourceAsset_UserWallet{
				UserWallet: &ac.UserWalletAsset{
					Type:   ac.WalletType_RESTRICTED,
					UserId: cast.ToString(order.ZalopayID),
				},
			},
		}},
	})
	if err != nil {
		logger.Errorw("msg", "submit refund settle order failed", "error", err)
		return err
	}

	logger.Infow("msg", "submit refund settle order success", "response", resp)

	return nil
}

func buildRefundFundbackOrderRequest(params *model.CreateRefundOrderRequest, orderCfg *configs.OrderConfig) (*acquiring_core.CreateOrderRequest, error) {
	userID := cast.ToString(params.ZaloPayID)
	embedData := &embedData{
		RefTransId: params.RefZPTransID,
	}
	embedDataBin, err := jsoniter.Marshal(embedData)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal embed data")
	}

	if params.Description == "" {
		params.Description = TopupOrderDescription
	}

	return &acquiring_core.CreateOrderRequest{
		AppId:         orderCfg.GetAppId(),
		AppTransId:    params.AppTransID,
		Amount:        params.Amount,
		PaymentType:   acquiring_core.PaymentType_TRANSFER,
		CaptureMethod: acquiring_core.CaptureMethod_CAPTURE_METHOD_AUTO,
		DestAsset: &acquiring_core.DestAsset{
			DestAssetType: acquiring_core.DestAssetType_USER,
			DestAsset: &acquiring_core.DestAsset_UserWallet{
				UserWallet: &acquiring_core.UserWalletAsset{
					UserId: userID,
					Type:   acquiring_core.WalletType_MAIN,
				},
			},
		},
		Metadata: &acquiring_core.OrderMeta{
			AppUser:     userID,
			AppTime:     time.Now().UnixMilli(),
			Description: params.Description,
			ProductCode: FundbackOrderProductCode,
			RefTransId:  params.RefZPTransID,
			EmbedData:   string(embedDataBin),
		},
	}, nil
}
