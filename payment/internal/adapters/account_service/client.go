package account_service

import (
	"context"
	accountv1 "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/account/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"

	"github.com/go-kratos/kratos/v2/log"
)

type client struct {
	logger     *log.Helper
	grpcClient accountv1.AccountClient
}

func (c *client) GetAccount(ctx context.Context, zalopayID int64, partnerCode string) (model.Account, error) {
	resp, err := c.grpcClient.GetAccountForPayment(ctx, &accountv1.GetAccountForPaymentRequest{
		ZalopayId:   zalopayID,
		PartnerCode: partnerCode,
	})
	if err != nil {
		c.logger.WithContext(ctx).Errorw("msg", "get account failed", "error", err)
		return model.Account{}, err
	}

	c.logger.WithContext(ctx).Infow("msg", "get account success", "response", resp, "zalopay_id", zalopayID, "partner_code", partnerCode)

	return model.Account{
		ID:                 resp.GetAccount().GetAccountId(),
		ZalopayID:          zalopayID,
		PartnerCode:        resp.GetAccount().GetPartnerCode(),
		Status:             toAccountStatus(resp.GetAccount().GetStatus()),
		AvailableBalance:   resp.GetAccount().GetInstallmentLimit(),
		PartnerAccountId:   resp.GetAccount().GetPartnerAccountNumber(),
		PartnerAccountName: resp.GetAccount().GetPartnerAccountName(),
	}, nil
}

func toAccountStatus(status accountv1.Status) model.AccountStatus {
	switch status {
	case accountv1.Status_ACTIVE:
		return model.AccountStatusActive
	case accountv1.Status_INACTIVE:
		return model.AccountStatusInactive
	default:
		return model.AccountStatusInactive
	}
}

func NewClient(grpcClient accountv1.AccountClient, kLogger log.Logger) types.AccountAdapter {
	logger := log.NewHelper(log.With(kLogger, "adapters", "account_service"))
	return &client{
		logger:     logger,
		grpcClient: grpcClient,
	}
}
