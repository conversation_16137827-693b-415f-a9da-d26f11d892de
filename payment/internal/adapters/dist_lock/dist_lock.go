package dist_lock

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/platform/common/redis"
)

type distLock struct {
	env     string
	logger  *log.Helper
	redSync *redsync.Redsync
	mutex   sync.RWMutex
	holder  map[string]*redsync.Mutex
}

const (
	seperator           = ":"
	namespace           = "fin:installment:payment"
	defaultTryLockTime  = 4
	defaultTryLockDelay = 500 * time.Millisecond

	reconRefundSettleKeyPattern        = "refund_settle:reconcile:%d"
	processRefundSettleKeyPattern      = "refund_settle:process:%d"
	refundTopupOrderProcessKeyPattern  = "refund_topup_order:%d"
	refundSettleOrderProcessKeyPattern = "refund_settle_order:%d"
)

func NewDistLock(conf *configs.Payment, redisCache redis.CacheNoCaller, logger log.Logger) types.DistributedLock {
	env := conf.GetApp().GetEnv()
	redCli := redisCache.GetRedisClient()
	redSync := redsync.New(goredis.NewPool(redCli))
	logging := log.With(logger, "adapters", "dist_lock")
	return &distLock{
		env:     env,
		redSync: redSync,
		mutex:   sync.RWMutex{},
		holder:  make(map[string]*redsync.Mutex),
		logger:  log.NewHelper(logging),
	}
}

func (d *distLock) buildLockKey(resource string) (string, error) {
	if resource == "" {
		return "", fmt.Errorf("resource is empty")
	}
	return fmt.Sprintf("%s:%s:%s", namespace, d.env, resource), nil
}

func (d *distLock) Acquire(ctx context.Context, resource string, ttl time.Duration) (string, error) {
	lockKey, err := d.buildLockKey(resource)
	if err != nil {
		return "", fmt.Errorf("build lock key failed, error=%v", err)
	}

	mutex := d.redSync.NewMutex(lockKey,
		redsync.WithExpiry(ttl),
		redsync.WithTries(defaultTryLockTime),
		redsync.WithRetryDelay(defaultTryLockDelay),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
		return "", errors.Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
	}

	d.mutex.Lock()
	d.holder[lockKey] = mutex
	d.mutex.Unlock()

	return lockKey, nil
}

func (d *distLock) Release(ctx context.Context, lockKey string) error {
	d.mutex.RLock()
	locker := d.holder[lockKey]
	d.mutex.RUnlock()

	if locker == nil {
		return nil
	}

	newCtx := context.WithoutCancel(ctx)
	if ok, err := locker.UnlockContext(newCtx); !ok || err != nil {
		d.logger.WithContext(newCtx).Errorf("release lock failed, lockKey=%s, error=%v", lockKey, err)
		return errors.Errorf("release lock failed, lockKey=%s, error=%v", lockKey, err)
	}

	// Release lock successfully
	d.mutex.Lock()
	delete(d.holder, lockKey)
	d.mutex.Unlock()

	return nil
}

func (d *distLock) IsLocked(ctx context.Context, lockKey string) (bool, error) {
	d.mutex.RLock()
	locker := d.holder[lockKey]
	d.mutex.RUnlock()

	if locker == nil {
		return false, nil
	}
	return true, nil
}

func (d *distLock) AcquireReconcileRefundSettle(ctx context.Context, zpTransID int64) (string, error) {
	resource := fmt.Sprintf(reconRefundSettleKeyPattern, zpTransID)
	lockKey, err := d.buildLockKey(resource)
	if err != nil {
		return "", fmt.Errorf("build lock key failed, error=%v", err)
	}

	mutex := d.redSync.NewMutex(lockKey,
		redsync.WithTries(3),
		redsync.WithExpiry(time.Minute),
		redsync.WithRetryDelay(time.Second*2),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
		return "", errors.Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
	}

	d.mutex.Lock()
	d.holder[lockKey] = mutex
	d.mutex.Unlock()

	return lockKey, nil
}

func (d *distLock) AcquireRefundTopupOrderProcess(ctx context.Context, zpTransID int64) (string, error) {
	resource := fmt.Sprintf(refundTopupOrderProcessKeyPattern, zpTransID)
	lockKey, err := d.buildLockKey(resource)
	if err != nil {
		return "", fmt.Errorf("build lock key failed, error=%v", err)
	}

	mutex := d.redSync.NewMutex(lockKey,
		redsync.WithExpiry(time.Minute),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
		return "", errors.Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
	}

	d.mutex.Lock()
	d.holder[lockKey] = mutex
	d.mutex.Unlock()

	return lockKey, nil
}

func (d *distLock) AcquireRefundSettleOrderProcess(ctx context.Context, zpTransID int64) (string, error) {
	resource := fmt.Sprintf(refundSettleOrderProcessKeyPattern, zpTransID)
	lockKey, err := d.buildLockKey(resource)
	if err != nil {
		return "", fmt.Errorf("build lock key failed, error=%v", err)
	}

	mutex := d.redSync.NewMutex(lockKey,
		redsync.WithExpiry(time.Minute),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
		return "", errors.Errorf("acquire lock failed, lockKey=%s, error=%v", lockKey, err)
	}

	d.mutex.Lock()
	d.holder[lockKey] = mutex
	d.mutex.Unlock()

	return lockKey, nil
}

func (d *distLock) AcquireProcessRefundSettle(ctx context.Context, settleID int64) (string, error) {
	resource := fmt.Sprintf(processRefundSettleKeyPattern, settleID)
	lockKey, err := d.buildLockKey(resource)
	if err != nil {
		return "", fmt.Errorf("build lock key failed, error=%v", err)
	}

	mutex := d.redSync.NewMutex(lockKey,
		redsync.WithExpiry(time.Minute*5),
		redsync.WithTries(3),
		redsync.WithRetryDelay(time.Second),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire process refund settle lock failed, lockKey=%s, error=%v", lockKey, err)
		return "", errors.Errorf("acquire process refund settle lock failed, lockKey=%s, error=%v", lockKey, err)
	}

	d.mutex.Lock()
	d.holder[lockKey] = mutex
	d.mutex.Unlock()

	return lockKey, nil
}
