package cimb

import (
	"errors"
	"fmt"

	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ConnectorErr struct {
	Code    codes.Code
	SubCode common.SubCode
}

func (ce *ConnectorErr) Error() string {
	return fmt.Sprintf("code: %s, sub_code: %s", ce.Code.String(), ce.SubCode.String())
}

func parseConnectorError(err error) (*ConnectorErr, error) {
	if err == nil {
		return nil, nil
	}
	sttErr, ok := status.FromError(err)
	if !ok {
		return nil, errors.New("fail to extract status code")
	}
	if len(sttErr.Details()) == 0 {
		return nil, errors.New("no error detail")
	}
	errorInfo, ok := sttErr.Details()[0].(*common.ErrorInfo)
	if !ok {
		return nil, errors.New("fail to extract detail")
	}
	return &ConnectorErr{
		Code:    sttErr.Code(),
		SubCode: errorInfo.SubCode,
	}, nil
}

const (
	ErrorCodeNotEnoughBalance               = "NOT_ENOUGH_BALANCE"
	ErrorCodeRepayAmountNotSameWithExpected = "REPAYMENT_AMOUNT_MUST_BE_SAME_WITH_EXPECTED_AMOUNT"
)
