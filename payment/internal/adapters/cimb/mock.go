package cimb

//import (
//	"context"
//	"github.com/go-kratos/kratos/v2/log"
//	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
//	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
//	"time"
//)
//
//type mockService struct {
//	logger *log.Helper
//}
//
//func (m *mockService) ODRepayment(ctx context.Context, req model.ODRepaymentRequest) (*model.RepaymentResult, error) {
//	//TODO implement me
//	panic("implement me")
//}
//
//func (m *mockService) GetInstallmentDetail(ctx context.Context, zalopayID int64, paymentID int64, includeDischargeFee bool) (*model.InstallmentDetail, error) {
//	//TODO implement me
//	panic("implement me")
//}
//
//func (m *mockService) InquiryMultiTransaction(ctx context.Context, refTransIDs []string) ([]*model.CIMBTransaction, error) {
//	//TODO implement me
//	panic("implement me")
//}
//
//func (m *mockService) InquiryTransaction(ctx context.Context, refTransID string) (*model.CIMBTransaction, error) {
//	//TODO implement me
//	panic("implement me")
//}
//
//func (m *mockService) CashInToCustomer(ctx context.Context, req model.CashInToCustomerRequest) (*model.RepaymentResult, error) {
//	//TODO implement me
//	panic("implement me")
//}
//
//func (m *mockService) GetAvailableBalance(ctx context.Context, zalopayID int64, partnerCode string) (int64, error) {
//	//TODO implement me
//	panic("implement me")
//}
//
//func (m *mockService) InitPurchasing(ctx context.Context, order model.PurchaseOrder) (*model.InitPurchasingResult, error) {
//	start := time.Now()
//	return &model.InitPurchasingResult{
//		MonitorTime: model.MonitorTime{
//			SendRequestTime:     start,
//			ReceiveResponseTime: start.Add(100 * time.Millisecond),
//		},
//		CIMBError:            model.CIMBError{},
//		TransactionStatus:    "COMPLETED",
//		CIMBTransID:          "cim_seq_001111",
//		RequiredStepupMethod: "PIN",
//		//RequiredStepupMethod: "SELFIE_WITH_PIN",
//	}, nil
//}
//
//func (m *mockService) ConfirmPurchasing(ctx context.Context, purchaseOrderID int64, partnerTransactionID string, zalopayID int64) (*model.ConfirmPurchaseResult, error) {
//	start := time.Now()
//	return &model.ConfirmPurchaseResult{
//		MonitorTime: model.MonitorTime{
//			SendRequestTime:     start,
//			ReceiveResponseTime: start.Add(100 * time.Millisecond),
//		},
//		CIMBError:               model.CIMBError{},
//		TransactionStatus:       "COMPLETED",
//		AvailableOdLimit:        4000000,
//		RepaymentAccountBalance: 30000,
//	}, nil
//}
//
//func (m *mockService) ConfirmDisbursement(ctx context.Context, purchaseOrderID int64, partnerTransactionID string, zalopayID int64, isCancel bool) (*model.ConfirmDisbursementResult, error) {
//	start := time.Now()
//
//	// Success case
//	return &model.ConfirmDisbursementResult{
//		MonitorTime: model.MonitorTime{
//			SendRequestTime:     start,
//			ReceiveResponseTime: start.Add(100 * time.Millisecond),
//		},
//		CIMBError:         model.CIMBError{},
//		TransactionStatus: "COMPLETED",
//	}, nil
//
//	// Failure case
//	//return &model.ConfirmDisbursementResult{
//	//	MonitorTime: model.MonitorTime{
//	//		SendRequestTime:     start,
//	//		ReceiveResponseTime: start.Add(100 * time.Millisecond),
//	//	},
//	//	CIMBError: model.CIMBError{
//	//		ErrorCode:   "AMOUNT_NOT_SUFFICIENT",
//	//		Description: "request amount is too large than available limit or too small base on product constraint",
//	//	},
//	//	TransactionStatus: "TRANSFER_FAILED",
//	//}, nil
//}
//
//func NewMockService(kLogger log.Logger) types.PartnerConnector {
//	return &mockService{
//		logger: log.NewHelper(log.With(kLogger, "adapters", "mock_cimb_connector")),
//	}
//}

//func (m *mockService) SubmitEarlyDischarge(ctx context.Context, req model.EarlyDischargeRequest) (*model.EarlyDischargeResult, error) {
//	start := time.Now()
//	return &model.EarlyDischargeResult{
//		MonitorTime: model.MonitorTime{
//			SendRequestTime:     start,
//			ReceiveResponseTime: start.Add(100 * time.Millisecond),
//		},
//		CIMBError:         model.CIMBError{},
//		TransactionStatus: "COMPLETED",
//		SystemCode:        "SYSTEM_001",
//		Message:           "Success",
//		ErrorCode:         "",
//	}, nil
//}
