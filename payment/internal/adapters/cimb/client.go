package cimb

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"google.golang.org/grpc/status"
)

type service struct {
	cimbConnector connector.CIMBConnectorClient
	logger        *log.Helper
}

func NewService(client connector.CIMBConnectorClient, kLogger log.Logger) types.PartnerConnector {
	kLogger = log.With(kLogger, "adapters", "cimb_connector")
	return &service{
		cimbConnector: client,
		logger:        log.NewHelper(log.With(kLogger, "adapters", "cimb_connector")),
	}
}

func (s *service) ODRepayment(ctx context.Context, req model.ODRepaymentRequest) (*model.RepaymentResult, error) {
	logger := s.logger.WithContext(ctx)
	var monitorTime = model.MonitorTime{}
	var cimbErr = model.CIMBError{}

	monitorTime.Start()
	resp, err := s.cimbConnector.ODRepayment(ctx, &connector.ODRepaymentRequest{
		PartnerUserAccountId:         cast.ToString(req.ZalopayID),
		PartnerTransferringRequestId: cast.ToString(req.PartnerReqID),
		TransactionType:              connector.TransactionType_TRANSACTION_TYPE_OD_REPAYMENT,
		TransferringAmount:           req.Amount,
		CustomerCreditAccountNumber:  req.BankAccountNumber,
		CustomerCreditAccountName:    req.BankAccountName,
		TransferRemarks:              req.PaymentDescription,
		PartnerDebitAccountNumber:    req.CorpAccountNumber,
		PartnerDebitAccountName:      req.CorpAccountName,
	})
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		logger.Errorw("msg", "ODRepayment to cimb fail", "errStatus", errStatus, "req", req)

		switch errStatus.Message() {
		case common.SubCode_NETWORK_FAILURE.String():
			cimbErr.ErrorCode = "SYSTEM_ERROR"
			cimbErr.Description = fmt.Sprintf("%s", err)
		default:
			cimbErr.ErrorCode = errStatus.Message()
			cimbErr.Description = err.Error()
		}
	}

	logger.WithContext(ctx).Infow(
		"msg", "ODRepayment to cimb success",
		"req", req, "response", resp, "cimbErr", cimbErr,
		"monitorTime", monitorTime,
	)

	return &model.RepaymentResult{
		MonitorTime:       monitorTime,
		CIMBError:         cimbErr,
		RepaymentStatus:   resp.GetPaymentStatus(),
		BankTransID:       resp.GetCimbTransactionSequenceId(),
		AvailableOdLimit:  resp.GetAvailableOdLimit(),
		TransactionStatus: model.ParseCIMBPaymentStatusFromString(resp.GetPaymentStatus()),
	}, nil
}

func (s *service) ConfirmDisbursement(ctx context.Context, purchaseOrderID int64, partnerTransactionID string, zalopayID int64, isCancel bool) (*model.ConfirmDisbursementResult, error) {
	var monitorTime = model.MonitorTime{}
	var cimbErr = model.CIMBError{}

	req := &connector.ConfirmODDisbursementRequest{
		ReferenceRequestId: cast.ToString(purchaseOrderID),
		CimbTransId:        partnerTransactionID,
		ZalopayId:          zalopayID,
		IsCancel:           isCancel,
	}

	monitorTime.Start()
	resp, err := s.cimbConnector.ConfirmODDisbursement(ctx, req)
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		if errStatus.Message() == common.SubCode_NETWORK_FAILURE.String() {
			s.logger.WithContext(ctx).Errorw("msg", "ConfirmODDisbursement to cimb fail", "errStatus", errStatus, "req", req)
			return nil, err
		}

		cimbErr.ErrorCode = errStatus.Message()
	}

	s.logger.WithContext(ctx).Infow("msg", "ConfirmODDisbursement to cimb success",
		"req", req, "response", resp, "cimbErr", cimbErr, "monitorTime", monitorTime)

	ret := &model.ConfirmDisbursementResult{
		MonitorTime:       monitorTime,
		CIMBError:         cimbErr,
		TransactionStatus: model.ParseCIMBPaymentStatusFromString(resp.GetData().GetTransactionStatus()),
	}

	return ret, nil
}

func (s *service) ConfirmPurchasing(ctx context.Context, po model.PurchaseOrder) (*model.ConfirmPurchaseResult, error) {
	var monitorTime = model.MonitorTime{}
	var cimbErr = model.CIMBError{}

	partnerAdvanceAuthInfo := po.PartnerData.InitPurchasingResult.RequiredStepupMethod
	partnerAdvanceAuthId := po.PartnerData.PaymentAuth.AuthSessionID

	if partnerAdvanceAuthInfo == model.AdvanceStepUpMethodNoAdvanceAuthRequired {
		partnerAdvanceAuthInfo = model.AdvanceStepUpMethodPartnerPinPass
	}
	if partnerAdvanceAuthInfo == model.AdvanceStepUpMethodPartnerPinPass {
		partnerAdvanceAuthId = po.PaymentNo
	}

	req := &connector.ConfirmODPurchasingRequest{
		ReferenceRequestId: cast.ToString(po.ID),
		CimbTransId:        po.PartnerData.InitPurchasingResult.CIMBTransID,
		ZalopayId:          po.AccountInfo.ZalopayID,
		PartnerAdvanceAuthenticationInfo: &connector.PartnerAuthenticationInfo{
			Id:   partnerAdvanceAuthId,
			Type: partnerAdvanceAuthInfo.String(),
		},
	}

	monitorTime.Start()
	resp, err := s.cimbConnector.ConfirmODPurchasing(ctx, req)
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		if errStatus.Message() == common.SubCode_NETWORK_FAILURE.String() {
			s.logger.WithContext(ctx).Errorw("msg", "ConfirmODPurchasing to cimb fail", "errStatus", errStatus, "req", req)
			return nil, err
		}

		cimbErr.ErrorCode = errStatus.Message()
	}

	s.logger.WithContext(ctx).Infow("msg", "ConfirmODPurchasing to cimb success",
		"req", req, "response", resp, "cimbErr", cimbErr, "monitorTime", monitorTime)

	ret := &model.ConfirmPurchaseResult{
		MonitorTime:             monitorTime,
		CIMBError:               cimbErr,
		TransactionStatus:       model.ParseCIMBPaymentStatusFromString(resp.GetData().GetTransactionStatus()),
		AvailableOdLimit:        resp.GetData().GetAvailableBalance(),
		RepaymentAccountBalance: resp.GetData().GetRepaymentAccountBalance(),
	}

	return ret, nil
}

func (s *service) InitPurchasing(ctx context.Context, purchaseOrder model.PurchaseOrder) (*model.InitPurchasingResult, error) {
	var cimbErr = model.CIMBError{}
	var monitorTime = model.MonitorTime{}

	purchaseRequest := connector.InitODPurchasingRequest{
		ReferenceRequestId: cast.ToString(purchaseOrder.ID),
		ZalopayId:          purchaseOrder.AccountInfo.ZalopayID,
		BankAccountNumber:  purchaseOrder.AccountInfo.PartnerAccountId,
		BankAccountName:    purchaseOrder.AccountInfo.PartnerAccountName,
		DrawdownPurpose:    purchaseOrder.PartnerData.DrawdownPurpose,
		TransactionInfo: &connector.TransactionInfo{
			TransactionAmount: purchaseOrder.Order.Amount,
			BeneficiaryAmount: purchaseOrder.Order.Amount,
			BeneficiaryDetails: &connector.TransactionInfo_BeneficiaryDetails{
				BankCode:          "01", // TODO: why not purchaseOrder.AccountInfo.PartnerCode?
				CorpAccountNumber: purchaseOrder.BankRoute.BankAccountNumber,
				CorpAccountName:   purchaseOrder.BankRoute.BankAccountName,
			},
		},
		CustomerRemark: purchaseOrder.PartnerData.CustomerRemark,
		InstallmentInstruction: &connector.InitODPurchasingRequest_InstallmentInstruction{
			Tenor:        purchaseOrder.InstallmentInstruction.Tenor,
			InterestRate: genInterestRateFromTenor(purchaseOrder.InstallmentInstruction.Tenor),
		},
		ProductType: connector.ProductType_PRODUCT_TYPE_INSTALLMENT,
		PartnerAuthenticationInfo: &connector.PartnerAuthenticationInfo{
			Id:   purchaseOrder.PaymentNo,
			Type: model.StepUpMethodPinPass.String(),
		},
		AutoProcessIfNoAdvanceAuthenRequired: false,
		ExtraData: &connector.ExtraData{
			DeviceId: purchaseOrder.DeviceID,
			UserIp:   purchaseOrder.UserIP,
		},
	}
	monitorTime.Start()
	resp, err := s.cimbConnector.InitODPurchasing(ctx, &purchaseRequest)
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		if errStatus.Message() == common.SubCode_NETWORK_FAILURE.String() {
			s.logger.WithContext(ctx).Errorw("msg", "InitPurchasing to cimb fail", "error", err, "purchaseOrder", purchaseOrder)
			return nil, err
		}

		cimbErr.ErrorCode = errStatus.Message()
	}

	s.logger.WithContext(ctx).Infow("msg", "InitPurchasing to cimb success",
		"purchaseOrder", purchaseOrder, "response", resp, "cimbErr", cimbErr, "monitorTime", monitorTime)

	return &model.InitPurchasingResult{
		MonitorTime:          monitorTime,
		CIMBError:            cimbErr,
		TransactionStatus:    model.ParseCIMBPaymentStatusFromString(resp.GetData().GetTransactionStatus()),
		CIMBTransID:          resp.GetData().GetCimbTransId(),
		RequiredStepupMethod: model.ParseAdvanceStepUpMethodFromString(resp.GetData().GetRequiredStepupMethod()),
	}, nil
}

func (s *service) GetAvailableBalance(ctx context.Context, zalopayID int64, accountNumber string) (int64, error) {
	var monitorTime = model.MonitorTime{}

	monitorTime.Start()
	resp, err := s.cimbConnector.InquiryCASABalance(ctx, &connector.InquiryCASABalanceRequest{
		Timestamp:     time.Now().Unix(),
		ZalopayId:     cast.ToString(zalopayID),
		AccountNumber: accountNumber,
		Overdraft:     true,
	})
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		s.logger.WithContext(ctx).Errorw("msg", "InquiryCASABalance to cimb fail",
			"error", errStatus, "zalopayID", zalopayID, "accountNumber", accountNumber)
		return 0, err
	}

	s.logger.WithContext(ctx).Infow("msg", "InquiryCASABalance to cimb success",
		"zalopayID", zalopayID, "accountNumber", accountNumber, "response", resp, "monitorTime", monitorTime)

	return resp.GetCasaBalance().GetAvailableOverdraftLimit(), nil
}

func (s *service) InquiryMultiTransaction(ctx context.Context, refTransID []string) ([]*model.CIMBTransaction, error) {
	var monitorTime = model.MonitorTime{}

	monitorTime.Start()
	resp, err := s.cimbConnector.InquiryMultiTransaction(ctx, &connector.InquiryMultiTransactionRequest{
		PartnerTransferRequestIds: refTransID,
	})
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		s.logger.WithContext(ctx).Errorw("msg", "InquiryMultiTransaction to cimb fail",
			"error", errStatus, "refTransID", refTransID)
		return nil, err
	}

	s.logger.WithContext(ctx).Infow("msg", "InquiryMultiTransaction to cimb success",
		"refTransID", refTransID, "response", resp, "monitorTime", monitorTime)

	var list []*model.CIMBTransaction
	for _, trans := range resp.GetTransactions() {
		paymentStatus := model.ParseCIMBPaymentStatusFromString(trans.GetPaymentStatus())
		list = append(list, &model.CIMBTransaction{
			PaymentStatus:             paymentStatus,
			CreatedTime:               trans.GetCreatedTime(),
			BankTransactionSequenceID: trans.GetBankTransactionSequenceId(),
		})
	}

	return list, nil
}

func (s *service) InquiryTransaction(ctx context.Context, refTransID string) (*model.CIMBTransaction, error) {
	logger := s.logger.WithContext(ctx)

	list, err := s.InquiryMultiTransaction(ctx, []string{refTransID})
	if err != nil {
		connErr, parseErr := parseConnectorError(err)
		if parseErr != nil || connErr == nil {
			logger.Errorf("Inquiry transaction failed: %v", err)
			return nil, errors.Errorf("inquiry transaction error: %v", err)
		}
		if connErr.SubCode == common.SubCode_TRANSACTION_NOT_FOUND {
			logger.Errorf("Transaction not found: %v", connErr)
			return nil, errors.Wrap(model.ErrPartnerTransactionNotFound, "transaction not found")
		}
		logger.Errorf("Connector error: %v", connErr)
		return nil, errors.Errorf("parse connector error: %v", connErr)
	}

	if len(list) == 0 {
		return nil, errors.Wrap(model.ErrPartnerTransactionNotFound, "transaction not found")
	}

	return list[0], nil
}

func (s *service) SubmitEarlyDischarge(ctx context.Context, req model.EarlyDischargeRequest) (*model.EarlyDischargeResult, error) {
	var cimbErr = model.CIMBError{}
	var monitorTime = model.MonitorTime{}

	submitRequest := &connector.SubmitEarlyDischargeRequest{
		CimbLoanNo:                  req.LoanID,
		ProductType:                 connector.ProductType_PRODUCT_TYPE_INSTALLMENT,
		OriginPartnerId:             "ZALOPAY",
		PartnerRepaymentRequestId:   req.RequestID,
		TransactionType:             connector.TransactionType_TRANSACTION_TYPE_REFUND,
		RepaymentAmount:             req.Amount,
		CustomerRemark:              req.PaymentDescription,
		PartnerRemark:               req.PartnerRemark,
		DebitCorporateAccountNumber: req.CorpAccountNumber,
		DebitCorporateAccountName:   req.CorpAccountName,
		IsEarlyDischarge:            true,
	}

	monitorTime.Start()
	resp, err := s.cimbConnector.SubmitEarlyDischarge(ctx, submitRequest)
	monitorTime.End()

	if err != nil {
		errStatus := status.Convert(err)
		s.logger.WithContext(ctx).Errorw("msg", "ODRepayment to cimb fail", "errStatus", errStatus, "req", req)

		switch errStatus.Message() {
		case common.SubCode_NETWORK_FAILURE.String():
			cimbErr.ErrorCode = "SYSTEM_ERROR"
			cimbErr.Description = fmt.Sprintf("%s", err)
		default:
			cimbErr.ErrorCode = errStatus.Message()
		}
		return &model.EarlyDischargeResult{
			CIMBError:   cimbErr,
			MonitorTime: monitorTime,
		}, nil
	}

	s.logger.WithContext(ctx).Infow(
		"msg", "SubmitEarlyDischarge to cimb success",
		"req", req, "response", resp, "cimbErr", cimbErr,
		"monitorTime", monitorTime,
	)

	result := &model.EarlyDischargeResult{
		MonitorTime:       monitorTime,
		ErrorCode:         resp.GetData().GetErrorCode(),
		PartnerTransID:    resp.GetData().GetBankTransactionSequenceId(),
		TransactionStatus: parseEarlyDischargeStatus(resp.GetData().GetStatus()),
		NeedReconcilation: needReconcileSettlementByCode(resp.GetData().GetErrorCode()),
	}

	return result, nil
}

func (s *service) GetEarlyDischargeStatus(ctx context.Context, paymentID int64, refTransID string) (*model.CIMBTransaction, error) {
	logger := s.logger.WithContext(ctx)

	resp, err := s.cimbConnector.GetEarlyDischargeStatus(ctx, &connector.GetEarlyDischargeStatusRequest{
		PartnerRepaymentRequestIds: []string{refTransID},
	})
	if err != nil {
		logger.Errorw("msg", "GetEarlyDischargeStatus to cimb fail", "error", err, "refTransID", refTransID)
		return nil, err
	}
	if len(resp.GetTransactions()) == 0 {
		logger.Errorw("msg", "GetEarlyDischargeStatus to cimb fail", "error", err, "refTransID", refTransID)
		return nil, errors.Wrap(model.ErrPartnerTransactionNotFound, "transaction not found")
	}

	transaction := resp.GetTransactions()[0]

	return &model.CIMBTransaction{
		PaymentStatus:             parseEarlyDischargeStatus(transaction.GetStatus()),
		BankTransactionSequenceID: transaction.GetBankTransactionSequenceId(),
	}, nil
}

func parseEarlyDischargeStatus(status connector.EarlyDischargeStatus) model.CIMBPaymentStatus {
	switch status {
	case connector.EarlyDischargeStatus_EARLY_STATUS_COMPLETED:
		return model.CIMBPaymentStatusComplete
	case connector.EarlyDischargeStatus_EARLY_STATUS_FAILED:
		return model.CIMBPaymentStatusFailed
	case connector.EarlyDischargeStatus_EARLY_STATUS_PROCESSING:
		return model.CIMBPaymentStatusProcessing
	default:
		return model.CIMBPaymentStatusNew
	}
}

func genInterestRateFromTenor(tenor int32) float32 {
	switch tenor {
	case 3:
		return 0.54
	case 6:
		return 0.6024
	case 9:
		return 0.6181
	case 12:
		return 0.6205
	default:
		return 0.54
	}
}

func needReconcileSettlementByCode(errorCode string) bool {
	statuses := []string{ErrorCodeNotEnoughBalance, ErrorCodeRepayAmountNotSameWithExpected}
	return slices.Contains(statuses, errorCode)
}
