package installment_service

import (
	"context"

	instv1 "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/installment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"

	krErr "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
)

type client struct {
	logger     *log.Helper
	grpcClient instv1.InstallmentClient
}

func NewClient(grpcClient instv1.InstallmentClient, kLogger log.Logger) types.InstallmentAdapter {
	logger := log.NewHelper(log.With(kLogger, "adapters", "installment_service"))
	return &client{
		logger:     logger,
		grpcClient: grpcClient,
	}
}

func (c *client) GetInstallmentStatus(ctx context.Context, zpTransID int64) (model.GetInstallmentStatusResponse, error) {
	logger := c.logger.WithContext(ctx)

	resp, err := c.grpcClient.GetInstallmentStatus(ctx, &instv1.GetInstallmentStatusRequest{
		ZpTransId:   zpTransID,
		ForceLatest: true,
	})
	if err != nil {
		if krErr.FromError(err).GetReason() == ErrorCodeInstallmentNotFound {
			logger.Warnf("get installment not found, maybe in creation, zp_trans_id: %d", zpTransID)
			return model.GetInstallmentStatusResponse{Status: model.InstallmentStatusInCreation}, nil
		}
		logger.Errorw("msg", "get installment status failed", "error", err)
		return model.GetInstallmentStatusResponse{}, err
	}

	status := toInstallmentStatus(resp.GetStatus())
	if !status.IsValid() {
		logger.Errorw("msg", "get installment status failed", "error", "invalid status", "raw_status", resp.GetStatus())
		return model.GetInstallmentStatusResponse{}, errors.Errorf("invalid status: %v", status)
	}

	logger.Infow("msg", "get installment status success", "response", resp, "zp_trans_id", zpTransID)

	return model.GetInstallmentStatusResponse{
		Status: status,
		LoanID: resp.GetInfo().GetPartnerInstId(),
	}, nil
}

func (c *client) GetEarlyDischarge(ctx context.Context, zpTransID int64, forceLatest bool) (model.EarlyDischarge, error) {
	logger := c.logger.WithContext(ctx)

	resp, err := c.grpcClient.GetEarlyDischargeRefund(ctx, &instv1.GetEarlyDischargeRefundRequest{
		ZpTransId:   zpTransID,
		ForceLatest: &forceLatest,
	})
	if err != nil {
		logger.Errorw("msg", "get early discharge failed", "error", err)
		return model.EarlyDischarge{}, err
	}

	info := resp.GetDischargeInfo()
	if info.GetStatus() == instv1.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED {
		logger.Errorw("msg", "receive unspecified status")
		return model.EarlyDischarge{}, errors.Errorf("invalid status: %v", info.GetStatus())
	}

	isCharged := false
	isEligible := false
	totalAmount := info.GetDischargeAmount()

	switch info.GetStatus() {
	case instv1.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED:
		isCharged = true
	case instv1.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_ELIGIBLE,
		instv1.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_PROCESSING:
		isEligible = true
	}

	return model.EarlyDischarge{
		IsSettled:   isCharged,
		IsEligible:  isEligible,
		TotalAmount: totalAmount,
		InSession:   info.GetSessionAvailable(), // Updated to use renamed field
	}, nil
}

func (c *client) NotifyInstallmentRefund(ctx context.Context, refundSettle *model.RefundSettle) error {
	logger := c.logger.WithContext(ctx)

	req := &instv1.InstallmentRefund{
		ZpTransId:         refundSettle.ZPTransID,
		Version:           refundSettle.EventVersion,
		NetRefundAmount:   refundSettle.NetRefundAmount,
		TotalRefundAmount: refundSettle.TotalRefundAmount,
		UserTopupAmount:   refundSettle.UserTopupAmount,
		UserTopupRequired: refundSettle.IsUserTopupRequired(),
	}
	resp, err := c.grpcClient.NotifyInstallmentRefund(ctx, req)
	if err != nil {
		logger.Errorw("msg", "notify installment refund failed", "error", err)
		return err
	}
	logger.Infow("msg", "notify installment refund success", "response", resp)
	return nil
}

func toInstallmentStatus(status instv1.InstallmentStatus) model.InstallmentStatus {
	switch status {
	case instv1.InstallmentStatus_INSTALLMENT_STATUS_OPEN:
		return model.InstallmentStatusOpen
	case instv1.InstallmentStatus_INSTALLMENT_STATUS_CLOSED:
		return model.InstallmentStatusClosed
	default:
		return model.InstallmentStatusUnknown
	}
}
