package auth_service

import (
	"context"
	"fmt"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/middleware/auth"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
)

type Session struct {
	ZaloPayId   string
	ZaloId      string
	DeviceId    string
	DeviceOs    string
	DeviceModel string
	Platform    string
	AppVersion  string
	UserAgent   string
	UserIp      string
	Mno         string
	ContentType string
}

func NewClient(client sessionv1.SessionServiceClient, logger log.Logger) auth.Authenticator {
	return &authenticator{
		client: client,
		logger: log.NewHelper(log.With(logger, "adapters", "auth-service")),
	}
}

type authenticator struct {
	logger *log.Helper
	client sessionv1.SessionServiceClient
}

func (a *authenticator) Authenticate(ctx context.Context, sessionID string) (*sessionv1.Session, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session id is empty")
	}

	session, err := a.client.GetSession(ctx, &sessionv1.GetSessionReq{
		SessionId: sessionID,
	})
	if err != nil {
		a.logger.Errorf("get session failed, sessionID=%v, error=%v", sessionID, err)
		return nil, fmt.Errorf("get session failed: %s", err.Error())
	}

	return session, nil
}
