package task_job

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
)

const (
	WfIDReconcileRefundSettle    = "REFUND-RECONCILE-REFUND-SETTLE-%s"
	WfIDRefundEarlyDischargePoll = "REFUND-EARLY-DISCHARGE-POLLING-%s"
	WfIDRefundExpiredRepayPoll   = "REFUND-EXPIRED-REPAY-POLLING-%s-%s"
)

type taskJob struct {
	logger   *log.Helper
	config   *configs.Payment
	temporal types.WorkflowAdapter
}

func NewTaskJob(kLogger log.Logger, conf *configs.Payment, wfAdapter types.WorkflowAdapter) types.TaskJobAdapter {
	logger := log.NewHelper(log.With(kLogger, "adapters", "task_job"))
	return &taskJob{
		logger:   logger,
		config:   conf,
		temporal: wfAdapter,
	}
}

func (tj *taskJob) ExecuteReconcileRefundSettleJob(ctx context.Context, params *model.RefundSettleReconParams) error {
	logger := tj.logger.WithContext(ctx)
	zpTransID := params.ZPTransID

	logger.Infow("ExecuteReconcileRefundSettleJob", "zpTransID", zpTransID)

	// Implementation
	wfConfig := tj.config.GetSchedulers().GetRefundSettleRecon()
	wfIdentifier := fmt.Sprintf(WfIDReconcileRefundSettle, cast.ToString(zpTransID))
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE

	wfID, err := tj.temporal.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.QueueName,
		WorkflowIDReusePolicy: wfReusePolicy,
	}, wfConfig.GetWorkflowType(), params)
	if err != nil {
		logger.Errorw("Failed to execute workflow", "error", err)
		return err
	}

	logger.Infow("Workflow started", "workflowID", wfID, "workflowType", wfConfig.GetWorkflowType(), "zpTransID", zpTransID)

	return nil
}

func (tj *taskJob) ExecuteEarlyDischargePollingJob(ctx context.Context, params *model.EarlyDischargeStatusWorkflowRequest) error {
	logger := tj.logger.WithContext(ctx)
	paymentID := params.PaymentID

	logger.Infow("ExecuteEarlyDischargePollingJob", "paymentID", paymentID)

	// Implementation
	wfConfig := tj.config.GetSchedulers().GetRefundDischargePoll()
	wfIdentifier := fmt.Sprintf(WfIDRefundEarlyDischargePoll, cast.ToString(paymentID))
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING

	wfID, err := tj.temporal.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.QueueName,
		WorkflowIDReusePolicy: wfReusePolicy,
	}, wfConfig.GetWorkflowType(), params)
	if err != nil {
		logger.Errorw("Failed to execute workflow", "error", err)
		return err
	}

	logger.Infow("Workflow started", "workflowID", wfID, "workflowType", wfConfig.GetWorkflowType(), "paymentID", params.PaymentID)

	return nil
}

func (tj *taskJob) ExecuteExpiredRefundRepaymentPollingJob(ctx context.Context, params *model.RepaymentStatusPollingRequest) error {
	logger := tj.logger.WithContext(ctx)
	orderID := params.OrderID
	paymentID := params.PaymentID

	logger.Infow("ExecuteExpiredRefundRepaymentPollingJob", "paymentID", paymentID)

	// Implementation
	wfConfig := tj.config.GetSchedulers().GetRefundExpiredRepayPoll()
	wfIdentifier := fmt.Sprintf(WfIDRefundExpiredRepayPoll, cast.ToString(paymentID), cast.ToString(orderID))
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING

	wfID, err := tj.temporal.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.QueueName,
		WorkflowIDReusePolicy: wfReusePolicy,
	}, wfConfig.GetWorkflowType(), params)
	if err != nil {
		logger.Errorw("Failed to execute workflow", "error", err)
		return err
	}

	logger.Infow("Workflow started", "workflowID", wfID, "workflowType", wfConfig.GetWorkflowType(), "paymentID", params.PaymentID)

	return nil
}
