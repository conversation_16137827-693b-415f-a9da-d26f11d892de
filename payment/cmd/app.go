package cmd

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cobra"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/server"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/logging"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/telemetry"
)

var consumerCmd = &cobra.Command{
	Use:   "consumer",
	Short: `start payment consumers`,
	RunE:  runConsumerCmd,
}

func NewConsumerCmd() *cobra.Command {
	return consumerCmd
}

func newConsumerApp(logger log.Logger, rc *server.RefundConsumer, oc *server.OrderStatusConsumer, rsc *server.RefundSettleProcessConsumer) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(rc, oc, rsc),
	)
}

func runConsumerCmd(cmd *cobra.Command, _ []string) error {
	bc, cfgCleanup := initConfig(cmd)
	defer cfgCleanup()

	err := telemetry.MustInitTracer(&telemetry.TracingInfo{
		SvcVer:      Version,
		SvcName:     bc.GetPayment().GetApp().GetName(),
		Environment: bc.GetPayment().GetApp().GetEnv(),
		AgentHost:   bc.GetPayment().GetTracer().GetAgentHost(),
		AgentPort:   bc.GetPayment().GetTracer().GetAgentPort(),
	})
	if err != nil {
		panic(err)
	}

	logger := logging.MustNewLogger(&logging.LoggerInfo{
		Id:      id,
		Name:    "payment-consumer",
		Version: Version,
	})

	app, cleanup, err := wireConsumerApp(bc.GetPayment(), logger)
	if err != nil {
		_ = logger.Log(log.LevelError, "msg", "wire consumer app fail", "error", err)
		panic(err)
	}
	defer cleanup()

	if err := app.Run(); err != nil {
		_ = logger.Log(log.LevelError, "msg", "run consumer app fail", "error", err)
		panic(err)
	}

	return nil
}
