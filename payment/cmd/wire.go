//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package cmd

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/acquiring_core"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/cimb"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/server"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service/polling"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/repay"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/maintenance"
)

// wireApp init kratos application.
func wireApp(*configs.Payment, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		ProviderSet,
		ConfigProviderSet,
		repo.ProviderSet,
		adapters.ProviderSet,
		usecase.ProviderSet,
		polling.ProviderSet,
		service.ProviderSet,
		server.ProviderSet,
		maintenance.ProviderSet,
		newApp,
	))
}

func wireKafkaHandler(*configs.Payment, log.Logger) (*service.PurchasingProcessHandler, func(), error) {
	panic(wire.Build(
		ProviderSet,
		repo.ProviderSet,
		adapters.ProviderSet,
		usecase.ProviderSet,
		service.NewPurchasingProcessHandler))
}

func wireRepayKafkaHandler(*configs.Payment, log.Logger) (*service.RePaymentService, func(), error) {
	panic(wire.Build(
		ProviderSet,
		ConfigProviderSet,
		wire.NewSet(repo.NewSQLDatabase, repo.NewRepository, repo.NewPaymentRepo, repo.NewOrderRepo),
		wire.NewSet(account_service.NewClient, acquiring_core.NewClient, cimb.NewService),
		wire.NewSet(repay.NewRepayUsecase),
		service.NewRePaymentService))
}

func wireConsumerApp(*configs.Payment, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		ProviderSet,
		repo.ProviderSet,
		ConfigProviderSet,
		adapters.ProviderSet,
		usecase.ProviderSet,
		service.NewRefundService,
		server.ProviderSet,
		newConsumerApp))
}
