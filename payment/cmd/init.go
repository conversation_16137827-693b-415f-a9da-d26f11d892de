package cmd

import (
	"crypto/tls"
	"strings"
	"time"

	pa "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/payment_auth"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/segmentio/kafka-go"
	accountv1 "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/account/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/acquiring_core"
	installmentv1 "gitlab.zalopay.vn/fin/installment/payment-service/api/external_services/installment/v1"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/bootstrap"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"gitlab.zalopay.vn/fin/platform/common/redis"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/contrib/opentelemetry"
	"go.temporal.io/sdk/interceptor"
	"go.uber.org/fx"
	"google.golang.org/grpc"
	kafka_client "zalopay.io/zgo/kafka-client"
)

const (
	clientID  = "client-id"
	clientKey = "client-key"
)

var ProviderSet = wire.NewSet(
	InitCIMBConnectorClient,
	InitAccountServiceClient,
	NewRefundSettlePublisher,
	NewPurchaseInitiatedPublisher,
	NewPEUpdateExchangeStatusPublisher,
	NewPurchaseAuthenticatedPublisher,
	InitAcquiringCoreClient,
	InitSessionGrpcConn,
	InitTemporalClient,
	InitRedisCache,
	InitPaymentAuthServiceClient,
	InitInstallmentServiceClient,
)

var ConfigProviderSet = wire.NewSet(
	ProvideOrderConfigs,
)

type FxNoopLifeCycle struct {
}

func (*FxNoopLifeCycle) Append(hook fx.Hook) {}

func ProvideOrderConfigs(config *configs.Payment) *configs.OrderConfigsHelper {
	return configs.NewOrderConfigs(config.GetOrderConfigs())
}

func InitSessionGrpcConn(config *configs.Payment, logger log.Logger) (sessionv1.SessionServiceClient, func(), error) {
	sessConfig := config.GetAdapters().GetSession()
	log.Info("InitSessionGrpcConn connecting..., target: ", sessConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(&bootstrap.GrpcConfig{
		Endpoint: sessConfig.GetAddress(),
		Timeout:  sessConfig.GetTimeout().AsDuration(),
		Secured:  sessConfig.GetSecured(),
		Logger:   logger,
	})
	if err != nil {
		log.Error("InitSessionGrpcConn, err: ", err, ", target: ", config.GetAdapters().GetSession().GetAddress())
		return nil, nil, err
	}
	log.Info("InitSessionGrpcConn success, target: ", config.GetAdapters().GetSession().GetAddress())

	return sessionv1.NewSessionServiceClient(conn), func() { _ = conn.Close() }, nil
}

func InitCIMBConnectorClient(config *configs.Payment, logger log.Logger) (connector.CIMBConnectorClient, func(), error) {
	cimbConfig := config.GetAdapters().GetCimbConnector()
	log.Info("InitCIMBConnectorClient is starting..., target: ", cimbConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Endpoint: cimbConfig.GetAddress(),
			Timeout:  cimbConfig.GetTimeout().AsDuration(),
			Secured:  cimbConfig.GetSecured(),
			Logger:   logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithMetadataAppendInterceptor(
				"client-id", cimbConfig.GetClientId(),
				"client-key", cimbConfig.GetClientKey(),
				common.MetadataKey_METADATA_PRODUCT_LINE.String(),
				common.ProductLine_PRODUCT_INSTALLMENT.String(),
			),
			bootstrap.WithCliTraceFromCtxInterceptor(bootstrap.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitCIMBConnectorClient error", err)
		return nil, nil, err
	}

	log.Info("InitCIMBConnectorClient success, target: ", cimbConfig.GetAddress())

	return connector.NewCIMBConnectorClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitAccountServiceClient(config *configs.Payment, logger log.Logger) (accountv1.AccountClient, func(), error) {
	accountConfig := config.GetAdapters().GetAccountService()
	log.Info("InitAccountServiceClient is starting..., target: ", accountConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Endpoint: accountConfig.GetAddress(),
			Timeout:  accountConfig.GetTimeout().AsDuration(),
			Secured:  accountConfig.GetSecured(),
			Logger:   logger,
		},
	)

	if err != nil {
		log.Error("InitAccountServiceClient error", err)
		return nil, nil, err
	}

	log.Info("InitAccountServiceClient success, target: ", accountConfig.GetAddress())

	return accountv1.NewAccountClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitAcquiringCoreClient(config *configs.Payment, logger log.Logger) (acquiring_core.UserPaymentClient, func(), error) {
	acConfig := config.GetAdapters().GetAcquiringCoreService()

	log.Info("InitAcquiringCoreClient is starting..., target: ", acConfig.GetGrpc().GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Endpoint: acConfig.GetGrpc().GetAddress(),
			Timeout:  acConfig.GetGrpc().GetTimeout().AsDuration(),
			Secured:  acConfig.GetGrpc().GetSecured(),
			Logger:   logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithMetadataAppendInterceptor(
				clientID, acConfig.GetGrpc().GetClientId(),
				clientKey, acConfig.GetGrpc().GetClientKey(),
			),
		),
	)

	if err != nil {
		log.Error("InitAcquiringCoreClient error", err)
		return nil, nil, err
	}

	log.Info("InitAcquiringCoreClient success, target: ", acConfig.GetGrpc().GetAddress())

	return acquiring_core.NewUserPaymentClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func NewPurchaseInitiatedPublisher(config *configs.Payment) (types.PurchaseInitiatedPublisher, func(), error) {
	event := config.GetPublishers().GetPurchaseInitiatedEvent()

	publisher, err := kafka_client.NewPublisher(&kafka_client.PublisherConfig{
		Brokers:  strings.Split(event.GetBrokers(), ","),
		Topic:    event.GetTopic(),
		Balancer: &kafka.Hash{},
		Logger:   nil,
		IsDebug:  false,
	})
	if err != nil {
		log.Errorw("Init Publisher fail", "error", err, "topic", event.GetTopic())
		return nil, nil, err
	}

	log.Infow("msg", "Init Publisher success", "topic", event.GetTopic())
	return publisher, func() {}, nil
}

func NewPurchaseAuthenticatedPublisher(config *configs.Payment) (types.PurchaseAuthenticatedPublisher, func(), error) {
	event := config.GetPublishers().GetPurchaseAuthenticatedEvent()

	publisher, err := kafka_client.NewPublisher(&kafka_client.PublisherConfig{
		Brokers:  strings.Split(event.GetBrokers(), ","),
		Topic:    event.GetTopic(),
		Balancer: &kafka.Hash{},
		Logger:   nil,
		IsDebug:  false,
	})
	if err != nil {
		log.Errorw("msg", "Init Publisher fail", "error", err, "brokers", event.GetBrokers(), "topic", event.GetTopic())
		return nil, nil, err
	}

	log.Infow("msg", "Init Publisher success", "brokers", event.GetBrokers(), "topic", event.GetTopic())
	return publisher, func() {}, nil
}

func NewPEUpdateExchangeStatusPublisher(config *configs.Payment) (types.PEUpdateExchangeStatusPublisher, func(), error) {
	event := config.GetPublishers().GetPeUpdateExchangeStatusEvent()

	publisher, err := kafka_client.NewPublisher(&kafka_client.PublisherConfig{
		Brokers:  strings.Split(event.GetBrokers(), ","),
		Topic:    event.GetTopic(),
		Balancer: &kafka.Hash{},
		Logger:   nil,
		IsDebug:  false,
	})
	if err != nil {
		log.Errorw("msg", "Init Publisher fail", "error", err, "brokers", event.GetBrokers(), "topic", event.GetTopic())
		return nil, nil, err
	}

	log.Infow("msg", "Init Publisher success", "topic", event.GetTopic())
	return publisher, func() {}, nil
}

func NewRefundSettlePublisher(config *configs.Payment) (kafka_client.Publisher, func(), error) {
	event := config.GetPublishers().GetRefundSettleProcess()

	publisher, err := kafka_client.NewPublisher(&kafka_client.PublisherConfig{
		Brokers:  strings.Split(event.GetBrokers(), ","),
		Topic:    event.GetTopic(),
		Balancer: &kafka.Hash{},
		Logger:   nil,
		IsDebug:  false,
	})
	if err != nil {
		log.Errorw("msg", "Init Publisher fail", "error", err, "brokers", event.GetBrokers(), "topic", event.GetTopic())
		return nil, nil, err
	}

	log.Infow("msg", "Init Publisher success", "topic", event.GetTopic())
	return publisher, func() {}, nil
}

func InitTemporalClient(config *configs.Payment) (types.WorkflowAdapter, func(), error) {
	var tlsConfig = &tls.Config{}
	tlsConfig.InsecureSkipVerify = true
	tlsConfig.ServerName = config.GetTemporal().GetAddress()

	tracerOpts := opentelemetry.TracerOptions{}
	otelIntercept, tErr := opentelemetry.NewTracingInterceptor(tracerOpts)
	if tErr != nil {
		log.Error("InitTemporalClient error", tErr)
		return nil, nil, tErr
	}

	clientOptions := client.Options{
		HostPort:          config.GetTemporal().GetAddress(),
		Namespace:         config.GetTemporal().GetNamespace(),
		ConnectionOptions: client.ConnectionOptions{TLS: tlsConfig},
		Interceptors:      []interceptor.ClientInterceptor{otelIntercept},
	}

	temporalClient, err := client.Dial(clientOptions)
	if err != nil {
		return nil, nil, err
	}

	log.Infow(
		"msg", "Init Temporal success",
		"namespace", config.GetTemporal().GetNamespace(),
		"address", config.GetTemporal().GetAddress(),
	)

	return temporalClient, temporalClient.Close, nil
}

func InitRedisCache(config *configs.Payment) (redis.CacheNoCaller, func(), error) {
	redisConfig := config.GetData().GetRedisCache()
	log.Info("InitRedisCache is starting..., target: ", redisConfig.GetAddress())

	cacheConf := &redis.Config{
		Addr:             []string{redisConfig.GetAddress()},
		PoolSize:         int(redisConfig.GetPoolSize()),
		MinIdleConn:      int(redisConfig.GetMinIdleConn()),
		MaxConnAge:       time.Minute * 5,
		MasterName:       redisConfig.GetMasterName(),
		Password:         redisConfig.GetPassword(),
		Username:         redisConfig.GetUsername(),
		SentinelUsername: redisConfig.GetUsername(),
		SentinelPassword: redisConfig.GetPassword(),
	}
	redisCache := redis.CreateRedisCache(&FxNoopLifeCycle{}, cacheConf)
	redisClient := redisCache.GetRedisClient()

	log.Info("InitRedisCache success, target: ", redisConfig.GetAddress())

	return redisCache, func() {
		if redisClient == nil {
			return
		}
		_ = redisClient.Close()
	}, nil
}

func InitPaymentAuthServiceClient(config *configs.Payment, logger log.Logger) (pa.PaymentAuthenticationServiceClient, func(), error) {
	grpcConfig := config.GetAdapters().GetPaymentAuth()

	log.Info("InitPaymentAuthServiceClient is starting..., target: ", grpcConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Endpoint: grpcConfig.GetAddress(),
			Timeout:  grpcConfig.GetTimeout().AsDuration(),
			Secured:  grpcConfig.GetSecured(),
			Logger:   logger,
		},
	)

	if err != nil {
		log.Error("InitPaymentAuthServiceClient error", err)
		return nil, nil, err
	}

	log.Info("InitPaymentAuthServiceClient success, target: ", grpcConfig.GetAddress())

	return pa.NewPaymentAuthenticationServiceClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitInstallmentServiceClient(config *configs.Payment, logger log.Logger) (installmentv1.InstallmentClient, func(), error) {
	installmentConfig := config.GetAdapters().GetInstallmentService()
	log.Info("InitInstallmentServiceClient is starting..., target: ", installmentConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Endpoint: installmentConfig.GetAddress(),
			Timeout:  installmentConfig.GetTimeout().AsDuration(),
			Secured:  installmentConfig.GetSecured(),
			Logger:   logger,
		},
	)

	if err != nil {
		log.Error("InitInstallmentServiceClient error", err)
		return nil, nil, err
	}

	log.Info("InitInstallmentServiceClient success, target: ", installmentConfig.GetAddress())

	return installmentv1.NewInstallmentClient(conn), func() {
		_ = conn.Close()
	}, nil
}
