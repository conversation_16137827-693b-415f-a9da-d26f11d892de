// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package cmd

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/acquiring_core"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/cimb"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/installment_service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/payment_auth"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/settle_notifier"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/adapters/task_job"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/server"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/service/polling"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/purchase"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/repay"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/transaction"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/maintenance"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(payment *configs.Payment, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup := repo.NewSQLDatabase(payment, logger)
	repository := repo.NewRepository(db, logger)
	paymentRepo := repo.NewPaymentRepo(repository, logger)
	cimbConnectorClient, cleanup2, err := InitCIMBConnectorClient(payment, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	partnerConnector := cimb.NewService(cimbConnectorClient, logger)
	accountClient, cleanup3, err := InitAccountServiceClient(payment, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountAdapter := account_service.NewClient(accountClient, logger)
	purchaseInitiatedPublisher, cleanup4, err := NewPurchaseInitiatedPublisher(payment)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	purchaseAuthenticatedPublisher, cleanup5, err := NewPurchaseAuthenticatedPublisher(payment)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	peUpdateExchangeStatusPublisher, cleanup6, err := NewPEUpdateExchangeStatusPublisher(payment)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	workflowAdapter, cleanup7, err := InitTemporalClient(payment)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	paymentAuthenticationServiceClient, cleanup8, err := InitPaymentAuthServiceClient(payment, logger)
	if err != nil {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	paymentAuthAdapter := payment_auth.NewClient(paymentAuthenticationServiceClient, logger)
	purchaseUsecase := purchase.NewPurchaseUsecase(logger, paymentRepo, partnerConnector, accountAdapter, purchaseInitiatedPublisher, purchaseAuthenticatedPublisher, peUpdateExchangeStatusPublisher, workflowAdapter, paymentAuthAdapter)
	paymentService := service.NewPaymentService(purchaseUsecase, logger, payment)
	feeService := service.NewFeeService(logger)
	refundRepo := repo.NewRefundRepo(repository, logger)
	typesTransaction := repo.NewTransaction(repository)
	taskJobAdapter := task_job.NewTaskJob(logger, payment, workflowAdapter)
	cacheNoCaller, cleanup9, err := InitRedisCache(payment)
	if err != nil {
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	distributedLock := dist_lock.NewDistLock(payment, cacheNoCaller, logger)
	orderConfigsHelper := ProvideOrderConfigs(payment)
	orderRepo := repo.NewOrderRepo(repository, logger)
	userPaymentClient, cleanup10, err := InitAcquiringCoreClient(payment, logger)
	if err != nil {
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	acquiringCoreAdapter := acquiring_core.NewClient(userPaymentClient, logger, orderConfigsHelper)
	publisher, cleanup11, err := NewRefundSettlePublisher(payment)
	if err != nil {
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	refundSettleNotifier := settle_notifier.NewNotifier(publisher, logger)
	installmentClient, cleanup12, err := InitInstallmentServiceClient(payment, logger)
	if err != nil {
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	installmentAdapter := installment_service.NewClient(installmentClient, logger)
	usecase := refund.NewRefundUsecase(logger, refundRepo, typesTransaction, taskJobAdapter, distributedLock, payment, orderConfigsHelper, orderRepo, paymentRepo, partnerConnector, acquiringCoreAdapter, accountAdapter, refundSettleNotifier, installmentAdapter)
	refundService := service.NewRefundService(logger, usecase, distributedLock, orderConfigsHelper)
	handler := maintenance.NewMaintenance(cacheNoCaller, logger)
	grpcServer := server.NewGRPCServer(payment, paymentService, feeService, refundService, handler, logger)
	repayUsecase := repay.NewRepayUsecase(logger, paymentRepo, accountAdapter, orderRepo, acquiringCoreAdapter, partnerConnector, workflowAdapter)
	rePaymentService := service.NewRePaymentService(logger, repayUsecase)
	transactionUsecase := transaction.NewTransactionUsecase(logger, paymentRepo)
	transactionService := service.NewTransactionService(logger, transactionUsecase)
	sessionServiceClient, cleanup13, err := InitSessionGrpcConn(payment, logger)
	if err != nil {
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	authenticator := auth_service.NewClient(sessionServiceClient, logger)
	httpServer := server.NewHTTPServer(payment, rePaymentService, refundService, transactionService, authenticator, handler, logger)
	pollingWorkflow := polling.NewPollingWorkflow(purchaseUsecase, logger)
	temporalWorker := server.NewTemporalWorker(payment, logger, refundService, pollingWorkflow, workflowAdapter)
	app := newApp(logger, grpcServer, httpServer, temporalWorker)
	return app, func() {
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}

func wireKafkaHandler(payment *configs.Payment, logger log.Logger) (*service.PurchasingProcessHandler, func(), error) {
	db, cleanup := repo.NewSQLDatabase(payment, logger)
	repository := repo.NewRepository(db, logger)
	paymentRepo := repo.NewPaymentRepo(repository, logger)
	cimbConnectorClient, cleanup2, err := InitCIMBConnectorClient(payment, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	partnerConnector := cimb.NewService(cimbConnectorClient, logger)
	accountClient, cleanup3, err := InitAccountServiceClient(payment, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountAdapter := account_service.NewClient(accountClient, logger)
	purchaseInitiatedPublisher, cleanup4, err := NewPurchaseInitiatedPublisher(payment)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	purchaseAuthenticatedPublisher, cleanup5, err := NewPurchaseAuthenticatedPublisher(payment)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	peUpdateExchangeStatusPublisher, cleanup6, err := NewPEUpdateExchangeStatusPublisher(payment)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	workflowAdapter, cleanup7, err := InitTemporalClient(payment)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	paymentAuthenticationServiceClient, cleanup8, err := InitPaymentAuthServiceClient(payment, logger)
	if err != nil {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	paymentAuthAdapter := payment_auth.NewClient(paymentAuthenticationServiceClient, logger)
	purchaseUsecase := purchase.NewPurchaseUsecase(logger, paymentRepo, partnerConnector, accountAdapter, purchaseInitiatedPublisher, purchaseAuthenticatedPublisher, peUpdateExchangeStatusPublisher, workflowAdapter, paymentAuthAdapter)
	purchasingProcessHandler := service.NewPurchasingProcessHandler(purchaseUsecase, logger)
	return purchasingProcessHandler, func() {
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}

func wireRepayKafkaHandler(payment *configs.Payment, logger log.Logger) (*service.RePaymentService, func(), error) {
	db, cleanup := repo.NewSQLDatabase(payment, logger)
	repository := repo.NewRepository(db, logger)
	paymentRepo := repo.NewPaymentRepo(repository, logger)
	accountClient, cleanup2, err := InitAccountServiceClient(payment, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	accountAdapter := account_service.NewClient(accountClient, logger)
	orderRepo := repo.NewOrderRepo(repository, logger)
	userPaymentClient, cleanup3, err := InitAcquiringCoreClient(payment, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	orderConfigsHelper := ProvideOrderConfigs(payment)
	acquiringCoreAdapter := acquiring_core.NewClient(userPaymentClient, logger, orderConfigsHelper)
	cimbConnectorClient, cleanup4, err := InitCIMBConnectorClient(payment, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	partnerConnector := cimb.NewService(cimbConnectorClient, logger)
	workflowAdapter, cleanup5, err := InitTemporalClient(payment)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	repayUsecase := repay.NewRepayUsecase(logger, paymentRepo, accountAdapter, orderRepo, acquiringCoreAdapter, partnerConnector, workflowAdapter)
	rePaymentService := service.NewRePaymentService(logger, repayUsecase)
	return rePaymentService, func() {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}

func wireConsumerApp(payment *configs.Payment, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup := repo.NewSQLDatabase(payment, logger)
	repository := repo.NewRepository(db, logger)
	refundRepo := repo.NewRefundRepo(repository, logger)
	typesTransaction := repo.NewTransaction(repository)
	workflowAdapter, cleanup2, err := InitTemporalClient(payment)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	taskJobAdapter := task_job.NewTaskJob(logger, payment, workflowAdapter)
	cacheNoCaller, cleanup3, err := InitRedisCache(payment)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	distributedLock := dist_lock.NewDistLock(payment, cacheNoCaller, logger)
	orderConfigsHelper := ProvideOrderConfigs(payment)
	orderRepo := repo.NewOrderRepo(repository, logger)
	paymentRepo := repo.NewPaymentRepo(repository, logger)
	cimbConnectorClient, cleanup4, err := InitCIMBConnectorClient(payment, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	partnerConnector := cimb.NewService(cimbConnectorClient, logger)
	userPaymentClient, cleanup5, err := InitAcquiringCoreClient(payment, logger)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	acquiringCoreAdapter := acquiring_core.NewClient(userPaymentClient, logger, orderConfigsHelper)
	accountClient, cleanup6, err := InitAccountServiceClient(payment, logger)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountAdapter := account_service.NewClient(accountClient, logger)
	publisher, cleanup7, err := NewRefundSettlePublisher(payment)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	refundSettleNotifier := settle_notifier.NewNotifier(publisher, logger)
	installmentClient, cleanup8, err := InitInstallmentServiceClient(payment, logger)
	if err != nil {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	installmentAdapter := installment_service.NewClient(installmentClient, logger)
	usecase := refund.NewRefundUsecase(logger, refundRepo, typesTransaction, taskJobAdapter, distributedLock, payment, orderConfigsHelper, orderRepo, paymentRepo, partnerConnector, acquiringCoreAdapter, accountAdapter, refundSettleNotifier, installmentAdapter)
	refundService := service.NewRefundService(logger, usecase, distributedLock, orderConfigsHelper)
	refundConsumer := server.NewRefundConsumer(payment, logger, refundService)
	orderStatusConsumer := server.NewOrderStatusConsumer(payment, logger, refundService)
	refundSettleProcessConsumer := server.NewRefundSettleProcessConsumer(payment, logger, refundService)
	app := newConsumerApp(logger, refundConsumer, orderStatusConsumer, refundSettleProcessConsumer)
	return app, func() {
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
