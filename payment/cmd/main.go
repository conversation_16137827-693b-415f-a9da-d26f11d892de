package cmd

import (
	"context"
	"os"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/server"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/logging"
	"gitlab.zalopay.vn/fin/installment/payment-service/pkg/telemetry"
	"zalopay.io/zgo/kafka-client/delaycalculator"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/spf13/cobra"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	_ "go.uber.org/automaxprocs"
	kafka_client "zalopay.io/zgo/kafka-client"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string = "1.0.0"

	id, _ = os.Hostname()
)

var cmd = &cobra.Command{
	Use:   "start",
	Short: `start payment service`,
	RunE:  runCmd,
}

func NewMainAppCmd() *cobra.Command {
	return cmd
}

func newApp(logger log.Logger,
	gs *grpc.Server,
	hs *http.Server,
	ts *server.TemporalWorker,
) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(gs, hs, ts),
	)
}

func runCmd(cmd *cobra.Command, _ []string) error {
	bc, cfgCleanup := initConfig(cmd)
	defer cfgCleanup()

	err := telemetry.MustInitTracer(&telemetry.TracingInfo{
		SvcVer:      Version,
		SvcName:     bc.GetPayment().GetApp().GetName(),
		Environment: bc.GetPayment().GetApp().GetEnv(),
		AgentHost:   bc.GetPayment().GetTracer().GetAgentHost(),
		AgentPort:   bc.GetPayment().GetTracer().GetAgentPort(),
	})
	if err != nil {
		panic(err)
	}

	logger := logging.MustNewLogger(&logging.LoggerInfo{
		Id:      id,
		Name:    "payment-service",
		Version: Version,
	})

	purchasingProcessHandler, cleanup, err := wireKafkaHandler(bc.GetPayment(), logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	startWorker(bc.GetPayment().GetServer().GetConsumer().GetPurchaseInitiated(), purchasingProcessHandler.Process)

	startWorker(bc.GetPayment().GetServer().GetConsumer().GetPurchaseAuthenticated(), purchasingProcessHandler.ConfirmProcess)

	repayKafkaHandler, cleanup1, err := wireRepayKafkaHandler(bc.GetPayment(), log.GetLogger())
	if err != nil {
		panic(err)
	}
	defer cleanup1()

	startWorker(bc.GetPayment().GetServer().GetConsumer().GetAcOrderStatusUpdated(), repayKafkaHandler.OrderStatusChangeProcess)

	startWorker(bc.GetPayment().GetServer().GetConsumer().GetPurchaseCommissionUpdated(), purchasingProcessHandler.UpdatePaymentProcess)

	startWorker(bc.GetPayment().GetServer().GetConsumer().GetPaymentAuthSessionStatus(), purchasingProcessHandler.PaymentAuthProcess)

	app, cleanup, err := wireApp(bc.GetPayment(), logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
	return nil
}

func initConfig(cmd *cobra.Command) (*configs.Bootstrap, func() error) {
	confFlag := cmd.Flag("config")
	if confFlag == nil {
		panic("config flag not found")
	}

	confPath := confFlag.Value.String()
	confSource := file.NewSource(confPath)
	confInstance := config.New(config.WithSource(confSource))
	defer confInstance.Close()

	if err := confInstance.Load(); err != nil {
		panic(err)
	}

	var bc configs.Bootstrap
	if err := confInstance.Scan(&bc); err != nil {
		panic(err)
	}

	return &bc, confInstance.Close
}

func startWorker(kafkaServer *configs.Consumer_Kafka, processFunc kafka_client.ProcessFunc) {
	log.Info("start kafka server, ", kafkaServer.GetTopic())
	w, err := kafka_client.NewRetryWorker(context.Background(), kafka_client.RetryWorkerConfig{
		MaxRetry:        0,
		Brokers:         strings.Split(kafkaServer.GetBrokers(), ","),
		Topic:           kafkaServer.GetTopic(),
		GroupId:         kafkaServer.GetGroupId(),
		ConsumerName:    kafkaServer.GetGroupId(),
		ProcessFunc:     processFunc,
		IsDebug:         false,
		DelayCalculator: delaycalculator.NewExponentialDelayCalculator(time.Second*2, 5),
		StartOffset:     kafka_client.LastOffset,
	})
	if err != nil {
		log.Fatal(kafkaServer.GetGroupId(), " consumer worker can not init: ", err)
	}

	go func() {
		err := w.Start()
		if err != nil {
			log.Fatal(kafkaServer.GetGroupId(), " consumer worker can not start: ", err)
		}
	}()
}
