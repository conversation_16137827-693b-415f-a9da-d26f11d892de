version: "2"
sql:
  - schema: "sql/"
    queries: "sql/"
    engine: "mysql"
    gen:
      go:
        package: "sqlc"
        out: "../internal/repo/sqlc"
        sql_package: "database/sql"
        sql_driver: "github.com/go-sql-driver/mysql"
        emit_prepared_queries: true
        emit_interface: true
        emit_exact_table_names: true
        emit_json_tags: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: true
        emit_exported_queries: true
        output_files_suffix: _generated
        output_db_file_name: db_generated
        output_models_file_name: models_generated
#        overrides:
#          - column: "payment_logs.extra"
#            go_type: "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/repo/extra_data.AccountLogExtra"
    rules:
        - no-delete
rules:
  - name: no-delete
    message: "don't use delete statements"
    rule: |
      query.sql.contains("DELETE")



