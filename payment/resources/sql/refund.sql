CREATE TABLE `refund_logs`
(
    `id`                        bigint auto_increment NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `refund_id`                 bigint             NOT NULL,
    `amount`                    bigint             NOT NULL,
    `app_trans_id`              varchar(255)       NOT NULL,
    `app_id`                    int                NOT NULL,
    `payment_description`       varchar(1024)      NOT NULL,
    `refund_type`               enum ('AUTO', 'MANUAL')    NOT NULL,
    `status`                    enum ('INIT', 'SUCCESS', 'FAILED', 'PENDING', 'PROCESSING') NOT NULL,
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `process_type`              enum ('SETTLEMENT', 'REPAYMENT', 'FUNDBACK', 'MANUAL') NULL,
    `deadline_at`               datetime           NULL,
    `extra`                     json               NOT NULL,
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zp_trans_id` (`zp_trans_id`),
    KEY `idx_deadline_process_type` (`deadline_at`, `process_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `refund_settle`
(
    `id`                        bigint auto_increment NOT NULL,
    `status`                    enum ('INIT', 'PENDING', 'PROCESSING', 'SETTLED', 'COMPLETED') NOT NULL DEFAULT 'INIT',
    `zp_trans_id`               bigint             NOT NULL,
    `net_refund_amount`         bigint             NOT NULL,
    `total_refund_amount`       bigint             NOT NULL,
    `settlement_amount`         bigint             NOT NULL,
    `user_topup_amount`         bigint             NOT NULL DEFAULT 0,
    `user_payback_amount`       bigint             NOT NULL DEFAULT 0,
    `event_version`             int                NOT NULL DEFAULT 0,
    `metadata`                  json               NOT NULL DEFAULT '{}',
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_settle` (`zp_trans_id`)
)ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- name: CreateRefundLog :execresult
INSERT INTO refund_logs(
                        zp_trans_id,
                        refund_id,
                        amount,
                        app_trans_id,
                        app_id,
                        payment_description,
                        refund_type,
                        status,
                        error_code,
                        error_message,
                        process_type,
                        extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: UpdateRefundLog :execresult
UPDATE refund_logs
SET
    status=?,
    error_code=?,
    error_message=?,
    extra=?
WHERE id = ?;

-- name: UpdateRefundLogComplete :execresult
UPDATE refund_logs
SET status=?,
    deadline_at=?,
    extra=?
WHERE id = ?;

-- name: GetRefundLog :one
SELECT *
FROM refund_logs
WHERE id = ?;

-- name: GetRefundLogForUpdate :one
SELECT *
FROM refund_logs
WHERE id = ? FOR UPDATE;

-- name: GetRefundLogByRefundID :one
SELECT *
FROM refund_logs
WHERE refund_id = ?;

-- name: GetRefundLogsByZPTransID :many
SELECT *
FROM refund_logs
WHERE zp_trans_id = ?;

-- name: GetRefundLogsByProcessTypeAndZpTransID :many
SELECT *
FROM refund_logs
WHERE zp_trans_id = ? AND process_type = ?;

-- name: GetRefundLogsExpiredByZPTransID :many
SELECT *
FROM refund_logs
WHERE zp_trans_id = ? 
    AND process_type = ? 
    AND deadline_at <= NOW();

-- name: GetRefundLogsExpiredByZPTransIDForUpdate :many
SELECT *
FROM refund_logs
WHERE zp_trans_id = ? 
    AND process_type = ? 
    AND deadline_at <= NOW() 
FOR UPDATE;

-- name: UpdateRefundLogsProcessTypeByIDs :exec
UPDATE refund_logs
SET process_type = ?
WHERE id IN (sqlc.slice('ids'));

-- name: CreateRefundSettle :execresult
INSERT INTO refund_settle(
  zp_trans_id,
  net_refund_amount,
  total_refund_amount,
  settlement_amount,
  event_version,
  metadata
) VALUES (?, ?, ?, ?, ?, ?);
  

-- name: GetRefundSettle :one
SELECT *
FROM refund_settle
WHERE zp_trans_id = ?;

-- name: GetRefundSettleByID :one
SELECT *
FROM refund_settle
WHERE id = ?;

-- name: GetRefundSettleForUpdate :one
SELECT *
FROM refund_settle
WHERE id = ? FOR UPDATE;

-- name: GetListRefundSettleByStatus :many
SELECT *
FROM refund_settle
WHERE status = ? AND id > sqlc.arg(id_gt) 
LIMIT ?;

-- name: UpdateRefundSettleRecon :exec
UPDATE refund_settle
SET status = ?,
    net_refund_amount = ?,
    total_refund_amount = ?,
    settlement_amount = ?
WHERE id = ?;

-- name: UpdateRefundSettle :execresult
UPDATE refund_settle
SET status=?,
    net_refund_amount = ?,
    total_refund_amount = ?,
    settlement_amount = ?,
    user_topup_amount = ?,
    user_payback_amount = ?,
    event_version = ?,
    metadata = ?
WHERE id = ?;

-- name: UpdateRefundSettleEvent :execresult
UPDATE refund_settle
SET event_version = sqlc.arg(new_version),
    metadata = ?
WHERE id = ? and event_version = sqlc.arg(old_version);

-- name: UpdateRefundSettleStatus :exec
UPDATE refund_settle
SET status = ?
WHERE id = ?;

-- name: UpdateRefundSettlementAmount :exec
UPDATE refund_settle
SET settlement_amount = ?
WHERE id = ?;

-- name: UpdateRefundTopupsAmount :exec
UPDATE refund_settle
SET user_topup_amount = ?
WHERE id = ?;

-- name: UpdateRefundPaybackInfo :exec
UPDATE refund_settle
SET status = ?,
    user_payback_amount = ?
WHERE id = ?;

-- name: GetListRefundSettleIDHasExpired :many
SELECT rs.id
FROM refund_settle rs
WHERE rs.status = ?
    AND rs.id > sqlc.arg(id_gt)
    AND EXISTS(
    SELECT 1 FROM refund_logs rl
    WHERE rl.zp_trans_id = rs.zp_trans_id
        AND rl.process_type = ?
        AND rl.deadline_at <= NOW())
LIMIT ?;