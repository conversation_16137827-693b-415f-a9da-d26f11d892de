CREATE TABLE `payment_logs`
(
    `id`                        bigint auto_increment    NOT NULL,
    `order_id`                  bigint                   NULL,
    `zp_trans_id`               bigint                   NOT NULL,
    `payment_no`                varchar(256)             NOT NULL DEFAULT '',
    `system_id`                 int                      NOT NULL,
    `trans_type`                int                      NOT NULL,
    `partner_req_id`            varchar(36)              NULL,
    `partner_trans_id`          varchar(255)             NOT NULL,
    `bank_trans_id`             varchar(255)             NOT NULL DEFAULT '',
    `app_trans_id`              varchar(255)             NOT NULL,
    `account_id`                bigint                   NOT NULL,
    `zalopay_id`                bigint                   NOT NULL,
    `user_bank_account_number`  varchar(255)             NOT NULL DEFAULT '',
    `bank_code`                 varchar(15)              NOT NULL,
    `bank_routing_number`       varchar(255)             NOT NULL,
    `app_id`                    int                      NOT NULL,
    `amount`                    bigint                   NOT NULL,
    `status`                    varchar(20)              NOT NULL,
    `bank_status`               varchar(20)              NOT NULL DEFAULT '',
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `current_available_balance` bigint                   NOT NULL DEFAULT 0,
    `payment_description`       varchar(1024)            NOT NULL,
    `extra`                     json                     NOT NULL,
    `installment_tenor`         int                      NOT NULL,
    `installment_interest_rate` float                    NOT NULL DEFAULT 0,
    `pe_request_id`            varchar(255)              NOT NULL DEFAULT '',
    `fs_charge_info`            json not null,
    `bank_connector_code` varchar(255) NOT NULL DEFAULT '',
    `escrow_account_no` varchar(255) NOT NULL DEFAULT '',
    `created_at`                datetime                 NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `is_commission` bool default false not null,
    `is_commission_updated_at` datetime default CURRENT_TIMESTAMP not null,
    `device_id`                 varchar(255)             NOT NULL DEFAULT '',
    `user_ip`                   varchar(255)             NOT NULL DEFAULT '',
    PRIMARY KEY (`id`, `created_at`),
    UNIQUE KEY `uk_payment_logs_v2` (`zp_trans_id`, `trans_type`, `order_id`),
    UNIQUE KEY `uk_partner_req_id` (`partner_req_id`),
    INDEX `idx_order_trans` (`order_id`, `trans_type`)
    INDEX `payment_logs_payment_no_index` (`payment_no`),
    INDEX `payment_logs_zalopay_id_bank_code_trans_type_created_at_index` (`zalopay_id`, `bank_code`, `trans_type`, `created_at`),
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- name: CreatePaymentLog :execresult
INSERT INTO payment_logs(zp_trans_id,
                         order_id,
                         system_id,
                         trans_type,
                         partner_req_id,
                         partner_trans_id,
                         app_trans_id,
                         account_id,
                         zalopay_id,
                         user_bank_account_number,
                         bank_code,
                         bank_routing_number,
                         app_id,
                         amount,
                         status,
                         bank_status,
                         error_code,
                         error_message,
                         current_available_balance,
                         payment_description,
                         extra,
                         installment_tenor,
                         installment_interest_rate,
                         pe_request_id,
                         fs_charge_info,
                         bank_connector_code,
                         escrow_account_no,
                         payment_no,
                         device_id,
                         user_ip)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: UpdatePaymentLog :execresult
UPDATE payment_logs
SET partner_trans_id= ?,
    bank_trans_id=?,
    account_id= ?,
    user_bank_account_number= ?,
    bank_code= ?,
    bank_routing_number= ?,
    status= ?,
    bank_status= ?,
    error_code= ?,
    error_message= ?,
    current_available_balance= ?,
    payment_description= ?,
    extra= ?,
    bank_connector_code=?,
    escrow_account_no=?,
    updated_at = CURRENT_TIMESTAMP
WHERE id = ?;

-- name: GetPaymentLogByZPTransID :one
SELECT *
FROM payment_logs
WHERE zp_trans_id = ?;

-- name: GetPaymentLogByID :one
SELECT *
FROM payment_logs
WHERE id = ?;

-- name: GetPaymentLogByPaymentNo :one
SELECT *
FROM payment_logs
WHERE payment_no = ?;

-- name: GetPaymentLogByOrderID :one
SELECT *
FROM payment_logs
WHERE order_id IS NOT NULL AND order_id = ?;

-- name: UpdatePaymentLogStatus :exec
UPDATE payment_logs
SET status = ?,
    bank_status = ?,
    error_code = ?,
    error_message = ?,
    extra = ?
WHERE id = ?;

-- name: UpdatePaymentLogOrderIDs :exec
UPDATE payment_logs
SET order_id = ?,
    zp_trans_id = ?,
    app_trans_id = ?
WHERE id = ?;

-- name: UpdatePartnerTransID :exec
UPDATE payment_logs
SET partner_trans_id = ?
WHERE id = ?;

-- name: UpdateCommission :execresult
UPDATE payment_logs
SET is_commission = ?,
    is_commission_updated_at = CURRENT_TIMESTAMP
WHERE zp_trans_id = ? and trans_type = 200;