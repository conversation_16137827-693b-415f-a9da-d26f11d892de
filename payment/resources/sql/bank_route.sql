CREATE TABLE `bank_route`
(
    `id`                  bigint       NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `internal_bank_code`  varchar(15)  NOT NULL,
    `partner_bank_code`   varchar(15)  NOT NULL,
    `bank_account_name`   varchar(255) NOT NULL,
    `bank_account_number` varchar(255) NOT NULL,
    `trans_type`          int          NOT NULL,
    `status`              enum ('active', 'inactive') NOT NULL DEFAULT 'active',
    `created_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_bank_route` (`internal_bank_code`, `bank_account_number`, `trans_type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- name: CreateBankRoute :exec
INSERT INTO bank_route (internal_bank_code,
                        partner_bank_code,
                        bank_account_name,
                        bank_account_number,
                        trans_type,
                        status)
VALUES (?, ?, ?, ?, ?, ?);

-- name: UpdateBankRoute :exec
UPDATE bank_route
SET status = ?
WHERE internal_bank_code = ?
  AND trans_type = ?;

-- name: GetBankRoute :one
SELECT *
FROM bank_route
WHERE internal_bank_code = ?
  AND trans_type = ?
  AND status = ?;