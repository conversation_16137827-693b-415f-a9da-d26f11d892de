CREATE TABLE `partner`
(
    `id`         bigint                    NOT NULL AUTO_INCREMENT,
    `code`       varchar(32)               NOT NULL,
    `status`     enum ('enable','disable') NOT NULL,
    `created_at` datetime                  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime                  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_partner_name` (`code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- name: CreatePartner :exec
INSERT INTO partner (code, status)
values (?, ?);

-- name: GetPartnerByID :one
SELECT *
FROM partner
WHERE id = ?;

-- name: GetPartners :many
SELECT *
FROM partner
ORDER BY id;