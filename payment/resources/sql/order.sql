CREATE TABLE `order`
(
    `id`             bigint not null                                 auto_increment,
    `zalopay_id`     bigint                                          NOT NULL,
    `partner_code`   varchar(15)                                     NOT NULL,
    `system_id`      int                                             NOT NULL,
    `app_id`         int                                             NOT NULL,
    `app_trans_id`   varchar(255)                                    NOT NULL DEFAULT '',
    `zp_trans_id`    varchar(255)                                    NOT NULL DEFAULT '',
    `zp_trans_token` varchar(255)                                    NOT NULL DEFAULT '',
    `refund_id`      bigint                                          NOT NULL DEFAULT 0,
    `settle_id`      bigint                                          NOT NULL DEFAULT 0,
    `amount`         bigint                                          NOT NULL,
    `status`         enum ('init', 'pending', 'processing', 'succeeded', 'failed', 'cancelled') NOT NULL,
    `type`           enum ('repayment', 'refund')                    NOT NULL,
    `stage`          varchar(50)                                     NULL DEFAULT NULL,
    `extra`          json                                            NOT NULL,
    `description`    varchar(255)                                    NOT NULL,
    `statement_id`   bigint                                          NOT NULL,
    `statement_date` date                                            NULL DEFAULT NULL,
    `created_at`     datetime                                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`     datetime                                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`, `created_at`),
    UNIQUE KEY `k_app_trans_id` (`app_trans_id`, `app_id`, `type`, `created_at`),
    KEY `k_zalopay_id` (`zalopay_id`),
    INDEX `idx_refund_id_type_stage` (`refund_id`, `type`, `stage`),
    INDEX `idx_settle_id_type_stage` (`settle_id`, `type`, `stage`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- name: CreateRepaymentOrder :execresult
INSERT INTO `order` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     status,
                     type,
                     zp_trans_token,
                     description,
                     statement_id,
                     statement_date,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreateRefundTopupOrder :execresult
INSERT INTO `order` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     type,
                     stage,
                     status,
                     settle_id,
                     zp_trans_token,
                     description,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreateRefundFundbackOrder :execresult
INSERT INTO `order` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     type,
                     stage,
                     status,
                     settle_id,
                     zp_trans_token,
                     description,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreateRefundSettleOrder :execresult
INSERT INTO `order` (zalopay_id,
                     partner_code,
                     system_id,
                     app_trans_id,
                     app_id,
                     amount,
                     type,
                     stage,
                     status,
                     refund_id,
                     settle_id,
                     zp_trans_token,
                     description,
                     extra)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: GetOrder :one
SELECT *
FROM `order`
WHERE app_trans_id = ?
  AND app_id = ?
  AND type = ?;

-- name: GetOrderByID :one
SELECT *
FROM `order`
WHERE id = ?;

-- name: GetOrderByAppTransTypeAndStage :one
SELECT *
FROM `order`
WHERE app_trans_id = ?
  AND app_id = ?
  AND type = ?
  AND stage = ?;

-- name: GetOrderBySettleIDAndTypeAndStage :one
SELECT *
FROM `order`
WHERE refund_id = ?
  AND type = ?
  AND stage = ?;

-- name: GetOrderByRefundIDAndTypeAndStage :one
SELECT *
FROM `order`
WHERE refund_id = ?
  AND type = ?
  AND stage = ?;

-- name: UpdateOrderStatus :exec
UPDATE `order`
SET status = ?,
    zp_trans_id = ?
WHERE id = ?;

-- name: UpdateOrderStatusAndExtra :exec
UPDATE `order`
SET status = ?,
    zp_trans_id = ?,
    extra = ?
WHERE id = ?;

-- name: ListOrderByTypeSettleAndStage :many
SELECT *
FROM `order`
WHERE `settle_id` = ?
  AND `type` = ?
  AND `stage` = ?;
