#!/usr/env/bash

# Dedicated to <PERSON> 1961 - 03/08/2023
# Thank you for bring us V<PERSON>, the best editor on the world

n_mock_generated=1 #global var used for gen_mock_from_def function only please DO NOT use it anywhere else
# Running mockgen based on definition
# 1. extract file directory and generate command
# 2. cd into the directory and run go generate command
# 3. cd back into root directory
function gen_mock_from_def {
  while read mockgen_file_relative_path;
  do
    echo "["$n_mock_generated"] START ========================================================================="
    echo ""

    # by default go generate will look for //go:genrate commands within the file and execute them all
    # we can specified which command should be run through the -run flag
    # here we will execute command with 'mockgen' run
    go_cmd="go generate -run mockgen "$mockgen_file_relative_path""
    echo "running: "$go_cmd""
    $go_cmd

    echo ""
    echo "["$n_mock_generated"] END ==========================================================================="
    n_mock_generated=$((n_mock_generated+1))
  done;
}

# find all file with generate command
# grep -rni is probably faster but it is not POSIX compliant
# hence not guarenteed to work on different environments
# see https://www.unix.com/man-page/posix/1p/grep/

echo "========================================================================="
echo "Start updating mocks for Installment"
echo "Checking for mock definitions..."
mockgen_defs=$(find . -type f -print0 | xargs -0 grep "//go:generate mockgen")
mockgen_files=$(echo "$mockgen_defs" | cut -d':' -f1 | grep ".go" | sort | uniq)
no_mockgen_files=$(echo "$mockgen_files" | wc -l)
echo "...$no_mockgen_files files with mockgen found"
echo "========================================================================="


echo "$mockgen_files" | gen_mock_from_def
