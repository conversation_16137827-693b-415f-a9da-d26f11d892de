payment:
  server:
    grpc:
      addr: "0.0.0.0:9090"
      timeout: 30s
      api_keys: {{ fin.installment.payment.server.grpc.api_keys }}
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    consumer:
      purchase_initiated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "qc.fin.installment.payment.purchase_init"
        group_id: "qc.fin.installment.payment.purchase_init.group"
      purchase_authenticated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "qc.fin.installment.payment.purchase_process"
        group_id: "qc.fin.installment.payment.purchase_process.group"
      ac_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "fin_installment_repay_orders_qc"
        group_id: "fin_installment_repay_orders_group_qc"
      purchase_commission_updated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "qc.fin.installment.payment.update_payment"
        group_id: "qc.fin.installment.payment.update_payment.group"
      payment_auth_session_status:
        brokers: "**********:9092,**********:9092,**********:9092"
        topic: "payment_authentication_auth_session_status_qc"
        group_id: "qc.fin.installment.payment_authentication_auth_session_status_qc.group"
  data:
    database:
      driver: "mysql"
      host: {{ private-cicd.zalopay.mysql.1100090.host }}
      port: {{ private-cicd.zalopay.mysql.1100090.port }}
      username: {{ private-cicd.zalopay.mysql.1100090.user.262.username }}
      password: {{ private-cicd.zalopay.mysql.1100090.user.262.password }}
      db_name: "fin_installment_payment"
      max_open_conn: 32
      max_idle_conn: 8
      conn_max_life_time: 600s
      allow_native_passwords: true
    redis_cache:
      address: {{ fin.installment.redis.address }}
      username: {{ fin.installment.redis.username }}
      password: {{ fin.installment.redis.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    acquiring_core_service:
      grpc:
        address: {{ fin.installment.adapters.acquiring_core_service.address }}
        timeout: 30s
        secured: {{ fin.installment.adapters.acquiring_core_service.secured }}
        client_id: {{ fin.installment.adapters.acquiring_core_service.client_id }}
        client_key: {{ fin.installment.adapters.acquiring_core_service.client_key }}
      partners:
        - partner_code: "CIMB"
          callback_url: "https://qcbnpl-gateway.zalopay.vn/api/v1/cimb/order/callback"
          app_id: 3721
          mac_key: "jLK3cxAk2vitTlatW88nFyGyv4xbgJlH"
          callback_key: "J5iioDzcri6KjS1H1CBpLjTL5T87RJSP"
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
      timeout: 10s
    payment_auth:
      address: "qcgrpc-pe-authentication.zalopay.vn:443"
      secured: true
      timeout: 15s
    installment_service:
      address: {{ fin.installment.adapters.management_service.address }}
      secured: {{ fin.installment.adapters.management_service.secured }}
      timeout: 30s
  publishers:
    purchase_initiated_event:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "qc.fin.installment.payment.purchase_init"
    purchase_authenticated_event:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "qc.fin.installment.payment.purchase_process"
    pe_update_exchange_status_event:
      brokers: {{ fin.installment.payment.kafka.pe.brokers }}
      topic: "assetexchangev2_exchange_asset_update_qc"
  temporal:
    address: {{ fin.installment.adapters.temporal.address }}
    namespace: "default"
    enable_ssl: true
  schedulers:
    payment_status:
      queue_name: "installment.payment"
      workflow_type: "PollingPaymentStatus"
  app:
    env: "qc"
    name: "fin.installment.payment"
    cimb_zas_id: 608468526543716489
  tracer:
    service_name: fin.installment.payment
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
    fractions: 1.0