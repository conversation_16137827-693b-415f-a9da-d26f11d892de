payment:
  server:
    grpc:
      addr: "0.0.0.0:9090"
      timeout: 30s
      api_keys: {{ fin.installment.payment.server.grpc.api_keys }}
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    consumer:
      purchase_initiated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
        topic: "prod.fin.installment.payment.purchase_init"
        group_id: "prod.fin.installment.payment.purchase_init.group"
      purchase_authenticated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
        topic: "prod.fin.installment.payment.purchase_process"
        group_id: "prod.fin.installment.payment.purchase_process.group"
      ac_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "production.aqr.core.upay.orders"
        group_id: "prod.fin.installment.payment.ac_order_status_updated.group"
        num_workers: 2
      topup_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "production.aqr.core.upay.orders"
        group_id: "prod.fin.installment.topup_order_status_updated.group"
      settle_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "production.aqr.core.upay.orders"
        group_id: "prod.fin.installment.settle_order_status_updated.group"
      fundback_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "production.aqr.core.upay.orders"
        group_id: "prod.fin.installment.fundback_order_status_updated.group"
      purchase_commission_updated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
        topic: "prod.fin.installment.payment.update_payment"
        group_id: "prod.fin.installment.payment.update_payment.group"
      payment_auth_session_status:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-2.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-2.port }}"
        topic: "payment_authentication_auth_session_status"
        group_id: "prod.fin.installment.payment_authentication_auth_session_status.group"
      ac_refund_info_updated:
        brokers: {{ fin.installment.payment.ac_kafka.refunds.brokers }}
        topic: "ZPReportTransLog"
        group_id: "prod.fin.installment.payment.ac_refund.group"
        num_workers: 3
      refund_settle_process:
        brokers: "TBU"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_process.group"
        num_workers: 2
      refund_settle_request:
        brokers: "TBU"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_request.group"
        num_workers: 2
      refund_settle_response:
        brokers: "TBU"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_response.group"
        num_workers: 2
  data:
    database:
      driver: "mysql"
      host: {{ private-cicd.zalopay.mysql.148.host }}
      port: {{ private-cicd.zalopay.mysql.148.port }}
      username: {{ private-cicd.zalopay.mysql.148.user.1015.username }}
      password: {{ private-cicd.zalopay.mysql.148.user.1015.password }}
      db_name: "fin_installment_payment"
      max_open_conn: 64
      max_idle_conn: 16
      conn_max_life_time: 600s
      allow_native_passwords: true
    redis_cache:
      address: "{{ private-cicd.zalopay.redis.host }}:{{ private-cicd.zalopay.redis.port }}"
      username:
      password: {{ private-cicd.zalopay.redis.1000107.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    acquiring_core_service:
      grpc:
        address: {{ fin.installment.adapters.acquiring_core_service.address }}
        timeout: 30s
        secured: {{ fin.installment.adapters.acquiring_core_service.secured }}
        client_id: {{ fin.installment.adapters.acquiring_core_service.client_id }}
        client_key: {{ fin.installment.adapters.acquiring_core_service.client_key }}
      partners:
        - partner_code: "CIMB"
          callback_url: ""
          app_id: 3721
          mac_key: "jLK3cxAk2vitTlatW88nFyGyv4xbgJlH"
          callback_key: "J5iioDzcri6KjS1H1CBpLjTL5T87RJSP"
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
      timeout: 10s
    payment_auth:
      address: "grpc-pe-authentication.zalopay.vn:443"
      secured: true
      timeout: 10s
    installment_service:
      address: {{ fin.installment.adapters.management_service.address }}
      secured: {{ fin.installment.adapters.management_service.secured }}
      timeout: 30s
  publishers:
    refund_settle_process:
      brokers: "TBU"
      topic: "prod.fin.installment.payment.refund.settle_process"
    purchase_initiated_event:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
      topic: "prod.fin.installment.payment.purchase_init"
    purchase_authenticated_event:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
      topic: "prod.fin.installment.payment.purchase_process"
    pe_update_exchange_status_event:
      brokers: {{ fin.installment.payment.kafka.pe.brokers }}
      topic: "assetexchangev2_exchange_asset_update"
  temporal:
    address: {{ fin.installment.adapters.temporal.address }}
    namespace: {{ fin.installment.adapters.temporal.namespace }}
    enable_ssl: true
  schedulers:
    payment_status:
      queue_name: "installment.payment"
      workflow_type: "PollingPaymentStatus"
    refund_settle_recon:
      queue_name: "installment.refund"
      workflow_type: "ReconcileRefundSettle"
    refund_discharge_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingEarlyDischarge"
    refund_fundback_process:
      queue_name: "installment.refund"
      workflow_type: "FundbackAfterSettlement"
    refund_expired_process:
      queue_name: "installment.refund"
      workflow_type: "RefundExpiredProcess"
    refund_expired_repay_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingExpiredRefundRepay"
  app:
    env: "prod"
    name: "fin.installment.payment"
    cimb_zas_id: 621488947198148745
  tracing:
    service_name: fin.installment.payment
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
    fractions: 1.0
  refund:
    trans_alive_in: 864000s 
  order_configs:
    repayment:
      - app_id: 3721
        partner_code: "CIMB"
        mac_key: "jLK3cxAk2vitTlatW88nFyGyv4xbgJlH"
        callback_key: "J5iioDzcri6KjS1H1CBpLjTL5T87RJSP"
    refund_topup:
      - app_id: 4142
        mac_key: ""
        callback_key: ""
        product_code: "TU010"
    refund_settle:
      - app_id: 4143
        mac_key: ""
        callback_key: ""
        merchant_id: "456668"
        product_code: "AC005"
      - app_id: 4143
        mac_key: ""
        callback_key: ""
        merchant_id: "456668"
        partner_code: "CIMB"
        product_code: "AC005"
    refund_fundback:
      - app_id: 4142
        mac_key: ""
        callback_key: ""
        product_code: "TF028"