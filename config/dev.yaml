payment:
  server:
    grpc:
      addr: "0.0.0.0:9090"
      timeout: 30s
      api_keys: {{ fin.installment.payment.server.grpc.api_keys }}
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    consumer:
      purchase_initiated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "dev.fin.installment.payment.purchase_init"
        group_id: "dev.fin.installment.payment.purchase_init.group"
      purchase_authenticated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "dev.fin.installment.payment.purchase_process"
        group_id: "dev.fin.installment.payment.purchase_process.group"
      ac_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "qc.aqr.core.upay.orders"
        group_id: "fin_installment_repay_orders_group"
      topup_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "qc.aqr.core.upay.orders"
        group_id: "dev.fin.installment.topup_order_status_updated.group"
      settle_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "qc.aqr.core.upay.orders"
        group_id: "dev.fin.installment.settle_order_status_updated.group"
        use_first_offset: true
      fundback_order_status_updated:
        brokers: {{ fin.installment.payment.ac_kafka.orders.brokers }}
        topic: "qc.aqr.core.upay.orders"
        group_id: "dev.fin.installment.fundback_order_status_updated.group"
      purchase_commission_updated:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "dev.fin.installment.payment.update_payment"
        group_id: "dev.fin.installment.payment.update_payment.group"
      payment_auth_session_status:
        brokers: "**********:9092,**********:9092,**********:9092"
        topic: "payment_authentication_auth_session_status_qc"
        group_id: "dev.fin.installment.payment_authentication_auth_session_status_qc.group"
      ac_refund_info_updated:
        brokers: {{ fin.installment.payment.ac_kafka.refunds.brokers }}
        topic: "ZPReportTransLog"
        group_id: "dev.fin.installment.payment.ac_refund.group"
        num_workers: 2
      refund_settle_process:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "dev.fin.installment.payment.refund.settle_process"
        group_id: "dev.fin.installment.payment.refund.settle_process.group"
        num_workers: 2
      refund_settle_request:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "dev.fin.installment.payment.refund.settle_process"
        group_id: "dev.fin.installment.payment.refund.settle_request.group"
        num_workers: 2
      refund_settle_response:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "dev.fin.installment.payment.refund.settle_process"
        group_id: "dev.fin.installment.payment.refund.settle_response.group"
        num_workers: 2
  data:
    database:
      driver: "mysql"
      host: {{ private-cicd.zalopay.mysql.1100090.host }}
      port: {{ private-cicd.zalopay.mysql.1100090.port }}
      username: {{ private-cicd.zalopay.mysql.1100090.user.262.username }}
      password: {{ private-cicd.zalopay.mysql.1100090.user.262.password }}
      db_name: "fin_installment_payment"
      max_open_conn: 32
      max_idle_conn: 8
      conn_max_life_time: 600s
      allow_native_passwords: true
    redis_cache:
      address: {{ fin.installment.redis.address }}
      username: {{ fin.installment.redis.username }}
      password: {{ fin.installment.redis.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    acquiring_core_service:
      grpc:
        address: {{ fin.installment.adapters.acquiring_core_service.address }}
        timeout: 30s
        secured: {{ fin.installment.adapters.acquiring_core_service.secured }}
        client_id: {{ fin.installment.adapters.acquiring_core_service.client_id }}
        client_key: {{ fin.installment.adapters.acquiring_core_service.client_key }}
      partners:
        - partner_code: "CIMB"
          callback_url: "https://qcbnpl-gateway.zalopay.vn/api/v1/cimb/order/callback"
          app_id: 3721
          mac_key: "jLK3cxAk2vitTlatW88nFyGyv4xbgJlH"
          callback_key: "J5iioDzcri6KjS1H1CBpLjTL5T87RJSP"
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
      timeout: 10s
    payment_auth:
      address: "qcgrpc-pe-authentication.zalopay.vn:443"
      secured: true
      timeout: 15s
    installment_service:
      address: {{ fin.installment.adapters.management_service.address }}
      secured: {{ fin.installment.adapters.management_service.secured }}
      timeout: 30s
  publishers:
    refund_settle_process:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "dev.fin.installment.payment.refund.settle_process"
    purchase_initiated_event:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "dev.fin.installment.payment.purchase_init"
    purchase_authenticated_event:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "dev.fin.installment.payment.purchase_process"
    pe_update_exchange_status_event:
      brokers: {{ fin.installment.payment.kafka.pe.brokers }}
      topic: "assetexchangev2_exchange_asset_update_qc"
  temporal:
    address: {{ fin.installment.adapters.temporal.address }}
    namespace: {{ fin.installment.adapters.temporal.namespace }}
    enable_ssl: true
  schedulers:
    payment_status:
      queue_name: "installment.payment"
      workflow_type: "PollingPaymentStatus"
    refund_settle_recon:
      queue_name: "installment.refund"
      workflow_type: "ReconcileRefundSettle"
    refund_discharge_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingEarlyDischarge"
    refund_fundback_process:
      queue_name: "installment.refund"
      workflow_type: "FundbackAfterSettlement"
    refund_expired_process:
      queue_name: "installment.refund"
      workflow_type: "RefundExpiredProcess"
    refund_expired_repay_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingExpiredRefundRepay"
  app:
    env: "dev"
    name: "fin.installment.payment"
    cimb_zas_id: 608468526543716489
  tracer:
    service_name: fin.installment.payment
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
    fractions: 1.0
  refund:
    trans_alive_in: 1800s
  order_configs:
    repayment:
      - app_id: 3721
        partner_code: "CIMB"
        mac_key: "jLK3cxAk2vitTlatW88nFyGyv4xbgJlH"
        callback_key: "J5iioDzcri6KjS1H1CBpLjTL5T87RJSP"
    refund_topup:
      - app_id: 4142
        mac_key: "4EZ8hivN056W0NoBICQdOyqqzoWDOqBj"
        callback_key: "kC5taID5INM63meGZRKu5qEOQZwy83e0"
        product_code: "TU010"
    refund_settle:
      - app_id: 4143
        mac_key: "tkukad8KzTXguZNhCQ3m0XSHjTpl1Lem"
        callback_key: "C5TXlAuy5Y0yg31B52R4Ny02ZO0Ukqim"
        merchant_id: "456668"
      - app_id: 4143
        mac_key: "tkukad8KzTXguZNhCQ3m0XSHjTpl1Lem"
        callback_key: "C5TXlAuy5Y0yg31B52R4Ny02ZO0Ukqim"
        merchant_id: "456668"
        partner_code: "CIMB"
    refund_fundback:
      - app_id: 4142
        mac_key: "tkukad8KzTXguZNhCQ3m0XSHjTpl1Lem"
        callback_key: "C5TXlAuy5Y0yg31B52R4Ny02ZO0Ukqim"
        product_code: "TF028"
